import os
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def load_data():
    with open("data.txt", "r", encoding="utf-8") as file:
        return file.read()

def get_response(messages):
    try:
        response = client.chat.completions.create(
            model="gpt-4",
            messages=messages,
            temperature=0.7,
            max_tokens=1000
        )
        return response.choices[0].message.content
    except Exception as e:
        return f"An error occurred: {str(e)}"

def format_message(text):
    """Format the message for WhatsApp display"""
    # Replace shortened "Location:" links with full URLs
    lines = text.split('\n')
    formatted_lines = []
    
    for line in lines:
        # Keep empty lines for spacing
        if not line.strip():
            formatted_lines.append('')
            continue
        
        # Ensure proper spacing around URLs
        if 'http' in line:
            line = line.replace('Location:', '\nLocation:')
        
        formatted_lines.append(line)
    
    # Join lines with proper spacing
    return '\n'.join(formatted_lines)

def generate_reply(user_message):
    context_data = load_data()
    messages = [
        {"role": "system", "content": """You are a friendly hotel concierge. When responding:
        - Use a warm, conversational tone
        - Keep URLs in their full form
        - Add proper spacing between different sections
        - Format lists with clear line breaks
        - Be concise but friendly
        - Don't use shortened URLs
        Context data: """ + context_data}
    ]
    messages.append({"role": "user", "content": user_message})
    response = get_response(messages)
    
    # Format the response before returning
    formatted_response = format_message(response)
    return formatted_response

def main():
    # Load the context data
    context_data = load_data()
    
    # Initialize conversation history
    messages = [
        {"role": "system", "content": f"You are a helpful assistant. Use this context to answer questions: {context_data}"},
    ]
    
    print("Chat with the bot (type 'quit' to exit)")
    
    while True:
        user_input = input("\nYou: ").strip()
        
        if user_input.lower() == 'quit':
            break
            
        # Add user message to history
        messages.append({"role": "user", "content": user_input})
        
        # Get response from OpenAI
        response = get_response(messages)
        
        # Add assistant response to history
        messages.append({"role": "assistant", "content": response})
        
        print(f"\nAssistant: {response}")

if __name__ == "__main__":
    main()
