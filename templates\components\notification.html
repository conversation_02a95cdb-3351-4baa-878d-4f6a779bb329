<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Notification Component</title>
    <!-- Import only the necessary Franken/UIkit CSS -->
    <link rel="stylesheet" href="https://unpkg.com/franken-ui@1.1.0/dist/css/core.min.css" />
    <!-- Import the Franken/UIkit JavaScript -->
    <script src="https://unpkg.com/franken-ui@1.1.0/dist/js/core.min.js"></script>
    <style>
        /* Add any isolated custom CSS for this notification here */
    </style>
</head>
<body class="light">
    <button class="uk-btn uk-btn-default" type="button" onclick="showNotification()">
        Click me
    </button>
    <script>
        function showNotification() {
            UIkit.notification({
                message: "Notification message!",
                status: "primary",
                pos: "top-right",
                timeout: 5000
            });
        }
    </script>
</body>
</html>