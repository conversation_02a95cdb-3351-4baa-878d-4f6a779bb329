    /* Chart container with border and rounded corners */
    .ggchart-container {
      /* background-color: white; */
      border-radius: 8px;
      /* border: 1px solid #e5e7eb; */
      overflow: hidden;
      margin: 0 auto;
    }

    /* Header section with title and stats */
    .ggchart-header {
      padding: 20px;
      height: 95px;
      /* border-bottom: 1px solid #e5e7eb; */
      display: flex;
      justify-content: space-between;
      align-items: stretch;
    }

    .ggchart-title {
      flex-grow: 1;
    }


    /* Statistics boxes styling */
    .ggchart-stats-container {
      display: flex;
      padding: 0;
      margin: -20px 0 -20px 20px;
      position: relative;
      /* border-left: 1px solid #e5e7eb; */
    }

    .ggchart-stat-box {
      text-align: center;
      padding: 20px;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      justify-content: center;
      transition: background-color 0.2s;
      min-width: 120px;
    }


    .ggchart-stat-box.active h3 {
      color: #2a9d90;
    }


    /* Chart visualization styles */
    .ggchart-body {
      padding: 20px;
      position: relative;
      height: 300px;
    }



    .ggchart-line {
      /* Path for the main line chart */
      fill: none;
      stroke: #2a9d90;
      stroke-width: 2px;
      stroke-linejoin: round;
      stroke-linecap: round;
      transition: 0.3s;
    }

    /* Tooltip and interaction elements */
    .ggchart-tooltip {
      position: absolute;
      background-color: #fff;
      border-radius: 6px;
      border: 1px solid #e5e7eb;
      padding: 10px 12px;
      font-size: 14px;
      pointer-events: none;
      opacity: 0;
      transition: all 0.2s ease-out;
      margin: 0;
      z-index: 100;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .ggchart-tooltip-date {
      font-weight: 600;
      margin-bottom: 5px;
      color: #333;
    }

    .ggchart-tooltip-value {
      display: flex;
      align-items: center;
      color: #666;
    }

    .ggchart-tooltip-value::before {
      content: '';
      display: inline-block;
      width: 10px;
      height: 10px;
      background-color: #2a9d90;
      border-radius: 2px;
      margin-right: 6px;
    }

    .ggchart-dot {
      fill: white;
      stroke: #2a9d90;
      stroke-width: 3;
      opacity: 0;
      transition: opacity 0.1s ease-out;
    }

    .ggchart-hover-line {
      stroke: #ddd;
      stroke-width: 1;
      opacity: 0;
      transition: transform 0.1s ease-out;
    }

    .ggchart-overlay {
      fill: none;
      pointer-events: all;
    }

    /* Rename colliding icon/option classes */
    .ggchart-line.mobile {
      stroke: #2662d9;
    }

    .ggchart-dot.mobile {
      stroke: #2662d9;
    }

    .ggchart-tooltip-value.mobile::before {
      background-color: #2662d9;
    }

    .ggchart-stat-box.active.mobile h3 {
      color: #2662d9;
    }