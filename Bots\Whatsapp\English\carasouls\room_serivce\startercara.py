import requests
import os
import json

# Authentication credentials
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')

# Payload data
payload = {
    "friendly_name": "food_and_beverage_menu_cara",
    "language": "en",
    "types": {
        "twilio/carousel": {
            "body": "Indulge in our curated selection of gourmet dishes and exquisite beverages. Explore our menus to find the perfect pairing for any occasion.",
            "cards": [
                {
                    "title": "Food Menu",
                    "body": "Explore our exquisite food selection!",
                    "media": "https://images-new.vercel.app/foodmenu/burrata.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Food",
                            "id": "order_food"
                        }
                    ]
                },
                {
                    "title": "Beverage Menu",
                    "body": "Explore our refreshing beverage selection!",
                    "media": "https://images-new.vercel.app/beveragemenu/drinks.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Beverages",
                            "id": "order_beverages"
                        }
                    ]
                }
            ]
        }
    }
}


# Make the POST request
response = requests.post(
    'https://content.twilio.com/v1/Content',
    auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN),
    json=payload
)

# Enhanced response handling
print("\n=== Response Details ===")
print(f"Status Code: {response.status_code}")
print("\n=== Headers ===")
for header, value in response.headers.items():
    print(f"{header}: {value}")

print("\n=== Response Content ===")
try:
    # Try to print formatted JSON
    print(json.dumps(response.json(), indent=2))
except json.JSONDecodeError:
    # If not JSON, print raw text
    print(response.text)

print("\n=== Request Details ===")
print(f"Request URL: {response.request.url}")
print(f"Request Method: {response.request.method}")
print("Request Headers:")
for header, value in response.request.headers.items():
    print(f"{header}: {value}")

print("\n=== Timing ===")
print(f"Elapsed Time: {response.elapsed.total_seconds()} seconds")

if response.status_code != 200:
    print("\n=== Error Details ===")
    print(f"Error Status Code: {response.status_code}")
    print("Error Response:", response.text)
