<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
  <title>AI Chat</title>
  <!-- Tailwind CSS -->
  {% include 'imports.html' %}

  <script>
    tailwind.config = {
      darkMode: 'class',
    }
  </script>
  <style>
    body {
      visibility: hidden;
    }

    /* Updated typing animation */
    .typing-animation {
      display: inline-flex;
      gap: 3px;
      padding: 8px 4px;
    }

    .typing-dot {
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: currentColor;
      opacity: 0.7;
      animation: typing-fade 1.4s infinite ease-in-out;
    }

    .typing-dot:nth-child(2) {
      animation-delay: 0.2s;
    }

    .typing-dot:nth-child(3) {
      animation-delay: 0.4s;
    }

    @keyframes typing-fade {

      0%,
      100% {
        opacity: 0.3;
      }

      50% {
        opacity: 0.7;
      }
    }

    /* Updated message container styles */
    .message-container {
      display: flex;
      align-items: flex-end;
      margin-bottom: 1rem;
    }

    .message-bubble {
      background-color: #f0f0f0;
      color: black;
      /* Keep text color */
      padding: 0.5rem 0.75rem;
      /* Reduced padding */
      border-radius: 0.375rem;
      /* Slightly reduced border radius */
      width: fit-content;
      max-width: 80%;
      min-height: 40px;
      /* Set minimum height */
      display: flex;
      align-items: center;
    }

    .dark .message-bubble {
      background-color: #18181b;
      color: white;
      /* Keep text color */
      /* Padding and border-radius inherit from the base .message-bubble style */
    }

    /* Avatar styles */
    .chat-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      transition: transform 0.2s ease;
      border: 2px solid #e5e7eb;
    }

    .chat-avatar:hover {
      transform: scale(1.05);
    }

    .chat-avatar:active {
      transform: scale(0.95);
    }

    .ai-avatar {
      padding: 2px;
      background-color: white;
    }
  </style>
  <style>
    /* Custom style for standalone file/image previews */
    .file-preview-standalone {
      background-color: #e9ecef;
      /* Light mode background */
      color: black;
      padding: 0.5rem 0.75rem;
      /* Minimal padding */
      border-radius: 0.5rem;
      /* Minimal radius */
      border: 1px solid #dee2e6;
      /* Subtle border */
      width: fit-content;
      max-width: 80%;
      display: inline-flex;
      /* Use inline-flex */
      align-items: center;
      gap: 0.25rem;
      /* Adjust gap */
      font-size: 0.875rem;
      /* text-sm */
      box-shadow: none;
      /* Remove potential card shadow */
    }

    .dark .file-preview-standalone {
      background-color: #212529;
      /* Dark mode background */
      color: white;
      border-color: #495057;
      /* Darker border */
    }

    /* Ensure icons inside are visible in dark mode */
    .dark .file-preview-standalone svg {
      stroke: white;
    }

    /* Style for the actual sent image */
    .sent-image-preview {
      max-width: 250px;
      /* Limit image width */
      max-height: 200px;
      /* Limit image height */
      border-radius: 0.5rem;
      /* Match bubble radius */
      object-fit: cover;
      /* Ensure image covers the area nicely */
      display: block;
      /* Ensure it takes block space */
    }
  </style>
  <style>
    /* Custom Scrollbar for Textarea */
    .chat-textarea::-webkit-scrollbar {
      width: 6px;
      /* Width of the scrollbar */
    }

    .chat-textarea::-webkit-scrollbar-track {
      background: transparent;
      /* Make track invisible */
      margin: 4px 0;
      /* Add some margin to top and bottom */
    }

    .chat-textarea::-webkit-scrollbar-thumb {
      background-color: #cbd5e1;
      /* Light mode thumb color (cool gray 300) */
      border-radius: 10px;
      /* Rounded corners */
      border: 2px solid transparent;
      /* Creates padding around thumb */
      background-clip: content-box;
      /* Ensures border creates padding */
    }

    .dark .chat-textarea::-webkit-scrollbar-thumb {
      background-color: #4b5563;
      /* Dark mode thumb color (gray 600) */
    }
  </style>
</head>

<body class="light">
  <!-- Loading Overlay -->
  {% include 'components/loading.html' %}

  <!-- Main Grid Layout -->
  <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr]">
    <!-- Sidebar -->
    {% include 'sidebar.html' %}

    <!-- Main Content -->
    <div class="flex flex-col">
      <header
        class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
        <div class="flex items-center gap-2 px-4 pl-0">
          <button id="toggle-btn" style="margin-left: 8px;"
            class="opacity-100 transition-opacity duration-300 focus:outline-none"
            style="background-color: transparent !important;">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-panel-left">
              <rect width="18" height="18" x="3" y="3" rx="2"></rect>
              <path d="M9 3v18"></path>
            </svg>
          </button>
          <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-2 h-4"
            style="background-color: var(--border-color);"></div>
          <nav aria-label="breadcrumb">
            <ol class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
              <div class="menubar" role="menubar">
                    <div class="menubar-indicator"></div>
                    <a href="/livechat" role="menuitem">Omni Channel</a>
                    <a href="/aichat" role="menuitem"  class="active">Guest Genius</a>
                    <a href="/voicebot" role="menuitem">Voice Agents</a>
                </div>
            </ol>
          </nav>
        </div>
        {% include 'topright.html' %}
      </header>
      <!-- ========== MAIN CONTENT ========== -->
      <main id="content">
        <!-- Content -->
        <div class="relative h-full">
          <div class="w-full px-4 py-6 sm:px-6 lg:px-8">
            <div id="welcomeSection" class="text-center py-8">
              <!-- Content removed as per user feedback -->
            </div>
            <ul class="space-y-5" id="chatArea">
              <!-- Chat bubbles will be inserted here -->
            </ul>
          </div>

          <!-- Template Buttons Container -->
          <div id="templateButtonsContainer" class="flex flex-wrap justify-center gap-2 mt-4 mx-auto">
            <!-- Template buttons will be added here by JS or server-side -->
            <!-- Example Buttons (will be replaced) -->
            <div class="flex flex-wrap justify-center gap-2">
              <button class="template-button card px-4 py-2 rounded-lg border text-sm universal-hover">What are the sales today?</button>
              <button class="template-button card px-4 py-2 rounded-lg border text-sm universal-hover">What are some latest food orders?</button>
              <button class="template-button card px-4 py-2 rounded-lg border text-sm universal-hover">What are the total remaining tasks?</button>
            </div>
            <div class="flex flex-wrap justify-center gap-2">
              <button class="template-button card px-4 py-2 rounded-lg border text-sm universal-hover">How many voice calls were done the past 2 hours?</button>
              <button class="template-button card px-4 py-2 rounded-lg border text-sm universal-hover">What was the recent live chat about etc</button>
            </div>
          </div>
          <!-- End Template Buttons Container -->

          <div class="fixed bottom-0 w-full border-t card pt-2 pb-2 sm:pt-4 sm:pb-4 px-4 sm:px-6 chat-input-card"
            style=" max-width: calc(100% - 280px);">
            <!-- Textarea and Buttons Container -->
            <div class=" mx-auto">
              <!-- Upload Preview Container -->
              <div id="uploadPreviewContainer" class="mb-2 flex flex-wrap gap-2" style="display: none;">
                <!-- Preview items will be inserted here -->
              </div>

              <!-- Input Row -->
              <div class="flex items-end gap-2">
                <!-- Left Buttons -->
                <div class="flex items-end gap-2"> <!-- Added gap-2 here -->
                  <!-- Image upload Button -->
                  <button id="imgUploadBtn" type="button"
                    class="card inline-flex shrink-0 justify-center items-center border rounded-lg hover:bg-gray-100 focus:z-10 focus:outline-none focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 universal-hover"
                    style="background-color: transparent; height: 40px; width: 38px;">
                    <svg xmlns="http://www.w3.org/2000/svg" class="button-svg" width="18" height="18"
                      viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round" class="lucide lucide-image-up-icon lucide-image-up">
                      <path
                        d="M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10l-3.1-3.1a2 2 0 0 0-2.814.014L6 21" />
                      <path d="m14 19.5 3-3 3 3" />
                      <path d="M17 22v-5.5" />
                      <circle cx="9" cy="9" r="2" />
                    </svg>
                  </button>
                  <!-- Attach Files Button -->
                  <button id="fileUploadBtn" type="button"
                    class="card inline-flex shrink-0 justify-center items-center border rounded-lg hover:bg-gray-100 focus:z-10 focus:outline-none focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 universal-hover"
                    style="background-color: transparent; height: 40px; width: 38px;">
                    <svg class="shrink-0 size-4 button-svg" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                      viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round">
                      <path
                        d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48" />
                    </svg>
                  </button>
                </div>

                <!-- Textarea Input -->
                <div class="relative flex-grow">
                  <textarea id="chatInput"
                    class="chat-textarea p-2 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600"
                    placeholder="How can I help you today?"
                    style="height: 40px; resize: none; overflow-y: hidden;"></textarea>
                  <!-- Added overflow-y: hidden -->
                </div>

                <!-- Right Buttons -->
                <div class="flex items-center gap-x-1">
                  <!-- Send Button -->
                  <button id="sendBtn" type="button"
                    class="card inline-flex shrink-0 justify-center items-center border rounded-lg focus:z-10 focus:outline-none dark:focus:bg-neutral-700 universal-hover"
                    style="background-color: transparent; height: 40px; width: 38px;">
                    <svg class="shrink-0 size-3.5 button-svg" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                      fill="currentColor" viewBox="0 0 16 16">
                      <path
                        d="M15.964.686a.5.5 0 0 0-.65-.65L.767 5.855H.766l-.452.18a.5.5 0 0 0-.082.887l.41.26.001.002 4.995 3.178 3.178 4.995.002.002.26.41a.5.5 0 0 0 .886-.083l6-15Zm-1.833 1.89L6.637 10.07l-.215-.338a.5.5 0 0 0-.154-.154l-.338-.215 7.494-7.494 1.178-.471-.47 1.178Z" />
                    </svg>
                  </button>
                </div>
              </div>
              <!-- End Input Row -->

              <!-- End Input Row -->

              <!-- Hidden file inputs for uploads -->
              <input type="file" id="imageUpload" accept="image/*" style="display:none" />
              <input type="file" id="fileUpload" style="display:none" />
            </div>
            <!-- End Textarea and Buttons Container -->
          </div>
        </div>

        <style>
          .grid:not(.collapsed) .chat-input-card {
            max-width: calc(100% - 280px) !important;
          }

          .grid.collapsed .chat-input-card {
            max-width: calc(100% - 60px) !important;
          }

          #chatArea {
            padding-bottom: 100px;
            /* adjust according to your input area height */
          }
        </style>
        <!-- End Content -->
      </main>
      <!-- ========== END MAIN CONTENT ========== -->

      <!-- JS Implementing Plugins -->

      <!-- JS PLUGINS -->
      <!-- Required plugins -->
      <script src="https://cdn.jsdelivr.net/npm/preline/dist/preline.min.js"></script>
    </div>
  </div>

  <script>
    // Function to update theme-aware images
    function updateThemeAwareImages() {
      const isDark = document.body.classList.contains('pure-black');
      document.querySelectorAll('.theme-aware-image').forEach(img => {
        const newSrc = isDark ? img.getAttribute('data-dark-src') : img.getAttribute('data-light-src');
        if (img.src !== newSrc) {
          img.src = newSrc;
        }
      });
    }

    // Call on page load
    updateThemeAwareImages();

    // Create observer to watch for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          updateThemeAwareImages();
        }
      });
    });

    // Start observing the body element for class changes
    observer.observe(document.body, {
      attributes: true
    });
  </script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const sendBtn = document.getElementById('sendBtn'); // Use ID selector
      const textArea = document.getElementById('chatInput'); // Use ID selector
      const chatArea = document.getElementById('chatArea');
      const welcomeSection = document.getElementById('welcomeSection');
      const templateButtonsContainer = document.getElementById('templateButtonsContainer');

      // Feature: Fill template message into input bar on click
      document.querySelectorAll('.template-button').forEach(btn => {
        btn.addEventListener('click', () => {
          textArea.value = btn.textContent;
          textArea.focus();
        });
      });

      // Function to show welcome section and template buttons
      function showInitialState() {
        if (chatArea.children.length === 0) {
          if (welcomeSection) welcomeSection.style.display = 'block';
          if (templateButtonsContainer) templateButtonsContainer.style.display = 'flex';
        }
      }

      // Show initial state on page load
      showInitialState();

      function sendMessage() {
        const userMessage = textArea.value.trim();
        const previewContainer = document.getElementById('uploadPreviewContainer');
        const previewItems = previewContainer.querySelectorAll('.flex.items-center.gap-2'); // Select the preview divs

        // Only proceed if there's a message OR files to send
        if (!userMessage && previewItems.length === 0) return;

        if (welcomeSection) welcomeSection.remove();


        // 1. Send file/image bubbles first
        if (previewItems.length > 0) {
          previewItems.forEach(item => {
            const imgElement = item.querySelector('img');
            const fileNameElement = item.querySelector('span');
            const fileName = fileNameElement ? fileNameElement.textContent : 'Unknown File';
            let fileBubbleContent = '';

            if (imgElement) {
              // It's an image preview
              fileBubbleContent = `
                <img src="${imgElement.src}" alt="${fileName}" class="sent-image-preview"> <!-- Display actual image -->
              `;
            } else {
              // It's a file preview
              let filePreviewClass = 'file-preview-standalone'; // Use standalone class only for files
              fileBubbleContent = `
                <div class="${filePreviewClass} flex items-center gap-1 p-1 rounded text-xs"> <!-- Added class, removed border/card -->
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>
                  <span>${fileName}</span>
                </div>
              `;
            }

            // Create and insert a separate bubble for this file/image
            // Create and insert a separate container for this file/image, without avatar
            const fileUserBubble = `
              <div class="message-container justify-end flex items-end mb-4">
                 ${fileBubbleContent} <!-- Directly insert the styled preview block or image -->
                 <!-- No avatar div -->
              </div>
            `;
            chatArea.insertAdjacentHTML('beforeend', fileUserBubble);
          });
        }

        // 2. Send text message bubble if text exists
        let textUserBubble = '';
        if (userMessage) {
          textUserBubble = `
            <div class="message-container justify-end flex items-end mb-4">
              <div class="message-bubble">
                 <p class="text-sm">${userMessage}</p>
              </div>
              <div class="ml-3">
                <img src="https://ui.shadcn.com/avatars/02.png" alt="user avatar" class="chat-avatar">
              </div>
            </div>
          `;

          chatArea.insertAdjacentHTML('beforeend', textUserBubble);
        }

        // Bot bubble remains the same for now
        const botBubble = `
          <div class="message-container justify-start flex items-end mb-4">
            <div class="mr-3">
              <img src="static/images/centered-gg-logo.png" alt="Restaurant Logo" class="chat-avatar ai-avatar">
            </div>
            <div class="message-bubble">
              <div class="typing-animation">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
              </div>
            </div>
          </div>
        `;


        // 3. Add bot typing indicator (only if user sent something)
        if (textUserBubble || previewItems.length > 0) {
          chatArea.insertAdjacentHTML('beforeend', botBubble);
        }
        chatArea.lastElementChild.scrollIntoView({ behavior: 'smooth' });


        // Simulate AI response after 2 seconds
        setTimeout(() => {
          const typingBubble = chatArea.lastElementChild;
          // Update the bot response logic if needed, for now just echoing text
          const responseText = userMessage ? `You said: ${userMessage}` : 'Received files.';
          typingBubble.innerHTML = `
            <div class="mr-3">
              <img src="static/images/centered-gg-logo.png" alt="Restaurant Logo" class="chat-avatar ai-avatar">
            </div>
            <div class="message-bubble">
              <p class="text-sm">${responseText}</p>
            </div>
          `;
        }, 2000);


        // Clear text area and preview container
        textArea.value = '';
        previewContainer.innerHTML = '';
        // Reset textarea height after sending
        // Reset textarea height after sending
        textArea.style.height = '40px';
        textArea.style.overflowY = 'hidden'; // Re-hide scrollbar if needed
        previewContainer.style.display = 'none';

        // Hide welcome section and template buttons after sending a message
        if (welcomeSection) welcomeSection.style.display = 'none';
        if (templateButtonsContainer) templateButtonsContainer.style.display = 'none';
      }

      // Click handler
      if (sendBtn) sendBtn.addEventListener('click', sendMessage); // Check if button exists

      // Enter key handler
      textArea.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          sendMessage();
        }
      });

      const autoResizeTextarea = () => {
        // Save current content and scroll position
        const text = textArea.value;
        const scrollTop = textArea.scrollTop;

        // Only reset height for measurement if we have significant content
        if (text.length > 50 || text.includes('\n')) {
          textArea.style.height = 'auto';

          const minHeight = 40;
          const maxHeight = 160;

          // Use a larger threshold to prevent jumpy behavior
          if (textArea.scrollHeight > minHeight + 10) {
            const newHeight = Math.min(textArea.scrollHeight, maxHeight);
            textArea.style.height = `${newHeight}px`;
            textArea.style.overflowY = newHeight >= maxHeight ? 'auto' : 'hidden';
          } else {
            textArea.style.height = `${minHeight}px`;
            textArea.style.overflowY = 'hidden';
          }
        }

        // Restore scroll position
        textArea.scrollTop = scrollTop;
      };
      // Adjust height on input
      textArea.addEventListener('input', autoResizeTextarea);

    });

    // Add event listeners to template buttons
    const templateButtons = document.querySelectorAll('.template-button');
    templateButtons.forEach(button => {
      button.addEventListener('click', () => {
        const buttonText = button.textContent.trim();
        textArea.value = buttonText;
        autoResizeTextarea(); // Adjust textarea height after populating
        textArea.focus(); // Focus the textarea
      });
    });
  </script>
  <script>
    // Script to update greeting based on the user's time
    document.addEventListener('DOMContentLoaded', () => {
      const greetingElem = document.getElementById('greeting');
      if (!greetingElem) return;
      const now = new Date();
      const hour = now.getHours();
      let greetingText = 'Good evening';
      if (hour < 12) {
        greetingText = 'Good morning';
      } else if (hour < 18) {
        greetingText = 'Good afternoon';
      }
      greetingElem.textContent = greetingText;
    });
  </script>
  <script>
    // New script for file and image upload handling
    document.addEventListener('DOMContentLoaded', () => {
      const imgUploadBtn = document.getElementById('imgUploadBtn');
      const fileUploadBtn = document.getElementById('fileUploadBtn');
      const imageUploadInput = document.getElementById('imageUpload');
      const fileUploadInput = document.getElementById('fileUpload');
      const previewContainer = document.getElementById('uploadPreviewContainer');

      function createPreviewItem(file, isImage = false) {
        const preview = document.createElement('div');
        preview.className = 'flex items-center gap-2 p-2 rounded-lg border card';

        if (isImage) {
          const img = document.createElement('img');
          img.src = URL.createObjectURL(file);
          img.className = 'h-8 w-8 object-cover rounded';
          preview.appendChild(img);
        } else {
          const fileIcon = document.createElement('svg');
          fileIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>`;
          fileIcon.className = 'h-6 w-6';
          preview.appendChild(fileIcon);
        }

        const fileName = document.createElement('span');
        fileName.textContent = file.name;
        fileName.className = 'text-sm truncate max-w-[150px]';
        preview.appendChild(fileName);

        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>`;
        closeBtn.className = 'hover:bg-gray-100 rounded p-1 dark:hover:bg-gray-700';
        closeBtn.onclick = () => {
          preview.remove();
          if (!previewContainer.hasChildNodes()) {
            previewContainer.style.display = 'none';
          }
        };
        preview.appendChild(closeBtn);

        return preview;
      }

      imgUploadBtn.addEventListener('click', () => {
        imageUploadInput.click();
      });

      fileUploadBtn.addEventListener('click', () => {
        fileUploadInput.click();
      });

      imageUploadInput.addEventListener('change', (e) => {
        const files = e.target.files;
        if (files.length > 0) {
          previewContainer.style.display = 'flex';
          for (const file of files) {
            if (file.type.startsWith('image/')) {
              previewContainer.appendChild(createPreviewItem(file, true));
            }
          }
        }
        e.target.value = '';
      });

      fileUploadInput.addEventListener('change', (e) => {
        const files = e.target.files;
        if (files.length > 0) {
          previewContainer.style.display = 'flex';
          for (const file of files) {
            previewContainer.appendChild(createPreviewItem(file, false));
          }
        }
        e.target.value = '';
      });
    });
  </script>
</body>

</html>