import os
from flask import Flask, request
from twilio.rest import Client
from dotenv import load_dotenv

load_dotenv()

account_sid = os.getenv('TWILIO_ACCOUNT_SID')
auth_token = os.getenv('TWILIO_AUTH_TOKEN')
client = Client(account_sid, auth_token)

from_whatsapp_number = os.getenv('FROM_WHATSAPP_NUMBER')
STARTER_OPTION_SELECTOR_SID = "HX2d5c9f3921da4d2f92035a2ad73eacf2"

app = Flask(__name__)

@app.route("/", methods=['POST'])
def handle_incoming_message():
    sender = 'whatsapp:+************'
    client.messages.create(
        from_=from_whatsapp_number,
        to=sender,
        content_sid=STARTER_OPTION_SELECTOR_SID
    )
    return '', 204

if __name__ == '__main__':
    app.run(debug=False, port=5000)