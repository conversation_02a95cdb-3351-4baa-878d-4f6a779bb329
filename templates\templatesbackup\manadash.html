<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Meta Tags -->
  <meta charset="utf-8">
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="generator" content="Astro v4.15.4">

  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/slate.min.css"/>
  <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit-icons.min.js"></script>
  <link rel="stylesheet" href="../static/styles/custom.css">
  <script src="../static/js/languagetranslator.js" defer></script>
  <script src="../static/js/themes.js" defer></script>
  <link rel="stylesheet" href="../static/styles/loadinganimations.css">
  <script src="../static/js/loading.js" defer></script>
  <script src="../static/js/dropdownslogic.js" defer></script>

  <!-- Franken UI -->
  <link rel="preconnect" href="https://rsms.me/" />
  <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />

  <link
    rel="stylesheet"
    href="https://unpkg.com/franken-ui/dist/css/core.min.css"
  />

  <script
    type="module"
    src="https://unpkg.com/franken-ui/dist/js/core.iife.js"
  ></script>
  <script
    type="module"
    src="https://unpkg.com/franken-ui/dist/js/icon.iife.js"
  ></script>
</head>
<body class="bg-background text-foreground">

  <!-- Franken UI -->
  
  <!-- Script to handle theme and mode (light/dark) based on localStorage and system preference -->
  <script>
    const htmlElement = document.documentElement;

    // Check if user preference is stored in localStorage or falls back to system setting for dark mode
    if (
      localStorage.getItem("mode") === "dark" ||
      (!("mode" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)
    ) {
      htmlElement.classList.add("dark");
    } else {
      htmlElement.classList.remove("dark");
    }

    // Apply saved theme from localStorage or default to "uk-theme-zinc"
    htmlElement.classList.add(localStorage.getItem("theme") || "uk-theme-zinc");
  </script>
  
  <!-- Script to handle theme and dark mode toggle via message events -->
  <script>
    window.addEventListener("message", function (event) {
      if (event.data.dark === true) {
        htmlElement.classList.add("dark");
        localStorage.setItem("mode", "dark");
      }

      if (event.data.dark === false) {
        htmlElement.classList.remove("dark");
        localStorage.setItem("mode", "light");
      }

      if (event.data.theme) {
        const theme = Array.from(htmlElement.classList).find((cls) =>
          cls.startsWith("uk-theme-")
        );

        // Remove current theme and apply the new one
        if (theme) {
          htmlElement.classList.remove(theme);
        }
        htmlElement.classList.add(event.data.theme);
        localStorage.setItem("theme", event.data.theme);
      }
    });
  </script>

  <!-- Page Title -->
  <title>Examples - Franken UI</title>

  <!-- Preload fonts for better performance -->
  <link rel="preload" href="/fonts/geist-font/fonts/GeistVariableVF.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="/fonts/geist-font/fonts/GeistMonoVariableVF.woff2" as="font" type="font/woff2" crossorigin>

  <!-- Custom font styles -->
  <link rel="stylesheet" href="/fonts/geist-font/style.css">

  <!-- Load Astro core JavaScript and other assets -->
  <script src="/_astro/core.iife.BtHbjxGz.js" type="module"></script>
  <script src="/_astro/icon.iife.CRFl9c9T.js" type="module"></script>
  <link rel="stylesheet" href="/_astro/authentication.tX3ha87v.css">
</head>

<body class="bg-background font-geist-sans text-foreground antialiased md:block">
  <div class="border-b border-border px-4">
    
    <!-- Navbar Section -->
    <nav class="uk-navbar" uk-navbar>
      <div class="uk-navbar-left gap-x-4 lg:gap-x-6">
        
        <!-- User Profile Dropdown -->
        <div class="uk-navbar-item w-[200px]">
          <button class="uk-button uk-button-default w-full" type="button">
            <div class="flex flex-1 items-center gap-2">
              <span class="relative flex h-5 w-5 shrink-0 overflow-hidden rounded-full">
                <!-- User Avatar Image -->
                <img class="aspect-square h-full w-full grayscale" alt="Alicia Koch" src="https://avatar.vercel.sh/personal.png">
              </span>
              <span class>Alicia Koch</span>
            </div>
            <span class="size-4 opacity-50">
              <uk-icon icon="chevrons-up-down"></uk-icon>
            </span>
          </button>
          
          <!-- Dropdown menu -->
          <div class="uk-drop uk-dropdown w-[200px]" uk-dropdown="mode: click; pos: bottom-center">
            <ul class="uk-dropdown-nav uk-nav">
              <li class="uk-nav-header">Personal Account</li>
              <li class="uk-active">
                <a class="uk-drop-close justify-between" href="#demo" uk-toggle>
                  <div class="flex flex-1 items-center gap-2">
                    <span class="relative flex h-5 w-5 shrink-0 overflow-hidden rounded-full">
                      <img class="aspect-square h-full w-full grayscale" alt="Alicia Koch" src="https://avatar.vercel.sh/personal.png">
                    </span>
                    <span class>Alicha Koch</span>
                  </div>
                  <uk-icon icon="check"></uk-icon>
                </a>
              </li>
              <li class="mt-3"></li>
              <li class="uk-nav-header">Teams</li>
              
              <!-- Teams List -->
              <li>
                <a class="uk-drop-close justify-between" href="#demo" uk-toggle>
                  <div class="flex flex-1 items-center gap-2">
                    <span class="relative flex h-5 w-5 shrink-0 overflow-hidden rounded-full">
                      <img class="aspect-square h-full w-full grayscale" alt="Alicia Koch" src="https://avatar.vercel.sh/personal.png">
                    </span>
                    <span class>Acme Inc.</span>
                  </div>
                </a>
              </li>
              <li>
                <a class="uk-drop-close justify-between" href="#demo" uk-toggle>
                  <div class="flex flex-1 items-center gap-2">
                    <span class="relative flex h-5 w-5 shrink-0 overflow-hidden rounded-full">
                      <img class="aspect-square h-full w-full grayscale" alt="Alicia Koch" src="https://avatar.vercel.sh/personal.png">
                    </span>
                    <span class>Monster Inc.</span>
                  </div>
                </a>
              </li>

              <li class="uk-nav-divider"></li>
              <li>
                <a class="uk-drop-close" href="#demo" uk-toggle>
                  <uk-icon class="mr-2" icon="circle-plus"></uk-icon> Create a Team
                </a>
              </li>
            </ul>
          </div>
        </div>

        <!-- Navbar Menu Links -->
        <ul class="uk-navbar-nav gap-x-4 lg:gap-x-6">
          <li class="uk-active"><a href="#demo" uk-toggle>Overview</a></li>
          <li><a href="#demo" uk-toggle>Customers</a></li>
          <li><a href="#demo" uk-toggle>Products</a></li>
          <li><a href="#demo" uk-toggle>Settings</a></li>
        </ul>
      </div>

      <!-- Right Navbar Section -->
      <div class="uk-navbar-right gap-x-4 lg:gap-x-6">
        
        <!-- Search Bar -->
        <div class="uk-navbar-item w-[150px] lg:w-[300px]">
          <input class="uk-input" placeholder="Search" type="text">
        </div>

        <!-- User Avatar and Dropdown Menu -->
        <div class="uk-navbar-item">
          <a class="inline-flex h-8 w-8 items-center justify-center rounded-full bg-accent ring-ring focus:outline-none focus-visible:ring-1" href="#">
            <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-full">
              <img class="aspect-square h-full w-full" alt="@shadcn" src="https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult">
            </span>
          </a>
          <div class="uk-drop uk-dropdown" uk-dropdown="mode: click; pos: bottom-right">
            <ul class="uk-dropdown-nav uk-nav">
              <li class="px-2 py-1.5 text-sm">
                <div class="flex flex-col space-y-1">
                  <p class="text-sm font-medium leading-none">sveltecult</p>
                  <p class="text-xs leading-none text-muted-foreground">
                    <a href="/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="...">[email protected]</a>
                  </p>
                </div>
              </li>
              <li class="uk-nav-divider"></li>
              <li><a class="uk-drop-close justify-between" href="#demo" uk-toggle>Profile <span class="ml-auto text-xs tracking-widest opacity-60">⇧⌘P</span></a></li>
              <li><a class="uk-drop-close justify-between" href="#demo" uk-toggle>Billing <span class="ml-auto text-xs tracking-widest opacity-60">⌘B</span></a></li>
              <li><a class="uk-drop-close justify-between" href="#demo" uk-toggle>Settings <span class="ml-auto text-xs tracking-widest opacity-60">⌘S</span></a></li>
              <li><a class="uk-drop-close justify-between" href="#demo" uk-toggle>New Team</a></li>
              <li class="uk-nav-divider"></li>
              <li><a class="uk-drop-close" href="#demo" uk-toggle>Sign Out</a></li>
            </ul>
          </div>
        </div>
      </div>
    </nav>
  </div>

  <!-- Main content placeholder -->
  <main style="display: block;">
    <section id="demo" uk-toggle>
      <!-- Example section content will go here -->
    </section>
  </main>

</body>
</html>