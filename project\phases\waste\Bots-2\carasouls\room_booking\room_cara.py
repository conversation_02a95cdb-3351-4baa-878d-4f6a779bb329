import requests
import os
import json

# Authentication credentials
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')

# Payload data with updated room details
payload = {
    "friendly_name": "room_booking_menu",
    "language": "en",
    "types": {
        "twilio/carousel": {
            "body": "Explore our room options for a luxurious stay!",
            "cards": [
                {
                    "title": "Junior Suite Cozy",
                    "body": "34 m² suite with premium amenities. Perfect for a relaxing stay.",
                    "media": "https://images-new.vercel.app/roombooking/juniorsuite.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Confirm Reservation",
                            "id": "book_junior_suite_cozy"
                        }
                    ]
                },
                {
                    "title": "The Raid",
                    "body": "Spacious 380 m² Presidential Suite with 40 m² terrace. Ideal for families or long stays.",
                    "media": "https://images-new.vercel.app/roombooking/raid.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Confirm Reservation",
                            "id": "book_the_raid"
                        }
                    ]
                },
                {
                    "title": "Junior Suite Deluxe Bright",
                    "body": "40 m² suite with modern tech and Mallorcan palace sophistication.",
                    "media": "https://images-new.vercel.app/roombooking/juniorsuitedeluxe.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Confirm Reservation",
                            "id": "book_junior_suite_deluxe_bright"
                        }
                    ]
                },
                {
                    "title": "Executive Suite",
                    "body": "60 m² suite with kitchen, ideal for extended stays. Serene and spacious.",
                    "media": "https://images-new.vercel.app/roombooking/executivesuite.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Confirm Reservation",
                            "id": "book_executive_suite"
                        }
                    ]
                }
            ]
        }
    }
}

# Make the POST request
response = requests.post(
    'https://content.twilio.com/v1/Content',
    auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN),
    json=payload
)

# Enhanced response handling
print("\n=== Response Details ===")
print(f"Status Code: {response.status_code}")
print("\n=== Headers ===")
for header, value in response.headers.items():
    print(f"{header}: {value}")

print("\n=== Response Content ===")
try:
    # Try to print formatted JSON
    print(json.dumps(response.json(), indent=2))
except json.JSONDecodeError:
    # If not JSON, print raw text
    print(response.text)

print("\n=== Request Details ===")
print(f"Request URL: {response.request.url}")
print(f"Request Method: {response.request.method}")
print("Request Headers:")
for header, value in response.request.headers.items():
    print(f"{header}: {value}")

print("\n=== Timing ===")
print(f"Elapsed Time: {response.elapsed.total_seconds()} seconds")

if response.status_code != 200:
    print("\n=== Error Details ===")
    print(f"Error Status Code: {response.status_code}")
    print("Error Response:", response.text)
