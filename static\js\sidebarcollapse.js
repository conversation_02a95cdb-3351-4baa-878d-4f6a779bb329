// Modified JavaScript for Toggle Functionality with Persistent State and Mobile Support
const toggleBtn = document.getElementById('toggle-btn');
const sidebar = document.getElementById('sidebar');
const sidebarLabels = document.querySelectorAll('.sidebar-label');
const toggleIcon = document.getElementById('toggle-icon');
const brandText = document.getElementById('brand-text');
const gridContainer = document.querySelector('.grid');

document.addEventListener('DOMContentLoaded', () => {
    const logoImg = document.getElementById('logo-img');

    const updateLogo = () => {
        if (document.body.classList.contains('light')) {
            logoImg.src = '../static/images/logo.png';
        } else {
            logoImg.src = '../static/images/logodark.png';
        }
    };

    // Initial check
    updateLogo();

    // Observe changes to the body's class attribute
    const observer = new MutationObserver(updateLogo);
    observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });
});

// Function to check if device is mobile
function isMobileDevice() {
    return window.innerWidth <= 768; // You can adjust this breakpoint
}

// Function to set sidebar state based on localStorage and device type
function setSidebarState(isCollapsed) {
    if (isCollapsed) {
        sidebar.classList.add('collapsedsidebar');
        gridContainer.classList.add('collapsed');
        sidebarLabels.forEach(label => {
            label.classList.add('opacity-0', 'hidden');
        });
        toggleIcon.classList.add('rotate-180');
    } else {
        sidebar.classList.remove('collapsedsidebar');
        gridContainer.classList.remove('collapsed');
        sidebarLabels.forEach(label => {
            label.classList.remove('hidden');
            setTimeout(() => {
                label.classList.remove('opacity-0');
            }, 50);
        });
        toggleIcon.classList.remove('rotate-180');
    }
}

// Initialize sidebar state on page load
document.addEventListener('DOMContentLoaded', () => {
    // Force reset saved state to expanded on every load
    localStorage.setItem('sidebar-collapsed', 'false');
    setSidebarState(false);
});

// Update sidebar state on window resize
let resizeTimeout;
window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
        if (isMobileDevice()) {
            setSidebarState(true);
        }
    }, 250);
});

toggleBtn.addEventListener('click', () => {
    sidebar.classList.toggle('w-full');
    sidebar.classList.toggle('collapsedsidebar');
    gridContainer.classList.toggle('collapsed');

    const isCollapsedNow = sidebar.classList.contains('collapsedsidebar');

    sidebarLabels.forEach(label => {
        if (isCollapsedNow) {
            label.classList.add('opacity-0', 'hidden');
        } else {
            label.classList.remove('hidden');
            setTimeout(() => {
                label.classList.remove('opacity-0');
            }, 50);
        }
    });

    toggleIcon.classList.toggle('rotate-180');
    localStorage.setItem('sidebar-collapsed', isCollapsedNow);
});

// Instant active link detection (runs immediately as elements are parsed)
(function () {
    const currentPath = window.location.pathname;
    const sidebarLinks = document.querySelectorAll('.sidebar-link');
    sidebarLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
})();