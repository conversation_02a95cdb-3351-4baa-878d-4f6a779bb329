import requests
import json
from datetime import datetime, timedelta

SUPABASE_URL = 'https://nuqxdjuaoccswunhqixz.supabase.co'
SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im51cXhkanVhb2Njc3d1bmhxaXh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTcxMjI2OTcsImV4cCI6MjAzMjY5ODY5N30.sHkkzEb5oCTlLB3MQ0420XtJpURXW1DIHuHm4M9kDPI'

def delete_all_chats():
    """Deletes all records from the 'chats' table in Supabase."""
    table_name = "chats"
    url = f"{SUPABASE_URL}/rest/v1/{table_name}"
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        # First, get all records to confirm deletion
        response = requests.get(url, headers=headers, params={"select": "*"})
        response.raise_for_status()
        data = response.json()
        record_count = len(data)
        
        # Delete all records
        delete_url = f"{SUPABASE_URL}/rest/v1/{table_name}"
        response = requests.delete(
            delete_url, 
            headers=headers,
            params={"id": "neq.0"}  # Delete all records
        )
        response.raise_for_status()
        
        print(f"Successfully deleted {record_count} records from '{table_name}'")
        return True
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
    except Exception as err:
        print(f"An error occurred: {err}")
    return False

def create_new_chats():
    """Creates 3 new chat conversations with English users about hotel issues."""
    table_name = "chats"
    url = f"{SUPABASE_URL}/rest/v1/{table_name}"
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=minimal"
    }
    
    # Create timestamp for today
    now = datetime.now()
    
    # Chat 1: Air conditioning issue
    chat1 = [
        {
            "user_id": 1,
            "sender": "Carlos Fernandez",
            "message": "Hello, the air conditioning in my room isn't working properly. It's too hot in here.",
            "timestamp": (now - timedelta(minutes=45)).isoformat(),
            "customer": "true",
            "phone_number": "34612345678"
        },
        {
            "user_id": 1,
            "sender": "Agent",
            "message": "I'm sorry for the inconvenience, Mr. Fernandez. I'll send a maintenance technician immediately. Will you be in your room for the next 15 minutes?",
            "timestamp": (now - timedelta(minutes=43)).isoformat(),
            "customer": "false",
            "phone_number": "34612345678"
        },
        {
            "user_id": 1,
            "sender": "Carlos Fernandez",
            "message": "Yes, I'll be here. Thank you for the quick response.",
            "timestamp": (now - timedelta(minutes=40)).isoformat(),
            "customer": "true",
            "phone_number": "34612345678"
        },
        {
            "user_id": 1,
            "sender": "Agent",
            "message": "The technician is on his way. Is there anything else I can help you with in the meantime?",
            "timestamp": (now - timedelta(minutes=38)).isoformat(),
            "customer": "false",
            "phone_number": "34612345678"
        },
        {
            "user_id": 1,
            "sender": "Carlos Fernandez",
            "message": "No, that's all for now. Thank you.",
            "timestamp": (now - timedelta(minutes=35)).isoformat(),
            "customer": "true",
            "phone_number": "34612345678"
        }
    ]
    
    # Chat 2: Room service request
    chat2 = [
        {
            "user_id": 2,
            "sender": "Elena Rodriguez",
            "message": "Good afternoon, can I order room service? I'd like to order dinner.",
            "timestamp": (now - timedelta(minutes=120)).isoformat(),
            "customer": "true",
            "phone_number": "34623456789"
        },
        {
            "user_id": 2,
            "sender": "Agent",
            "message": "Of course, Ms. Rodriguez. Would you like to see our menu?",
            "timestamp": (now - timedelta(minutes=118)).isoformat(),
            "customer": "false",
            "phone_number": "34623456789"
        },
        {
            "user_id": 2,
            "sender": "Elena Rodriguez",
            "message": "Yes, please.",
            "timestamp": (now - timedelta(minutes=115)).isoformat(),
            "customer": "true",
            "phone_number": "34623456789"
        },
        {
            "user_id": 2,
            "sender": "Agent",
            "message": "I've sent the menu to your room. You can also view it on the hotel app. What would you like to order?",
            "timestamp": (now - timedelta(minutes=112)).isoformat(),
            "customer": "false",
            "phone_number": "34623456789"
        },
        {
            "user_id": 2,
            "sender": "Elena Rodriguez",
            "message": "I'd like the seafood paella and a bottle of Albariño white wine.",
            "timestamp": (now - timedelta(minutes=108)).isoformat(),
            "customer": "true",
            "phone_number": "34623456789"
        },
        {
            "user_id": 2,
            "sender": "Agent",
            "message": "Excellent choice. Your order will arrive in approximately 30 minutes. Anything else?",
            "timestamp": (now - timedelta(minutes=105)).isoformat(),
            "customer": "false",
            "phone_number": "34623456789"
        },
        {
            "user_id": 2,
            "sender": "Elena Rodriguez",
            "message": "No, that's all. Thank you.",
            "timestamp": (now - timedelta(minutes=103)).isoformat(),
            "customer": "true",
            "phone_number": "34623456789"
        }
    ]
    
    # Chat 3: Lost item
    chat3 = [
        {
            "user_id": 3,
            "sender": "Miguel Lopez",
            "message": "I think I left my watch in the hotel gym this morning. Could you check if someone found it?",
            "timestamp": (now - timedelta(minutes=180)).isoformat(),
            "customer": "true",
            "phone_number": "34634567890"
        },
        {
            "user_id": 3,
            "sender": "Agent",
            "message": "I'm sorry to hear that, Mr. Lopez. I'll check with our lost and found department immediately. Can you describe the watch?",
            "timestamp": (now - timedelta(minutes=178)).isoformat(),
            "customer": "false",
            "phone_number": "34634567890"
        },
        {
            "user_id": 3,
            "sender": "Miguel Lopez",
            "message": "It's a silver Omega watch with a black leather strap. It has significant sentimental value to me.",
            "timestamp": (now - timedelta(minutes=175)).isoformat(),
            "customer": "true",
            "phone_number": "34634567890"
        },
        {
            "user_id": 3,
            "sender": "Agent",
            "message": "I understand the importance. I'm checking with the gym staff and cleaning team. I'll let you know as soon as I have news.",
            "timestamp": (now - timedelta(minutes=172)).isoformat(),
            "customer": "false",
            "phone_number": "34634567890"
        },
        {
            "user_id": 3,
            "sender": "Agent",
            "message": "Good news! One of our employees found your watch in the gym. We have it secured at the front desk. You can pick it up at your convenience.",
            "timestamp": (now - timedelta(minutes=160)).isoformat(),
            "customer": "false",
            "phone_number": "34634567890"
        },
        {
            "user_id": 3,
            "sender": "Miguel Lopez",
            "message": "What a relief! Thank you so much. I'll come down to pick it up in an hour.",
            "timestamp": (now - timedelta(minutes=158)).isoformat(),
            "customer": "true",
            "phone_number": "34634567890"
        },
        {
            "user_id": 3,
            "sender": "Agent",
            "message": "Perfect. We'll have it ready for you at the front desk. Thank you for your patience.",
            "timestamp": (now - timedelta(minutes=155)).isoformat(),
            "customer": "false",
            "phone_number": "34634567890"
        }
    ]
    
    all_chats = chat1 + chat2 + chat3
    
    try:
        # Insert all chat messages
        for chat_message in all_chats:
            response = requests.post(url, headers=headers, json=chat_message)
            response.raise_for_status()
        
        print(f"Successfully created 3 new chat conversations with {len(all_chats)} messages")
        return True
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
    except Exception as err:
        print(f"An error occurred: {err}")
    return False

def get_livechat_data():
    """Fetches data from the 'chats' table in Supabase."""
    table_name = "chats"
    url = f"{SUPABASE_URL}/rest/v1/{table_name}"
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers, params={"select": "*"})
        response.raise_for_status()
        data = response.json()
        print(f"Current data in '{table_name}':")
        for row in data:
            print(row)
        return data
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
    except Exception as err:
        print(f"An error occurred: {err}")
    return None

if __name__ == "__main__":
    print("Checking current chat data...")
    get_livechat_data()
    
    print("\nDeleting all existing chats...")
    if delete_all_chats():
        print("\nCreating new English user chats...")
        create_new_chats()
        
        print("\nVerifying new chat data...")
        get_livechat_data()
    else:
        print("Failed to delete existing chats. Operation aborted.")
