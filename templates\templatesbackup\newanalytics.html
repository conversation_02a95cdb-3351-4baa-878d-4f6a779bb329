<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
    <script src="https://cdn.amcharts.com/lib/4/core.js"></script>
    <script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <script src="https://kit.fontawesome.com/c6c71387b9.js" crossorigin="anonymous"></script>
    <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
    <link rel="preconnect" href="https://rsms.me/" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
    <link rel="stylesheet" href="../static/styles/custom.css">
    <script src="../static/js/languagetranslator.js" defer></script>
    <script src="../static/js/themes.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@latest/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@latest/dist/js/uikit-icons.min.js"></script>
    <link rel="stylesheet" href="../static/styles/loadinganimations.css">
    <link rel="stylesheet" href="../static/styles/chartdropdown.css">
    <script src="../static/js/loading.js" defer></script>
    <script src="../static/js/chartsdropsdown.js" defer></script>
    <script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
    <script src="https://cdn.amcharts.com/lib/4/themes/dark.js"></script>
    <link rel="stylesheet" href="../static/styles/scrollbar.css">
    <link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/slate.min.css"/>
    <!-- Resources -->
    <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/radar.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>
    <script
        type="module"
        src="https://unpkg.com/franken-wc@latest/dist/js/wc.iife.js"
        ></script>
    <script>
        tailwind.config = {
        darkMode: 'class',
        }

        document.querySelectorAll('.uk-iconnav a').forEach(icon => {
            icon.addEventListener('click', (e) => {
            e.preventDefault(); // Prevent default action
            setTimeout(() => {
                icon.classList.remove('hidden');
            }, 0);
            });
        });
    </script>
</head>
<body class="bg-background text-foreground">
    <div id="loading-overlay" class="loading-overlay">
        <div class="loader"></div>
    </div>
    <div id="google_translate_element" style="display:none;"></div>
    <div class="grid min-h-screen w-full overflow-hidden lg:grid-cols-[280px_1fr]">
        {% include 'sidebar.html' %}
        <div class="flex flex-col">
            <header class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-6">
                <div class="flex items-center gap-4">
                    <a class="lg:hidden text-gray-600 hover:text-gray-800" href="#">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                            <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                            <path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1L21 9"></path>
                            <path d="M12 3v6"></path>
                        </svg>
                        <span class="sr-only">Home</span>
                    </a>
                    <h1 class="font-semibold text-lg">Analytics</h1>
                </div>
                {% include 'topright.html' %}
            </header>
            <main class="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6 card">
                            <div class="grid grid-cols-3 gap-x-4 gap-y-4">
   
                <!-- Left Section: Main Container Spanning 2 Columns -->
                <div class="col-span-2">
                    <!-- Nested Grid for First Row: Three Small Containers -->
                    <div class="grid grid-cols-3 gap-x-4 gap-y-4">

                        <!-- Card 2: Total Reservations -->
                        <div class="card rounded-lg border shadow-sm">
                            <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Total Reservations</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-notebook"><path d="M2 6h4"/><path d="M2 10h4"/><path d="M2 14h4"/><path d="M2 18h4"/><rect width="16" height="20" x="4" y="2" rx="2"/><path d="M16 2v20"/></svg>
                            </div>
                            <div class="p-6">
                                <div class="text-2xl font-bold">864</div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Lifetime reservations</p>
                            </div>
                        </div>
                        
                        <!-- Card 3: Total Orders -->
                        <div class="card rounded-lg border shadow-sm">
                            <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Total Orders</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar-arrow-up"><path d="m14 18 4-4 4 4"/><path d="M16 2v4"/><path d="M18 22v-8"/><path d="M21 11.343V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h9"/><path d="M3 10h18"/><path d="M8 2v4"/></svg>
                            </div>
                            <div class="p-6">
                                <div class="text-2xl font-bold">2312</div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Lifetime orders</p>
                            </div>
                        </div>

                        <!-- Combined Container -->
                        <div class="card rounded-lg border shadow-sm col-span-1">
                            <div class="p-6">
                                <!-- Content similar to product rankings container -->
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Product Rankings</h3>
                                <div class="mt-4">
                                    <!-- Add your product rankings content here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Nested Grid for Second Row: Bookings and Guest Feedback -->
                    <div class="grid grid-cols-3 gap-x-4 gap-y-4 mt-4">
                        
                        <!-- Guest Feedback (Spanning 2 Columns) -->
                        <div class="card rounded-lg border shadow-sm col-span-2">
                            <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                <div class="flex items-center gap-2">
                                    <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Guest Feedback</h3>
                                    <div class="group relative">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 bg-black text-white text-xs rounded py-1 px-2 hidden group-hover:block w-48 text-center">
                                            Reviews are categorized as follows: 1 and 2 star ratings are classified as negative, 3 star ratings are considered neutral, and 4 and 5 star ratings are regarded as positive.
                                        </div>
                                    </div>
                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-star"><path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"/></svg>
                            </div>
                            <div class="p-6">
                                <!-- Container for the progress bars -->
                                <div id="progress-bar-container" style="position: relative; width: 100%; height: 16px; margin-bottom: 10px;"></div>
                                <!-- Labels -->
                                <div class="flex justify-around -mx-4 mt-4">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-black rounded mr-1"></div>
                                        <span class="text-xs text-gray-500">Total: 52</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-[#4A90E2] rounded mr-1"></div>
                                        <span class="text-xs text-gray-500">Positive: 12</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-[#887cfd] rounded mr-1"></div>
                                        <span class="text-xs text-gray-500">Neutral: 17</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-[#F5A623] rounded mr-1"></div>
                                        <span class="text-xs text-gray-500">Negative: 22</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- New Combined Container -->
                        <div class="card rounded-lg border shadow-sm col-span-1">
                            <!-- Add content similar to product rankings container -->
                        </div>
                    </div>
                </div>
                                
                <!-- Right Section: Product Rankings -->
                <div class="card rounded-lg border shadow-sm col-span-1">
                    <div class="p-4 flex justify-between items-center">
                        <div class="pr-6">
                            <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">Product Rankings</h3>
                            <p class="text-sm text-muted-foreground mt-1">Ranking of products by sales</p>
                        </div>
                        <button class="uk-button border card" type="button">PMS</button>
                    </div>
                    <div class="pt-0 relative min-h-[240px] md:h-[220px]">
                        <div id="chartdiv" style="width: 100%; height: 100%;"></div>
                    </div>
                </div>
            
                <!-- Scripts related to Guest Feedback -->
            <script src="https://cdn.jsdelivr.net/npm/progressbar.js"></script> <!-- Ensure ProgressBar.js is included -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // Initialize the first segment for Positive feedback
                    var bar1 = new ProgressBar.Line('#progress-bar-container', {
                        strokeWidth: 3,
                        easing: 'easeInOut',
                        duration: 1400,
                        color: '#47a8f8',
                        trailColor: 'transparent',
                        trailWidth: 1,
                        svgStyle: {width: '69%', height: '100%', position: 'absolute', left: '0', borderRadius: '5px'},
                    });

                    // Initialize the second segment for Neutral feedback
                    var bar2 = new ProgressBar.Line('#progress-bar-container', {
                        strokeWidth: 3,
                        easing: 'easeInOut',
                        duration: 1400,
                        color: '#7e80e7',
                        trailColor: 'transparent',
                        trailWidth: 1,
                        svgStyle: {width: '19%', height: '100%', position: 'absolute', left: '70.5%', borderRadius: '5px'},
                    });

                    // Initialize the third segment for Negative feedback
                    var bar3 = new ProgressBar.Line('#progress-bar-container', {
                        strokeWidth: 3,
                        easing: 'easeInOut',
                        duration: 1400,
                        color: '#f3a23a',
                        trailColor: 'transparent',
                        trailWidth: 1,
                        svgStyle: {width: '9%', height: '100%', position: 'absolute', left: '90.5%', borderRadius: '5px'},
                    });

                    // Set the progress for each segment
                    bar1.animate(1.0);  // Number from 0.0 to 1.0
                    bar2.animate(1.0);
                    bar3.animate(1.0);
                });
            </script>

            </div>
                <style>
                    .platform-selector {
                        position: relative;
                        z-index: 2000;
                    }
                    .platform-selector-btn {
                        display: flex;
                        align-items: center;
                        padding: 0.5rem 1rem;
                        background-color: var(--theme-selector-bg, #e2e8f0);
                        color: var(--theme-selector-color, #212529);
                        border: 1px solid var(--theme-selector-border, #dee2e6);
                        border-radius: 20px;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        white-space: nowrap;
                        font-size: 0.875rem;
                    }
                    .platform-selector-btn:hover {
                        background-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.1));
                    }
                    .platform-selector-btn i {
                        margin-right: 0.5rem;
                    }
                    .platform-selector-menu {
                        display: block;
                        opacity: 0;
                        visibility: hidden;
                        position: absolute;
                        right: 0;
                        top: calc(100% + 5px);
                        background-color: var(--theme-selector-menu-bg, #ffffff);
                        border: 1px solid var(--theme-selector-menu-border, #dee2e6);
                        border-radius: 0.375rem;
                        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                        z-index: 10;
                        transition: all 0.3s ease;
                        transform: translateY(-10px);
                        width: 150px;
                    }
                    .platform-selector:hover .platform-selector-menu,
                    .platform-selector.active .platform-selector-menu {
                        opacity: 1;
                        visibility: visible;
                        transform: translateY(0);
                    }
                    .platform-option {
                        padding: 0.5rem 1rem;
                        cursor: pointer;
                        color: var(--theme-option-color, #212529);
                        transition: background-color 0.2s ease;
                        display: flex;
                        align-items: center;
                    }
                    .platform-option:hover {
                        background-color: var(--theme-option-hover-bg, #f1f5f9);
                    }
                    .platform-option i {
                        margin-right: 0.5rem;
                        width: 20px;
                        text-align: center;
                    }
                </style>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const platformSelector = document.querySelector('.platform-selector');
                        const platformOptions = document.querySelectorAll('.platform-option');
                        const platformSelectorBtn = platformSelector.querySelector('.platform-selector-btn');
                        let timeoutId;

                        function showDropdown() {
                            clearTimeout(timeoutId);
                            platformSelector.classList.add('active');
                        }

                        function hideDropdown() {
                            timeoutId = setTimeout(() => {
                                platformSelector.classList.remove('active');
                            }, 300);
                        }

                        function applyPlatform(platformName, iconClass) {
                            platformSelectorBtn.innerHTML = `<i class="${iconClass}"></i> ${platformName}`;
                            // Add logic to update the chart based on the selected platform
                            console.log('Selected platform:', platformName);
                        }

                        platformSelector.addEventListener('mouseenter', showDropdown);
                        platformSelector.addEventListener('mouseleave', hideDropdown);

                        platformOptions.forEach(option => {
                            option.addEventListener('click', function() {
                                const selectedPlatform = this.getAttribute('data-platform');
                                const iconClass = this.querySelector('i').className;
                                applyPlatform(this.textContent.trim(), iconClass);
                                hideDropdown();
                            });
                            option.addEventListener('mouseenter', showDropdown);
                        });

                        // Set default platform
                        applyPlatform('Facebook', 'fab fa-facebook');
                    });
                </script>
                <style>
                    #aiInteractionsChart {
                        width: 100%;
                        height: 100%;
                    }
                    
                    .chart-container-1 {
                        height: 350px; /* Custom height for the first chart */
                    }
                    
                    .chart-container-2 {
                        height: 350px; /* Custom height for the second chart */
                    }

                    .chart-container-2 .amcharts-ColumnSeries-column {
                        stroke: none; /* Remove the border */
                        stroke-width: 0; /* Ensure no border width */
                        fill: #18181b; /* Set the desired fill color */
                    }
                    
                    .chart-container-3 {
                        height: 350px; /* Custom height for the third chart */
                    }
                </style>
                <div class="grid gap-4 md:grid-cols-3 lg:grid-cols-3">
                    <div class="combined-charts grid gap-4 card rounded-lg border shadow-sm col-span-2">
                        <div class="flex justify-between items-start p-4">
                            <div class="flex flex-col">
                                <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">Random DIV</h3>
                                <p class="text-sm text-muted-foreground mt-1">RANDOM DIV SECOND</p>
                            </div>
                        </div>
                        <div class="pt-0 relative min-h-[240px] md:h-[220px]">
                            <div id="chartdivtrial" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>
                    <script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
<script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>
                    <style>
                        #chartdivtrial {
                          width: 100%;
                          height: 500px;
                        }
                        </style>
                    <script>
                        am5.ready(function() {
                        
                        // Create root element
                        // https://www.amcharts.com/docs/v5/getting-started/#Root_element
                        var root = am5.Root.new("chartdivtrial");
                        
                        // Set themes
                        // https://www.amcharts.com/docs/v5/concepts/themes/
                        root.setThemes([
                          am5themes_Animated.new(root)
                        ]);
                        
                        root.container.set("layout", root.verticalLayout);
                        
                        // Create container to hold charts
                        var chartContainer = root.container.children.push(am5.Container.new(root, {
                          layout: root.horizontalLayout,
                          width: am5.p100,
                          height: am5.p100
                        }));
                        
                        // Create the 1st chart
                        // https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/
                        var chart = chartContainer.children.push(
                          am5percent.PieChart.new(root, {
                            endAngle: 270,
                            innerRadius: am5.percent(60)
                          })
                        );
                        
                        
                        var series = chart.series.push(
                          am5percent.PieSeries.new(root, {
                            valueField: "value",
                            categoryField: "category",
                            endAngle: 270,
                            alignLabels: false
                          })
                        );
                        
                        series.children.push(am5.Label.new(root, {
                          centerX: am5.percent(50),
                          centerY: am5.percent(50),
                          text: "First: {valueSum}",
                          populateText: true,
                          fontSize: "1.5em"
                        }));
                        
                        series.slices.template.setAll({
                          cornerRadius: 8
                        })
                        
                        series.states.create("hidden", {
                          endAngle: -90
                        });
                        
                        series.labels.template.setAll({
                          textType: "circular"
                        });
                        
                        
                        // Create the 2nd chart
                        // https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/
                        var chart2 = chartContainer.children.push(
                          am5percent.PieChart.new(root, {
                            endAngle: 270,
                            innerRadius: am5.percent(60)
                          })
                        );
                        
                        var series2 = chart2.series.push(
                          am5percent.PieSeries.new(root, {
                            valueField: "value",
                            categoryField: "category",
                            endAngle: 270,
                            alignLabels: false,
                            tooltip: am5.Tooltip.new(root, {}) // a separate tooltip needed for this series
                          })
                        );
                        
                        series2.children.push(am5.Label.new(root, {
                          centerX: am5.percent(50),
                          centerY: am5.percent(50),
                          text: "Second: {valueSum}",
                          populateText: true,
                          fontSize: "1.5em"
                        }));
                        
                        series2.slices.template.setAll({
                          cornerRadius: 8
                        })
                        
                        series2.states.create("hidden", {
                          endAngle: -90
                        });
                        
                        series2.labels.template.setAll({
                          textType: "circular"
                        });
                        
                        
                        // Duplicate interaction
                        // Must be added before setting data
                        series.slices.template.events.on("pointerover", function(ev) {
                          var slice = ev.target;
                          var dataItem = slice.dataItem;
                          var otherSlice = getSlice(dataItem, series2);
                        
                          if (otherSlice) {
                            otherSlice.hover();
                          }
                        });
                        
                        series.slices.template.events.on("pointerout", function(ev) {
                          var slice = ev.target;
                          var dataItem = slice.dataItem;
                          var otherSlice = getSlice(dataItem, series2);
                        
                          if (otherSlice) {
                            otherSlice.unhover();
                          }
                        });
                        
                        series.slices.template.on("active", function(active, target) {
                          var slice = target;
                          var dataItem = slice.dataItem;
                          var otherSlice = getSlice(dataItem, series2);
                        
                          if (otherSlice) {
                            otherSlice.set("active", active);
                          }
                        });
                        
                        // Same for the 2nd series
                        series2.slices.template.events.on("pointerover", function(ev) {
                          var slice = ev.target;
                          var dataItem = slice.dataItem;
                          var otherSlice = getSlice(dataItem, series);
                        
                          if (otherSlice) {
                            otherSlice.hover();
                          }
                        });
                        
                        series2.slices.template.events.on("pointerout", function(ev) {
                          var slice = ev.target;
                          var dataItem = slice.dataItem;
                          var otherSlice = getSlice(dataItem, series);
                        
                          if (otherSlice) {
                            otherSlice.unhover();
                          }
                        });
                        
                        series2.slices.template.on("active", function(active, target) {
                          var slice = target;
                          var dataItem = slice.dataItem;
                          var otherSlice = getSlice(dataItem, series);
                        
                          if (otherSlice) {
                            otherSlice.set("active", active);
                          }
                        });
                        
                        
                        // Set data
                        // https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Setting_data
                        series.data.setAll([{
                          category: "Lithuania",
                          value: 501
                        }, {
                          category: "Czechia",
                          value: 301
                        }, {
                          category: "Ireland",
                          value: 201
                        }, {
                          category: "Germany",
                          value: 165
                        }]);
                        
                        // Set data
                        // https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Setting_data
                        series2.data.setAll([{
                          category: "Lithuania",
                          value: 201
                        }, {
                          category: "Czechia",
                          value: 101
                        }, {
                          category: "Ireland",
                          value: 51
                        }, {
                          category: "Germany",
                          value: 15
                        }]);
                        
                        
                        
                        function getSlice(dataItem, series) {
                          var otherSlice;
                          am5.array.each(series.dataItems, function(di) {
                            if (di.get("category") === dataItem.get("category")) {
                              otherSlice = di.get("slice");
                            }
                          });
                        
                          return otherSlice;
                        }
                        
                        // Create legend
                        var legend = root.container.children.push(am5.Legend.new(root, {
                          x: am5.percent(50),
                          centerX: am5.percent(50)
                        }));
                        
                        
                        // Trigger all the same for the 2nd series
                        legend.itemContainers.template.events.on("pointerover", function(ev) {
                          var dataItem = ev.target.dataItem.dataContext;
                          var slice = getSlice(dataItem, series2);
                          slice.hover();
                        });
                        
                        legend.itemContainers.template.events.on("pointerout", function(ev) {
                          var dataItem = ev.target.dataItem.dataContext;
                          var slice = getSlice(dataItem, series2);
                          slice.unhover();
                        });
                        
                        legend.itemContainers.template.on("disabled", function(disabled, target) {
                          var dataItem = target.dataItem.dataContext;
                          var slice = getSlice(dataItem, series2);
                          if (disabled) {
                            series2.hideDataItem(slice.dataItem);
                          }
                          else {
                            series2.showDataItem(slice.dataItem);
                          }
                        });
                        
                        legend.data.setAll(series.dataItems);
                        
                        series.appear(1000, 100);
                        
                        }); // end am5.ready()
                        </script>
                    
                    
        
                    <div class="card rounded-lg border  shadow-sm chart-container chart-container-3" data-v0-t="card">
                        <div class="flex justify-between items-start p-4">
                            <div class="flex flex-col">
                                <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">Automated conversations</h3>
                                <p class="text-sm text-muted-foreground mt-1">AI vs manual (ratio representation)</p>
                            </div>
                        </div>
                        <div class="p-4 relative min-h-[260px] md:h-[260px]">
                            <div class="p-4 pt-0 h-full flex flex-col relative">
                                <div class="relative w-11/12 mx-auto h-48 flex items-center justify-center mb-6">
                                    <svg viewBox="0 0 200 100" class="w-full h-full">
                                        <!-- Grey styling circle -->
                                        <path d="M10 100 A 90 90 0 0 1 190 100" fill="none" stroke="#F3F4F6" stroke-width="6"/>
                                        
                                        <!-- Main half circles -->
                                        <path d="M20 100 A 80 80 0 0 1 180 100" fill="none" stroke="#ABBDD3" stroke-width="5" class="transition-all duration-300 hover:filter hover:brightness-105" filter="url(#shadow)"/>
                                        <path d="M20 100 A 80 80 0 0 1 180 100" fill="none" stroke="#5347ce" stroke-width="5" stroke-dasharray="188 251" class="transition-all duration-300 hover:filter hover:brightness-105" filter="url(#shadow)"/>
                                        
                                        <!-- Define shadow filter -->
                                        <defs>
                                            <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                                                <feDropShadow dx="0" dy="1" stdDeviation="2" flood-opacity="0.1"/>
                                            </filter>
                                        </defs>
                                    </svg>
                                    <div class="absolute inset-0 flex flex-col items-center justify-center" style="padding-top: 70px;">
                                        <div class="rounded-full bg-white p-1.5 mb-3 border border-gray-200">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-blend"><circle cx="9" cy="9" r="7"/><circle cx="15" cy="15" r="7"/></svg>
                                        </div>
                                        <span class="text-lg font-semibold ">2,324</span>
                                        <p class="text-xs  mt-1">Total conversations</p>
                                    </div>
                                </div>
                                <div class="flex justify-between w-full">
                                    <div class="flex items-center">
                                        <div class="w-1 h-10 bg-[#5347ce] rounded-full mr-2"></div>
                                        <div>
                                            <span class="text-sm font-semibold text-gray-800">1,809</span>
                                            <p class="text-xs text-gray-500">AI Responses</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-end">
                                        <div class="text-right mr-2">
                                            <span class="text-sm font-semibold text-gray-800">314</span>
                                            <p class="text-xs text-gray-500">Manual Responses</p>
                                        </div>
                                        <div class="w-1 h-10 bg-[#ABBDD3] rounded-full"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                    <style>
                        @keyframes fillAnimation {
                            0% { stroke-dasharray: 0 251; }
                            100% { stroke-dasharray: 188 251; }
                        }
                        svg path:nth-child(3) {
                            animation: fillAnimation 1.5s ease-out forwards;
                        }
                    </style>
                    <script>
                        am4core.ready(function() {
                            am4core.useTheme(am4themes_animated);
                    
                            var currentAIChart;
                            var aiChartType = "pie";
                    
                            var data = [
                                { category: "AI Answered", value: 65, color: am4core.color("#85a9d1") },
                                { category: "Manually Answered", value: 35, color: am4core.color("#18181b") }
                            ];
                    
                            function createAIPieChart() {
                                var chart = am4core.create("questionResponsesChart", am4charts.PieChart);
                                chart.radius = am4core.percent(65); // Adjusted to 70%
                    
                                chart.data = data;
                    
                                var pieSeries = chart.series.push(new am4charts.PieSeries());
                                pieSeries.dataFields.value = "value";
                                pieSeries.dataFields.category = "category";
                                pieSeries.slices.template.propertyFields.fill = "color";
                    
                                chart.logo.disabled = true;
                                chart.innerRadius = am4core.percent(30);
                    
                                pieSeries.slices.template.padding = 1;
                                pieSeries.slices.template.cornerRadius = 5;
                                pieSeries.slices.template.fillOpacity = 0.8;
                                pieSeries.slices.template.strokeWidth = 2;
                                pieSeries.slices.template.stroke = am4core.color("#ffffff");
                    
                                pieSeries.labels.template.disabled = false;
                                pieSeries.labels.template.text = "{category}: {value}%";
                                pieSeries.labels.template.radius = 1;
                                pieSeries.labels.template.fontSize = 12;
                                pieSeries.labels.template.maxWidth = 80;
                                pieSeries.labels.template.wrap = true;
                    
                                pieSeries.ticks.template.disabled = false;
                                pieSeries.ticks.template.strokeOpacity = 0.7;
                                pieSeries.ticks.template.strokeWidth = 2; // Increased stroke width
                                pieSeries.ticks.template.length = 25; // Increased length
                    
                                pieSeries.slices.template.tooltipText = "{category}: {value}%";
                    
                                return chart;
                            }
                    
                            function updateAIChartColors() {
                                var body = document.body;
                                var isDarkTheme = body.classList.contains('pure-black') || 
                                                  body.classList.contains('dark-gray') || 
                                                  body.classList.contains('navy-blue') ||
                                                  body.classList.contains('cool-blue') ||
                                                  body.classList.contains('deep-burgundy') ||
                                                  body.classList.contains('charcoal');
                    
                                if (currentAIChart.series.getIndex(0) instanceof am4charts.PieSeries) {
                                    var pieSeries = currentAIChart.series.getIndex(0);
                                    pieSeries.labels.template.fill = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
                                    pieSeries.ticks.template.stroke = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
                                }
                            }
                    
                            function createAIChart() {
                                if (currentAIChart) {
                                    currentAIChart.dispose();
                                }
                    
                                currentAIChart = createAIPieChart();
                                updateAIChartColors();
                            }
                    
                            // Initial chart creation
                            createAIChart();
                    
                            // Listen for theme changes
                            var observer = new MutationObserver(function(mutations) {
                                mutations.forEach(function(mutation) {
                                    if (mutation.type === "attributes" && mutation.attributeName === "class") {
                                        updateAIChartColors();
                                    }
                                });
                            });
                    
                            observer.observe(document.body, {
                                attributes: true
                            });
                        });
                    </script>
                    
<!-- Styles -->
<style>
    #chartdiv {
      width: 100%;
      height: 500px;
    }
    </style>
    

    
    <!-- Chart code -->
    <script>
    am5.ready(function() {
    
    // Create root element
    var root = am5.Root.new("chartdiv");
    
    // Set themes
    root.setThemes([
      am5themes_Animated.new(root)
    ]);
    
    // Create chart
    var chart = root.container.children.push(am5radar.RadarChart.new(root, {
      panX: false,
      panY: false,
      wheelX: "panX",
      wheelY: "zoomX",
      innerRadius: am5.percent(20),
      startAngle: -90,
      endAngle: 180
    }));
    
    // Data
    var data = [{
      category: "Research",
      value: 80,
      full: 100,
      columnSettings: {
        fill: chart.get("colors").getIndex(0)
      }
    }, {
      category: "Marketing",
      value: 35,
      full: 100,
      columnSettings: {
        fill: chart.get("colors").getIndex(1)
      }
    }, {
      category: "Distribution",
      value: 92,
      full: 100,
      columnSettings: {
        fill: chart.get("colors").getIndex(2)
      }
    }, {
      category: "Human Resources",
      value: 68,
      full: 100,
      columnSettings: {
        fill: chart.get("colors").getIndex(3)
      }
    }];
    
    // Add cursor
    var cursor = chart.set("cursor", am5radar.RadarCursor.new(root, {
      behavior: "zoomX"
    }));
    
    cursor.lineY.set("visible", false);
    
    // Create axes and their renderers
    var xRenderer = am5radar.AxisRendererCircular.new(root, {});
    
    xRenderer.labels.template.setAll({
      radius: 10
    });
    
    xRenderer.grid.template.setAll({
      forceHidden: true
    });
    
    var xAxis = chart.xAxes.push(am5xy.ValueAxis.new(root, {
      renderer: xRenderer,
      min: 0,
      max: 100,
      strictMinMax: true,
      numberFormat: "#'%'",
      tooltip: am5.Tooltip.new(root, {})
    }));
    
    var yRenderer = am5radar.AxisRendererRadial.new(root, {
      minGridDistance: 20
    });
    
    yRenderer.labels.template.setAll({
      centerX: am5.p100,
      fontWeight: "500",
      fontSize: 18,
      templateField: "columnSettings"
    });
    
    yRenderer.grid.template.setAll({
      forceHidden: true
    });
    
    var yAxis = chart.yAxes.push(am5xy.CategoryAxis.new(root, {
      categoryField: "category",
      renderer: yRenderer
    }));
    
    yAxis.data.setAll(data);
    
    // Create series
    var series1 = chart.series.push(am5radar.RadarColumnSeries.new(root, {
      xAxis: xAxis,
      yAxis: yAxis,
      clustered: false,
      valueXField: "full",
      categoryYField: "category",
      fill: root.interfaceColors.get("alternativeBackground")
    }));
    
    series1.columns.template.setAll({
      width: am5.p100,
      fillOpacity: 0.08,
      strokeOpacity: 0,
      cornerRadius: 20
    });
    
    series1.data.setAll(data);
    
    var series2 = chart.series.push(am5radar.RadarColumnSeries.new(root, {
      xAxis: xAxis,
      yAxis: yAxis,
      clustered: false,
      valueXField: "value",
      categoryYField: "category"
    }));
    
    series2.columns.template.setAll({
      width: am5.p100,
      strokeOpacity: 0,
      tooltipText: "{category}: {valueX}%",
      cornerRadius: 20,
      templateField: "columnSettings"
    });
    
    series2.data.setAll(data);
    
    // Animate chart and series in
    series1.appear(1000);
    series2.appear(1000);
    chart.appear(1000, 100);
    
    }); // end am5.ready()
    </script>
    


                
                <script src="https://cdn.amcharts.com/lib/4/core.js"></script>
                <script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
                <script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>

                                </main>
                            </div>
                <script>
                function fetchCustomers() {
                    fetch('/customers')
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Network response was not ok');
                            }
                            return response.json();
                        })
                        .then(data => {
                            const tableBody = document.getElementById('customerTableBody');
                            tableBody.innerHTML = ''; // Clear existing rows
            
                            // Sort data in reverse order (latest first)
                            data.sort((a, b) => b.id - a.id);
            
                            data.forEach(customer => {
                                const row = document.createElement('tr');
                                row.className = 'border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted';
            
                                row.innerHTML = `
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0 font-medium">${customer.Name}</td>
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.room_number}</td>
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.phone_number}</td>
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.language}</td>
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.order}</td>
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.Platform}</td>
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0 text-right">${customer.spend}</td>
                                `;
            
                                tableBody.appendChild(row);
                            });
                        })
                        .catch(error => {
                            console.error('Error fetching customers:', error);
                        });
                }
            
                // Initial fetch
                fetchCustomers();
            
                // Refresh every 5 seconds
                setInterval(fetchCustomers, 60000);
            
                async function fetchAIvsManualData() {
                    try {
                        const response = await fetch('/aivsmanual');
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        const data = await response.json();
                        return data;
                    } catch (error) {
                        console.error('Error fetching AI vs Manual data:', error);
                        return null;
                    }
                }

                function calculateStepSize(maxValue) {
                    return Math.ceil(maxValue / 5);
                }

                async function updateChart(chart) {
                    const interactionData = await fetchAIvsManualData();
                    if (!interactionData) return;

                    const aiData = interactionData.ai_inter[0];
                    const manualData = interactionData.manual_inter[0];

                    const aiInteractionsData = [
                        aiData.mon, aiData.tue, aiData.wed, aiData.thu, 
                        aiData.fri, aiData.sat, aiData.sun
                    ];

                    const manualInteractionsData = [
                        manualData.mon, manualData.tue, manualData.wed, manualData.thu, 
                        manualData.fri, manualData.sat, manualData.sun
                    ];

                    const maxDataValue = Math.max(...aiInteractionsData, ...manualInteractionsData);
                    const stepSize = calculateStepSize(maxDataValue);

                    chart.data.datasets[0].data = aiInteractionsData;
                    chart.data.datasets[1].data = manualInteractionsData;
                    chart.options.scales.y.ticks.stepSize = stepSize;
                    chart.update();
                }


                async function fetchData(url) {
                    try {
                        const response = await fetch(url);
                        const data = await response.json();
                        return data;
                    } catch (error) {
                        console.error('Error fetching data:', error);
                        return [];
                    }
                }

            
    function updatePlatforms(platforms) {
        const container = document.getElementById('platforms-container');
        container.innerHTML = '';

        const platformItems = [
            {
                name: 'WhatsApp',
                icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 "><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>`,
                bgColor: '#25D366',
                customers: platforms.whatsapp_amount,
                sales: platforms.whatsapp_sales
            },
            {
                name: 'Facebook Messenger',
                icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 "><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1                     1 0 0 1 1-1h3z"></path></svg>`,
            bgColor: '#1877F2',
            customers: platforms.messenger_users,
            sales: platforms.messenger_sales
        },
        {
            name: 'Instagram',
            icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 "><rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line></svg>`,
            bgColor: '#E1306C',
            customers: platforms.instagram_users,
            sales: platforms.instagram_sales
        },
        {
            name: 'Voice',
            icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 "><path d="M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3"></path></svg>`,
            bgColor: '#4A5568',
            customers: platforms.voice_users,
            sales: platforms.voice_sales
        },
        {
            name: 'Web chat',
            icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 "><path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg>`,
            bgColor: '#6B46C1',
            customers: platforms.web_users,
            sales: platforms.web_sales
        }
    ];

    platformItems.forEach((platform, index) => {
        const platformHtml = `
            <div class="flex items-start gap-4">
                <div class="rounded-lg w-12 h-12 bg-[${platform.bgColor}] text-3xl flex items-center justify-center platform-icon">
                    ${platform.icon}
                </div>
                <div class="flex-1">
                    <p class="font-medium">${platform.name}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">No. of sales: ${platform.sales}</p>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">$${platform.sales}</div>
            </div>
            ${index < platformItems.length - 1 ? '<div class="divider"></div>' : ''}
        `;
        container.insertAdjacentHTML('beforeend', platformHtml);
    });
}

async function updateProducts(products) {
    const container = document.getElementById('products-container');
    container.innerHTML = '';

    const productItems = [
        { name: 'Food', emoji: '🍔', amount: products.food_amount, sales: products.food_sales, customers: products.food_cus, bgColor: '#55efc4' },
        { name: 'Beverage', emoji: '🍹', amount: products.beverage_amount, sales: products.beverage_sales, customers: products.beverage__cus, bgColor: '#ffeaa7' },
        { name: 'Spa', emoji: '🛀', amount: products.spa_bookings, sales: products.spa_sales, customers: products.spa_cus, bgColor: '#fdcb6e' },
        { name: 'Massage', emoji: '💆‍♀️', amount: products.massage_amount, sales: products.massage_sales, customers: products.massage_cus, bgColor: '#74b9ff' },
        { name: 'Room Bookings', emoji: '🛏️', amount: products.room_bookings, sales: products.room_sales, customers: products.room__cus, bgColor: '#5347ce' }
    ];

    productItems.forEach((product, index) => {
        const productHtml = `
            <div class="flex items-start gap-4">
                <div class="rounded-lg w-12 h-12 text-3xl flex items-center justify-center icon" style="background-color: ${product.bgColor};">${product.emoji}</div>
                <div class="flex-1">
                    <p class="font-medium">${product.name}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">No. of Sales: ${product.amount}</p>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">€${product.sales}</div>
            </div>
            ${index < productItems.length - 1 ? '<div class="divider"></div>' : ''}
        `;
        container.insertAdjacentHTML('beforeend', productHtml);
    });
}

let totalSalesElement = document.getElementById('total-sales');
let totalTipsElement = document.getElementById('total-tips');
let totalCustomersElement = document.getElementById('total-customers');

function updateFirstRowSales(data) {
    if (data && data.length > 0) {
        const firstRow = data[0];
        totalSalesElement.textContent = firstRow.total_sales;
        totalTipsElement.textContent = `+${firstRow.total_tips}`;
        totalCustomersElement.textContent = `+${firstRow.total_reviews}`;
        document.getElementById('total-messages').textContent = `+${firstRow.total_messages || 0}`;
        document.getElementById('total-bookings').textContent = `+${firstRow.total_bookings || 0}`;
    } else {
        totalSalesElement.textContent = '0';
        totalTipsElement.textContent = '+0';
        totalCustomersElement.textContent = '+0';
        document.getElementById('total-messages').textContent = '+0';
        document.getElementById('total-bookings').textContent = '+0';
    }
}

async function fetchFirstRowData() {
    try {
        const response = await fetch('/firstrowana');
        const data = await response.json();
        updateFirstRowSales(data);
    } catch (error) {
        console.error('Error fetching first row sales data:', error);
        updateFirstRowSales([]);
    }
}

// Initial fetch
fetchFirstRowData();

// Refresh every 5 seconds
setInterval(fetchFirstRowData, 60000);

async function init() {
    const platforms = await fetchData('/fetch-platforms');
    if (platforms.length > 0) {
        updatePlatforms(platforms[0]);
    }

    const products = await fetchData('/fetch-tpp');
    if (products.length > 0) {
        updateProducts(products[0]);
    }

    await updateFirstRowSales();
}

// Initial call
init();

// Refresh data every 5 seconds
setInterval(init, 60000);
                </script>


    </div>
    <script>
        async function initializeChart() {
            const interactionData = await fetchAIvsManualData();
            if (!interactionData) return;
    
            const aiData = interactionData.ai_inter[0];
            const manualData = interactionData.manual_inter[0];
    
            const aiInteractionsData = [
                aiData.mon, aiData.tue, aiData.wed, aiData.thu, 
                aiData.fri, aiData.sat, aiData.sun
            ];
    
            const manualInteractionsData = [
                manualData.mon, manualData.tue, manualData.wed, manualData.thu, 
                manualData.fri, manualData.sat, manualData.sun
            ];
    
            const maxDataValue = Math.max(...aiInteractionsData, ...manualInteractionsData);
            const stepSize = calculateStepSize(maxDataValue);
    
            const interactionChartData = {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [
                    {
                        label: 'AI help (Automated)',
                        data: aiInteractionsData,
                        backgroundColor: '#151519', // Solid color
                        borderRadius: {
                            topLeft: 6,
                            topRight: 6,
                            bottomLeft: 0,
                            bottomRight: 0
                        },
                        borderSkipped: 'bottom'
                    },
                    {
                        label: 'Staff help (manual)',
                        data: manualInteractionsData,
                        backgroundColor: '#abbbd3', // Solid color, no gradient
                        borderRadius: {
                            topLeft: 6,
                            topRight: 6,
                            bottomLeft: 0,
                            bottomRight: 0
                        },
                        borderSkipped: 'bottom'
                    }
                ]
            };
    
            const ctx = document.getElementById('salesOverviewChart').getContext('2d');
            const interactionChart = new Chart(ctx, {
                type: 'bar',
                data: interactionChartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    barPercentage: 1,
                    categoryPercentage: 0.7,
                    scales: {
                        x: {
                            grid: {
                                display: false,
                                color: 'transparent'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)',
                                borderDash: [5, 5]
                            },
                            ticks: {
                                stepSize: stepSize
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(200, 200, 200, 0.3)',
                            titleColor: '#000',
                            bodyColor: '#000'
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });
    
            // Set interval to update the chart every 5 seconds
            setInterval(() => {
                updateChart(interactionChart);
            }, 6000);
        }
    
        document.addEventListener('DOMContentLoaded', initializeChart);
    </script>
</div>
</body>
</html>