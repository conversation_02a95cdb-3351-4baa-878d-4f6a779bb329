(function() {
    let originalContent = new Map();
    
    function applyLanguage(langCode) {
      localStorage.setItem('selectedLanguage', langCode);
      
      
      // Translate the page or restore original content
      if (langCode === 'en') {
        restoreOriginalContent();
      } else {
        translatePage(langCode);
      }
    }
  
  
    function saveOriginalContent() {
      const elements = document.body.getElementsByTagName('*');
      for (let element of elements) {
        if (shouldTranslateNode(element)) {
          for (let node of element.childNodes) {
            if (node.nodeType === Node.TEXT_NODE) {
              const text = node.textContent.trim();
              if (text) {
                originalContent.set(node, text);
              }
            }
          }
        }
      }
    }
  
    function restoreOriginalContent() {
      originalContent.forEach((text, node) => {
        node.textContent = text;
      });
    }
  
    function translatePage(targetLang) {
      const elements = document.body.getElementsByTagName('*');
      for (let element of elements) {
        if (shouldTranslateNode(element)) {
          for (let node of element.childNodes) {
            if (node.nodeType === Node.TEXT_NODE) {
              const text = node.textContent.trim();
              if (text) {
                translateText(text, targetLang).then(translatedText => {
                  node.textContent = translatedText;
                });
              }
            }
          }
        }
      }
    }
  
    function shouldTranslateNode(node) {
      const nodeName = node.nodeName.toLowerCase();
      const ignoreList = ['script', 'style', 'textarea', 'input', 'button'];
      return !ignoreList.includes(nodeName) && !node.closest('[data-no-translate]');
    }
  
    function translateText(text, targetLang) {
      return fetch(`https://translate.googleapis.com/translate_a/single?client=gtx&sl=auto&tl=${targetLang}&dt=t&q=${encodeURIComponent(text)}`)
        .then(response => response.json())
        .then(data => {
          if (data[0] && data[0][0] && data[0][0][0]) {
            return data[0][0][0];
          }
          return text;
        })
        .catch(error => {
          console.error('Translation error:', error);
          return text;
        });
    }
  
    function setupLanguageSelectors() {
      document.querySelectorAll('.language-option').forEach(option => {
        option.addEventListener('click', function(event) {
          event.preventDefault();
          const selectedLang = this.getAttribute('data-lang');
          applyLanguage(selectedLang);
        });
      });
    }
  
    function initializeLanguage() {
      saveOriginalContent();
      setupLanguageSelectors();
      const savedLanguage = localStorage.getItem('selectedLanguage');
      if (savedLanguage) {
        applyLanguage(savedLanguage);
      } else {
        applyLanguage('en'); // Default to English
      }
    }
  
    // Run initialization when the DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeLanguage);
    } else {
      initializeLanguage();
    }
  
    // Expose a global function to change language
    window.changeLanguage = function(langCode) {
      applyLanguage(langCode);
    };
  })();