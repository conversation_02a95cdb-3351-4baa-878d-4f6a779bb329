<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loading Page</title>
    <link rel="stylesheet" href="../static/styles/custom.css">
    <style>
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            /* Glass Morphic Background */
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            /* Prevent any visible flicker */
            transition: opacity 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
            /* Smoother easing */
            pointer-events: none;
            /* Ensure it doesn't block interactions */
            will-change: opacity;
            /* Hardware acceleration hint */
        }

        .loader {
            width: 55px;
            height: 55px;
            position: relative;
            z-index: 1;
            transform: rotate(15deg);
            will-change: transform;
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .loader::before,
        .loader::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 51%;
            /* Slightly over 50% to prevent edge artifacts */
            mix-blend-mode: difference;
            will-change: transform, left;
            backface-visibility: hidden;
            /* Prevent flickering */
            transform-style: preserve-3d;
            filter: blur(0.2px);
            /* Subtle blur to smooth jagged edges */
            box-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
            /* Very subtle glow to mask edges */
            animation: rotate92523 1.6s infinite cubic-bezier(0.45, 0.05, 0.55, 0.95);
            /* Animation starts from middle of cycle to prevent initial stutter */
            animation-play-state: running;
        }

        .loader::before {
            background-color: #000000;
            animation-delay: -0.8s;
        }

        .loader::after {
            background-color: #e6e5e5;
            animation-delay: 0s;
        }

        @keyframes rotate92523 {

            0%,
            100% {
                left: 30px;
                /* Reduced from 40px to 30px (70%) */
                transform: scale(1) translateZ(0);
            }

            25% {
                transform: scale(0.45) translateZ(0);
                /* Increased from 0.35 for less dramatic scale */
            }

            50% {
                left: -5px;
                /* Changed from -10px to -5px */
                transform: scale(0.7) translateZ(0);
            }

            75% {
                transform: scale(1) translateZ(0);
            }
        }

        /* Pulsating effect - now using cubic-bezier for smoother motion */
        @keyframes pulse {

            0%,
            100% {
                transform: scale(1) rotate(15deg) translateZ(0);
            }

            50% {
                transform: scale(1.05) rotate(15deg) translateZ(0);
            }
        }

        .loader {
            animation: pulse 1.2s infinite cubic-bezier(0.445, 0.05, 0.55, 0.95);
            /* Improved smoothness */
            animation-play-state: running;
            animation-delay: -0.2s;
            /* Start from middle of animation */
        }
    </style>
</head>

<body>
    <div id="loading-overlay" class="loading-overlay">
        <div class="loader"></div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
                requestAnimationFrame(() => {
                    requestAnimationFrame(() => {
                        loadingOverlay.style.opacity = '1';
                        document.body.style.visibility = 'visible';
                    });
                });

                // Set minimum loading time to 2 seconds
                const minLoadTime = 2000;
                // Record the time when DOM content loaded
                window.domContentLoadedTime = Date.now();
                
                // Set maximum loading time
                setTimeout(() => requestAnimationFrame(hideLoadingOverlay), maxLoadingTime);
            }
        });

        // Handle window load event to ensure minimum loading time
        window.addEventListener('load', () => {
            // Calculate how much time has passed since DOM content loaded
            const elapsedTime = Date.now() - (window.domContentLoadedTime || 0);
            const remainingTime = Math.max(0, minLoadTime - elapsedTime);
            
            // Hide overlay after ensuring minimum display time
            if (elapsedTime < maxLoadingTime) {
                setTimeout(() => requestAnimationFrame(hideLoadingOverlay), remainingTime);
            }
        });

        const maxLoadingTime = 5000; // Maximum loading time set to 5 seconds
        const minLoadTime = 2000; // Minimum loading time set to 2 seconds

        function hideLoadingOverlay() {
            const loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.transition = 'opacity 0.3s cubic-bezier(0.215, 0.61, 0.355, 1)';
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 300);
            }
        }

        // Optimize pulseLoader to ensure it starts smoothly
        function pulseLoader() {
            // Animation now handled by CSS
            // This function remains for compatibility but does nothing
        }

        // Call the pulseLoader function on page load
        window.addEventListener('load', pulseLoader);
    </script>
</body>

</html>