import requests
import json

SUPABASE_URL = 'https://nuqxdjuaoccswunhqixz.supabase.co'
SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im51cXhkanVhb2Njc3d1bmhxaXh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTcxMjI2OTcsImV4cCI6MjAzMjY5ODY5N30.sHkkzEb5oCTlLB3MQ0420XtJpURXW1DIHuHm4M9kDPI'

# Define the API endpoint for the Sales_list table
api_url = f"{SUPABASE_URL}/rest/v1/Sales_list"

# Set the headers required by Supabase API
headers = {
    'apikey': SUPABASE_ANON_KEY,
    'Authorization': f'Bearer {SUPABASE_ANON_KEY}',
    'Content-Type': 'application/json'
}

# Make the GET request to fetch data
response = requests.get(api_url, headers=headers, params={'select': '*'}) # Fetch all columns

# Check if the request was successful
if response.status_code == 200:
    data = response.json()
    print("Successfully fetched data:")
    print(json.dumps(data, indent=4))
else:
    print(f"Failed to fetch data. Status code: {response.status_code}")
    print(f"Response: {response.text}")
