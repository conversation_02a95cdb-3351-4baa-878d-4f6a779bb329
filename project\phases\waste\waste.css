
/* Dark Gray Theme */
.dark-gray .card {
    border-color: #27272a;
    /* Example border color for dark gray theme */
}

/* Navy Blue Theme */
.navy-blue .card {
    border-color: #1e3a8a;
    /* Example border color for navy blue theme */
}

/* Slate Gray Theme */
.slate-gray .card {
    border-color: #64748b;
    /* Example border color for slate gray theme */
}

/* Warm Gray Theme */
.warm-gray .card {
    border-color: #a8a29e;
    /* Example border color for warm gray theme */
}

/* Cool Blue Theme */
.cool-blue .card {
    border-color: #3b82f6;
    /* Example border color for cool blue theme */
}

/* Olive Green Theme */
.olive-green .card {
    border-color: #6b7280;
    /* Example border color for olive green theme */
}

/* Deep Burgundy Theme */
.deep-burgundy .card {
    border-color: #7f1d1d;
    /* Example border color for deep burgundy theme */
}

/* Charcoal Theme */
.charcoal .card {
    border-color: #374151;
    /* Example border color for charcoal theme */
}

/* Base styles for the buttons */

.dark-gray .uk-tab-alt li a,
.navy-blue .uk-tab-alt li a,
.slate-gray .uk-tab-alt li a,
.warm-gray .uk-tab-alt li a,
.cool-blue .uk-tab-alt li a,
.olive-green .uk-tab-alt li a,
.deep-burgundy .uk-tab-alt li a,
.charcoal .uk-tab-alt li a,
.light .uk-tab-alt li.uk-active a,
.pure-black .uk-tab-alt li.uk-active a,
.dark-gray .uk-tab-alt li.uk-active a,
.navy-blue .uk-tab-alt li.uk-active a,
.slate-gray .uk-tab-alt li.uk-active a,
.warm-gray .uk-tab-alt li.uk-active a,
.cool-blue .uk-tab-alt li.uk-active a,
.olive-green .uk-tab-alt li.uk-active a,
.deep-burgundy .uk-tab-alt li.uk-active a,
.charcoal .uk-tab-alt li.uk-active a {
    background-color: transparent !important;
    border: none !important;
}

.dark-gray {
    /* ... existing styles ... */
    --theme-overlay-bg: rgb(60, 60, 60);
}

.navy-blue {
    /* ... existing styles ... */
    --theme-overlay-bg: rgba(20, 50, 80);
}

.slate-gray {
    /* ... existing styles ... */
    --theme-overlay-bg: rgba(80, 100, 120);
}

.warm-gray {
    /* ... existing styles ... */
    --theme-overlay-bg: rgba(100, 95, 90);
}

.cool-blue {
    /* ... existing styles ... */
    --theme-overlay-bg: rgba(50, 70, 90);
}

.olive-green {
    /* ... existing styles ... */
    --theme-overlay-bg: rgba(70, 90, 70);
}

.deep-burgundy {
    /* ... existing styles ... */
    --theme-overlay-bg: rgba(80, 30, 50);
}

.charcoal {
    /* ... existing styles ... */
    --theme-overlay-bg: rgba(55, 60, 70);
}


.dark-gray .intro-prompt-button {
    background-color: transparent;
    border: 1px solid #4b5563;
    color: #f8f9fa;
}

.navy-blue .intro-prompt-button {
    background-color: transparent;
    border: 1px solid #2c5c7d;
    color: #ffffff;
}

.slate-gray .intro-prompt-button {
    background-color: transparent;
    border: 1px solid #6f7f8f;
    color: #ffffff;
}

.warm-gray .intro-prompt-button {
    background-color: transparent;
    border: 1px solid #a8a29e;
    color: #fafaf9;
}

.cool-blue .intro-prompt-button {
    background-color: transparent;
    border: 1px solid #46627f;
    color: #ecf0f1;
}

.olive-green .intro-prompt-button {
    background-color: transparent;
    border: 1px solid #607660;
    color: #e8f0e8;
}

.deep-burgundy .intro-prompt-button {
    background-color: transparent;
    border: 1px solid #722c41;
    color: #f9e8ec;
}

.charcoal .intro-prompt-button {
    background-color: transparent;
    border: 1px solid #4b5563;
    color: #f5f6fa;
}

.navy-blue .whatsapp-theme-icon,
.slate-gray .whatsapp-theme-icon,
.warm-gray .whatsapp-theme-icon,
.cool-blue .whatsapp-theme-icon,
.olive-green .whatsapp-theme-icon,
.deep-burgundy .whatsapp-theme-icon,
.charcoal .whatsapp-theme-icon {
    content: url('../icons/white-whatsapp-icon.png');
}

.dark-gray .uk-label.uk-label-primary {
    background: #374151;
    color: #f9fafb;
}

.navy-blue .uk-label.uk-label-primary {
    background: #1e3a8a;
    color: #ffffff;
}

.slate-gray .uk-label.uk-label-primary {
    background: #475569;
    color: #f8fafc;
}

.warm-gray .uk-label.uk-label-primary {
    background: #57534e;
    color: #fafaf9;
}

.cool-blue .uk-label.uk-label-primary {
    background: #1e40af;
    color: #eff6ff;
}

.olive-green .uk-label.uk-label-primary {
    background: #3f6212;
    color: #f7fee7;
}

.deep-burgundy .uk-label.uk-label-primary {
    background: #881337;
    color: #fff1f2;
}

.charcoal .uk-label.uk-label-primary {
    background: #1f2937;
    color: #f9fafb;
}

.pure-black .uk-icon svg,
.dark-gray .uk-icon svg,
.navy-blue .uk-icon svg,
.slate-gray .uk-icon svg,
.warm-gray .uk-icon svg,
.cool-blue .uk-icon svg,
.olive-green .uk-icon svg,
.deep-burgundy .uk-icon svg,
.charcoal .uk-icon svg {
    color: #ffffff;
}

.pure-black .uk-icon svg path,
.dark-gray .uk-icon svg path,
.navy-blue .uk-icon svg path,
.slate-gray .uk-icon svg path,
.warm-gray .uk-icon svg path,
.cool-blue .uk-icon svg path,
.olive-green .uk-icon svg path,
.deep-burgundy .uk-icon svg path,
.charcoal .uk-icon svg path {
    fill: #ffffff !important;
    stroke: #ffffff !important;
}

/* Ensure this hover color is consistent across all themes */
.pure-black .uk-dropdown-nav.uk-nav li:hover,
.dark-gray .uk-dropdown-nav.uk-nav li:hover,
.navy-blue .uk-dropdown-nav.uk-nav li:hover,
.slate-gray .uk-dropdown-nav.uk-nav li:hover,
.warm-gray .uk-dropdown-nav.uk-nav li:hover,
.cool-blue .uk-dropdown-nav.uk-nav li:hover,
.olive-green .uk-dropdown-nav.uk-nav li:hover,
.deep-burgundy .uk-dropdown-nav.uk-nav li:hover,
.charcoal .uk-dropdown-nav.uk-nav li:hover {
    background-color: #f4f4f5 !important;
}


/* Dark themes dropdown hover */
.pure-black .uk-dropdown-nav.uk-nav li:hover,
.dark-gray .uk-dropdown-nav.uk-nav li:hover,
.navy-blue .uk-dropdown-nav.uk-nav li:hover,
.slate-gray .uk-dropdown-nav.uk-nav li:hover,
.warm-gray .uk-dropdown-nav.uk-nav li:hover,
.cool-blue .uk-dropdown-nav.uk-nav li:hover,
.olive-green .uk-dropdown-nav.uk-nav li:hover,
.deep-burgundy .uk-dropdown-nav.uk-nav li:hover,
.charcoal .uk-dropdown-nav.uk-nav li:hover {
    background-color: #27272a !important;
    color: #ffffff !important;
}


/* Ensure this applies to all dropdown types in dark themes */
.pure-black .uk-drop.uk-dropdown .uk-dropdown-nav li:hover,
.dark-gray .uk-drop.uk-dropdown .uk-dropdown-nav li:hover,
.navy-blue .uk-drop.uk-dropdown .uk-dropdown-nav li:hover,
.slate-gray .uk-drop.uk-dropdown .uk-dropdown-nav li:hover,
.warm-gray .uk-drop.uk-dropdown .uk-dropdown-nav li:hover,
.cool-blue .uk-drop.uk-dropdown .uk-dropdown-nav li:hover,
.olive-green .uk-drop.uk-dropdown .uk-dropdown-nav li:hover,
.deep-burgundy .uk-drop.uk-dropdown .uk-dropdown-nav li:hover,
.charcoal .uk-drop.uk-dropdown .uk-dropdown-nav li:hover {
    background-color: #27272a !important;
}

.dark .custom-hover:hover,
.pure-black .custom-hover:hover,
.dark-gray .custom-hover:hover,
.navy-blue .custom-hover:hover,
.slate-gray .custom-hover:hover,
.warm-gray .custom-hover:hover,
.cool-blue .custom-hover:hover,
.olive-green .custom-hover:hover,
.deep-burgundy .custom-hover:hover,
.charcoal .custom-hover:hover {
    background-color: #27272a;
}

.dark .universal-hover:hover,
.pure-black .universal-hover:hover,
.dark-gray .universal-hover:hover,
.navy-blue .universal-hover:hover,
.slate-gray .universal-hover:hover,
.warm-gray .universal-hover:hover,
.cool-blue .universal-hover:hover,
.olive-green .universal-hover:hover,
.deep-burgundy .universal-hover:hover,
.charcoal .universal-hover:hover {
    background-color: #27272a !important;
}



/* Dark Gray */
.dark-gray {
    background-color: #212529;
    color: #f8f9fa;
}

.dark-gray #form-container {
    background-color: #343a40;
    border: 1px solid #000000;
}

.dark-gray input,
.dark-gray textarea {
    background-color: #a8a29e;
    border: 1px solid #d6d3d1;
    color: #1c1917;
}

.dark-gray button {
    background-color: #3d7ea6;
    color: #ffffff;
}

/* Navy Blue */
.navy-blue {
    background-color: #0e253a;
    color: #ffffff;
}

.navy-blue #form-container {
    background-color: #1a3a54;
    border: 1px solid #2c5c7d;
}

.navy-blue input,
.navy-blue textarea {
    background-color: #2c5c7d;
    border: 1px solid #3d7ea6;
    color: #ffffff;
}

.navy-blue button {
    background-color: #3d7ea6;
    color: #ffffff;
}

/* Slate Gray */
.slate-gray {
    background-color: #3f4d5a;
    color: #ffffff;
}

.slate-gray #form-container {
    background-color: #576574;
    border: 1px solid #6f7f8f;
}

.slate-gray input,
.slate-gray textarea {
    background-color: #6f7f8f;
    border: 1px solid #8795a7;
    color: #ffffff;
}

.slate-gray button {
    background-color: #8795a7;
    color: #ffffff;
}

/* Warm Gray */
.warm-gray {
    background-color: #57534e;
    color: #fafaf9;
}

.warm-gray #form-container {
    background-color: #78716c;
    border: 1px solid #a8a29e;
}

.warm-gray input,
.warm-gray textarea {
    background-color: #a8a29e;
    border: 1px solid #d6d3d1;
    color: #1c1917;
}

.warm-gray input::placeholder,
.warm-gray textarea::placeholder {
    color: #57534e;
}

.warm-gray button {
    background-color: #d6d3d1;
    color: #1c1917;
}

/* Cool Blue */
.cool-blue {
    background-color: #2c3e50;
    color: #ecf0f1;
}

.cool-blue #form-container {
    background-color: #34495e;
    border: 1px solid #46627f;
}

.cool-blue input,
.cool-blue textarea {
    background-color: #46627f;
    border: 1px solid #5c7a99;
    color: #ecf0f1;
}

.cool-blue button {
    background-color: #5c7a99;
    color: #ecf0f1;
}

/* Olive Green */
.olive-green {
    background-color: #3c4c3c;
    color: #e8f0e8;
}

.olive-green #form-container {
    background-color: #4e614e;
    border: 1px solid #607660;
}

.olive-green input,
.olive-green textarea {
    background-color: #607660;
    border: 1px solid #728c72;
    color: #e8f0e8;
}

.olive-green button {
    background-color: #728c72;
    color: #e8f0e8;
}

/* Deep Burgundy */
.deep-burgundy {
    background-color: #4a1c2b;
    color: #f9e8ec;
}

.deep-burgundy #form-container {
    background-color: #5e2436;
    border: 1px solid #722c41;
}

.deep-burgundy input,
.deep-burgundy textarea {
    background-color: #722c41;
    border: 1px solid #86344c;
    color: #f9e8ec;
}

.deep-burgundy button {
    background-color: #86344c;
    color: #f9e8ec;
}

/* Charcoal */
.charcoal {
    background-color: #2f3640;
    color: #f5f6fa;
}

.charcoal #form-container {
    background-color: #353b48;
    border: 1px solid #4b5563;
}

.charcoal input,
.charcoal textarea {
    background-color: #4b5563;
    border: 1px solid #616e7c;
    color: #f5f6fa;
}

.charcoal button {
    background-color: #616e7c;
    color: #f5f6fa;
}



/* Dark Gray */
.dark-gray {
    background-color: #212529;
    color: #f8f9fa;
    --theme-selector-bg: #343a40;
    --theme-selector-color: #f8f9fa;
    --theme-selector-border: #495057;
    --theme-selector-menu-bg: #343a40;
    --theme-selector-menu-border: #495057;
    --theme-option-color: #f8f9fa;
    --theme-option-hover-bg: #495057;
}

/* Navy Blue */
.navy-blue {
    background-color: #0e253a;
    color: #ffffff;
    --theme-selector-bg: #1a3a54;
    --theme-selector-color: #ffffff;
    --theme-selector-border: #2c5c7d;
    --theme-selector-menu-bg: #1a3a54;
    --theme-selector-menu-border: #2c5c7d;
    --theme-option-color: #ffffff;
    --theme-option-hover-bg: #2c5c7d;
}

/* Slate Gray */
.slate-gray {
    background-color: #3f4d5a;
    color: #ffffff;
    --theme-selector-bg: #576574;
    --theme-selector-color: #ffffff;
    --theme-selector-border: #6f7f8f;
    --theme-selector-menu-bg: #576574;
    --theme-selector-menu-border: #6f7f8f;
    --theme-option-color: #ffffff;
    --theme-option-hover-bg: #6f7f8f;
}

/* Warm Gray */
.warm-gray {
    background-color: #57534e;
    color: #fafaf9;
    --theme-selector-bg: #78716c;
    --theme-selector-color: #fafaf9;
    --theme-selector-border: #a8a29e;
    --theme-selector-menu-bg: #78716c;
    --theme-selector-menu-border: #a8a29e;
    --theme-option-color: #fafaf9;
    --theme-option-hover-bg: #a8a29e;
}

/* Cool Blue */
.cool-blue {
    background-color: #2c3e50;
    color: #ecf0f1;
    --theme-selector-bg: #34495e;
    --theme-selector-color: #ecf0f1;
    --theme-selector-border: #46627f;
    --theme-selector-menu-bg: #34495e;
    --theme-selector-menu-border: #46627f;
    --theme-option-color: #ecf0f1;
    --theme-option-hover-bg: #46627f;
}

/* Olive Green */
.olive-green {
    background-color: #546854;
    color: #e8f0e8;
    --theme-selector-bg: #4e614e;
    --theme-selector-color: #e8f0e8;
    --theme-selector-border: #607660;
    --theme-selector-menu-bg: #4e614e;
    --theme-selector-menu-border: #607660;
    --theme-option-color: #e8f0e8;
    --theme-option-hover-bg: #607660;
}

/* Deep Burgundy */
.deep-burgundy {
    background-color: #4a1c2b;
    color: #f9e8ec;
    --theme-selector-bg: #5e2436;
    --theme-selector-color: #f9e8ec;
    --theme-selector-border: #722c41;
    --theme-selector-menu-bg: #5e2436;
    --theme-selector-menu-border: #722c41;
    --theme-option-color: #f9e8ec;
    --theme-option-hover-bg: #722c41;
}

/* Charcoal */
.charcoal {
    background-color: #2f3640;
    color: #f5f6fa;
    --theme-selector-bg: #353b48;
    --theme-selector-color: #f5f6fa;
    --theme-selector-border: #4b5563;
    --theme-selector-menu-bg: #353b48;
    --theme-selector-menu-border: #4b5563;
    --theme-option-color: #f5f6fa;
    --theme-option-hover-bg: #4b5563;
}


/* Dark Gray Theme */
.dark-gray .card {
    border-color: #27272a;
    /* Example border color for dark gray theme */
}

/* Navy Blue Theme */
.navy-blue .card {
    border-color: #1e3a8a;
    /* Example border color for navy blue theme */
}/* Slate Gray Theme */
.slate-gray .card {
    border-color: #64748b;
    /* Example border color for slate gray theme */
}

/* Warm Gray Theme */
.warm-gray .card {
    border-color: #a8a29e;
    /* Example border color for warm gray theme */
}

/* Cool Blue Theme */
.cool-blue .card {
    border-color: #3b82f6;
    /* Example border color for cool blue theme */
}

/* Olive Green Theme */
.olive-green .card {
    border-color: #ffffff;
    /* Example border color for olive green theme */
}

/* Deep Burgundy Theme */
.deep-burgundy .card {
    border-color: #7f1d1d;
    /* Example border color for deep burgundy theme */
}

/* Charcoal Theme */
.charcoal .card {
    border-color: #374151;
    /* Example border color for charcoal theme */
}
/* Dark Gray Theme */
.dark-gray .uk-tab-alt li a {
    color: #f8f9fa;
    background-color: #343a40;
    border: 1px solid #495057;
}

.dark-gray .uk-tab-alt li.uk-active a,
.dark-gray .uk-tab-alt li a:hover {
    background-color: #495057;
    border-color: #6c757d;
}

/* Navy Blue Theme */
.navy-blue .uk-tab-alt li a {
    color: #ffffff;
    background-color: #1a3a54;
    border: 1px solid #2c5c7d;
}

.navy-blue .uk-tab-alt li.uk-active a,
.navy-blue .uk-tab-alt li a:hover {
    background-color: #2c5c7d;
    border-color: #3d7ea6;
}

/* Slate Gray Theme */
.slate-gray .uk-tab-alt li a {
    color: #ffffff;
    background-color: #576574;
    border: 1px solid #6f7f8f;
}

.slate-gray .uk-tab-alt li.uk-active a,
.slate-gray .uk-tab-alt li a:hover {
    background-color: #6f7f8f;
    border-color: #8795a7;
}

/* Warm Gray Theme */
.warm-gray .uk-tab-alt li a {
    color: #fafaf9;
    background-color: #78716c;
    border: 1px solid #a8a29e;
}

.warm-gray .uk-tab-alt li.uk-active a,
.warm-gray .uk-tab-alt li a:hover {
    background-color: #a8a29e;
    border-color: #d6d3d1;
}

/* Cool Blue Theme */
.cool-blue .uk-tab-alt li a {
    color: #ecf0f1;
    background-color: #34495e;
    border: 1px solid #46627f;
}

.cool-blue .uk-tab-alt li.uk-active a,
.cool-blue .uk-tab-alt li a:hover {
    background-color: #46627f;
    border-color: #5c7a99;
}

/* Olive Green Theme */
.olive-green .uk-tab-alt li a {
    color: #e8f0e8;
    background-color: #4e614e;
    border: 1px solid #607660;
}

.olive-green .uk-tab-alt li.uk-active a,
.olive-green .uk-tab-alt li a:hover {
    background-color: #607660;
    border-color: #ffffff;
}

/* Deep Burgundy Theme */
.deep-burgundy .uk-tab-alt li a {
    color: #f9e8ec;
    background-color: #5e2436;
    border: 1px solid #722c41;
}

.deep-burgundy .uk-tab-alt li.uk-active a,
.deep-burgundy .uk-tab-alt li a:hover {
    background-color: #722c41;
    border-color: #86344c;
}

/* Charcoal Theme */
.charcoal .uk-tab-alt li a {
    color: #f5f6fa;
    background-color: #353b48;
    border: 1px solid #4b5563;
}

.charcoal .uk-tab-alt li.uk-active a,
.charcoal .uk-tab-alt li a:hover {
    background-color: #4b5563;
    border-color: #616e7c;
}

/* Remove background from the parent div */
.uk-tab-alt.ml-auto.max-w-40 {
    background-color: transparent !important;
    box-shadow: none !important;
}


/* Ensure transparency in all themes */
.light .uk-tab-alt.ml-auto.max-w-40,
.pure-black .uk-tab-alt.ml-auto.max-w-40,
.dark-gray .uk-tab-alt.ml-auto.max-w-40,
.navy-blue .uk-tab-alt.ml-auto.max-w-40,
.slate-gray .uk-tab-alt.ml-auto.max-w-40,
.warm-gray .uk-tab-alt.ml-auto.max-w-40,
.cool-blue .uk-tab-alt.ml-auto.max-w-40,
.olive-green .uk-tab-alt.ml-auto.max-w-40,
.deep-burgundy .uk-tab-alt.ml-auto.max-w-40,
.charcoal .uk-tab-alt.ml-auto.max-w-40 {
    background-color: transparent !important;
    box-shadow: none !important;
}

.dark-gray {
    --language-label-bg: #27272a;
    --language-label-color: #f0f0f0;
    --platform-label-bg: #2980b9;
    --platform-label-color: #f0f0f0;
}

.navy-blue {
    --language-label-bg: #34495e;
    --language-label-color: #ecf0f1;
    --platform-label-bg: #3498db;
    --platform-label-color: #ecf0f1;
}

.slate-gray {
    --language-label-bg: #7f8c8d;
    --language-label-color: #ecf0f1;
    --platform-label-bg: #2980b9;
    --platform-label-color: #ecf0f1;
}

.warm-gray {
    --language-label-bg: #95a5a6;
    --language-label-color: #2c3e50;
    --platform-label-bg: #16a085;
    --platform-label-color: #ecf0f1;
}

.cool-blue {
    --language-label-bg: #3498db;
    --language-label-color: #ecf0f1;
    --platform-label-bg: #2980b9;
    --platform-label-color: #ecf0f1;
}

.olive-green {
    --language-label-bg: #7f8c8d;
    --language-label-color: #ecf0f1;
    --platform-label-bg: #27ae60;
    --platform-label-color: #ecf0f1;
}

.deep-burgundy {
    --language-label-bg: #7f8c8d;
    --language-label-color: #ecf0f1;
    --platform-label-bg: #c0392b;
    --platform-label-color: #ecf0f1;
}

.charcoal {
    --language-label-bg: #34495e;
    --language-label-color: #ecf0f1;
    --platform-label-bg: #2980b9;
    --platform-label-color: #ecf0f1;
}


/* Dark Gray */
.dark-gray {
    /* ... existing variables ... */
    --dropdown-bg: #343a40;
    --dropdown-text: #f8f9fa;
    --dropdown-hover-bg: #495057;
    --dropdown-hover-text: #ffffff;
    --dropdown-border: #495057;
}

/* Navy Blue */
.navy-blue {
    /* ... existing variables ... */
    --dropdown-bg: #1a3a54;
    --dropdown-text: #ffffff;
    --dropdown-hover-bg: #2c5c7d;
    --dropdown-hover-text: #ffffff;
    --dropdown-border: #2c5c7d;
}

/* Slate Gray */
.slate-gray {
    /* ... existing variables ... */
    --dropdown-bg: #576574;
    --dropdown-text: #ffffff;
    --dropdown-hover-bg: #6f7f8f;
    --dropdown-hover-text: #ffffff;
    --dropdown-border: #6f7f8f;
}

/* Warm Gray */
.warm-gray {
    /* ... existing variables ... */
    --dropdown-bg: #78716c;
    --dropdown-text: #fafaf9;
    --dropdown-hover-bg: #a8a29e;
    --dropdown-hover-text: #1c1917;
    --dropdown-border: #a8a29e;
}

/* Cool Blue */
.cool-blue {
    /* ... existing variables ... */
    --dropdown-bg: #34495e;
    --dropdown-text: #ecf0f1;
    --dropdown-hover-bg: #46627f;
    --dropdown-hover-text: #ecf0f1;
    --dropdown-border: #46627f;
}

/* Olive Green */
.olive-green {
    /* ... existing variables ... */
    --dropdown-bg: #4e614e;
    --dropdown-text: #e8f0e8;
    --dropdown-hover-bg: #607660;
    --dropdown-hover-text: #e8f0e8;
    --dropdown-border: #607660;
}

/* Deep Burgundy */
.deep-burgundy {
    /* ... existing variables ... */
    --dropdown-bg: #5e2436;
    --dropdown-text: #f9e8ec;
    --dropdown-hover-bg: #722c41;
    --dropdown-hover-text: #f9e8ec;
    --dropdown-border: #722c41;
}

/* Charcoal */
.charcoal {
    /* ... existing variables ... */
    --dropdown-bg: #353b48;
    --dropdown-text: #f5f6fa;
    --dropdown-hover-bg: #4b5563;
    --dropdown-hover-text: #f5f6fa;
    --dropdown-border: #4b5563;
}

