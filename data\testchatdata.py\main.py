import requests

SUPABASE_URL = 'https://nuqxdjuaoccswunhqixz.supabase.co'
SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im51cXhkanVhb2Njc3d1bmhxaXh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTcxMjI2OTcsImV4cCI6MjAzMjY5ODY5N30.sHkkzEb5oCTlLB3MQ0420XtJpURXW1DIHuHm4M9kDPI'


def get_chats_data():
    """Fetches data from the 'chats' table in Supabase."""
    table_name = "chats"
    url = f"{SUPABASE_URL}/rest/v1/{table_name}"
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers, params={"select": "*"})
        response.raise_for_status()  # Raises an HTTPError for bad responses (4XX or 5XX)
        data = response.json()
        print(f"Successfully fetched data from '{table_name}':")
        for row in data:
            print(row)
        return data
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
    except requests.exceptions.ConnectionError as conn_err:
        print(f"Connection error occurred: {conn_err}")
    except requests.exceptions.Timeout as timeout_err:
        print(f"Timeout error occurred: {timeout_err}")
    except requests.exceptions.RequestException as req_err:
        print(f"An error occurred: {req_err}")
    return None

if __name__ == "__main__":
    get_chats_data()
