#------------------------ IMPORTS AND CONFIGURATIONS -------------------------------
from flask import Flask, request
from twilio.rest import Client
from twilio.twiml.messaging_response import MessagingResponse
from dotenv import load_dotenv
import os
import json
import textwrap
from openai_agent import generate_reply

#---------------------------- GLOBAL VARIABLES ----------------------------------
TERMINAL_RAW_OUTPUT = True

# Modern color scheme
BLUE = '\033[38;2;100;149;237m'    # Cornflower Blue
GREEN = '\033[38;2;72;187;120m'    # Emerald Green
RED = '\033[38;2;235;87m'       # Soft Red
RESET = '\033[0m'
DIVIDER = '─' * 100  # More minimal and elegant divider

#-------------------------- APP INITIALIZATION ---------------------------------
# Load environment variables
load_dotenv()

app = Flask(__name__)

# Initialize Twilio client
client = Client(
    os.getenv('TWILIO_ACCOUNT_SID'),
    os.getenv('TWILIO_AUTH_TOKEN')
)

#--------------------------- HELPER FUNCTIONS ---------------------------------
# Authorized phone number
AUTHORIZED_NUMBER = "whatsapp:+************"

def is_authorized(phone_number):
    return phone_number == AUTHORIZED_NUMBER

# Add text wrapper function
def wrap_text(text, width=90):  # 90 to account for box characters and padding
    return textwrap.fill(text, width=width)

#-------------------------- TERMINAL STYLING ----------------------------------
def display_status_update(status, message_sid):
    if not TERMINAL_RAW_OUTPUT:
        print(f"\n{BLUE}╭{'─' * 98}╮")
        print(f"│ Status: {status} - SID: {message_sid[:8]}...")
        print(f"╰{'─' * 98}╯{RESET}")

def display_incoming_message(sender, message):
    if not TERMINAL_RAW_OUTPUT:
        print(f"\n{GREEN}╭{'─' * 98}╮")
        print(f"│ 📱 INCOMING MESSAGE")
        print(f"├{'─' * 98}┤")
        print(f"│ From: {sender}")
        for line in wrap_text(f"Message: {message}").split('\n'):
            print(f"│ {line}")
        print(f"╰{'─' * 98}╯{RESET}\n")

def display_outgoing_response(response, message_sid):
    if not TERMINAL_RAW_OUTPUT:
        print(f"{RED}╭{'─' * 98}╮")
        print(f"│ 📤 OUTGOING RESPONSE")
        print(f"├{'─' * 98}┤")
        for line in wrap_text(f"Response: {response}").split('\n'):
            print(f"│ {line}")
        print(f"│ Message SID: {message_sid[:8]}...")
        print(f"╰{'─' * 98}╯{RESET}\n")

def display_raw_webhook_data(data_dict):
    if TERMINAL_RAW_OUTPUT:
        pretty_json = json.dumps(data_dict, indent=4)
        print(f"\n{DIVIDER}")
        print(pretty_json)
        print(f"{DIVIDER}\n")

#--------------------------- WEBHOOK HANDLER ---------------------------------
@app.route("/", methods=['POST'])
def webhook():
    # Convert request values to dict and output pretty JSON in raw mode
    display_raw_webhook_data(request.values.to_dict())
    
    if TERMINAL_RAW_OUTPUT:
        print(request.get_data(as_text=True))
        
    # Check if this is a status update webhook
    if request.values.get('MessageStatus') in ['sent', 'delivered']:
        display_status_update(
            request.values.get('MessageStatus'),
            request.values.get('MessageSid')
        )
        return 'OK'
    
    display_incoming_message(
        request.values.get('From'),
        request.values.get('Body')
    )
    
    # Get incoming message and sender's number
    incoming_msg = request.values.get('Body', '').strip()
    sender_number = request.values.get('From', '')
    
    # Skip if there's no actual message content
    if not incoming_msg:
        if not TERMINAL_RAW_OUTPUT:
            print("=== Skipping empty message webhook ===")
        return 'OK'
    
    # Check if sender is authorized
    if not is_authorized(sender_number):
        unauthorized_message = "Unauthorized access. This service is not available for your number."
        message = client.messages.create(
            from_=f"whatsapp:{os.getenv('TWILIO_WHATSAPP_NUMBER')}",
            body=unauthorized_message,
            to=sender_number
        )
        return 'Unauthorized'
    
    # Use the agent to generate a reply
    reply = generate_reply(incoming_msg)
    
    # Send WhatsApp message using Twilio client
    message = client.messages.create(
        from_=f"whatsapp:{os.getenv('TWILIO_WHATSAPP_NUMBER')}",
        body=reply,
        to=sender_number
    )
    
    display_outgoing_response(reply, message.sid)
    return 'OK'

#---------------------------- MAIN EXECUTION ---------------------------------
if __name__ == "__main__":
    app.run(debug=True, port=9000)