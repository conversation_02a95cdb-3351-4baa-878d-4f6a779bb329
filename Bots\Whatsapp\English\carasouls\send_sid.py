# Download the helper library from https://www.twilio.com/docs/python/install
import os
from twilio.rest import Client
import json

# Find your Account SID and Auth Token at twilio.com/console
# and set the environment variables. See http://twil.io/secure
TWILIO_ACCOUNT_SID="**********************************"
TWILIO_AUTH_TOKEN="005d910f85546392a91f58a3878c437c"

review_sid = "HXed90b6b9e2c773c459b6860b44b81292"
tip_sid = "HX2df9ec3a8bfd5cffb7eb9d38951f806b" # New SID for tip

try:
    # Initialize client with correct credentials
    client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

    # --- Block for Review SID ---
    # Define the content variables for the list picker (Review)
    content_vars_review = {
        "1": "Please rate our service:", # Body text for service review
        "2": "Select Rating" # Button text for service review
    }

    message_review = client.messages.create(
        content_sid=review_sid,
        content_variables=json.dumps(content_vars_review),
        to="whatsapp:+************",
        from_="whatsapp:+***********"
    )

    print(f"Review Message SID: {message_review.sid}")

except Exception as e:
    print(f"Error sending review message: {str(e)}")


# --- New Block for Tip SID ---
try:
    # Initialize client again or reuse if scope allows (re-initializing for clarity)
    client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

    # Define the content variables for the list picker (Tip)
    content_vars_tip = {
        "1": "Please select a tip amount:", # Body text for tip
        "2": "Select Tip" # Button text for tip
    }

    message_tip = client.messages.create(
        content_sid=tip_sid, # Use the new tip SID
        content_variables=json.dumps(content_vars_tip), # Pass the tip variables
        to="whatsapp:+************",
        from_="whatsapp:+***********"
    )

    print(f"Tip Message SID: {message_tip.sid}")

except Exception as e:
    print(f"Error sending tip message: {str(e)}")