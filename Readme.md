# 🖥️ FRONTEND DOCUMENTATION 🖥️

## Architecture Overview

The frontend implementation follows a component-based architecture with a reactive DOM manipulation pattern. The system employs a hybrid rendering approach combining server-side template injection with client-side dynamic content generation.

```mermaid
graph TD
    A[Client Browser] --> B[HTML Templates]
    B --> C[CSS Styling Layer]
    B --> D[JavaScript Controllers]
    D --> E[DOM Manipulation]
    D --> F[WebSocket Connection]
    F --> G[Real-time Updates]
    D --> H[File Upload System]
    D --> I[Message Processing]
    I --> J[UI Rendering]
    K[Server API] --> F
    L[Theme System] --> C
    M[Event Handlers] --> D
```

## Technical Components

### Template Engine

The application utilizes a Jinja2-compatible templating system with component inclusion patterns:

- **Component Hierarchy**:
  - `aichat.html`: Primary container template
  - Included components:
    - `imports.html`: External resource dependencies
    - `components/loading.html`: Loading state visualization
    - `sidebar.html`: Navigation sidebar
    - `topright.html`: User controls and settings

### CSS Architecture

Implements a multi-layered styling approach:

1. **Base Layer**: Tailwind CSS framework (v3.x)
2. **Component Layer**: Custom component-specific styles
3. **Theme Layer**: Dynamic theme switching system with class-based selectors
4. **Animation Layer**: CSS transitions and keyframe animations

**Selector Specificity Matrix**:

| Selector Type | Purpose | Example |
|--------------|---------|--------|
| Element | Base styling | `body`, `textarea` |
| Class | Component styling | `.message-bubble`, `.chat-avatar` |
| Compound | State management | `.dark .message-bubble` |
| Pseudo-classes | Interactive states | `.button-svg:hover` |

### JavaScript Event Architecture

```mermaid
sequenceDiagram
    participant User
    participant DOM
    participant EventHandlers
    participant MessageProcessor
    participant UIRenderer
    participant APIClient

    User->>DOM: Input Text/Files
    DOM->>EventHandlers: Trigger Events
    EventHandlers->>MessageProcessor: Process Input
    MessageProcessor->>UIRenderer: Update UI
    MessageProcessor->>APIClient: Send to Backend
    APIClient->>MessageProcessor: Receive Response
    MessageProcessor->>UIRenderer: Render Response
    UIRenderer->>DOM: Update View
```

## Real-time Communication System

The frontend implements a bidirectional communication protocol with the following characteristics:

- **Message Format**: JSON-structured payloads
- **Transport Layer**: WebSockets with fallback to long-polling
- **State Management**: Client-side message queue with optimistic UI updates
- **Reconnection Strategy**: Exponential backoff with jitter

## File Processing Pipeline

1. **Capture**: File input elements with MIME type filtering
2. **Preview Generation**: Client-side blob URL creation
3. **Metadata Extraction**: File type detection and size validation
4. **UI Integration**: Preview component generation and insertion
5. **Upload Queue Management**: Parallel upload with progress tracking
6. **Transmission**: Base64 encoding for image data, FormData for documents

## Responsive Design Implementation

The interface employs a device-agnostic layout system with:

- **Grid System**: CSS Grid with dynamic template areas
- **Breakpoint Strategy**: Mobile-first with progressive enhancement
- **Container Queries**: Element-specific responsive behavior
- **Viewport Adaptations**: Safe area insets for notched devices

## Theme System Architecture

```mermaid
graph LR
    A[Theme Controller] --> B{Theme Detection}
    B -->|System Preference| C[OS Theme]
    B -->|User Selection| D[Stored Preference]
    B -->|Default| E[Light Theme]
    C --> F[Theme Application]
    D --> F
    E --> F
    F --> G[CSS Variables]
    F --> H[Class Toggling]
    F --> I[Image Swapping]
    J[MutationObserver] --> A
```

## Performance Optimizations

- **Resource Loading**:
  - Font preloading with WOFF2 compression
  - Conditional script loading with async/defer attributes
  - Critical CSS inlining

- **Rendering Pipeline**:
  - Throttled DOM updates (16ms intervals)
  - Passive event listeners
  - IntersectionObserver for lazy loading
  - CSS containment for layout isolation

- **Memory Management**:
  - Event delegation pattern
  - DOM recycling for chat messages
  - Garbage collection hints via nullification

## Accessibility Implementation

- **Semantic HTML**: Proper element hierarchy and ARIA roles
- **Keyboard Navigation**: Focus management and tab indexing
- **Screen Reader Support**: Alternative text and aria-live regions
- **Reduced Motion**: Respects prefers-reduced-motion media query

## Security Measures

- **Input Sanitization**: HTML escaping for user-generated content
- **Content Security**: Strict CSP implementation
- **Cross-Site Protections**: CSRF tokens and SameSite cookies
- **File Validation**: Client-side MIME type verification

## Chat Interface Implementation

### Message Processing Architecture

```mermaid
flowchart TB
    A[User Input] --> B{Input Type}
    B -->|Text| C[Text Processor]
    B -->|File| D[File Processor]
    B -->|Image| E[Image Processor]
    C --> F[Message Formatter]
    D --> F
    E --> F
    F --> G[DOM Insertion]
    G --> H[View Update]
    I[AI Response] --> J[Response Parser]
    J --> K[Typing Animation]
    K --> L[Response Formatter]
    L --> G
```

### Message Bubble System

The chat interface implements a sophisticated message bubble system with the following technical characteristics:

- **Bubble Types**:
  - User message bubbles (right-aligned)
  - AI response bubbles (left-aligned)
  - System notification bubbles (centered)
  - File/image attachment bubbles (specialized containers)

- **Rendering Pipeline**:
  1. Message object creation with metadata
  2. Template string generation with dynamic content
  3. DOM fragment creation and optimization
  4. Insertion with requestAnimationFrame scheduling
  5. Post-insertion animations and transitions

- **Performance Considerations**:
  - DOM fragment batching for multiple messages
  - IntersectionObserver for viewport-aware rendering
  - Element recycling for long conversations
  - Virtualized scrolling for memory optimization

### Avatar Management System

Implements a context-aware avatar display system:

- **Avatar Sources**:
  - Local static assets with theme-aware variants
  - Dynamic user avatars with caching strategy
  - Fallback placeholder generation

- **Loading Strategy**:
  - Preloaded critical avatars
  - Lazy-loaded secondary avatars
  - Progressive image loading with blur-up technique

## AI Integration Architecture

### Client-Side AI Processing

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant MessageQueue
    participant APIClient
    participant AIProcessor
    participant ResponseRenderer

    User->>UI: Submit Message
    UI->>MessageQueue: Enqueue Message
    MessageQueue->>APIClient: Send to Backend
    APIClient->>AIProcessor: Process Request
    AIProcessor-->>UI: Show Typing Indicator
    AIProcessor->>ResponseRenderer: Stream Response
    ResponseRenderer->>UI: Render Incrementally
    UI->>User: Display Complete Response
```

### Technical Implementation Details

- **Request Processing**:
  - Message normalization and sanitization
  - Context window management (4K tokens)
  - Attachment handling with MIME type verification
  - Request compression for bandwidth optimization

- **Response Handling**:
  - Streaming response processing with chunked transfer encoding
  - Progressive rendering with typing animation
  - Markdown parsing and syntax highlighting
  - Special token handling for UI actions

- **Error Recovery Mechanisms**:
  - Connection state monitoring with heartbeat protocol
  - Automatic retry with exponential backoff
  - Graceful degradation for offline operation
  - Session recovery after disconnection

## Frontend Testing Infrastructure

### Test Coverage Matrix

| Component | Unit Tests | Integration Tests | E2E Tests |
|-----------|------------|-------------------|----------|
| UI Components | Jest + Testing Library | Storybook | Cypress |
| Event Handlers | Jest with Mocks | Custom Harness | Playwright |
| API Integration | Mock Service Worker | API Simulators | Live Endpoint Tests |
| Rendering Engine | JSDOM | Headless Browser | Cross-browser Matrix |

### Automated Testing Pipeline

```mermaid
graph LR
    A[Code Changes] --> B[Linting]
    B --> C[Unit Tests]
    C --> D[Integration Tests]
    D --> E[Visual Regression]
    E --> F[Performance Tests]
    F --> G[Accessibility Tests]
    G --> H[Security Scans]
    H --> I[Deployment]
```

## Browser Compatibility

The frontend is engineered for cross-browser compatibility with the following technical specifications:

- **Rendering Engines**:
  - Blink (Chrome, Edge, Opera): Full support
  - Gecko (Firefox): Full support
  - WebKit (Safari): Full support with polyfills

- **JavaScript Compatibility**:
  - ECMAScript 2020 with selective transpilation
  - Polyfill strategy for legacy browsers
  - Feature detection with graceful degradation

- **CSS Implementation**:
  - Progressive enhancement with @supports queries
  - Vendor prefix automation via PostCSS
  - Fallback strategies for CSS Grid and Flexbox

# 📊 ANALYTICS IMPLEMENTATION

## Architecture Overview

The analytics system implements a multi-layered data visualization architecture with real-time theme adaptation and dynamic data rendering capabilities. The system employs a modular component approach with isolated rendering contexts and optimized memory management.

```mermaid
graph TD
    A[Data Sources] --> B[Data Processing Layer]
    B --> C[Visualization Rendering Engine]
    C --> D[DOM Integration]
    E[Theme System] --> F[Rendering Adaptation]
    F --> C
    G[User Interaction] --> H[Event Processing]
    H --> I[Chart Reconfiguration]
    I --> C
    J[WebSocket Updates] -.-> B
    K[MutationObserver] --> L[Theme Detection]
    L --> F
```

## Technical Components

### Chart Rendering System

The analytics implementation utilizes multiple specialized rendering engines:

- **AmCharts 5**: Vector-based rendering for complex visualizations
  - Custom theme integration with animation capabilities
  - Memory-optimized rendering with element recycling
  - SVG-based path generation with dynamic attributes

- **Chart.js**: Canvas-based rendering for performance-critical visualizations
  - Hardware-accelerated rendering with devicePixelRatio optimization
  - Custom tooltip implementation with DOM-based overlays
  - Responsive layout system with breakpoint detection

- **Custom SVG Rendering**: Direct SVG manipulation for specialized visualizations
  - Path-based data representation with cubic Bézier curves
  - CSS animation integration with keyframe definitions
  - Dynamic attribute manipulation via JavaScript

### Data Processing Pipeline

```mermaid
sequenceDiagram
    participant Client
    participant DataProcessor
    participant ChartConfiguration
    participant RenderingEngine
    participant DOMIntegration

    Client->>DataProcessor: Raw Data
    DataProcessor->>DataProcessor: Normalize & Transform
    DataProcessor->>DataProcessor: Calculate Aggregates
    DataProcessor->>ChartConfiguration: Processed Data
    ChartConfiguration->>ChartConfiguration: Apply Visual Mapping
    ChartConfiguration->>ChartConfiguration: Set Theme Parameters
    ChartConfiguration->>RenderingEngine: Configuration Object
    RenderingEngine->>RenderingEngine: Create Visual Elements
    RenderingEngine->>RenderingEngine: Apply Animations
    RenderingEngine->>DOMIntegration: Rendered Output
    DOMIntegration->>Client: Visual Feedback
```

## Visualization Components

### Semi-Circle Progress Indicator

Implements a sophisticated SVG-based progress visualization with the following technical characteristics:

- **Rendering Technique**: SVG path-based with precise arc calculations
- **Animation Strategy**: CSS keyframe animation with stroke-dasharray manipulation
- **Theme Adaptation**: Dynamic stroke color adjustment via MutationObserver
- **Performance Optimization**: Minimal DOM updates with attribute-only changes

**Technical Implementation:**
```javascript
@keyframes fillAnimation {
    0% { stroke-dasharray: 0 251; }
    100% { stroke-dasharray: 188 251; }
}

// Dynamic color adaptation based on theme
function updateSemiCircleColors() {
    const isPureBlack = document.body.classList.contains('pure-black');
    foregroundSemiCircle.setAttribute('stroke',
        isPureBlack ? '#fafafa' : '#151519');
}
```

### Pie Chart System

Implements a data-driven sectional visualization with the following characteristics:

- **Rendering Engine**: AmCharts 5 with custom theme integration
- **Optimization Techniques**:
  - Container padding elimination for precise sizing
  - Custom slice rendering with zero-width strokes
  - Selective label positioning with collision detection

- **Theme Integration**:
  - Dynamic color palette switching
  - Label and tick color adaptation
  - Background transparency adjustment

### Bar Chart Implementation

Implements a high-performance time-series visualization:

- **Rendering Context**: Canvas-based with 2x devicePixelRatio
- **Optimization Techniques**:
  - Minimal grid rendering with selective axis display
  - Custom tooltip implementation for performance
  - Optimized bar width calculation with percentage-based sizing

- **Responsive Behavior**:
  - Layout recalculation on container resize
  - Dynamic padding adjustment
  - Maintained aspect ratio with flexible dimensions

## Theme System Integration

```mermaid
flowchart TD
    A[MutationObserver] --> B{Class Change Detection}
    B -->|body.class modified| C[Theme Identification]
    C -->|Pure Black Theme| D[Dark Mode Parameters]
    C -->|Light Theme| E[Light Mode Parameters]
    D --> F[Update Chart Colors]
    E --> F
    F --> G[Update SVG Elements]
    F --> H[Update Canvas Charts]
    F --> I[Update DOM Elements]
    J[Window Resize] --> K[Recalculate Dimensions]
    K --> L[Redraw Visualizations]
```

### Technical Implementation

- **Observer Pattern**: MutationObserver implementation for real-time theme detection
- **Attribute-Based Updates**: Direct attribute manipulation for minimal repaints
- **Selective Redrawing**: Component-specific update strategy to minimize performance impact

```javascript
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'attributes' &&
            mutation.attributeName === 'class') {
            updateChartColors();
            initializeTotalTipsChart();
            updateSemiCircleColors();
        }
    });
});

observer.observe(document.body, { attributes: true });
```

## Data Visualization Techniques

### Language Analytics Implementation

Implements a grid-based multi-metric visualization system:

- **Data Structure**: Hierarchical object model with nested metrics
- **Rendering Strategy**: Dynamic DOM generation with template literals
- **Visual Elements**:
  - Percentage-based indicators
  - SVG mini-graphs with gradient fills
  - Flag-based visual identifiers

- **Performance Considerations**:
  - Virtualized scrolling for large datasets
  - Lazy-loaded flag images
  - Optimized SVG path generation

### Revenue Visualization

Implements a composite visualization with multiple data representation techniques:

- **Primary Visualization**: Semi-circular gauge with percentage completion
- **Secondary Elements**: Categorical breakdown with color-coded segments
- **Data Integration**: Real-time API data with transformation layer

## Performance Optimization

- **Rendering Strategies**:
  - Deferred initialization with setTimeout
  - Batch DOM updates
  - Hardware acceleration with transform properties
  - Selective animation disabling for low-power devices

- **Memory Management**:
  - Chart instance recycling
  - Explicit garbage collection hints
  - Event listener cleanup
  - DOM element reuse

- **Network Optimization**:
  - Data caching strategies
  - Incremental updates
  - Compressed data formats
  - Request batching

## Responsive Design Implementation

- **Container-Based Sizing**: Percentage and viewport-relative dimensions
- **Breakpoint System**: Media query integration with JavaScript detection
- **Adaptive Layout**: Grid template adjustment based on available space
- **Component Behavior**:
  - Chart resize with maintained aspect ratios
  - Label density adjustment
  - Tooltip positioning adaptation

## Cross-Browser Compatibility

- **Rendering Consistency**:
  - Polyfills for SVG support
  - Canvas fallback strategies
  - CSS property normalization

- **Event Handling**:
  - Pointer events with mouse fallback
  - Touch event normalization
  - Passive event listeners for scrolling

# 📊 DASHBOARD ARCHITECTURE

## System Overview

The dashboard implements a real-time data visualization framework with dynamic SVG rendering and time-series data processing. The system employs a modular component architecture with isolated metric tracking and responsive layout adaptation.

```mermaid
graph TD
    A[Data Sources] --> B[Data Normalization Layer]
    B --> C[Metric Processing Engine]
    C --> D[Visualization Renderer]
    D --> E[DOM Integration]
    F[User Interaction] --> G[Event Handler]
    G --> H[Date Range Processor]
    H --> I[Data Filtering]
    I --> C
    J[AJAX Polling] --> K[Data Refresh]
    K --> B
    L[SVG Path Generator] --> D
```

## Technical Components

### Metric Visualization System

The dashboard implements a sophisticated SVG-based visualization system with the following characteristics:

- **Rendering Technique**: Dynamic SVG path generation with gradient fills
- **Data Processing**: Time-series normalization with min-max scaling
- **Visual Elements**:
  - Line paths with 2px stroke width
  - Gradient-filled areas with variable opacity
  - Responsive viewBox adaptation

**Technical Implementation:**
```javascript
function generateSVGPath(dataArray) {
    // Normalize the data to fit within the SVG viewBox (0-100)
    const normalizedData = normalizeData(dataArray);

    // Build the path data string
    let pathData = "M0 " + (100 - normalizedData[0]);

    // Calculate points evenly distributed across the x-axis
    const xStep = 100 / (normalizedData.length - 1);
    for (let i = 1; i < normalizedData.length; i++) {
        const x = xStep * i;
        const y = 100 - normalizedData[i]; // Invert y since SVG y=0 is at the top
        pathData += " L" + x + " " + y;
    }

    return pathData;
}
```

### Data Normalization Pipeline

```mermaid
sequenceDiagram
    participant RawData
    participant Processor
    participant Normalizer
    participant PathGenerator
    participant SVGRenderer

    RawData->>Processor: Input Metrics
    Processor->>Processor: Extract Numerical Values
    Processor->>Processor: Add to Time Series
    Processor->>Normalizer: Data Array
    Normalizer->>Normalizer: Find Min/Max Values
    Normalizer->>Normalizer: Scale to 10-90 Range
    Normalizer->>PathGenerator: Normalized Array
    PathGenerator->>PathGenerator: Generate Path String
    PathGenerator->>SVGRenderer: SVG Path Data
    SVGRenderer->>SVGRenderer: Update Line Path
    SVGRenderer->>SVGRenderer: Update Area Path
```

## Calendar System Implementation

Implements a sophisticated date range selection system with the following technical characteristics:

- **DOM Generation**: Dynamic calendar grid with month-aware rendering
- **State Management**: Multi-mode selection with start/end date tracking
- **Interaction Model**: Click-based date selection with range visualization
- **Constraint Handling**: Date boundary enforcement with visual feedback

**Technical Implementation:**
```javascript
function updateCalendar() {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Clear and rebuild calendar grid
    calendarDiv.innerHTML = '';

    // Add day headers
    ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].forEach(day => {
        const dayElem = document.createElement('div');
        dayElem.textContent = day;
        dayElem.classList.add('text-gray-500', 'font-medium', 'text-sm');
        calendarDiv.appendChild(dayElem);
    });

    // Calculate first day of month and total days
    const firstDay = new Date(year, month, 1).getDay();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    // Add empty cells for days before the first of the month
    for (let i = 0; i < firstDay; i++) {
        calendarDiv.appendChild(document.createElement('div'));
    }

    // Generate day cells with appropriate states and event handlers
    for (let day = 1; day <= daysInMonth; day++) {
        // Implementation details...
    }
}
```

## Metric Card System

Implements a modular metric visualization framework:

- **Card Architecture**: Self-contained components with isolated rendering contexts
- **Data Binding**: Attribute-based metric identification with selective updates
- **Visual Elements**:
  - Numerical displays with formatted values
  - Trend visualization with SVG mini-charts
  - Contextual metadata with supporting text

- **Update Mechanism**:
  - Selective DOM updates for changed values only
  - Path attribute manipulation without re-rendering
  - CSS transition-based animations for smooth updates

## Data Fetching Architecture

```mermaid
flowchart TD
    A[Initialization] --> B[Generate Initial Data]
    B --> C[Set Polling Interval]
    C --> D{Fetch Data}
    D -->|Success| E[Parse Response]
    D -->|Error| F[Use Fallback Patterns]
    E --> G[Update DOM Elements]
    E --> H[Update SVG Paths]
    F --> H
    G --> I[Apply Transitions]
    H --> I
    I --> J[Wait for Interval]
    J --> D
```

### Technical Implementation

- **Polling Strategy**: Interval-based AJAX requests with error handling
- **Data Processing**: JSON parsing with type conversion and formatting
- **History Management**: Fixed-length arrays with FIFO data rotation
- **Fallback Mechanism**: Predefined patterns for visualization continuity

```javascript
window.fetchData = function() {
    // Make AJAX request
    $.ajax({
        url: '/fetch-data',
        method: 'GET',
        success: function(data) {
            if (data.length > 0) {
                // Extract and process metrics
                const totalReservations = parseFloat(data[0]['Total reservations']) || 0;
                const totalRevenue = parseFloat(data[0]['Total revenue'].replace(/[^0-9.]/g, '')) || 0;
                // Additional metrics...

                // Update DOM elements
                document.querySelector('#total-revenue').textContent = '£' + totalRevenue;
                // Additional updates...

                // Update visualization charts
                updateChart('revenue', totalRevenue);
                // Additional chart updates...
            }
        },
        error: function(error) {
            console.error('Error fetching data:', error);

            // Use fallback patterns on error
            Object.keys(fixedPatterns).forEach(metric => {
                updateChart(metric, fixedPatterns[metric][fixedPatterns[metric].length - 1]);
            });
        }
    });
};
```

## Performance Optimization

- **Rendering Efficiency**:
  - Attribute-only updates for SVG paths
  - Class toggling for state changes
  - Minimal DOM manipulation with element reuse
  - requestAnimationFrame for visual updates

- **Memory Management**:
  - Fixed-size data arrays with controlled growth
  - Element recycling for calendar generation
  - Event delegation for date selection
  - Explicit cleanup of unused references

- **Network Optimization**:
  - Consolidated AJAX requests
  - Polling rate limitation (60-second intervals)
  - Error state recovery with graceful degradation
  - Cached fallback patterns for offline operation

## Responsive Design Implementation

- **Layout Architecture**:
  - CSS Grid with responsive column configuration
  - Viewport-relative sizing for consistent scaling
  - Breakpoint-based component reorganization
  - Flexible SVG viewBox with preserved aspect ratios

- **Interaction Adaptation**:
  - Touch-friendly date selection targets
  - Keyboard navigation support
  - Focus management for accessibility
  - State preservation across viewport changes

## Animation System

- **Transition Types**:
  - CSS-based transitions for UI elements
  - Class-toggling for state changes
  - Transform-based animations for performance
  - Opacity transitions for visibility changes

- **Technical Implementation**:
  - Hardware-accelerated properties (transform, opacity)
  - Cubic-bezier timing functions for natural movement
  - Duration-controlled transitions (300ms standard)
  - State-based animation triggering

```css
.calendarDropdown {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: scale(0.95);
    opacity: 0;
    visibility: hidden;
}

.calendarDropdown.visible {
    transform: scale(1);
    opacity: 1;
    visibility: visible;
}
```

## Cross-Browser Compatibility

- **Feature Detection**:
  - Graceful degradation for older browsers
  - Polyfill implementation for missing features
  - CSS fallback strategies with @supports queries

- **Rendering Consistency**:
  - Vendor prefix handling for CSS properties
  - SVG rendering normalization
  - Font loading optimization with system font fallbacks
  - Box model consistency with border-box sizing

# 💬 LIVE CHAT IMPLEMENTATION

## Architecture Overview

The Live Chat system implements a real-time communication platform with bidirectional message exchange, dynamic user interface rendering, and multilingual support. The system employs an optimized component architecture with memory-efficient DOM manipulation and intelligent caching strategies.

```mermaid
graph TD
    A[User Interface Layer] --> B[Chat Management System]
    B --> C[Message Processing Engine]
    C --> D[DOM Rendering Pipeline]
    E[Translation Service] --> F[Language Processing]
    F --> C
    G[User Data Store] --> H[User Profile Management]
    H --> B
    I[WebSocket/Polling] --> J[Real-time Updates]
    J --> B
    K[Gender Detection API] --> L[Avatar Selection]
    L --> D
    M[Search System] --> N[User Filtering]
    N --> A
```

## Technical Components

### Real-time Communication System

The Live Chat implementation utilizes an optimized polling architecture with the following characteristics:

- **Data Synchronization**: Intelligent differential updates with JSON comparison
- **Concurrency Control**: Request throttling with in-progress flag management
- **Error Handling**: Graceful degradation with automatic recovery
- **Performance Optimization**: Cached data structures with selective DOM updates

**Technical Implementation:**
```javascript
function fetchChatUpdates() {
    if (!selectedUser || fetchInProgress) return;
    fetchInProgress = true;

    fetch(`/get_chat_data?userId=${selectedUser}`)
        .then(response => response.json())
        .then(data => {
            // Only update if there's a change in messages for the current user
            const currentMessages = chatData?.chats?.[selectedUser] || [];
            const newMessages = data?.chats?.[selectedUser] || [];

            if (JSON.stringify(currentMessages) !== JSON.stringify(newMessages)) {
                chatData = data;
                loadChatHistory(selectedUser, false);
                if (currentLanguage !== 'original') {
                    translateChat(currentLanguage);
                }
            }
            fetchInProgress = false;
        })
        .catch(error => {
            console.error('Error fetching chat updates:', error);
            fetchInProgress = false;
        });
}
```

### Message Rendering Pipeline

```mermaid
sequenceDiagram
    participant UI as User Interface
    participant Processor as Message Processor
    participant DOM as DOM Manager
    participant Cache as Cache System
    participant API as Backend API

    UI->>Processor: New Message
    Processor->>DOM: Create Message Element
    DOM->>UI: Optimistic Update
    Processor->>Cache: Store Original Text
    Processor->>API: Send Message Request
    API-->>Processor: Confirmation
    Processor->>DOM: Update Message Status
    DOM->>UI: Apply Animation
    UI->>UI: Scroll to Bottom
```

## User Interface Components

### Chat Interface System

Implements a sophisticated dual-panel interface with the following technical characteristics:

- **Layout Architecture**: CSS Grid with custom column template
- **Component Isolation**: Modular structure with independent rendering contexts
- **State Management**: Visibility toggling with conditional display properties
- **Animation System**: CSS keyframes with transform and opacity transitions

**Technical Implementation:**
```css
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.message-container.left {
    animation: slideInLeft 0.3s ease-out;
}

.message-container.right {
    animation: slideInRight 0.3s ease-out;
}
```

### User List Implementation

Implements an efficient user directory with the following characteristics:

- **DOM Management**: Differential updates with element recycling
- **Performance Optimization**: Fragment-based batch rendering
- **Memory Efficiency**: Map-based element tracking
- **Scroll Position Preservation**: Position restoration after DOM updates

**Technical Implementation:**
```javascript
function updateUserList() {
    // Create a map of existing users in the DOM for quick lookup
    const existingUsers = new Map();
    userList.querySelectorAll('li[data-user]').forEach(item => {
        existingUsers.set(item.getAttribute('data-user'), item);
    });

    // Keep track of elements to add/update in order
    const updatedElements = [];

    // Process each user in the data
    chatData.users.forEach(user => {
        const userId = user.id.toString();
        const existingElement = existingUsers.get(userId);

        if (existingElement) {
            // Update only necessary parts of existing element
            // ...
            updatedElements.push(existingElement);
            existingUsers.delete(userId);
        } else {
            // Create new user element if it doesn't exist
            // ...
            updatedElements.push(li);
        }
    });

    // If order has changed or elements were added/removed, rebuild the list
    if (updatedElements.length !== userList.children.length ||
        existingUsers.size > 0 ||
        !Array.from(userList.children).every((child, i) => child === updatedElements[i])) {

        // Remove any elements that weren't in the updated data
        existingUsers.forEach(element => element.remove());

        // Create a fragment to update the DOM in one operation
        const fragment = document.createDocumentFragment();
        updatedElements.forEach(el => fragment.appendChild(el));

        // Clear and append in one operation to avoid flickering
        const scrollPos = userList.scrollTop; // Save scroll position
        userList.innerHTML = '';
        userList.appendChild(fragment);
        userList.scrollTop = scrollPos; // Restore scroll position
    }
}
```

## Translation System

Implements a multilingual communication platform with the following technical characteristics:

- **Translation API Integration**: Google Translate API with asynchronous processing
- **Caching Strategy**: Local storage persistence with FIFO eviction policy
- **Memory Management**: Maximum entry limitation with automatic cleanup
- **Original Text Preservation**: Message ID-based original content tracking

**Technical Implementation:**
```javascript
async function translateChat(language) {
    const messageElements = chatContainer.querySelectorAll('.message-bubble p');

    for (let element of messageElements) {
        const messageBubble = element.parentElement;
        const messageId = messageBubble.getAttribute('data-message-id');
        const originalText = originalMessages[messageId] || element.textContent;

        if (language === 'original') {
            element.textContent = originalText;
        } else {
            if (translationCache[`${originalText}-${language}`]) {
                element.textContent = translationCache[`${originalText}-${language}`];
            } else {
                try {
                    const translatedText = await translateMessage(originalText, language);
                    element.textContent = translatedText;
                    addToCache(originalText, language, translatedText);
                } catch (error) {
                    console.error('Translation error:', error);
                }
            }
        }
    }
}
```

## Gender Detection System

Implements an intelligent avatar selection mechanism:

- **API Integration**: External gender detection service with name-based analysis
- **Caching Strategy**: In-memory caching with first-name keys
- **Performance Optimization**: Promise-based resolved cache returns
- **Fallback Mechanism**: Default avatar assignment for unknown gender

**Technical Implementation:**
```javascript
function getGender(fullName) {
    const firstName = fullName.split(' ')[0];
    // If exists in cache, return a resolved promise
    if (genderCache[firstName]) {
        return Promise.resolve(genderCache[firstName]);
    }
    return fetch(`/guess_gender?name=${encodeURIComponent(fullName)}`)
        .then(response => response.json())
        .then(data => {
            const gender = data.gender || 'unknown';
            // Cache the result
            genderCache[firstName] = gender;
            return gender;
        })
        .catch(err => {
            console.error("Gender API error", err);
            return 'unknown';
        });
}
```

## Search System Implementation

Implements a real-time user filtering mechanism:

- **Event Handling**: Input event with debounced processing
- **Search Algorithm**: Case-insensitive substring matching
- **Multi-field Search**: Name and phone number combined matching
- **DOM Manipulation**: Display property toggling for filtered elements

**Technical Implementation:**
```javascript
function filterUserList(query) {
    const userItems = userList.querySelectorAll('li[data-user]');

    userItems.forEach(item => {
        // Get the user name from the item
        const nameElement = item.querySelector('.font-semibold');
        const phoneElement = item.querySelector('.text-xs.font-medium');

        if (nameElement) {
            const userName = nameElement.textContent.toLowerCase();
            const userPhone = phoneElement ? phoneElement.textContent.toLowerCase() : '';

            // Check if the name contains the search query
            if (userName.includes(query) || userPhone.includes(query)) {
                item.style.display = ''; // Show the item
            } else {
                item.style.display = 'none'; // Hide the item
            }
        }
    });
}
```

## Performance Optimization

- **DOM Manipulation Strategies**:
  - Document fragment batching for bulk updates
  - Element recycling with selective property updates
  - Attribute-only modifications to minimize reflows
  - Scroll position preservation during updates

- **Memory Management**:
  - Fixed-size caches with FIFO eviction
  - Explicit reference cleanup
  - Local storage persistence with size limitations
  - Selective data structure updates

- **Network Optimization**:
  - Request throttling with in-progress flags
  - Differential data updates
  - JSON stringification for change detection
  - Optimistic UI updates with backend confirmation

## Responsive Design Implementation

- **Layout Architecture**:
  - CSS Grid with responsive column configuration
  - Flexbox for component alignment and distribution
  - Viewport-relative sizing for consistent scaling
  - Container queries for component-specific adaptation

- **Mobile Optimization**:
  - Touch-friendly interaction targets
  - Reduced animation complexity
  - Adjusted component dimensions
  - Simplified UI for smaller screens

```css
@media (max-width: 768px) {
    #emoji-container {
        width: 250px;
        /* Adjust width for smaller screens */
        max-height: 150px;
        /* Adjust height for smaller screens */
        bottom: 60px;
        /* Adjust positioning if necessary */
    }

    #emoji-container .emoji-mart {
        max-height: 150px !important;
    }

    #emoji-container .emoji-mart-scroll {
        max-height: 130px !important;
    }

    .emoji-button {
        font-size: 20px;
        /* Slightly smaller on mobile */
    }
}
```

## Theme System Integration

- **Theme Detection**: Body class observation with MutationObserver
- **Dynamic Asset Loading**: Theme-aware image path selection
- **CSS Variables**: Custom property integration for theme colors
- **Selective Styling**: Theme-specific class selectors

```css
/* Message bubble theme-based styling */
body.light .message-bubble {
    background-color: #18181b !important;
    color: white !important;
}

/* If you want to keep original styling in dark mode, add this */
body:not(.light) .message-bubble {
    background-color: #e5e7eb;
    color: black;
}
```

## Accessibility Implementation

- **Semantic HTML**: Proper element hierarchy with ARIA roles
- **Keyboard Navigation**: Focus management and tab indexing
- **Screen Reader Support**: Alternative text and ARIA attributes
- **Visual Indicators**: State-based styling for interactive elements

# 📃 TASK MANAGEMENT SYSTEM

## Architecture Overview

The Task Management System implements a comprehensive data-driven interface with real-time filtering capabilities, dynamic content generation, and theme-aware component rendering. The system employs a modular architecture with optimized DOM manipulation and intelligent caching strategies.

```mermaid
graph TD
    A[Data Source] --> B[Data Processing Layer]
    B --> C[DOM Rendering Engine]
    D[Filter System] --> E[Query Processing]
    E --> F[DOM Visibility Control]
    G[Category Management] --> H[Filter Application]
    H --> B
    I[Price Range System] --> J[Range Filtering]
    J --> B
    K[Theme System] --> L[Component Adaptation]
    L --> C
    M[User Interaction] --> N[Event Handlers]
    N --> O[UI State Management]
    O --> C
```

## Technical Components

### Data Fetching Architecture

The Task Management implementation utilizes an optimized data retrieval system with the following characteristics:

- **Request Strategy**: Fetch API with JSON response parsing
- **Error Handling**: Graceful degradation with user feedback
- **Data Processing**: Client-side sorting and transformation
- **State Management**: Global data store with filtered view generation

**Technical Implementation:**
```javascript
function fetchTaskList() {
    fetch('/tasklist')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Sort data by Task ID
            data.sort((a, b) => {
                return a["Task ID"].localeCompare(b["Task ID"]);
            });

            populateTable(data);
            updateCategoryFilters(data);
            updatePriceFilters(data);
        })
        .catch(error => {
            console.error('Error fetching task list:', error);
            document.getElementById('order-table-body').innerHTML =
                '<tr><td colspan="10" class="text-center p-4">Failed to load data. Please try again later.</td></tr>';
        });
}
```

### Table Rendering Pipeline

```mermaid
sequenceDiagram
    participant DataSource as Data Source
    participant Processor as Data Processor
    participant Renderer as DOM Renderer
    participant UI as User Interface

    DataSource->>Processor: Raw Task Data
    Processor->>Processor: Sort by Task ID
    Processor->>Processor: Parse JSON Fields
    Processor->>Processor: Format Phone Numbers
    Processor->>Processor: Process Item Quantities
    Processor->>Renderer: Processed Data
    Renderer->>Renderer: Create Table Rows
    Renderer->>Renderer: Generate Cell Content
    Renderer->>Renderer: Apply Theme Classes
    Renderer->>UI: Render Table
    Renderer->>UI: Update Category Filters
    Renderer->>UI: Update Price Filters
```

## Filtering System Implementation

Implements a sophisticated multi-criteria filtering mechanism with the following technical characteristics:

- **Text-based Filtering**: Real-time input event handling with debounced processing
- **Category Filtering**: Dynamic category detection with count aggregation
- **Price Range Filtering**: Adaptive range generation based on data distribution
- **Combined Filtering**: Composite filter application with preserved state

**Technical Implementation:**
```javascript
// Real-time text filtering
filterInput.addEventListener('input', function () {
    const searchText = this.value.toLowerCase();
    const rows = tableBody.querySelectorAll('tr');

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let shouldShow = false;

        // Check each cell except the first (checkbox) and last (actions)
        for (let i = 1; i < cells.length - 1; i++) {
            const cellText = cells[i].textContent.toLowerCase();
            if (cellText.includes(searchText)) {
                shouldShow = true;
                break;
            }
        }

        row.style.display = shouldShow ? '' : 'none';
    });
});

// Category filtering implementation
function updateCategoryFilters(data) {
    // Get unique categories
    const categories = [...new Set(data.map(item => item.Category))].sort();
    const categoryDropdown = document.querySelector('.uk-drop.uk-dropdown:nth-of-type(1) .uk-dropdown-nav');

    if (categoryDropdown) {
        // Clear existing category items (keep the divider)
        const divider = categoryDropdown.querySelector('.uk-nav-divider');
        categoryDropdown.innerHTML = '';
        if (divider) categoryDropdown.appendChild(divider.cloneNode(true));

        // Add new category items
        categories.forEach(category => {
            const count = data.filter(item => item.Category === category).length;
            // Implementation details...
        });
    }
}
```

### Dynamic Category System

Implements an intelligent category management system:

- **Category Detection**: Automatic extraction from data source
- **Count Aggregation**: Real-time category frequency calculation
- **Visual Representation**: Dynamic pill generation with removal capability
- **Filter Application**: Category-to-data mapping with alias support

**Technical Implementation:**
```javascript
// Category mapping system
const categoryMap = {
    'Food': ['Food', 'Food & Beverage'],
    'Beverage': ['Beverage', 'Food & Beverage'],
    'Spa': ['Spa & Massage'],
    'Massage': ['Spa & Massage'],
    'Room Booking': ['Room Bookings']
};

const matchCategories = categoryMap[categoryText] || [];
const filteredData = window.allTaskData.filter(item =>
    matchCategories.includes(item.Category));
```

### Price Range System

Implements an adaptive price filtering mechanism:

- **Range Generation**: Dynamic range creation based on data distribution
- **Count Calculation**: Per-range item frequency computation
- **Visual Representation**: Range display with item counts
- **Filter Application**: Range-based numerical filtering

**Technical Implementation:**
```javascript
function updatePriceFilters(data) {
    // Create price ranges based on the data
    const prices = data.map(item => Number(item["Total Price"] || 0));
    const maxPrice = Math.max(...prices);

    let priceRanges = [
        { label: '€0-€50', filter: price => price <= 50 },
        { label: '€51-€100', filter: price => price > 50 && price <= 100 },
        { label: '€101-€150', filter: price => price > 100 && price <= 150 }
    ];

    // Add higher range if there are items with higher prices
    if (maxPrice > 150) {
        priceRanges.push({ label: '€151+', filter: price => price > 150 });
    }

    // Implementation details for rendering...
}
```

## Data Processing Techniques

### JSON Field Parsing

Implements a robust JSON field handling system:

- **Error Handling**: Try-catch blocks for malformed JSON
- **Default Values**: Fallback to empty objects for missing fields
- **Type Conversion**: Automatic type coercion for numerical values
- **Field Extraction**: Selective property access with optional chaining

**Technical Implementation:**
```javascript
// Parse the pricing and quantity JSON strings if they exist
let pricing = {};
let quantities = {};

try {
    if (order.Pricing) {
        pricing = JSON.parse(order.Pricing);
    }
    if (order.Quantity) {
        quantities = JSON.parse(order.Quantity);
    }
} catch (e) {
    console.error('Error parsing JSON:', e);
}

// Create the items display with quantities
const items = order.Items ? order.Items.split(', ') : [];
const itemsWithQuantity = items.map(item => {
    const qty = quantities[item] ? ` (${quantities[item]})` : '';
    return `${item}${qty}`;
}).join(', ');
```

### Phone Number Formatting

Implements an intelligent phone number formatting system:

- **Format Detection**: Length-based format identification
- **Segment Extraction**: Substring-based number segmentation
- **Format Application**: Region-specific formatting rules
- **Fallback Handling**: Original value preservation for invalid formats

**Technical Implementation:**
```javascript
function formatPhoneNumber(phoneNumber) {
    // Format: +91 XXXX XXX XXX (assuming Indian number format)
    if (phoneNumber.length >= 12) {
        return `+${phoneNumber.substring(0, 2)} ${phoneNumber.substring(2, 6)} ${phoneNumber.substring(6, 9)} ${phoneNumber.substring(9)}`;
    }
    return phoneNumber;
}
```

## UI Component Architecture

### Metric Card System

Implements a data-driven metric visualization framework:

- **Layout Structure**: CSS Grid with responsive column configuration
- **Visual Elements**: Icon integration with semantic meaning
- **Data Representation**: Numerical display with trend indicators
- **Theme Adaptation**: Dynamic color scheme application

### Table Component Implementation

Implements a sophisticated data table with the following characteristics:

- **Header Architecture**: Sticky positioning with z-index management
- **Row Generation**: Dynamic content creation with template literals
- **Cell Formatting**: Content-specific rendering strategies
- **Selection System**: Checkbox-based row selection with "Select All" capability

**Technical Implementation:**
```css
/* Enhanced fixed header solution */
.order-table-container {
    height: calc(100vh - 250px);
    min-height: 400px;
    overflow-y: auto;
    position: relative;
}

/* Make header fully opaque with proper shadow for separation */
.order-table-container thead {
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.order-table-container thead tr {
    background-color: var(--background);
}

/* Ensure each header cell has proper background */
.order-table-container thead th {
    background-color: var(--background);
    position: relative;
    z-index: 11;
}
```

## Theme System Integration

```mermaid
flowchart TD
    A[Theme Classes] --> B{Theme Detection}
    B -->|Light Theme| C[Light Mode Styles]
    B -->|Dark Theme| D[Dark Mode Styles]
    C --> E[Component Styling]
    D --> E
    E --> F[Table Header]
    E --> G[Table Rows]
    E --> H[Form Elements]
    E --> I[Cards]
    E --> J[Buttons]
    K[CSS Variables] --> L[Dynamic Properties]
    L --> E
```

### Technical Implementation

- **Class-Based Theming**: Body class detection for theme identification
- **CSS Variable Integration**: Custom property usage for theme colors
- **Component-Specific Styling**: Targeted selectors for theme application
- **Border Handling**: Theme-specific border color management

```css
/* Specific theme colors */
.light .order-table-container thead tr,
.light .order-table-container thead th {
    background-color: white;
}

.pure-black .order-table-container thead tr,
.pure-black .order-table-container thead th {
    background-color: #09090b;
}

.light .uk-table-divider>tr:not(:first-child),
.uk-table-divider>:not(:first-child)>tr,
.uk-table-divider>:first-child>tr:not(:first-child) {
    border-color: #e5e7eb;
}

.pure-black .uk-table-divider>tr:not(:first-child),
.pure-black .uk-table-divider>:not(:first-child)>tr,
.pure-black .uk-table-divider>:first-child>tr:not(:first-child) {
    border-color: #27272a;
}
```

## Performance Optimization

- **DOM Manipulation Strategies**:
  - Fragment-based batch rendering
  - Selective element updates
  - Event delegation for dynamic elements
  - Optimized selector usage

- **Memory Management**:
  - Element recycling for dropdown items
  - Explicit reference cleanup
  - Minimal state tracking
  - Efficient data structures (Sets, Maps)

- **Rendering Efficiency**:
  - CSS containment for layout isolation
  - Minimal reflow operations
  - Hardware-accelerated animations
  - Optimized scrolling with fixed headers

## Responsive Design Implementation

- **Layout Architecture**:
  - CSS Grid with responsive column configuration
  - Viewport-relative sizing for consistent scaling
  - Flexible table layout with controlled overflow
  - Mobile-optimized interaction targets

- **Visual Adaptation**:
  - Truncated content with ellipsis
  - Responsive spacing system
  - Flexible width constraints
  - Preserved functionality across viewports

```css
/* Add fixed height container styles */
.order-table-container {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: none; /* For Firefox */
}

.order-table-container::-webkit-scrollbar {
    display: none; /* For Chrome, Safari and Opera */
}

/* Style for icon images */
.platform-icon,
.language-icon {
    width: 20px;
    height: 20px;
    object-fit: contain;
}
```

## Accessibility Implementation

- **Semantic Structure**: Proper table markup with appropriate roles
- **Keyboard Navigation**: Focusable interactive elements
- **Screen Reader Support**: Descriptive text and ARIA attributes
- **Visual Indicators**: State-based styling for interactive elements

# 💰 SALES ANALYTICS SYSTEM

## Architecture Overview

The Sales Analytics System implements a comprehensive data visualization platform with real-time chart rendering, interactive category analysis, and platform-specific performance metrics. The system employs a modular component architecture with optimized SVG generation and intelligent data processing algorithms.

```mermaid
graph TD
    A[Data Sources] --> B[Data Processing Layer]
    B --> C[Visualization Engine]
    C --> D[Chart Rendering]
    C --> E[Category Analysis]
    C --> F[Platform Metrics]
    G[User Interaction] --> H[Event Handlers]
    H --> I[Chart Updates]
    J[Theme System] --> K[Visual Adaptation]
    K --> C
    L[Time Series Data] --> M[Temporal Analysis]
    M --> B
    N[Export System] --> O[Data Extraction]
    O --> P[File Generation]
```

## Technical Components

### Chart Rendering System

The Sales Analytics implementation utilizes multiple specialized rendering engines:

- **D3.js**: Vector-based SVG rendering with precise data binding
- **ApexCharts**: Canvas-based rendering for complex visualizations
- **Custom SVG Generation**: Programmatic path creation with dynamic attributes

**Technical Implementation:**
```javascript
const svg = d3.select('#chart')
    .append('svg')
    .attr('width', '100%')
    .attr('height', height + margin.top + margin.bottom)
    .attr('viewBox', `0 0 ${width + margin.left + margin.right} ${height + margin.top + margin.bottom}`)
    .attr('preserveAspectRatio', 'none')
    .append('g')
    .attr('transform', `translate(${margin.left},${margin.top})`);

// Create fixed x-axis with consistent date intervals
const x = d3.scaleTime()
    .domain([startDate, endDate])
    .range([0, width]);

// Generate fixed tick values for more days to show all 30 days
const xTickValues = [];
for (let i = 0; i <= 29; i += 1) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);
    xTickValues.push(date);
}
```

### Data Processing Pipeline

```mermaid
sequenceDiagram
    participant DataSource as Data Source
    participant Generator as Data Generator
    participant Processor as Data Processor
    participant Normalizer as Data Normalizer
    participant Renderer as Chart Renderer
    participant UI as User Interface

    DataSource->>Generator: Request Data
    Generator->>Generator: Generate Time Series
    Generator->>Generator: Apply Patterns
    Generator->>Processor: Raw Data
    Processor->>Processor: Sort Data
    Processor->>Normalizer: Process Data Points
    Normalizer->>Normalizer: Scale to Range
    Normalizer->>Normalizer: Apply Curve
    Normalizer->>Renderer: Normalized Data
    Renderer->>Renderer: Generate SVG Path
    Renderer->>Renderer: Create Visual Elements
    Renderer->>UI: Render Chart
```

## Interactive Visualization Components

### Time Series Chart System

Implements a sophisticated line chart with the following technical characteristics:

- **Data Binding**: Direct DOM-to-data mapping with D3.js
- **Interaction Model**: Hover-based data point highlighting
- **Tooltip System**: Dynamic positioning with content generation
- **Axis Management**: Time-based x-axis with custom tick formatting

**Technical Implementation:**
```javascript
const line = d3.line()
    .x(d => x(d.date))
    .y(d => y(d.value))
    .curve(d3.curveMonotoneX);

svg.append('path')
    .datum(currentData)
    .attr('class', 'ggchart-line')
    .attr('stroke', '#16a34a') // Default green color
    .attr('d', line);

const hoverLine = svg.append('line')
    .attr('class', 'ggchart-hover-line')
    .attr('y1', 0)
    .attr('y2', height);

const dots = svg.selectAll('.ggchart-dot')
    .data(currentData)
    .enter()
    .append('circle')
    .attr('class', 'ggchart-dot')
    .attr('cx', d => x(d.date))
    .attr('cy', d => y(d.value))
    .attr('r', 6)
    .attr('fill', '#16a34a');
```

### Category Analysis System

Implements a comprehensive category visualization framework:

- **Progress Bar**: Proportional segment rendering with color coding
- **Card System**: Interactive category cards with hover effects
- **Data Representation**: Percentage-based distribution visualization
- **Visual Feedback**: Hover-based glow effects with color matching

**Technical Implementation:**
```css
/* Glow effects for category cards */
.main-custom-hover-effect:hover {
    box-shadow: 0 0 15px 5px;
}
/* Food */
.main-custom-hover-effect:hover [class*="bg-emerald-500"] ~ dd,
.main-custom-hover-effect:hover .bg-emerald-500 {
    box-shadow: 0 0 15px 5px rgba(16, 185, 129, 0.5);
}
/* Beverages */
.main-custom-hover-effect:hover [class*="bg-amber-500"] ~ dd,
.main-custom-hover-effect:hover .bg-amber-500 {
    box-shadow: 0 0 15px 5px rgba(245, 158, 11, 0.5);
}
```

### Platform Metrics System

Implements a platform-specific performance visualization:

- **Progress Bar**: Multi-segment rendering with platform-specific colors
- **Card System**: Platform cards with icon integration
- **Data Representation**: Percentage and absolute value display
- **Visual Feedback**: Interactive hover effects with platform-specific colors

## Data Visualization Techniques

### SVG Path Generation

Implements a sophisticated path generation system:

- **Data Normalization**: Min-max scaling with range adjustment
- **Curve Fitting**: Monotone cubic interpolation for smooth lines
- **Point Calculation**: Precise coordinate mapping with domain transformation
- **Path String Generation**: Optimized SVG path command construction

**Technical Implementation:**
```javascript
function generateSVGPath(dataArray) {
    // Normalize the data to fit within the SVG viewBox (0-100)
    const normalizedData = normalizeData(dataArray);

    // Build the path data string
    let pathData = "M0 " + (100 - normalizedData[0]);

    // Calculate points evenly distributed across the x-axis
    const xStep = 100 / (normalizedData.length - 1);
    for (let i = 1; i < normalizedData.length; i++) {
        const x = xStep * i;
        const y = 100 - normalizedData[i]; // Invert y since SVG y=0 is at the top
        pathData += " L" + x + " " + y;
    }

    return pathData;
}

function normalizeData(dataArray) {
    // Find min and max values
    const min = Math.min(...dataArray);
    const max = Math.max(...dataArray);

    if (min === max) {
        // If all values are the same, create a flat line at 50%
        return dataArray.map(() => 50);
    }

    // Scale to 10-90 range to ensure visibility within the viewBox
    return dataArray.map(value => {
        const normalized = ((value - min) / (max - min)) * 80 + 10;
        return normalized;
    });
}
```

### Interactive Tooltip System

Implements an intelligent tooltip positioning system:

- **Position Calculation**: Viewport-aware placement with edge detection
- **Content Generation**: Dynamic data formatting with date and value display
- **Visual Feedback**: Synchronized highlight with data point and hover line
- **Event Handling**: Mouse movement tracking with bisector-based data selection

**Technical Implementation:**
```javascript
function mousemove(event) {
    const [mouseX, mouseY] = d3.pointer(event);
    const bisectDate = d3.bisector(d => d.date).left;

    const x0 = x.invert(mouseX);
    const i = bisectDate(currentData, x0, 1);
    const d0 = currentData[i - 1];
    const d1 = currentData[i];
    // Select the closer data point
    const d = x0 - d0.date > d1.date - x0 ? d1 : d0;
    const xPos = x(d.date);
    const yPos = y(d.value);
    updateTooltipAndHighlights(d, xPos, yPos, mouseY);
}

function updateTooltipAndHighlights(d, xPos, yPos) {
    // Set position of the hover line to exactly match the data point
    hoverLine
        .attr('transform', `translate(${xPos},0)`)
        .style('opacity', 1);

    // Hide all dots first
    dots.style('opacity', 0);

    // Show only the selected dot
    svg.selectAll('.ggchart-dot')
        .filter(dt => dt.date.getTime() === d.date.getTime())
        .style('opacity', 1);

    // Position the tooltip with clear offset from the point
    const tooltipWidth = tooltip.node().offsetWidth;
    const containerWidth = container.clientWidth;

    // Calculate ideal position (right of the point by default)
    let leftPos = xPos + margin.left + 15;

    // If tooltip would go off the right edge, place it to the left of the point
    if (leftPos + tooltipWidth > containerWidth - 10) {
        leftPos = xPos + margin.left - tooltipWidth - 15;
    }

    // Ensure tooltip stays within container bounds
    leftPos = Math.max(10, Math.min(containerWidth - tooltipWidth - 10, leftPos));

    // Position tooltip and update content
    tooltip
        .style('opacity', 1)
        .style('left', `${leftPos}px`)
        .style('top', `${margin.top + 10}px`);

    const formatDate = d3.timeFormat('%b %d, %Y');
    tooltip.select('.ggchart-tooltip-date').text(formatDate(d.date));
    tooltip.select('.ggchart-tooltip-value').text(d.value.toLocaleString());
}
```

## Data Generation System

Implements a sophisticated synthetic data generation system:

- **Time Series Generation**: Date-based sequential data creation
- **Pattern Application**: Sinusoidal patterns with varying frequencies
- **Random Variation**: Controlled randomness with baseline values
- **Type-Specific Adjustments**: Platform-specific data characteristics

**Technical Implementation:**
```javascript
function generateData(type) {
    const data = [];
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 29);

    for (let i = 0; i < 30; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);
        let value = 200 + Math.random() * 150;
        if (type === 'mobile') {
            value = 220 + Math.random() * 180;
        }
        value += Math.sin(i / 7) * (type === 'mobile' ? 70 : 50);
        value += Math.sin(i / 30) * (type === 'mobile' ? 100 : 80);
        if (i % 12 === 0) {
            value += Math.random() * (type === 'mobile' ? 120 : 100);
        }
        data.push({
            date: date,
            value: Math.round(value)
        });
    }
    return data;
}
```

## Chart Update System

Implements a dynamic chart switching mechanism:

- **Data Binding**: D3.js enter-update-exit pattern for smooth transitions
- **Color Management**: Source-specific color application
- **Animation System**: Transition-based smooth updates
- **State Management**: Current data tracking with source identification

**Technical Implementation:**
```javascript
function updateChart(newData, color) {
    currentData = newData;
    y.domain([0, d3.max(newData, d => d.value) * 1.1]);

    svg.select('.ggchart-line')
        .datum(newData)
        .attr('stroke', color)
        .transition()
        .duration(300)
        .attr('d', line);

    const dots = svg.selectAll('.ggchart-dot')
        .data(newData);

    dots.exit().remove();

    dots.enter()
        .append('circle')
        .attr('class', 'ggchart-dot')
        .attr('r', 6)
        .merge(dots)
        .attr('cx', d => x(d.date))
        .attr('cy', d => y(d.value))
        .attr('fill', color);
}
```

## Performance Optimization

- **Rendering Strategies**:
  - SVG attribute manipulation for minimal reflows
  - Batch DOM updates with document fragments
  - Viewport-based rendering with viewBox
  - Selective element updates

- **Memory Management**:
  - Fixed-size data arrays with FIFO rotation
  - Element recycling with enter-update-exit pattern
  - Explicit reference cleanup
  - Event handler debouncing

- **Interaction Optimization**:
  - Throttled event handlers
  - Bisector-based data point selection
  - Cached DOM references
  - Optimized tooltip positioning

## Responsive Design Implementation

- **Layout Architecture**:
  - CSS Grid with responsive column configuration
  - Percentage-based sizing for fluid layouts
  - Viewport-relative dimensions
  - Preserved aspect ratios with viewBox

- **Chart Adaptation**:
  - Responsive SVG with preserveAspectRatio
  - Dynamic viewBox calculation
  - Debounced resize handlers
  - Recalculated scales on viewport changes

```javascript
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
```

## Theme System Integration

- **Class-Based Theming**: Body class detection for theme identification
- **Color Management**: Theme-specific color palettes
- **Icon Adaptation**: Conditional icon rendering based on theme
- **Border Handling**: Theme-specific border styling

```javascript
// Conditional rendering for platform column with theme-aware class
const platformContent = customer.Platform.toLowerCase() === 'whatsapp'
    ? `<img src="/static/icons/black-whatsapp-icon.png" alt="WhatsApp" class="w-6 h-6 inline-block whatsapp-theme-icon">`
    : customer.Platform;
```

## Data Export System

- **Export Triggers**: Button-based export initiation
- **Format Selection**: CSV/Excel format generation
- **Data Extraction**: Comprehensive data collection from visualizations
- **File Generation**: Client-side file creation and download

# 🏨 PMS ANALYTICS SYSTEM

## Architecture Overview

The PMS Analytics System implements a comprehensive hotel management analytics platform with multi-source data integration, interactive visualization components, and real-time performance metrics. The system employs a modular component architecture with specialized chart rendering and intelligent data processing algorithms.

```mermaid
graph TD
    A[PMS Data Sources] --> B[Data Integration Layer]
    B --> C[Analytics Engine]
    C --> D[Metric Visualization]
    C --> E[Geographic Analysis]
    C --> F[Revenue Tracking]
    G[User Interaction] --> H[Filter System]
    H --> I[Data Refinement]
    I --> C
    J[Theme System] --> K[Visual Adaptation]
    K --> C
    L[Booking Platforms] --> M[Platform Analytics]
    M --> B
    N[Guest Feedback] --> O[Sentiment Analysis]
    O --> B
```

## Technical Components

### Metric Card System

The PMS Analytics implementation utilizes a sophisticated metric visualization framework:

- **Card Architecture**: Modular card components with consistent styling
- **Trend Indicators**: Dynamic trend visualization with directional icons
- **Value Formatting**: Currency and numeric value formatting with locale support
- **Visual Hierarchy**: Information prioritization with typographic scaling

**Technical Implementation:**
```html
<div class="card rounded-lg border shadow-sm">
    <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
        <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Lifetime Revenue</h3>
        <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round" class="lucide lucide-badge-euro">
            <path d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z" />
            <path d="M7 12h5" />
            <path d="M15 9.4a4 4 0 1 0 0 5.2" />
        </svg>
    </div>
    <div class="p-6 relative">
        <div class="text-2xl font-bold">£12,893</div>
        <p class="text-xs text-gray-500 dark:text-gray-400">Total lifetime revenue</p>
        <div class="absolute bottom-7 right-4 flex items-center text-xs text-green-600 border border-green-500 bg-transparent px-1.5 py-0.5 rounded-md">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-arrow-up-narrow-wide mr-0.5">
                <path d="m3 8 4-4 4 4" />
                <path d="M7 4v16" />
                <path d="M11 12h4" />
                <path d="M11 16h7" />
                <path d="M11 20h10" />
            </svg>
            8.5%
        </div>
    </div>
</div>
```

### Data Processing Pipeline

```mermaid
sequenceDiagram
    participant DataSource as PMS Data Source
    participant Processor as Data Processor
    participant Analyzer as Data Analyzer
    participant Visualizer as Visualization Engine
    participant UI as User Interface

    DataSource->>Processor: Raw PMS Data
    Processor->>Processor: Format Data
    Processor->>Processor: Normalize Values
    Processor->>Analyzer: Processed Data
    Analyzer->>Analyzer: Calculate Metrics
    Analyzer->>Analyzer: Generate Insights
    Analyzer->>Visualizer: Analysis Results
    Visualizer->>Visualizer: Create Visual Components
    Visualizer->>Visualizer: Apply Theme
    Visualizer->>UI: Render Dashboard
```

## Interactive Visualization Components

### Donut Chart System

Implements a sophisticated donut chart with the following technical characteristics:

- **SVG-Based Rendering**: Direct SVG path generation with precise control
- **Animation System**: Keyframe-based path animation with easing functions
- **Interaction Model**: Hover-based visual feedback with brightness adjustment
- **Shadow Effects**: Filter-based shadow rendering for depth perception

**Technical Implementation:**
```html
<div class="relative w-11/12 mx-auto h-[212px] flex items-center justify-center mb-6">
    <svg viewBox="0 0 200 100" class="w-full h-full">
        <!-- Grey styling circle -->
        <path d="M10 100 A 90 90 0 0 1 190 100" fill="none" stroke="#F3F4F6"
            stroke-width="6" />

        <!-- Main half circles -->
        <path d="M20 100 A 80 80 0 0 1 180 100" fill="none" stroke="#9ca3af"
            stroke-width="5"
            class="transition-all duration-300 hover:filter hover:brightness-105"
            filter="url(#shadow)" />
        <path d="M20 100 A 80 80 0 0 1 180 100" fill="none" stroke="#151519"
            stroke-width="5" stroke-dasharray="188 251"
            class="transition-all duration-300 hover:filter hover:brightness-105"
            filter="url(#shadow)" />

        <!-- Define shadow filter -->
        <defs>
            <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                <feDropShadow dx="0" dy="1" stdDeviation="2" flood-opacity="0.1" />
            </filter>
        </defs>
    </svg>
    <div class="absolute inset-0 flex flex-col items-center justify-center"
        style="padding-top: 70px;">
        <div class="rounded-full p-1.5 mb-3 border border-gray-300">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-blend">
                <circle cx="9" cy="9" r="7" />
                <circle cx="15" cy="15" r="7" />
            </svg>
        </div>
        <span class="text-lg font-semibold">2,324</span>
        <p class="text-xs mt-1">Total tips</p>
    </div>
</div>
```

### Pie Chart System

Implements a comprehensive pie chart visualization framework:

- **Data Binding**: Direct data-to-visual mapping with category/value pairs
- **Color Management**: Category-specific color assignment with theme awareness
- **Label System**: Positioned labels with automatic text wrapping
- **Interaction Model**: Hover-based tooltip display with formatted values

**Technical Implementation:**
```javascript
// Initialize the Chart once amCharts are ready
am4core.ready(async function () {
    // Apply the animated theme
    am4core.useTheme(am4themes_animated);

    // Create a Pie Chart instance in the chartdiv
    var chart = am4core.create("chartdiv", am4charts.PieChart);
    chart.radius = am4core.percent(60); // Adjust the size as needed

    // Fetch data and update the chart
    const tppData = await fetchTPPData();
    if (tppData) {
        updatePieChart(chart, tppData);
    }

    // Add and configure the Pie Series
    var pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "sales";
    pieSeries.dataFields.category = "product";
    pieSeries.slices.template.propertyFields.fill = "color";

    // Create a Donut Chart by setting an inner radius
    chart.innerRadius = am4core.percent(30);

    // Customize slice appearance
    pieSeries.slices.template.padding = 1;
    pieSeries.slices.template.cornerRadius = 5;
    pieSeries.slices.template.fillOpacity = 1;
    pieSeries.slices.template.strokeWidth = 0;
    pieSeries.slices.template.stroke = am4core.color("#ffffff");
});
```

### Geographic Map System

Implements a sophisticated geographic visualization system:

- **Vector-Based Rendering**: GeoJSON-based map rendering with country polygons
- **Interaction Control**: Configurable zoom and pan restrictions
- **Data Binding**: Country-specific data mapping with tooltips
- **Visual Differentiation**: Highlighted regions with distinct styling

**Technical Implementation:**
```javascript
am5.ready(function () {
    var root = am5.Root.new("europeMap");

    root.setThemes([
        am5themes_Animated.new(root)
    ]);

    root._logo.dispose();

    var chart = root.container.children.push(am5map.MapChart.new(root, {
        panX: "none",
        panY: "none",
        wheelX: "none",  // Disable mouse wheel zoom
        wheelY: "none",  // Disable mouse wheel zoom
        pinchZoom: false, // Disable pinch zoom
        projection: am5map.geoMercator(),
        homeGeoPoint: { latitude: 54, longitude: 15 },
        homeZoomLevel: 2
    }));

    // Highlighted countries series
    var polygonSeries = chart.series.push(am5map.MapPolygonSeries.new(root, {
        geoJSON: am5geodata_region_world_europeLow,
        include: ["NL", "GB", "FR", "DE", "IT", "ES", "SE"]
    }));

    polygonSeries.mapPolygons.template.setAll({
        tooltipText: "{name}: {guests}", // Updated tooltip format
        interactive: true,
        fill: am5.color(0xd1d5db),
        stroke: am5.color(0x000000),
        strokeWidth: 1,
        strokeOpacity: 0.4
    });
});
```

## Data Visualization Techniques

### Progress Bar System

Implements a sophisticated progress bar visualization system:

- **Segment Generation**: Dynamic segment creation with proportional widths
- **Color Coding**: Category-specific color assignment with semantic meaning
- **Legend Integration**: Synchronized legend generation with color matching
- **Data Calculation**: Percentage-based width calculation with total normalization

**Technical Implementation:**
```javascript
document.addEventListener('DOMContentLoaded', function () {
    // All progress items, including Total
    const progressData = [
        { name: 'Positive', value: 130, color: 'bg-[#3b82f6]' }, // Blue
        { name: 'Negative', value: 170, color: 'bg-[#22c55e]' }, // Green
        { name: 'Neutral', value: 220, color: 'bg-[#f59e0b]' }, // Orange
    ];

    // Filter out the "Total" item so it doesn't appear as a bar
    const filteredData = progressData.filter(item => item.name !== 'Total');

    // Calculate total
    const total = progressData.reduce((sum, p) => sum + p.value, 0);

    const container = document.getElementById('myGeneratedProgressBar');
    const legendContainer = document.getElementById('progressBarLegend');

    container.innerHTML = '';
    legendContainer.innerHTML = '';
    legendContainer.className = 'flex flex-row flex-wrap justify-center mt-2 text-[12px]';

    // Create bars without legends
    filteredData.forEach(item => {
        const barWidth = ((item.value / total) * 100).toFixed(2);

        // Bar
        const barDiv = document.createElement('div');
        barDiv.className = `h-full ${item.color}`;
        barDiv.style.width = barWidth + '%';
        container.appendChild(barDiv);
    });
});
```

### Platform Analytics System

Implements a comprehensive platform-specific analytics framework:

- **Card System**: Platform-specific cards with consistent styling
- **Icon Integration**: Platform-specific icons with consistent sizing
- **Data Representation**: Dual-metric display with booking count and revenue
- **Visual Feedback**: Hover-based animation with elevation and glow effects

**Technical Implementation:**
```css
.hover-float {
    position: relative;
    transition: all 0.3s ease-out;
    cursor: pointer;
}

.hover-float:hover {
    transform: translateY(-6px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border-color: rgba(99, 102, 241, 0.4);
}

/* Prepare for animation */
.hover-float:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    opacity: 0;
    border-radius: 8px;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
    transition: opacity 0.3s ease;
}

.hover-float:hover:before {
    opacity: 1;
}

/* Add a slight scale effect */
.hover-float:hover {
    transform: translateY(-6px) scale(1.01);
}
```

## Chart Animation System

Implements a sophisticated animation framework for visualizations:

- **Keyframe Animation**: CSS-based keyframe definition for smooth transitions
- **Stroke Dasharray Manipulation**: SVG path animation with stroke-dasharray
- **Timing Functions**: Easing functions for natural motion
- **Delayed Initialization**: Staggered animation start for visual hierarchy

**Technical Implementation:**
```css
@keyframes fillAnimation {
    0% {
        stroke-dasharray: 0 251;
    }

    100% {
        stroke-dasharray: 188 251;
    }
}

svg path:nth-child(3) {
    animation: fillAnimation 1.5s ease-out forwards;
}
```

## Theme System Integration

- **Observer Pattern**: MutationObserver for theme change detection
- **Dynamic Color Adaptation**: Theme-specific color palette application
- **Chart Reconfiguration**: Real-time chart updates on theme changes
- **Conditional Styling**: Theme-based conditional class application

**Technical Implementation:**
```javascript
// Function to update chart colors based on the current theme
function updateChartColors() {
    var body = document.body;
    var isDarkTheme = body.classList.contains('pure-black') ||
        body.classList.contains('dark-gray') ||
        body.classList.contains('navy-blue') ||
        body.classList.contains('cool-blue') ||
        body.classList.contains('deep-burgundy') ||
        body.classList.contains('charcoal');

    // Update text color for labels and ticks based on the theme
    pieSeries.labels.template.fill = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
    pieSeries.ticks.template.stroke = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
}

// Initial color update
updateChartColors();

// Observe theme changes to update chart colors dynamically
var observer = new MutationObserver(function (mutations) {
    mutations.forEach(function (mutation) {
        if (mutation.type === "attributes" && mutation.attributeName === "class") {
            updateChartColors();
        }
    });
});

observer.observe(document.body, {
    attributes: true
});
```

## Data Fetching System

Implements an intelligent data retrieval mechanism:

- **Request Management**: Concurrent request handling with abort capability
- **Error Handling**: Graceful error management with user feedback
- **Data Transformation**: Response parsing and value formatting
- **DOM Updates**: Targeted element updates with formatted values

**Technical Implementation:**
```javascript
function fetchData() {
    if (currentRequest && currentRequest.readyState !== 4) {
        currentRequest.abort();
    }

    currentRequest = $.ajax({
        url: '/fetch-data',
        method: 'GET',
        success: function (data) {
            if (data.length > 0) {
                const totalReservations = data[0]['Total reservations'];
                const totalRevenue = data[0]['Total revenue'];
                const conversationOutsideBusinessHours = data[0]['Conversation Outside Business Hours'];
                const queriesLeftToAnswer = data[0]['Queries Left to Answer'];

                document.querySelector('#total-revenue').textContent = '£' + totalRevenue;
                document.querySelector('#total-reservations').textContent = totalReservations;
                document.querySelector('#conversation-outside-business-hours').textContent = '£' + conversationOutsideBusinessHours;
                document.querySelector('#queries-left-to-answer').textContent = queriesLeftToAnswer;
            }
        },
        error: function (error) {
            console.error('Error fetching data:', error);
        }
    });
}
```

## Performance Optimization

- **Rendering Strategies**:
  - SVG attribute manipulation for minimal reflows
  - Viewport-based rendering with viewBox
  - Selective element updates
  - Hardware-accelerated animations

- **Memory Management**:
  - Request cancellation for concurrent operations
  - Element recycling with document fragments
  - Explicit reference cleanup
  - Event handler debouncing

- **Interaction Optimization**:
  - Throttled event handlers
  - Cached DOM references
  - Optimized tooltip positioning
  - Minimal DOM manipulation

## Responsive Design Implementation

- **Layout Architecture**:
  - CSS Grid with responsive column configuration
  - Percentage-based sizing for fluid layouts
  - Viewport-relative dimensions
  - Preserved aspect ratios with viewBox

- **Chart Adaptation**:
  - Responsive SVG with preserveAspectRatio
  - Dynamic viewBox calculation
  - Recalculated dimensions on viewport changes
  - Mobile-optimized interaction targets

# 👤 USER MANAGEMENT SYSTEM

## Architecture Overview

The User Management System implements a comprehensive user data visualization platform with dynamic side panel integration, real-time filtering, and interactive user profiles. The system employs a modular component architecture with optimized DOM manipulation and intelligent state management.

```mermaid
graph TD
    A[User Data Source] --> B[Data Processing Layer]
    B --> C[DOM Rendering Engine]
    D[Search System] --> E[Query Processing]
    E --> F[DOM Visibility Control]
    G[Side Panel System] --> H[User Detail Rendering]
    I[Sales Data Source] --> J[Sales Processing]
    J --> K[User-Sales Association]
    K --> H
    L[Activity Log System] --> M[Timeline Generation]
    M --> H
    N[Theme System] --> O[Component Adaptation]
    O --> C
    P[User Interaction] --> Q[Event Handlers]
    Q --> R[UI State Management]
    R --> C
```

## Technical Components

### Grid Layout System

The User Management implementation utilizes a sophisticated grid-based layout system:

- **Dynamic Column Configuration**: State-based grid template columns
- **Transition Management**: Smooth animation between layout states
- **Overflow Control**: Strategic overflow handling for nested components
- **Responsive Adaptation**: Viewport-based layout adjustments

**Technical Implementation:**
```css
.grid {
    display: grid;
    grid-template-columns: 280px 1fr;
    transition: grid-template-columns 0.3s ease-in-out;
    overflow: hidden !important;
}

.grid.side-panel-active {
    grid-template-columns: 280px 1fr 25%;
}

.grid.collapsed.side-panel-active {
    grid-template-columns: 60px 1fr 25%;
}

@media (min-width: 890px) {
    .side-panel {
        position: relative;
        transform: translateX(100%);
    }
}
```

### Data Processing Pipeline

```mermaid
sequenceDiagram
    participant DataSource as Data Sources
    participant Processor as Data Processor
    participant Renderer as DOM Renderer
    participant UI as User Interface
    participant Panel as Side Panel

    DataSource->>Processor: User Data
    DataSource->>Processor: Sales Data
    Processor->>Processor: Enrich User Data
    Processor->>Processor: Associate Sales
    Processor->>Renderer: Processed Data
    Renderer->>Renderer: Generate Table Rows
    Renderer->>Renderer: Apply Theme Classes
    Renderer->>UI: Render User List
    UI->>Panel: User Selection
    Panel->>Panel: Filter User Sales
    Panel->>Panel: Generate Activity Log
    Panel->>UI: Render User Details
```

## Interactive Component Systems

### Side Panel System

Implements a sophisticated sliding panel with the following technical characteristics:

- **Transform-Based Animation**: CSS transform for hardware-accelerated animation
- **State Management**: Class-based state tracking with transition timing
- **Event Propagation Control**: Strategic event stopping for nested interactions
- **Keyboard Accessibility**: Escape key handling for panel dismissal

**Technical Implementation:**
```css
.side-panel {
    transform: translateX(100%);
    transition: transform 0.3s ease-out;
    display: none;
    z-index: 1000;
    overflow-y: auto;
    padding: 32px;
    padding-left: 5px;
    padding-right: 5px;
    border-radius: 0 12px 12px 0;
}

.side-panel.active {
    transform: translateX(0);
    display: block;
}
```

**JavaScript Implementation:**
```javascript
function showUserDetails(user) {
    const mainGrid = document.getElementById('mainGrid');
    const panel = document.getElementById('userDetailPanel');
    const panelContent = document.getElementById('panelContent');

    // Filter sales data for this user
    const userSales = salesData.filter(sale =>
        sale.guest_phone_or_id.toString() === user.guest_phone_or_id.toString()
    );

    // Generate panel content
    panelContent.innerHTML = `...`;

    // Initialize view selector
    initializeViewSelector();

    // Activate panel with slight delay for smooth animation
    setTimeout(() => {
        mainGrid.classList.add('side-panel-active');
        panel.classList.add('active');
    }, 10);
}

function hideUserDetails() {
    const mainGrid = document.getElementById('mainGrid');
    const panel = document.getElementById('userDetailPanel');
    panel.classList.remove('active');
    mainGrid.classList.remove('side-panel-active');
    document.body.style.overflow = '';
}
```

### User List System

Implements a comprehensive user list with the following technical characteristics:

- **Dynamic Row Generation**: Template-based row creation with event binding
- **Data Binding**: Direct DOM-to-data mapping with sanitized content
- **Platform Detection**: Conditional rendering based on platform type
- **Search Integration**: Real-time filtering with multi-field matching

**Technical Implementation:**
```javascript
function updateUserList(filteredUsers = users) {
    const userListContainer = document.getElementById('user-list');
    userListContainer.innerHTML = '';

    filteredUsers.forEach((user, index) => {
        const userElement = document.createElement('tr');
        userElement.className = 'card border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted user-item cursor-pointer relative';
        userElement.innerHTML = `
            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">
                <div class="flex items-center gap-4 ml-3">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user">
                        <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <div>
                        <div class="font-semibold">${sanitizeHTML(user.guest_name)}</div>
                        <div class="text-sm text-gray-500">${sanitizeHTML(user.guest_phone_or_id.toString())}</div>
                    </div>
                </div>
            </td>
            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">
                ${user.guest_platform.toLowerCase() === 'whatsapp' ?
                    `<img src="../static/icons/black-whatsapp-icon.png"
                        alt="WhatsApp"
                        title="WhatsApp"
                        class="whatsapp-theme-icon"
                        style="width: 24px; height: 24px;"
                        loading="lazy">`
                    : sanitizeHTML(user.guest_platform)}
            </td>
            <!-- Additional cells... -->
        `;
        userElement.addEventListener('click', (e) => {
            if (!e.target.closest('.admin-required')) {
                showUserDetails(user);
            }
        });
        userListContainer.appendChild(userElement);
    });
}
```

### Activity Timeline System

Implements a sophisticated activity visualization framework:

- **Timeline Visualization**: Vertical timeline with connected activity nodes
- **Animation System**: Staggered animations with sequential timing
- **Visual Hierarchy**: Priority-based styling for recent activities
- **Icon Integration**: Activity-specific icon rendering

**Technical Implementation:**
```css
/* Timeline line animation */
.activity-timeline .absolute.left-\[15px\] {
    height: 0;
    animation: growDown 0.8s ease-out forwards;
}

@keyframes growDown {
    from {
        height: 0;
    }

    to {
        height: 100%;
    }
}

/* Activity item animations */
.activity-item {
    opacity: 0;
    animation: fadeInUp 0.5s ease-out forwards;
    position: relative;
    transition: all 0.2s ease;
}

.activity-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Staggered animation delay for activity items */
.mb-6:nth-child(1) .activity-item {
    animation-delay: 0.1s;
}

.mb-6:nth-child(2) .activity-item {
    animation-delay: 0.2s;
}

.mb-6:nth-child(3) .activity-item {
    animation-delay: 0.3s;
}
```

**HTML Implementation:**
```html
<div class="activity-timeline relative">
    <!-- Line positioned to align with center of icons -->
    <div class="absolute left-[15px] top-1 h-full w-0.5 bg-gradient-to-b from-blue-500 to-gray-300 opacity-70"></div>
    ${activityLog.map((activity, index) => `
        <div class="mb-6 pl-8 relative ">
            <!-- Icon centered with the timeline -->
            <div class="absolute left-0 top-1 w-4 h-4 ${index === 0
                    ? 'bg-blue-500 ring-4 ring-blue-100 dark:ring-blue-900/30'
                    : 'bg-gray-400 ring-2 ring-gray-100 dark:ring-gray-800/30'}
                rounded-full shadow-sm flex items-center justify-center z-10">
                <div class="activity-icon text-white" style="transform: scale(0.7);">
                    ${getActivityIcon(activity.type)}
                </div>
            </div>
            <div class="activity-item border border-opacity-50 rounded-lg p-3 shadow-sm hover:shadow-md transition-all ${index === 0 ? 'border-blue-200 bg-blue-50/50 dark:bg-blue-900/10 dark:border-blue-900/30' : 'border-gray-200 bg-gray-50/50 dark:bg-gray-800/10 dark:border-gray-700/30'
                }">
                <p class="text-gray-400 text-xs mb-1 flex items-center">
                    <span class="font-medium ${index === 0 ? 'text-blue-500 dark:text-blue-400' : ''}">${getActivityTitle(activity.type)}</span>
                    <span class="mx-1">•</span>
                    <span>${sanitizeHTML(activity.time)}</span>
                </p>
                <p class="${index === 0 ? '' : ''} text-sm font-medium">${sanitizeHTML(activity.details)}</p>
            </div>
        </div>
    `).join('')}
</div>
```

## Data Visualization Techniques

### User Analytics Dashboard

```mermaid
graph TD
    A[User Data] --> B[Data Processing]
    B --> C[Visualization Components]
    C --> D[User Profile Card]
    C --> E[Sales History Visualization]
    C --> F[Activity Timeline]
    C --> G[Platform Distribution]
    H[User Interaction] --> I[View Selection]
    I --> J[Content Switching]
    K[Theme System] --> L[Visual Adaptation]
    L --> C
```

### User Metrics Visualization

Implements a comprehensive user metrics visualization system:

- **Data Aggregation**: User-specific metrics calculation and display
- **Visual Representation**: Card-based metric presentation with icons
- **Comparative Analysis**: Historical data comparison with trend indicators
- **Platform Distribution**: Visual breakdown of user platform preferences

**Technical Implementation:**
```javascript
function generateUserMetrics(userData, salesData) {
    // Calculate key metrics
    const totalUsers = userData.length;
    const activeUsers = userData.filter(user => user.active).length;
    const totalSales = salesData.reduce((sum, sale) => sum + parseFloat(sale.sale_price), 0);
    const averageSaleValue = totalSales / salesData.length;

    // Platform distribution calculation
    const platforms = {};
    userData.forEach(user => {
        const platform = user.guest_platform || 'Unknown';
        platforms[platform] = (platforms[platform] || 0) + 1;
    });

    // Generate platform distribution visualization
    const platformColors = {
        'WhatsApp': '#25D366',
        'Web': '#4285F4',
        'Mobile App': '#FF5722',
        'Unknown': '#9E9E9E'
    };

    let platformHTML = '';
    Object.entries(platforms).forEach(([platform, count]) => {
        const percentage = ((count / totalUsers) * 100).toFixed(1);
        const color = platformColors[platform] || '#9E9E9E';
        platformHTML += `
            <div class="platform-metric">
                <div class="platform-name">${platform}</div>
                <div class="platform-bar-container">
                    <div class="platform-bar" style="width: ${percentage}%; background-color: ${color};"></div>
                </div>
                <div class="platform-percentage">${percentage}%</div>
            </div>
        `;
    });

    // Update DOM with visualizations
    document.getElementById('user-metrics-container').innerHTML = `
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-icon user-icon"></div>
                <div class="metric-value">${totalUsers}</div>
                <div class="metric-label">Total Users</div>
            </div>
            <div class="metric-card">
                <div class="metric-icon active-icon"></div>
                <div class="metric-value">${activeUsers}</div>
                <div class="metric-label">Active Users</div>
            </div>
            <div class="metric-card">
                <div class="metric-icon sales-icon"></div>
                <div class="metric-value">£${totalSales.toFixed(2)}</div>
                <div class="metric-label">Total Sales</div>
            </div>
            <div class="metric-card">
                <div class="metric-icon average-icon"></div>
                <div class="metric-value">£${averageSaleValue.toFixed(2)}</div>
                <div class="metric-label">Avg. Sale Value</div>
            </div>
        </div>
        <div class="platform-distribution">
            <h3>Platform Distribution</h3>
            ${platformHTML}
        </div>
    `;
}
```

### User Activity Heatmap

Implements a sophisticated activity visualization system:

- **Temporal Analysis**: Time-based activity aggregation and display
- **Color Intensity Mapping**: Activity frequency represented through color intensity
- **Day/Hour Visualization**: Two-dimensional grid showing activity patterns
- **Interactive Elements**: Hover-based detailed information display

```mermaid
graph LR
    A[User Activity Data] --> B[Temporal Aggregation]
    B --> C[Activity Matrix Generation]
    C --> D[Color Intensity Calculation]
    D --> E[Heatmap Rendering]
    E --> F[Cell Generation]
    F --> G[Color Application]
    H[User Interaction] --> I[Cell Hover]
    I --> J[Tooltip Display]
    J --> K[Detailed Activity Info]
```

**Technical Implementation:**
```javascript
function generateActivityHeatmap(activityData) {
    // Initialize the activity matrix (days × hours)
    const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const hoursOfDay = Array.from({length: 24}, (_, i) => i);
    const activityMatrix = Array(7).fill().map(() => Array(24).fill(0));

    // Populate the matrix with activity counts
    activityData.forEach(activity => {
        const date = new Date(activity.timestamp);
        const day = date.getDay(); // 0-6 (Sunday-Saturday)
        const hour = date.getHours(); // 0-23
        activityMatrix[day][hour]++;
    });

    // Find the maximum activity count for color scaling
    const maxActivity = Math.max(...activityMatrix.flat());

    // Generate the heatmap HTML
    let heatmapHTML = '<div class="heatmap-container"><div class="heatmap-hours">';

    // Add hour labels
    hoursOfDay.forEach(hour => {
        const formattedHour = hour === 0 ? '12am' : hour === 12 ? '12pm' : hour < 12 ? `${hour}am` : `${hour-12}pm`;
        heatmapHTML += `<div class="hour-label">${formattedHour}</div>`;
    });

    heatmapHTML += '</div>';

    // Generate the heatmap grid with day labels
    daysOfWeek.forEach((day, dayIndex) => {
        heatmapHTML += `
            <div class="heatmap-row">
                <div class="day-label">${day}</div>
        `;

        hoursOfDay.forEach(hour => {
            const activityCount = activityMatrix[dayIndex][hour];
            const intensity = maxActivity > 0 ? activityCount / maxActivity : 0;
            const colorIntensity = Math.floor(intensity * 100);

            heatmapHTML += `
                <div class="heatmap-cell"
                     style="background-color: rgba(59, 130, 246, ${intensity});"
                     data-day="${day}"
                     data-hour="${hour}"
                     data-count="${activityCount}">
                </div>
            `;
        });

        heatmapHTML += '</div>';
    });

    heatmapHTML += '</div>';

    // Add the heatmap to the DOM
    document.getElementById('activity-heatmap').innerHTML = heatmapHTML;

    // Add event listeners for interactive tooltips
    document.querySelectorAll('.heatmap-cell').forEach(cell => {
        cell.addEventListener('mouseenter', function() {
            const day = this.getAttribute('data-day');
            const hour = this.getAttribute('data-hour');
            const count = this.getAttribute('data-count');
            const formattedHour = hour === '0' ? '12am' : hour === '12' ? '12pm' :
                                 parseInt(hour) < 12 ? `${hour}am` : `${parseInt(hour)-12}pm`;

            const tooltip = document.createElement('div');
            tooltip.className = 'heatmap-tooltip';
            tooltip.innerHTML = `
                <div class="tooltip-content">
                    <div class="tooltip-day">${day}</div>
                    <div class="tooltip-hour">${formattedHour}</div>
                    <div class="tooltip-count">${count} activities</div>
                </div>
            `;

            document.body.appendChild(tooltip);

            // Position the tooltip near the cell
            const rect = this.getBoundingClientRect();
            tooltip.style.left = `${rect.left + window.scrollX + rect.width / 2}px`;
            tooltip.style.top = `${rect.top + window.scrollY - tooltip.offsetHeight - 5}px`;
        });

        cell.addEventListener('mouseleave', function() {
            const tooltip = document.querySelector('.heatmap-tooltip');
            if (tooltip) {
                tooltip.remove();
            }
        });
    });
}
```

### Sales Card System

Implements a sophisticated sales visualization system:

- **Card Architecture**: Consistent card styling with border and shadow
- **Animation System**: Staggered fade-in animations with sequential timing
- **Layout Structure**: Flexbox-based content organization with spacing
- **Platform Integration**: Conditional platform icon rendering

**Technical Implementation:**
```css
/* Animation for sales cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.sale-card {
    opacity: 0;
    animation: fadeInUp 0.5s ease-out forwards;
    border-radius: 0.5rem;
    padding: 1.25rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Create staggered animation delay for multiple cards */
.sale-card:nth-child(1) {
    animation-delay: 0.1s;
}

.sale-card:nth-child(2) {
    animation-delay: 0.2s;
}

.sale-card:nth-child(3) {
    animation-delay: 0.3s;
}
```

**HTML Implementation:**
```html
<div class="sale-card border card">
    <div class="sale-main">
        <span class="sale-title">${sanitizeHTML(sale.sale_item)}</span>
        <span class="sale-price">£${sanitizeHTML(sale.sale_price.toString())}</span>
    </div>
    <div class="sale-info border-t card">
        <span>Time: ${sanitizeHTML(sale.sale_time)}</span>
        ${(sale.guest_platform && sale.guest_platform.toLowerCase() === 'whatsapp') ? `
            <img src="../static/icons/black-whatsapp-icon.png"
                 alt="WhatsApp"
                 title="WhatsApp"
                 class="whatsapp-theme-icon whatsapp-icon">
        ` : `
            <span>${sanitizeHTML(sale.guest_platform || '')}</span>
        `}
    </div>
</div>
```

### View Selector System

Implements an intelligent view switching mechanism:

- **State Management**: Active view tracking with class toggling
- **Animation Control**: Animation reset and replay for view transitions
- **Visual Feedback**: Active state indication with check icons
- **Event Handling**: Click-based view switching with dropdown integration

**Technical Implementation:**
```javascript
function initializeViewSelector() {
    const viewSelector = document.getElementById('viewSelector');
    const viewSelectorDropdown = document.getElementById('viewSelectorDropdown');
    const viewOptions = document.querySelectorAll('.view-option');
    const salesView = document.getElementById('salesView');
    const activityView = document.getElementById('activityView');

    // Initialize UIkit dropdown if not already done
    if (typeof UIkit !== 'undefined' && viewSelectorDropdown) {
        const dropdown = UIkit.dropdown(viewSelectorDropdown, {
            mode: 'click',
            pos: 'bottom-justify'
        });

        // Add animation classes
        viewSelectorDropdown.classList.add('dropdown-content');

        // Add animation handlers
        UIkit.util.on(viewSelectorDropdown, 'beforeshow', function () {
            this.style.transformOrigin = 'top center';
            this.setAttribute('data-state', '');

            requestAnimationFrame(() => {
                this.setAttribute('data-state', 'open');
            });
        });
    }

    // Handle view option selection
    viewOptions.forEach(option => {
        option.addEventListener('click', function (e) {
            e.preventDefault();

            // Get the display text and value
            const selectedValue = this.getAttribute('data-value');
            const selectedText = this.querySelector('.uk-cs-item-text').textContent;

            // Update button text and main view title
            document.getElementById('viewSelectorText').textContent = selectedText;
            const viewTitle = document.getElementById('viewTitle');
            if (viewTitle) {
                viewTitle.textContent = selectedText;
            }

            // Update active state and show/hide sections
            if (selectedValue === 'sales') {
                salesView.classList.remove('hidden');
                activityView.classList.add('hidden');
            } else {
                salesView.classList.add('hidden');
                activityView.classList.remove('hidden');
                animateActivityView(); // Trigger activity animations
            }

            // Update active state in dropdown
            viewOptions.forEach(opt => {
                const parentLi = opt.closest('li');
                if (parentLi) {
                    parentLi.classList.remove('uk-active');
                    const checkIcon = opt.querySelector('.uk-cs-check');
                    if (checkIcon) {
                        checkIcon.remove();
                    }
                }
            });

            // Add active state and check icon to selected item
            const parentLi = this.closest('li');
            if (parentLi) {
                parentLi.classList.add('uk-active');
                if (!this.querySelector('.uk-cs-check')) {
                    const checkIcon = document.createElement('uk-icon');
                    checkIcon.className = 'uk-cs-check';
                    checkIcon.setAttribute('icon', 'check');
                    this.appendChild(checkIcon);
                }
            }
        });
    });
}
```

## Data Processing Techniques

### User-Sales Association

Implements a robust data association system:

- **Data Enrichment**: User object enhancement with sales data
- **Matching Algorithm**: Phone-based user identification
- **ID Propagation**: User ID assignment from sales data
- **Filtering System**: User-specific sales extraction

**Technical Implementation:**
```javascript
function fetchAllData() {
    Promise.all([
        fetch('/updateduserslist').then(res => res.json()),
        fetch('/fetch-sales-list').then(res => res.json())
    ])
    .then(([usersData, salesList]) => {
        users = usersData;
        salesData = salesList;

        // Enrich users with user_id from salesData
        users.forEach(user => {
            const match = salesData.find(sale =>
                sale.guest_phone_or_id.toString() === user.guest_phone_or_id.toString()
            );
            if (match) {
                user.user_id = match.user_id;
            }
        });

        updateUserList();
    });
}
```

### Search System

Implements an intelligent search mechanism:

- **Real-time Filtering**: Input event-based filtering
- **Multi-field Matching**: Name, phone, and room number matching
- **Case Insensitivity**: Lowercase normalization for case-insensitive search
- **DOM Updates**: Filtered list rendering with preserved event binding

**Technical Implementation:**
```javascript
function filterUsers() {
    const searchInput = document.getElementById('searchInput');
    if (!searchInput) return;

    const searchValue = searchInput.value.toLowerCase();
    const filteredUsers = users.filter(user =>
        (user.Name && user.Name.toLowerCase().includes(searchValue)) ||
        (user.phone && user.phone.toLowerCase().includes(searchValue)) ||
        (user.room_no && user.room_no.toString().includes(searchValue))
    );
    updateUserList(filteredUsers);
}
```

## Security Implementation

### Content Sanitization

Implements a comprehensive HTML sanitization system:

- **Character Escaping**: Special character conversion to HTML entities
- **XSS Prevention**: Script injection prevention through escaping
- **Consistent Application**: Sanitization applied to all user-generated content
- **Null Handling**: Empty string fallback for null or undefined values

**Technical Implementation:**
```javascript
function sanitizeHTML(str) {
    if (!str) return '';
    return str.replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}
```

### User Engagement Funnel

Implements a sophisticated user engagement visualization system:

- **Funnel Visualization**: Step-by-step user journey visualization
- **Conversion Metrics**: Percentage-based transition between stages
- **Drop-off Analysis**: Visual indication of user drop-off points
- **Stage Comparison**: Relative size representation of each funnel stage

```mermaid
flowchart TD
    A[Total Users] --> |"View Profile (78%)"| B[Profile Viewers]
    B --> |"Browse Products (65%)"| C[Product Browsers]
    C --> |"Add to Cart (42%)"| D[Cart Users]
    D --> |"Complete Purchase (28%)"| E[Purchasers]
    E --> |"Repeat Purchase (15%)"| F[Repeat Customers]

    style A fill:#4285F4,stroke:#2A56C6,stroke-width:2px,color:white
    style B fill:#5E97F6,stroke:#2A56C6,stroke-width:2px,color:white
    style C fill:#7BAAF7,stroke:#2A56C6,stroke-width:2px,color:white
    style D fill:#A1C2FA,stroke:#2A56C6,stroke-width:2px,color:white
    style E fill:#C6D8FC,stroke:#2A56C6,stroke-width:2px,color:black
    style F fill:#E8F0FE,stroke:#2A56C6,stroke-width:2px,color:black
```

**Technical Implementation:**
```javascript
function generateEngagementFunnel(userData) {
    // Calculate funnel stages
    const totalUsers = userData.length;
    const profileViewers = userData.filter(user => user.viewed_profile).length;
    const productBrowsers = userData.filter(user => user.browsed_products).length;
    const cartUsers = userData.filter(user => user.added_to_cart).length;
    const purchasers = userData.filter(user => user.completed_purchase).length;
    const repeatCustomers = userData.filter(user => user.repeat_purchase).length;

    // Calculate percentages
    const profileViewPercentage = (profileViewers / totalUsers * 100).toFixed(1);
    const browsePercentage = (productBrowsers / totalUsers * 100).toFixed(1);
    const cartPercentage = (cartUsers / totalUsers * 100).toFixed(1);
    const purchasePercentage = (purchasers / totalUsers * 100).toFixed(1);
    const repeatPercentage = (repeatCustomers / totalUsers * 100).toFixed(1);

    // Calculate conversion rates between stages
    const viewToBrowseRate = (productBrowsers / profileViewers * 100).toFixed(1);
    const browseToCartRate = (cartUsers / productBrowsers * 100).toFixed(1);
    const cartToPurchaseRate = (purchasers / cartUsers * 100).toFixed(1);
    const purchaseToRepeatRate = (repeatCustomers / purchasers * 100).toFixed(1);

    // Generate the funnel HTML
    const funnelHTML = `
        <div class="funnel-container">
            <div class="funnel-stage" style="width: 100%">
                <div class="stage-label">Total Users</div>
                <div class="stage-count">${totalUsers}</div>
                <div class="stage-bar bg-blue-600"></div>
            </div>
            <div class="funnel-stage" style="width: ${profileViewPercentage}%">
                <div class="stage-label">Profile Viewers</div>
                <div class="stage-count">${profileViewers}</div>
                <div class="stage-percentage">${profileViewPercentage}%</div>
                <div class="stage-bar bg-blue-500"></div>
            </div>
            <div class="funnel-stage" style="width: ${browsePercentage}%">
                <div class="stage-label">Product Browsers</div>
                <div class="stage-count">${productBrowsers}</div>
                <div class="stage-percentage">${browsePercentage}%</div>
                <div class="conversion-rate">${viewToBrowseRate}% conversion</div>
                <div class="stage-bar bg-blue-400"></div>
            </div>
            <div class="funnel-stage" style="width: ${cartPercentage}%">
                <div class="stage-label">Cart Users</div>
                <div class="stage-count">${cartUsers}</div>
                <div class="stage-percentage">${cartPercentage}%</div>
                <div class="conversion-rate">${browseToCartRate}% conversion</div>
                <div class="stage-bar bg-blue-300"></div>
            </div>
            <div class="funnel-stage" style="width: ${purchasePercentage}%">
                <div class="stage-label">Purchasers</div>
                <div class="stage-count">${purchasers}</div>
                <div class="stage-percentage">${purchasePercentage}%</div>
                <div class="conversion-rate">${cartToPurchaseRate}% conversion</div>
                <div class="stage-bar bg-blue-200"></div>
            </div>
            <div class="funnel-stage" style="width: ${repeatPercentage}%">
                <div class="stage-label">Repeat Customers</div>
                <div class="stage-count">${repeatCustomers}</div>
                <div class="stage-percentage">${repeatPercentage}%</div>
                <div class="conversion-rate">${purchaseToRepeatRate}% conversion</div>
                <div class="stage-bar bg-blue-100"></div>
            </div>
        </div>
    `;

    // Add the funnel to the DOM
    document.getElementById('engagement-funnel').innerHTML = funnelHTML;
}
```

## Animation System

```mermaid
flowchart TD
    A[Animation Triggers] --> B{Animation Type}
    B -->|Side Panel| C[Transform Animation]
    B -->|Sales Cards| D[Staggered FadeInUp]
    B -->|Activity Timeline| E[Multiple Animations]
    C --> F[Panel Slide-in]
    D --> G[Sequential Card Reveal]
    E --> H[Timeline Growth]
    E --> I[Item FadeIn]
    E --> J[Icon Scale]
    K[User Interaction] --> L{Interaction Type}
    L -->|Hover| M[Transform & Shadow]
    L -->|Click| N[View Switching]
    M --> O[Element Elevation]
    N --> P[Content Swap]
    N --> Q[Animation Reset]
```

### Technical Implementation

- **Keyframe Definitions**: Custom animation sequences with transform properties
- **Staggered Timing**: Child-index-based delay application
- **State Transitions**: Class-based animation triggering
- **Hardware Acceleration**: Transform-based animations for performance

```css
/* Animation for the side panel */
.side-panel {
    transform: translateX(100%);
    transition: transform 0.3s ease-out;
    display: none;
}

.side-panel.active {
    transform: translateX(0);
    display: block;
}

/* Animation for sales cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.sale-card {
    opacity: 0;
    animation: fadeInUp 0.5s ease-out forwards;
}

/* Create staggered animation delay for multiple cards */
.sale-card:nth-child(1) {
    animation-delay: 0.1s;
}

.sale-card:nth-child(2) {
    animation-delay: 0.2s;
}
```

## Theme System Integration

- **Variable-Based Theming**: CSS variable usage for theme colors
- **Conditional Styling**: Theme-specific class application
- **Icon Adaptation**: Theme-aware icon rendering
- **Border Handling**: Theme-specific border styling

```css
/* Light theme variables */
.light {
    --color-vala-border: #e5e7eb;
}

/* Dark theme variables */
.pure-black {
    --color-vala-border: #38393b;
}

/* Theme-aware styling */
.table-container thead tr.search-row {
    position: sticky;
    top: 0;
    z-index: 30;
    background-color: var(--card);
    box-shadow: 0 1px 0 0 var(--color-vala-border);
}

/* Theme-specific dropdown hover states */
html .pure-black .uk-drop.uk-dropdown .uk-dropdown-nav li:hover,
body.pure-black .uk-drop.uk-dropdown .uk-dropdown-nav li:hover {
    background-color: transparent !important;
    background: transparent !important;
}

html .light .uk-drop.uk-dropdown .uk-dropdown-nav li:hover,
body .light .uk-drop.uk-dropdown .uk-dropdown-nav li:hover {
    background-color: transparent !important;
    background: transparent !important;
}
```

## Performance Optimization

- **DOM Manipulation Strategies**:
  - Batch DOM updates with innerHTML
  - Event delegation for dynamic elements
  - Cached DOM references
  - Minimal reflow operations

- **Animation Performance**:
  - Hardware-accelerated transforms
  - Opacity/transform-based animations
  - Staggered animation timing
  - Animation cleanup and reset

- **Scrolling Optimization**:
  - Hidden scrollbars for clean UI
  - Sticky positioning for headers
  - Smooth scrolling behavior
  - Optimized scroll containers

```css
/* Scrollbar optimization */
.table-container {
    overflow-y: auto;
    -ms-overflow-style: none; /* Hide scrollbar in IE 10+ */
    scrollbar-width: none; /* Hide scrollbar in Firefox */
    scroll-behavior: smooth;
}

.table-container::-webkit-scrollbar {
    display: none; /* Hide scrollbar in Chrome, Safari, Opera */
}

/* Sticky header optimization */
.table-container thead tr.search-row {
    position: sticky;
    top: 0;
    z-index: 30;
    background-color: var(--card);
}
```

## Responsive Design Implementation

- **Layout Architecture**:
  - Grid-based responsive layout
  - Media query breakpoints
  - Percentage-based sizing
  - Viewport-relative dimensions

- **Component Adaptation**:
  - Conditional rendering for small screens
  - Flexible card layouts
  - Touch-optimized interaction targets
  - Preserved functionality across viewports

```css
/* Responsive grid layout */
.grid {
    display: grid;
    grid-template-columns: 280px 1fr;
    transition: grid-template-columns 0.3s ease-in-out;
    overflow: hidden !important;
}

/* Media query for side panel positioning */
@media (min-width: 890px) {
    .side-panel {
        position: relative;
        transform: translateX(100%);
    }
}
```

