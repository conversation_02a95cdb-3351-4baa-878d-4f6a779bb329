<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Onest font from Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Onest:wght@400;500;600&display=swap" rel="stylesheet">
    <title>PMS analytics</title>
    {% include 'imports.html' %}

</head>
<style>
    body {
        visibility: hidden;
    }
    .pms-draggable-card.dragging {
        opacity: 0.4; /* Made it more transparent to appear lighter */
        border: 2px dashed #D1D5DB; /* Lighter dashed border (Tailwind gray-300 equivalent) */
        border-radius: 0.5rem; /* Added curved edges (matches 'rounded-lg') */
    }
    .pms-drop-indicator-before, .pms-drop-indicator-after {
        position: relative; /* Needed for pseudo-element positioning */
    }
    .pms-drop-indicator-before::before {
        content: "";
        position: absolute;
        /* Centered in the 1rem gap (gap/2 - width/2). gap=1rem, width=4px. calc(-0.5rem + 2px) from edge of element */
        /* To be precise: left edge of indicator should be at - (gap/2 + width/2) if card starts at 0.
           If card starts after gap, then left: -(gap) + (gap - width)/2 = -gap + gap/2 - width/2 = -gap/2 - width/2
           So, left: calc(-0.5rem - 2px); places the left edge of the 4px indicator.
        */
        left: calc(-0.5rem - 2px); /* Positioned in the gap to the left. (1rem gap / 2 + 4px width / 2) */
        top: 0;
        bottom: 0;
        width: 4px; /* Width of the indicator line */
        background-color: #3b82f6; /* Blue indicator color (Tailwind blue-500) */
        border-radius: 2px; /* Rounded ends for the line */
        z-index: 10; /* Ensure it's visible */
    }
    .pms-drop-indicator-after::after { /* Note: using ::after for the right side */
        content: "";
        position: absolute;
        right: calc(-0.5rem - 2px); /* Positioned in the gap to the right */
        top: 0;
        bottom: 0;
        width: 4px;
        background-color: #3b82f6;
        border-radius: 2px;
        z-index: 10;
    }
    /* Styles for the new draggable metric cards */
    .pms-metric-card.dragging {
        opacity: 0.4;
        border: 2px dashed #D1D5DB; /* Lighter dashed border */
        border-radius: 0.5rem; /* Curved edges */
    }
    .pms-metric-drop-indicator-before, .pms-metric-drop-indicator-after {
        position: relative;
    }
    .pms-metric-drop-indicator-before::before {
        content: "";
        position: absolute;
        left: calc(-0.5rem - 2px); /* Based on gap-x-4 (1rem gap) */
        top: 0;
        bottom: 0;
        width: 4px;
        background-color: #3b82f6; /* Blue indicator */
        border-radius: 2px;
        z-index: 10;
    }
    .pms-metric-drop-indicator-after::after {
        content: "";
        position: absolute;
        right: calc(-0.5rem - 2px); /* Based on gap-x-4 (1rem gap) */
        top: 0;
        bottom: 0;
        width: 4px;
        background-color: #3b82f6; /* Blue indicator */
        border-radius: 2px;
        z-index: 10;
    }

</style>

</head>

<body class="light">
    {% include 'components/loading.html' %}
    <div class="grid min-h-screen w-full  lg:grid-cols-[280px_1fr]">
        {% include 'sidebar.html' %}
        <div class="flex flex-col">
            <header
                class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
                <div style="margin-left: 8px;" class="flex items-center gap-2 px-4 pl-0">
                    <button id="toggle-btn" class="opacity-100 transition-opacity duration-300 focus:outline-none"
                        style="background-color: transparent !important;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-panel-left">
                            <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                            <path d="M9 3v18"></path>
                        </svg>
                    </button>
                    <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-3 h-4"
                        style="background-color: var(--border-color);"></div>
                    <!-- New Menubar -->
                    <div class="menubar" role="menubar">
                        <div class="menubar-indicator"></div>
                        <a href="/sales" role="menuitem">Sales Report</a>
                        <a href="/pmsanalytics" role="menuitem" class="active">PMS Analytics</a>
                        <a href="/googleana" role="menuitem">Google Analytics</a>
                        <a href="/status" role="menuitem">Status Report</a>
                    </div>
                </div>
                {% include 'topright.html' %}
            </header>
            <main class="card flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6">
                <div class="grid grid-cols-3 gap-x-4 gap-y-4">

                    <!-- Left Section: Main Container Spanning 2 Columns -->
                    <div class="col-span-2">
                        <!-- Nested Grid for Metric Cards -->
                        <div id="pms-metric-cards-row-container" class="grid grid-cols-3 gap-x-4 gap-y-4">
                            <!-- Card 1: Lifetime Revenue -->
                            <div class="card rounded-lg border shadow-sm pms-metric-card" draggable="true">
                                <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                    <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Lifetime Revenue
                                    </h3>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-badge-euro">
                                        <path
                                            d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z" />
                                        <path d="M7 12h5" />
                                        <path d="M15 9.4a4 4 0 1 0 0 5.2" />
                                    </svg>
                                </div>
                                <div class="p-6 relative">
                                    <div class="text-2xl font-bold">£12,893</div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Total lifetime revenue</p>
                                    <div
                                        class="absolute bottom-7 right-4 flex items-center text-xs text-green-600 border border-green-500 bg-transparent px-1.5 py-0.5 rounded-md">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-arrow-up-narrow-wide mr-0.5">
                                            <path d="m3 8 4-4 4 4" />
                                            <path d="M7 4v16" />
                                            <path d="M11 12h4" />
                                            <path d="M11 16h7" />
                                            <path d="M11 20h10" />
                                        </svg>
                                        8.5%
                                    </div>
                                </div>
                            </div>

                            <!-- Card 2: Total Reservations -->
                            <div class="card rounded-lg border shadow-sm pms-metric-card" draggable="true">
                                <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                    <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Total Reservations
                                    </h3>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-notebook">
                                        <path d="M2 6h4" />
                                        <path d="M2 10h4" />
                                        <path d="M2 14h4" />
                                        <path d="M2 18h4" />
                                        <rect width="16" height="20" x="4" y="2" rx="2" />
                                        <path d="M16 2v20" />
                                    </svg>
                                </div>
                                <div class="p-6 relative">
                                    <div class="text-2xl font-bold">864</div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Lifetime reservations</p>
                                    <div
                                        class="absolute bottom-7 right-4 flex items-center text-xs text-red-600 border border-red-500  px-1.5 py-0.5 rounded-md">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-arrow-down-narrow-wide mr-0.5">
                                            <path d="m3 16 4 4 4-4" />
                                            <path d="M7 20V4" />
                                            <path d="M11 4h4" />
                                            <path d="M11 8h7" />
                                            <path d="M11 12h10" />
                                        </svg>
                                        3.2%
                                    </div>
                                </div>
                            </div>

                            <!-- Card 3: Total Orders -->
                            <div class="card rounded-lg border shadow-sm pms-metric-card" draggable="true">
                                <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                    <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Total Orders</h3>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-calendar-arrow-up">
                                        <path d="m14 18 4-4 4 4" />
                                        <path d="M16 2v4" />
                                        <path d="M18 22v-8" />
                                        <path d="M21 11.343V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h9" />
                                        <path d="M3 10h18" />
                                        <path d="M8 2v4" />
                                    </svg>
                                </div>
                                <div class="p-6 relative">
                                    <div class="text-2xl font-bold">2312</div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Lifetime orders</p>
                                    <div
                                        class="absolute bottom-7 right-4 flex items-center text-xs text-green-600 border border-green-500 bg-transparent px-1.5 py-0.5 rounded-md">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-arrow-up-narrow-wide mr-0.5">
                                            <path d="m3 8 4-4 4 4" />
                                            <path d="M7 4v16" />
                                            <path d="M11 12h4" />
                                            <path d="M11 16h7" />
                                            <path d="M11 20h10" />
                                        </svg>
                                        12.7%
                                    </div>
                                </div>
                            </div>

                            <!-- Card 4: Bookings -->
                            <div class="card rounded-lg border shadow-sm pms-metric-card">
                                <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                    <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Bookings</h3>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-hotel">
                                        <path d="M10 22v-6.57" />
                                        <path d="M12 11h.01" />
                                        <path d="M12 7h.01" />
                                        <path d="M14 15.43V22" />
                                        <path d="M15 16a5 5 0 0 0-6 0" />
                                        <path d="M16 11h.01" />
                                        <path d="M16 7h.01" />
                                        <path d="M8 11h.01" />
                                        <path d="M8 7h.01" />
                                        <rect x="4" y="2" width="16" height="20" rx="2" />
                                    </svg>
                                </div>
                                <div class="p-6 relative">
                                    <div class="text-2xl font-bold">1584</div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Lifetime bookings</p>
                                    <div
                                        class="absolute bottom-7 right-4 flex items-center text-xs text-red-600 border border-red-500 px-1.5 py-0.5 rounded-md">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-arrow-down-narrow-wide mr-0.5">
                                            <path d="m3 16 4 4 4-4" />
                                            <path d="M7 20V4" />
                                            <path d="M11 4h4" />
                                            <path d="M11 8h7" />
                                            <path d="M11 12h10" />
                                        </svg>
                                        5.9%
                                    </div>
                                </div>
                            </div>

                            <!-- Guest Feedback (Spanning 2 Columns) -->
                            <div class="card rounded-lg border shadow-sm col-span-2 pms-metric-card">
                                <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                    <div class="flex items-center gap-2">
                                        <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Guest Feedback
                                        </h3>
                                        <div class="group relative">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <div
                                                class="absolute bottom-full left-1/2 transform -translate-x-1/2 bg-black text-white text-xs rounded py-1 px-2 hidden group-hover:block w-48 text-center">
                                                Reviews are categorized as follows: 1 and 2 star ratings are classified
                                                as negative, 3 star ratings are considered neutral, and 4 and 5 star
                                                ratings are regarded as positive.
                                            </div>
                                        </div>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-star">
                                        <path
                                            d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z" />
                                    </svg>
                                </div>
                                <div class="p-6">
                                    <!-- Container for the progress bars -->
                                    <div id="myGeneratedProgressBar" style="margin-bottom: 5px;"
                                        class="mt-3 flex h-1.5 w-full rounded-full bg-gray-100 h-[10px] overflow-hidden">
                                    </div>

                                    <!-- Legends -->
                                    <div id="progressBarLegend" class="mt-2 text-sm"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            // All progress items, including Total
                            const progressData = [
                                { name: 'Positive', value: 130, color: 'bg-[#3b82f6]' }, // Blue
                                { name: 'Negative', value: 170, color: 'bg-[#22c55e]' }, // Green
                                { name: 'Neutral', value: 220, color: 'bg-[#f59e0b]' }, // Orange
                            ];

                            // Filter out the "Total" item so it doesn't appear as a bar
                            const filteredData = progressData.filter(item => item.name !== 'Total');

                            // Calculate total
                            const total = progressData.reduce((sum, p) => sum + p.value, 0);

                            const container = document.getElementById('myGeneratedProgressBar');
                            const legendContainer = document.getElementById('progressBarLegend');

                            container.innerHTML = '';
                            legendContainer.innerHTML = '';
                            legendContainer.className = 'flex flex-row flex-wrap justify-center mt-2 text-[12px]';

                            // Create bars without legends
                            filteredData.forEach(item => {
                                const barWidth = ((item.value / total) * 100).toFixed(2);

                                // Bar
                                const barDiv = document.createElement('div');
                                barDiv.className = `h-full ${item.color}`;
                                barDiv.style.width = barWidth + '%';
                                container.appendChild(barDiv);
                            });

                            // Add new feedback labels
                            const positiveLabel = document.createElement('div');
                            positiveLabel.className = 'flex items-center gap-1 mr-3';
                            positiveLabel.innerHTML = `
                                <span class="inline-block w-2.5 h-2.5 rounded-sm bg-[#3b82f6]"></span>
                                <span class="text-xs">Positive: ${filteredData.find(item => item.name === 'Positive').value}</span>
                            `;
                            legendContainer.appendChild(positiveLabel);

                            const negativeLabel = document.createElement('div');
                            negativeLabel.className = 'flex items-center gap-1 mr-3';
                            negativeLabel.innerHTML = `
                                <span class="inline-block w-2.5 h-2.5 rounded-sm bg-[#22c55e]"></span>
                                <span class="text-xs">Negative: ${filteredData.find(item => item.name === 'Negative').value}</span>
                            `;
                            legendContainer.appendChild(negativeLabel);

                            const neutralLabel = document.createElement('div');
                            neutralLabel.className = 'flex items-center gap-1 mr-3';
                            neutralLabel.innerHTML = `
                                <span class="inline-block w-2.5 h-2.5 rounded-sm bg-[#f59e0b]"></span>
                                <span class="text-xs">Neutral: ${filteredData.find(item => item.name === 'Neutral').value}</span>
                            `;
                            legendContainer.appendChild(neutralLabel);
                        });
                    </script>
                    <!-- Right Section: Product Rankings -->
                    <div class="card rounded-lg border shadow-sm col-span-1">
                        <div class="p-4 flex justify-between items-center border-b card">
                            <div class="pr-6">
                                <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">Product
                                    Rankings</h3>
                                <p class="text-sm text-muted-foreground mt-1">Ranking of products by sales</p>
                            </div>
                            <button class="uk-button border card" type="button">
                                <svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 3px;" width="16"
                                    height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-left">
                                    <path d="M7 17V7h10" />
                                    <path d="M17 17 7 7" />
                                </svg>
                                PMS
                            </button></button>
                        </div>
                        <div class="p-4 pt-0 relative min-h-[240px] md:h-[220px]">
                            <div id="chartdiv" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>

                    <!-- Scripts related to Guest Feedback -->
                    <script src="https://cdn.jsdelivr.net/npm/progressbar.js"></script>
                    <!-- Ensure ProgressBar.js is included -->
                    <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            // Initialize the first segment for Positive feedback
                            var bar1 = new ProgressBar.Line('#progress-bar-container', {
                                strokeWidth: 3,
                                easing: 'easeInOut',
                                duration: 1400,
                                color: '#47a8f8',
                                trailColor: 'transparent',
                                trailWidth: 1,
                                svgStyle: { width: '69%', height: '100%', position: 'absolute', left: '0', borderRadius: '5px' },
                            });

                            // Initialize the second segment for Neutral feedback
                            var bar2 = new ProgressBar.Line('#progress-bar-container', {
                                strokeWidth: 3,
                                easing: 'easeInOut',
                                duration: 1400,
                                color: '#7e80e7',
                                trailColor: 'transparent',
                                trailWidth: 1,
                                svgStyle: { width: '19%', height: '100%', position: 'absolute', left: '70.5%', borderRadius: '5px' },
                            });

                            // Initialize the third segment for Negative feedback
                            var bar3 = new ProgressBar.Line('#progress-bar-container', {
                                strokeWidth: 3,
                                easing: 'easeInOut',
                                duration: 1400,
                                color: '#f3a23a',
                                trailColor: 'transparent',
                                trailWidth: 1,
                                svgStyle: { width: '9%', height: '100%', position: 'absolute', left: '90.5%', borderRadius: '5px' },
                            });

                            // Set the progress for each segment
                            bar1.animate(1.0);  // Number from 0.0 to 1.0
                            bar2.animate(1.0);
                            bar3.animate(1.0);
                        });
                    </script>

                </div>
                <script>
                    // Function to fetch TPP (Third-Party Product) Data from the backend
                    async function fetchTPPData() {
                        try {
                            const response = await fetch('/fetch-tpp');
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            const data = await response.json();
                            return data[0]; // Assuming the data is an array and we need the first object
                        } catch (error) {
                            console.error('Error fetching TPP data:', error);
                            return null;
                        }
                    }

                    // Function to update the Pie Chart with fetched data
                    function updatePieChart(chart, data) {
                        chart.data = [
                            {
                                product: "Food",
                                sales: parseFloat(data.food_amount),
                                color: am4core.color("#e76e51")
                            },
                            {
                                product: "Beverage",
                                sales: parseFloat(data.beverage_amount),
                                color: am4core.color("#2c9b91")
                            },
                            {
                                product: "Spa",
                                sales: parseFloat(data.spa_sales),
                                color: am4core.color("#f2a262")
                            },
                            {
                                product: "Massage",
                                sales: parseFloat(data.massage_sales),
                                color: am4core.color("#e7c468")
                            },
                            {
                                product: "Room Bookings",
                                sales: parseFloat(data.room_sales),
                                color: am4core.color("#264754")
                            }
                        ];
                    }

                    // Initialize the Chart once amCharts are ready
                    am4core.ready(async function () {
                        // Apply the animated theme
                        am4core.useTheme(am4themes_animated);

                        // Create a Pie Chart instance in the chartdiv
                        var chart = am4core.create("chartdiv", am4charts.PieChart);
                        chart.radius = am4core.percent(60); // Adjust the size as needed

                        // Fetch data and update the chart
                        const tppData = await fetchTPPData();
                        if (tppData) {
                            updatePieChart(chart, tppData);
                        }

                        // Add and configure the Pie Series
                        var pieSeries = chart.series.push(new am4charts.PieSeries());
                        pieSeries.dataFields.value = "sales";
                        pieSeries.dataFields.category = "product";
                        pieSeries.slices.template.propertyFields.fill = "color";

                        // Disable the default amCharts logo
                        chart.logo.disabled = true;

                        // Create a Donut Chart by setting an inner radius
                        chart.innerRadius = am4core.percent(30);

                        // Customize slice appearance
                        pieSeries.slices.template.padding = 1;
                        pieSeries.slices.template.cornerRadius = 5;
                        pieSeries.slices.template.fillOpacity = 1;
                        pieSeries.slices.template.strokeWidth = 0;
                        pieSeries.slices.template.stroke = am4core.color("#ffffff");

                        // Configure Labels
                        pieSeries.labels.template.disabled = false;
                        pieSeries.labels.template.text = "{category}: €{value}";
                        pieSeries.labels.template.radius = 1;
                        pieSeries.labels.template.fontSize = 12;
                        pieSeries.labels.template.maxWidth = 80;
                        pieSeries.labels.template.wrap = true;

                        // Configure Ticks
                        pieSeries.ticks.template.disabled = false;
                        pieSeries.ticks.template.strokeOpacity = 0.7;
                        pieSeries.ticks.template.strokeWidth = 2;
                        pieSeries.ticks.template.length = 20; // Default length for all lines

                        // Adjust the length for specific categories if needed
                        pieSeries.ticks.template.adapter.add("length", function (length, target) {
                            if (target.dataItem && target.dataItem.category === "Spa") {
                                return 5; // Adjusted length for the "Spa" line
                            }
                            return length;
                        });

                        // Add Tooltips
                        pieSeries.slices.template.tooltipText = "{category}: €{value}";

                        // Function to update chart colors based on the current theme
                        function updateChartColors() {
                            var body = document.body;
                            var isDarkTheme = body.classList.contains('pure-black') ||
                                body.classList.contains('dark-gray') ||
                                body.classList.contains('navy-blue') ||
                                body.classList.contains('cool-blue') ||
                                body.classList.contains('deep-burgundy') ||
                                body.classList.contains('charcoal');

                            // Update text color for labels and ticks based on the theme
                            pieSeries.labels.template.fill = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
                            pieSeries.ticks.template.stroke = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
                        }

                        // Initial color update
                        updateChartColors();

                        // Observe theme changes to update chart colors dynamically
                        var observer = new MutationObserver(function (mutations) {
                            mutations.forEach(function (mutation) {
                                if (mutation.type === "attributes" && mutation.attributeName === "class") {
                                    updateChartColors();
                                }
                            });
                        });

                        observer.observe(document.body, {
                            attributes: true
                        });
                    });

                </script>
                <div class="grid grid-cols-3 gap-4">
                    <div class="col-span-2">
                        <div class="rounded-lg border card shadow-sm" data-v0-t="card">
                            <div class="flex justify-between items-center p-6">
                                <h3 class="text-2xl font-semibold leading-none tracking-tight">Total Guests</h3>
                                <div class="flex items-center gap-4">
                                    <div class="flex items-center gap-1">
                                        <span class="inline-block w-2.5 h-2.5 rounded-sm bg-rose-500"></span>
                                        <span class="text-xs">Direct Bookings</span>
                                    </div>
                                    <div class="flex items-center gap-1  mr-1">
                                        <span class="inline-block w-2.5 h-2.5 rounded-sm bg-cyan-500"></span>
                                        <span class="text-xs">OTA Bookings</span>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="h-[300px]">
                                    <canvas id="newSignupsChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- container for the 3rd column -->
                    <div class="col-span-1">
                        <div class="rounded-lg border card shadow-sm h-full" data-v0-t="card">
                            <div class="p-3">
                                <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">Bookings
                                    by Platform</h3>
                                <p class="text-sm text-muted-foreground mt-1">Distribution of bookings across platforms
                                </p>
                            </div>
                            <div class="p-3 mt-1">
                                <!-- Revenue Stats Container - Added id="booking-platform-list" -->
                                <div id="booking-platform-list" class="space-y-3">
                                    <!-- Hotel Website -->
                                    <div
                                        class="p-3 border rounded-lg flex items-center justify-between card transition-colors booking-container hover-float" draggable="true">
                                        <div class="flex items-center gap-3 ">
                                            <div class="p-2 border rounded-lg card">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="28"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-blend">
                                                    <circle cx="9" cy="9" r="7" />
                                                    <circle cx="15" cy="15" r="7" />
                                                </svg>
                                            </div>
                                            <div>
                                                <p class="font-medium">Hotel Bookings</p>
                                                <p class="text-sm text-gray-500">No of Bookings : 512</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-semibold">€227,459</p>
                                            <p class="text-sm text-green-600">43%</p>
                                        </div>
                                    </div>

                                    <!-- Booking.com -->
                                    <div
                                        class="p-3 border rounded-lg flex items-center justify-between card transition-colors booking-container hover-float" draggable="true">
                                        <div class="flex items-center gap-3">
                                            <div class="p-2 border rounded-lg flex items-center justify-center card">
                                                <img src="https://cdn.worldvectorlogo.com/logos/bookingcom-1.svg"
                                                    alt="Booking.com" class="w-7 h-7 object-contain" />
                                            </div>
                                            <div>
                                                <p class="font-medium">Booking.com</p>
                                                <p class="text-sm text-gray-500">No of Bookings : 432</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-semibold">€142,823</p>
                                            <p class="text-sm text-green-600">27%</p>
                                        </div>
                                    </div>

                                    <!-- Expedia -->
                                    <div
                                        class="p-3 border rounded-lg flex items-center justify-between card transition-colors booking-container hover-float" draggable="true">
                                        <div class="flex items-center gap-3 ">
                                            <div class="p-2 border rounded-lg flex items-center justify-center card">
                                                <img src="static\icons\expedia.png" alt="Expedia"
                                                    class="w-7 h-7 object-contain" />
                                            </div>
                                            <div>
                                                <p class="font-medium">Expedia</p>
                                                <p class="text-sm text-gray-500">No of Bookings : 322</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-semibold">€89,935</p>
                                            <p class="text-sm text-green-600">11%</p>
                                        </div>
                                    </div>

                                    <!-- Airbnb -->
                                    <div
                                        class="p-3 border rounded-lg flex items-center justify-between card transition-colors booking-container hover-float" draggable="true">
                                        <div class="flex items-center gap-3">
                                            <div class="p-2 border rounded-lg flex items-center justify-center card">
                                                <img src="https://cdn.worldvectorlogo.com/logos/airbnb-1.svg"
                                                    alt="Airbnb" class="w-7 h-7 object-contain" />
                                            </div>
                                            <div>
                                                <p class="font-medium">Airbnb</p>
                                                <p class="text-sm text-gray-500">No of Bookings : 129</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-semibold">€37,028</p>
                                            <p class="text-sm text-green-600">7%</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Add CSS style for hover float effect -->
                <style>
                    .hover-float {
                        position: relative;
                        transition: all 0.3s ease-out;
                        cursor: pointer;
                    }

                    .hover-float:hover {
                        transform: translateY(-6px);
                        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
                        border-color: rgba(99, 102, 241, 0.4);
                    }

                    /* Prepare for animation */
                    .hover-float:before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        z-index: -1;
                        opacity: 0;
                        border-radius: 8px;
                        background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
                        transition: opacity 0.3s ease;
                    }

                    .hover-float:hover:before {
                        opacity: 1;
                    }

                    /* Add a slight scale effect */
                    .hover-float:hover {
                        transform: translateY(-6px) scale(1.01);
                    }

                    /* Make the transition smoother on return */
                    .hover-float:active {
                        transform: translateY(-2px);
                        transition: all 0.1s;
                    }
                    /* Styles for drag and drop for booking list (already present) */
                    .booking-container.dragging {
                        opacity: 0.5;
                        background-color: var(--card-background-hover, #f0f0f0); /* Example hover color */
                        /* Important to override hover-float transform during drag */
                        transform: none !important;
                        box-shadow: 0 5px 15px rgba(0,0,0,0.2) !important;
                    }
                    .drag-over-placeholder-line {
                        height: 3px; /* Make it a bit more visible */
                        background-color: #3b82f6; /* Tailwind's blue-500, or your theme's accent */
                        margin: 4px 0; /* Adjust to fit within space-y-3 aesthetic */
                        pointer-events: none; /* So it doesn't interfere with drag events */
                    }
                </style>
                <div id="pms-analytics-cards-container" class="grid gap-4 md:grid-cols-3 lg:grid-cols-3 mt-4">
                    <div class="col-span-1 pms-draggable-card" draggable="true">
                        <div class="rounded-lg border card shadow-sm h-full">
                            <div class="p-4 border-b card">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg font-semibold leading-none">Guest Nationalities</h3>
                                        <p class="text-sm text-muted-foreground mt-1">Map representation</p>
                                    </div>
                                    <button id="nationality-filter-btn" class="uk-button border card default"
                                        type="button">
                                        Filters
                                        <svg class="w-2.5 h-2.5 ms-3" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" d="m1 1 4 4 4-4" />
                                        </svg>
                                    </button>
                                    <div class="uk-drop uk-dropdown" uk-dropdown="mode: click; pos: bottom-right">
                                        <ul class="uk-dropdown-nav uk-nav">
                                            <li>
                                                <a href="#" id="nationality-europe" class="nationality-option">Today</a>
                                            </li>
                                            <li>
                                                <a href="#" id="nationality-global" class="nationality-option">Last
                                                    week</a>
                                            </li>
                                            <li>
                                                <a href="#" id="nationality-top10" class="nationality-option">Last
                                                    Month</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="">
                                <div id="europeMap" style="height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                    <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
                    <script src="https://cdn.amcharts.com/lib/5/map.js"></script>
                    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>
                    <script src="https://cdn.amcharts.com/lib/5/geodata/region/world/europeLow.js"></script>

                    <script>
                        am5.ready(function () {
                            var root = am5.Root.new("europeMap");

                            root.setThemes([
                                am5themes_Animated.new(root)
                            ]);

                            root._logo.dispose();

                            var chart = root.container.children.push(am5map.MapChart.new(root, {
                                panX: "none",
                                panY: "none",
                                wheelX: "none",  // Disable mouse wheel zoom
                                wheelY: "none",  // Disable mouse wheel zoom
                                pinchZoom: false, // Disable pinch zoom
                                projection: am5map.geoMercator(),
                                homeGeoPoint: { latitude: 54, longitude: 15 },
                                homeZoomLevel: 2
                            }));

                            // Highlighted countries series
                            var polygonSeries = chart.series.push(am5map.MapPolygonSeries.new(root, {
                                geoJSON: am5geodata_region_world_europeLow,
                                include: ["NL", "GB", "FR", "DE", "IT", "ES", "SE"]
                            }));

                            // Generate random guest numbers for each country
                            const guestData = {
                                "NL": Math.floor(Math.random() * (400 - 200 + 1)) + 200,
                                "GB": Math.floor(Math.random() * (400 - 200 + 1)) + 200,
                                "FR": Math.floor(Math.random() * (400 - 200 + 1)) + 200,
                                "DE": Math.floor(Math.random() * (400 - 200 + 1)) + 200,
                                "IT": Math.floor(Math.random() * (400 - 200 + 1)) + 200,
                                "ES": Math.floor(Math.random() * (400 - 200 + 1)) + 200,
                                "SE": Math.floor(Math.random() * (400 - 200 + 1)) + 200
                            };

                            polygonSeries.mapPolygons.template.setAll({
                                tooltipText: "{name}: {guests}", // Updated tooltip format
                                interactive: true,
                                fill: am5.color(0xd1d5db),
                                stroke: am5.color(0x000000),
                                strokeWidth: 1,
                                strokeOpacity: 0.4
                            });

                            // Add guest data to each polygon
                            polygonSeries.mapPolygons.template.adapters.add("tooltipText", function (text, target) {
                                return text.replace("{guests}", guestData[target.dataItem.get("id")]);
                            });

                            // Hover state
                            polygonSeries.mapPolygons.template.states.create("hover", {
                                fill: am5.color(0x9ca3af),    // Darker on hover (Tailwind gray-400)
                                strokeWidth: 1.5              // Even thicker borders on hover
                            });

                            // Rest of Europe series
                            var restOfEuropeSeries = chart.series.push(am5map.MapPolygonSeries.new(root, {
                                geoJSON: am5geodata_region_world_europeLow,
                                exclude: ["NL", "GB", "FR", "DE", "IT", "ES", "SE"]
                            }));

                            restOfEuropeSeries.mapPolygons.template.setAll({
                                fill: am5.color(0xf3f4f6),    // Light grey (Tailwind gray-100)
                                stroke: am5.color(0x000000),   // Black borders
                                strokeWidth: 0.5,              // Thinner borders for non-highlighted countries
                                strokeOpacity: 0.2,            // More subtle borders for non-highlighted
                                interactive: false
                            });

                            polygonSeries.events.on("datavalidated", function () {
                                chart.goHome(); // Ensure initial zoom and position are applied
                            });

                            chart.appear(1000, 100);
                        });
                    </script>
                    </script>
                    <style>
                        .relative:hover .absolute {
                            filter: brightness(1.1);
                            transition: all 0.2s ease;
                        }
                    </style>
                    <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            var bars = [
                                { id: "bookings-progress", value: 75, color: "#abbdd3" },
                                { id: "revenue-progress", value: 50, color: "#eca492" },
                                { id: "occupancy-progress", value: 60, color: "#fddda2" },
                                { id: "satisfaction-progress", value: 85, color: "#5cd1ba" },
                                { id: "performance-progress", value: 70, color: "#0000" }
                            ];

                            bars.forEach(function (bar) {
                                var progressBar = document.getElementById(bar.id);
                                var valueSpan = document.getElementById(bar.id.replace("-progress", "-value"));
                                var currentValue = 0;

                                var animate = setInterval(function () {
                                    if (currentValue < bar.value) {
                                        currentValue += 1;
                                        progressBar.style.width = currentValue + '%';
                                        valueSpan.textContent = currentValue + "%";
                                    } else {
                                        clearInterval(animate);
                                    }
                                }, 20);
                            });
                        });
                    </script>

                    <!-- Total Sales Chart Container -->
                    <div class="col-span-1 pms-draggable-card" draggable="true">
                        <div class="rounded-lg border card h-full chart-container" data-v0-t="card">
                            <div class="flex justify-between items-center p-4 border-b card">
                                <div class="flex flex-col">
                                    <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">
                                        Total Revenue</h3>
                                    <p class="text-sm text-muted-foreground mt-1">Revenue of the hotel from PMS
                                    </p>
                                </div>

                                <button id="revenue-filter-btn" class="uk-button border card default" type="button">
                                    Filters
                                    <svg class="w-2.5 h-2.5 ms-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                        fill="none" viewBox="0 0 10 6">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                            stroke-width="2" d="m1 1 4 4 4-4" />
                                    </svg>
                                </button>
                                <div class="uk-drop uk-dropdown" uk-dropdown="mode: click; pos: bottom-right">
                                    <ul class="uk-dropdown-nav uk-nav">
                                        <li>
                                            <a href="#" id="revenue-daily" class="revenue-option">Today</a>
                                        </li>
                                        <li>
                                            <a href="#" id="revenue-weekly" class="revenue-option">Last Week</a>
                                        </li>
                                        <li>
                                            <a href="#" id="revenue-monthly" class="revenue-option">Last Month</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="px-4 pb-1 pt-0 relative min-h-[300px] md:h-[300px]">
                                <div class="flex items-start h-full">
                                    <canvas id="TotalSalesChart" style="width: 100%; height: 100%;"></canvas>
                                </div>
                            </div>
                        </div>
                        <script>
                            let totalSalesChart = null;

                            async function initializeTotalSalesChart() {
                                // Updated sales data for each day (Monday to Sunday)
                                const salesData = [2000, 1780, 1200, 2400, 3000, 1800, 1500];

                                // Check if body has pure-black class and set color accordingly
                                const isPureBlack = document.body.classList.contains('pure-black');
                                const chartBarColor = isPureBlack ? '#fafafa' : '#18181b'; // White for dark theme, dark for light theme

                                // Add this line to set grid color based on theme
                                const gridColor = isPureBlack ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

                                console.log('Sales chart color:', isPureBlack ? 'Using white bars (#fafafa)' : 'Using dark bars (#18181b)');
                                console.log('Grid color:', isPureBlack ? 'Using light grid' : 'Using dark grid');

                                const salesChartData = {
                                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                                    datasets: [
                                        {
                                            data: salesData,
                                            backgroundColor: chartBarColor,
                                            borderColor: chartBarColor,
                                            borderWidth: 0, // Remove border completely
                                            borderRadius: {
                                                topLeft: 5,
                                                topRight: 5,
                                                bottomLeft: 5,
                                                bottomRight: 5
                                            },
                                            borderSkipped: false
                                        }
                                    ]
                                };

                                const ctx = document.getElementById('TotalSalesChart').getContext('2d');

                                // Destroy existing chart if it exists
                                if (totalSalesChart) {
                                    totalSalesChart.destroy();
                                }

                                // Create a high-quality chart with crisp edges
                                totalSalesChart = new Chart(ctx, {
                                    type: 'bar',
                                    data: salesChartData,
                                    options: {
                                        responsive: true,
                                        maintainAspectRatio: false,
                                        layout: {
                                            padding: {
                                                bottom: 20,
                                                top: 40
                                            }
                                        },
                                        barPercentage: 1,
                                        categoryPercentage: 0.7,
                                        devicePixelRatio: 2,
                                        scales: {
                                            x: {
                                                grid: {
                                                    display: false,
                                                    color: 'transparent'
                                                },
                                                border: {
                                                    display: false
                                                },
                                                // Change the x-axis label color to a default gray
                                                ticks: { color: '#555555' }
                                            },
                                            y: {
                                                beginAtZero: true,
                                                min: 0,
                                                max: 3000,
                                                grid: {
                                                    color: gridColor, // Use theme-aware grid color
                                                    borderDash: [5, 5]
                                                },
                                                border: {
                                                    display: false
                                                },
                                                ticks: {
                                                    display: false // Hide y-axis values
                                                }
                                            }
                                        },
                                        plugins: {
                                            tooltip: {
                                                enabled: false,  // Disable default tooltip
                                                external: createCustomTooltipForTips  // Use our custom tooltip function
                                            },
                                            legend: {
                                                display: false
                                            }
                                        }
                                    }
                                });
                            }

                            document.addEventListener('DOMContentLoaded', function () {
                                // Initial chart creation
                                setTimeout(() => {
                                    initializeTotalSalesChart();
                                }, 100); // Small delay to ensure all theme classes are applied

                                // Watch for theme changes and update chart
                                const observer = new MutationObserver(function (mutations) {
                                    mutations.forEach(function (mutation) {
                                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                                            // Brief timeout to ensure theme is fully applied
                                            setTimeout(() => {
                                                initializeTotalSalesChart();
                                            }, 100);
                                        }
                                    });
                                });

                                observer.observe(document.body, {
                                    attributes: true
                                });
                            });
                        </script>
                        <style>
                            /* Adjust the chart container to move the chart down */
                            #TotalSalesChart {
                                margin-bottom: -0px;
                                /* Adjust this value to move the chart down */
                            }
                        </style>
                    </div>

                    <div class="col-span-1 pms-draggable-card" draggable="true">
                        <div class="rounded-lg border card shadow-sm h-full overflow-hidden" data-v0-t="card">
                            <div class="p-4 flex justify-between items-center border-b card">
                                <div>
                                    <h3
                                        class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight dark:">
                                        Tip Analytics</h3>
                                    <p class="text-sm text-muted-foreground mt-1">Compared analytics</p>
                                </div>
                                <!-- Comparison Dropdown -->
                                <button id="toggle-chart-btn" class="uk-button border card default" type="button">
                                    Filters
                                    <svg class="w-2.5 h-2.5 ms-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                        fill="none" viewBox="0 0 10 6">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                            stroke-width="2" d="m1 1 4 4 4-4" />
                                    </svg>
                                </button>
                                <div class="uk-drop uk-dropdown" uk-dropdown="mode: click; pos: bottom-right">
                                    <ul class="uk-dropdown-nav uk-nav">
                                        <li>
                                            <a href="#" id="dropdown1" class="review-option-1">Reviews</a>
                                        </li>
                                        <li>
                                            <a href="#" id="dropdown2" class="review-option-1">Tips</a>
                                        </li>
                                    </ul>
                                </div>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function () {
                                        // Get the dropdown toggle button
                                        const toggleButton = document.getElementById('toggle-chart-btn');

                                        // Get all dropdown options
                                        const options = document.querySelectorAll('.review-option-1');
                                        const dropdownElement = document.querySelector('.uk-drop');

                                        // Initialize the dropdown
                                        const dropdown = UIkit.dropdown(dropdownElement);

                                        // Close dropdown when an option is clicked
                                        options.forEach(option => {
                                            option.addEventListener('click', function (event) {
                                                event.preventDefault();  // Prevent the page from scrolling to the top
                                                dropdown.hide(); // Close the dropdown
                                            });
                                        });
                                    });
                                </script>


                            </div>
                            <div class="p-4 pt-0 h-full flex flex-col relative">
                                <div class="relative w-11/12 mx-auto h-[212px] flex items-center justify-center mb-6">
                                    <svg viewBox="0 0 200 100" class="w-full h-full">
                                        <!-- Grey styling circle -->
                                        <path d="M10 100 A 90 90 0 0 1 190 100" fill="none" stroke="#F3F4F6"
                                            stroke-width="6" />

                                        <!-- Main half circles -->
                                        <path d="M20 100 A 80 80 0 0 1 180 100" fill="none" stroke="#9ca3af"
                                            stroke-width="5"
                                            class="transition-all duration-300 hover:filter hover:brightness-105"
                                            filter="url(#shadow)" />
                                        <path d="M20 100 A 80 80 0 0 1 180 100" fill="none" stroke="#151519"
                                            stroke-width="5" stroke-dasharray="188 251"
                                            class="transition-all duration-300 hover:filter hover:brightness-105"
                                            filter="url(#shadow)" />

                                        <!-- Define shadow filter -->
                                        <defs>
                                            <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                                                <feDropShadow dx="0" dy="1" stdDeviation="2" flood-opacity="0.1" />
                                            </filter>
                                        </defs>
                                    </svg>
                                    <div class="absolute inset-0 flex flex-col items-center justify-center"
                                        style="padding-top: 70px;">
                                        <div class="rounded-full p-1.5 mb-3 border border-gray-300">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-blend">
                                                <circle cx="9" cy="9" r="7" />
                                                <circle cx="15" cy="15" r="7" />
                                            </svg>
                                        </div>
                                        <span class="text-lg font-semibold ">2,324</span>
                                        <p class="text-xs mt-1">Total tips</p>
                                    </div>
                                </div>
                                <div class="flex justify-between w-full">
                                    <div class="flex items-center">
                                        <!-- Add an ID for the first legend bar -->
                                        <div id="tipLegendTipped" class="w-1 h-10 rounded-full mr-2"
                                            style="background-color: #9ca3af;"></div>
                                        <div>
                                            <span class="text-sm font-semibold ">1,809</span>
                                            <p class="text-xs ">Tipped users</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-end">
                                        <div class="text-right mr-2">
                                            <span class="text-sm font-semibold ">515</span>
                                            <p class="text-xs ">Non tipped users</p>
                                        </div>
                                        <!-- Add an ID for the second legend bar -->
                                        <div id="tipLegendNonTipped" class="w-1 h-10 rounded-full"
                                            style="background-color: #ABBDD3;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <style>
                        @keyframes fillAnimation {
                            0% {
                                stroke-dasharray: 0 251;
                            }

                            100% {
                                stroke-dasharray: 188 251;
                            }
                        }

                        svg path:nth-child(3) {
                            animation: fillAnimation 1.5s ease-out forwards;
                        }
                    </style>

                    <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            const ctx = document.getElementById('salesDistribution').getContext('2d');
                            new Chart(ctx, {
                                type: 'doughnut',
                                data: {
                                    datasets: [{
                                        data: [45, 30, 25],
                                        backgroundColor: ['#4A90E2', '#7ED321', '#F5A623'],
                                        borderWidth: 0,
                                        cutout: '80%',
                                        circumference: 180,
                                        rotation: -90
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    plugins: {
                                        legend: {
                                            display: false
                                        },
                                        tooltip: {
                                            enabled: false
                                        }
                                    }
                                }
                            });
                        });
                    </script>

                    <!-- ==============================================================TOTAL REVIEWS CHART =========================================================================== -->

            </main>
        </div>
    </div>
    </div>

    <!-- Include Flatpickr library -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize Flatpickr for both date inputs
            flatpickr("#startDate", {
                dateFormat: "Y-m-d",
                onChange: function (selectedDates) {
                    // Set the minimum date for the end date picker
                    const endDatePicker = flatpickr("#endDate");
                    endDatePicker.set('minDate', selectedDates[0]);
                }
            });
            flatpickr("#endDate", {
                dateFormat: "Y-m-d",
                onChange: function (selectedDates) {
                    // Set the maximum date for the start date picker
                    const startDatePicker = flatpickr("#startDate");
                    startDatePicker.set('maxDate', selectedDates[0]);
                }
            });

            // Toggle date range picker visibility
            document.getElementById('dateRangeButton').addEventListener('click', function () {
                const dateRangePicker = document.getElementById('dateRangePicker');
                dateRangePicker.classList.toggle('hidden');
            });

            // Close the date range picker
            document.getElementById('closePicker').addEventListener('click', function () {
                document.getElementById('dateRangePicker').classList.add('hidden');
            });

            // Apply date range
            document.getElementById('applyDateRange').addEventListener('click', function () {
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                if (startDate && endDate) {
                    document.getElementById('dateRangeText').textContent = `${startDate} - ${endDate}`;
                    document.getElementById('dateRangePicker').classList.add('hidden');
                } else {
                    alert('Please select both dates.');
                }
            });
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://kit.fontawesome.com/c5d6c4974.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/css/flag-icon.min.css"
        integrity="sha512-rRkkH/qCvU95u1uPId/ Schw9iwRqF5d85" crossorigin="anonymous" />

    <script>
        let visibilityTimeout;
        let fetchInterval;
        let currentRequest;

        function fetchData() {
            if (currentRequest && currentRequest.readyState !== 4) {
                currentRequest.abort();
            }

            currentRequest = $.ajax({
                url: '/fetch-data',
                method: 'GET',
                success: function (data) {
                    if (data.length > 0) {
                        const totalReservations = data[0]['Total reservations'];
                        const totalRevenue = data[0]['Total revenue'];
                        const conversationOutsideBusinessHours = data[0]['Conversation Outside Business Hours'];
                        const queriesLeftToAnswer = data[0]['Queries Left to Answer'];

                        document.querySelector('#total-revenue').textContent = '£' + totalRevenue;
                        document.querySelector('#total-reservations').textContent = totalReservations;
                        document.querySelector('#conversation-outside-business-hours').textContent = '£' + conversationOutsideBusinessHours;
                        document.querySelector('#queries-left-to-answer').textContent = queriesLeftToAnswer;
                    }
                },
                error: function (error) {
                    console.error('Error fetching data:', error);
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function () {
            fetchData();

            // Check if dark mode is active
            const isPureBlack = document.body.classList.contains('pure-black');

            // Set grid color based on theme - use white with opacity for dark mode
            const gridColor = isPureBlack ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

            const newSignupsData = {
                labels: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
                datasets: [
                    {
                        label: 'New Guests',
                        data: [60, 70, 35, 62, 58, 65, 55],
                        backgroundColor: '#e21d48',
                        borderRadius: {
                            topLeft: 5,
                            topRight: 5,
                            bottomLeft: 5,
                            bottomRight: 5
                        },
                        borderSkipped: false
                    },
                    {
                        label: 'Returning Guests',
                        data: [20, 40, 67, 41, 57, 45, 39],
                        backgroundColor: '#5ea5f6',
                        borderRadius: {
                            topLeft: 5,
                            topRight: 5,
                            bottomLeft: 5,
                            bottomRight: 5
                        },
                        borderSkipped: false
                    }
                ]
            };

            const maxValue = Math.max(...newSignupsData.datasets.flatMap(d => d.data));
            const stepSize = Math.ceil(maxValue / 5);
            const maxTick = Math.ceil(maxValue / stepSize) * stepSize;

            const newSignupsChart = new Chart(document.getElementById('newSignupsChart'), {
                type: 'bar',
                data: newSignupsData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: false,
                            grid: { display: false },
                            ticks: { color: '#555555' }
                        },
                        y: {
                            stacked: false,
                            beginAtZero: true,
                            grid: {
                                color: gridColor, // Theme-aware grid color
                                borderDash: [5, 5]
                            },
                            ticks: {
                                stepSize: stepSize,
                                max: maxTick,
                                color: '#555555'
                            }
                        }
                    },
                    elements: {
                        bar: {
                            borderRadius: {
                                bottomLeft: 0,
                                bottomRight: 0
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false,
                            external: createCustomTooltipForTips
                        }
                    },
                    barPercentage: 0.9,
                    categoryPercentage: 0.9
                }
            });

            // Add theme change observer to update grid colors when theme changes
            const observer = new MutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const isDarkMode = document.body.classList.contains('pure-black');
                        const newGridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

                        // Update grid color
                        newSignupsChart.options.scales.y.grid.color = newGridColor;
                        newSignupsChart.update();
                    }
                });
            });

            observer.observe(document.body, {
                attributes: true
            });

            function resizeChart() {
                const container = document.querySelector('.h-[300px]');
                if (container) {
                    newSignupsChart.resize(container.offsetWidth, container.offsetHeight);
                }
            }

            window.addEventListener('resize', resizeChart);
            resizeChart(); // Initial resize
        });
    </script>
    <script>
        function updateTipAnalyticsColors() {
            const isPureBlack = document.body.classList.contains('pure-black');

            // Select the specific SVG in the Tip Analytics section
            const tipAnalyticsSVG = document.querySelector('.col-span-1 .rounded-lg .relative svg');

            if (!tipAnalyticsSVG) {
                console.log('Tip Analytics SVG not found');
                return;
            }

            // Select the SVG paths within this specific SVG
            const greyStylingCircle = tipAnalyticsSVG.querySelector('path:nth-child(1)');
            const backgroundSemiCircle = tipAnalyticsSVG.querySelector('path:nth-child(2)');
            const foregroundSemiCircle = tipAnalyticsSVG.querySelector('path:nth-child(3)');

            if (!greyStylingCircle || !backgroundSemiCircle || !foregroundSemiCircle) {
                console.log('Some SVG paths not found in Tip Analytics chart');
                return;
            }

            if (isPureBlack) {
                // Pure black theme - use lighter colors
                greyStylingCircle.setAttribute('stroke', '#27272A'); // Darker background
                backgroundSemiCircle.setAttribute('stroke', '#71717A'); // Mid-tone gray
                foregroundSemiCircle.setAttribute('stroke', '#fafafa'); // White foreground
            } else {
                // Default theme - use original colors
                greyStylingCircle.setAttribute('stroke', '#F3F4F6');
                backgroundSemiCircle.setAttribute('stroke', '#9ca3af');
                foregroundSemiCircle.setAttribute('stroke', '#151519');
            }

            // Update Tip Analytics legend colors to match the chart's stroke colors
            const tipLegendTipped = document.getElementById('tipLegendTipped');
            const tipLegendNonTipped = document.getElementById('tipLegendNonTipped');
            if (tipLegendTipped && tipLegendNonTipped) {
                tipLegendTipped.style.backgroundColor = isPureBlack ? '#fafafa' : '#151519';
                tipLegendNonTipped.style.backgroundColor = isPureBlack ? '#71717A' : '#9ca3af';
            }

            console.log('Tip Analytics semi-circle and legend colors updated:',
                isPureBlack ? 'Pure black theme' : 'Default theme');
        }

        // Call the function when the page loads
        document.addEventListener('DOMContentLoaded', function () {
            setTimeout(() => {
                updateTipAnalyticsColors();
            }, 300); // Small delay to ensure SVG is rendered

            // Watch for theme changes
            const observer = new MutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        setTimeout(() => {
                            updateTipAnalyticsColors();
                        }, 100); // Brief timeout to ensure theme is fully applied
                    }
                });
            });

            observer.observe(document.body, {
                attributes: true
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Get the dropdown toggle buttons
            const toggleTipButton = document.getElementById('toggle-chart-btn');
            const toggleRevenueButton = document.getElementById('revenue-filter-btn');
            const toggleNationalityButton = document.getElementById('nationality-filter-btn');

            // Get all dropdown options
            const tipOptions = document.querySelectorAll('.review-option-1');
            const revenueOptions = document.querySelectorAll('.revenue-option');
            const nationalityOptions = document.querySelectorAll('.nationality-option');

            // Get all dropdowns
            const dropdowns = document.querySelectorAll('.uk-drop');

            // Initialize all dropdowns
            dropdowns.forEach(dropdownElement => {
                const dropdown = UIkit.dropdown(dropdownElement);

                // Store the dropdown instance on the element
                dropdownElement.ukDropdown = dropdown;
            });

            // Close dropdown when an option is clicked for tip options
            tipOptions.forEach(option => {
                option.addEventListener('click', function (event) {
                    event.preventDefault();  // Prevent the page from scrolling to the top
                    const button = document.getElementById('toggle-chart-btn');
                    if (button && button.childNodes.length > 0 && button.childNodes[0].nodeType === Node.TEXT_NODE) {
                        button.childNodes[0].nodeValue = this.textContent.trim() + " "; // Update button text
                    }
                    const dropdown = option.closest('.uk-drop').ukDropdown;
                    if (dropdown) dropdown.hide(false); // Close the dropdown instantly
                });
            });

            // Close dropdown when an option is clicked for revenue options
            revenueOptions.forEach(option => {
                option.addEventListener('click', function (event) {
                    event.preventDefault();  // Prevent the page from scrolling to the top
                    const button = document.getElementById('revenue-filter-btn');
                    if (button && button.childNodes.length > 0 && button.childNodes[0].nodeType === Node.TEXT_NODE) {
                        button.childNodes[0].nodeValue = this.textContent.trim() + " "; // Update button text
                    }
                    const dropdown = option.closest('.uk-drop').ukDropdown;
                    if (dropdown) dropdown.hide(false); // Close the dropdown instantly
                });
            });

            // Close dropdown when an option is clicked for nationality options
            nationalityOptions.forEach(option => {
                option.addEventListener('click', function (event) {
                    event.preventDefault();  // Prevent the page from scrolling to the top
                    const button = document.getElementById('nationality-filter-btn');
                    if (button && button.childNodes.length > 0 && button.childNodes[0].nodeType === Node.TEXT_NODE) {
                        button.childNodes[0].nodeValue = this.textContent.trim() + " "; // Update button text
                    }
                    const dropdown = option.closest('.uk-drop').ukDropdown;
                    if (dropdown) dropdown.hide(false); // Close the dropdown instantly
                });
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Add dropdown-content class to all the specific dropdowns
            const dropdowns = document.querySelectorAll('.uk-drop.uk-dropdown');
            dropdowns.forEach(function (dropdown) {
                dropdown.classList.add('dropdown-content');
            });

            // Ultra-fast animations
            if (typeof UIkit !== 'undefined') {
                // Show animation - make it instantaneous
                UIkit.util.on('.dropdown-content', 'beforeshow', function () {
                    // Set transform origin and immediately show
                    this.style.transformOrigin = 'top right';
                    this.setAttribute('data-state', 'open');
                });

                // Hide animation - almost instantaneous
                UIkit.util.on('.dropdown-content', 'beforehide', function () {
                    this.setAttribute('data-state', '');
                    // Removed the logic that was causing a delay and re-triggering an animated hide.
                    // Now, if hide(false) is called, it will proceed instantly.
                });
            }
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const bookingList = document.getElementById('booking-platform-list');
            let draggedItem = null;
            let placeholderLine = null;

            if (bookingList) {
                bookingList.addEventListener('dragstart', function (e) {
                    if (e.target.classList.contains('booking-container')) {
                        draggedItem = e.target;
                        // Timeout to allow the browser to capture the drag image before applying 'dragging' styles
                        setTimeout(() => {
                            draggedItem.classList.add('dragging');
                        }, 0);
                        e.dataTransfer.effectAllowed = 'move';
                        e.dataTransfer.setData('text/plain', null); // Necessary for Firefox

                        // Create placeholder
                        placeholderLine = document.createElement('div');
                        placeholderLine.className = 'drag-over-placeholder-line';
                    }
                });

                bookingList.addEventListener('dragend', function (e) {
                    if (draggedItem) {
                        draggedItem.classList.remove('dragging');
                        draggedItem = null;
                        if (placeholderLine && placeholderLine.parentNode) {
                            placeholderLine.parentNode.removeChild(placeholderLine);
                        }
                        placeholderLine = null;
                    }
                });

                bookingList.addEventListener('dragover', function (e) {
                    e.preventDefault(); // Necessary to allow dropping
                    if (!draggedItem || !placeholderLine) return;
                    e.dataTransfer.dropEffect = 'move';

                    const afterElement = getDragAfterElement(bookingList, e.clientY);
                    if (afterElement) {
                        bookingList.insertBefore(placeholderLine, afterElement);
                    } else {
                        bookingList.appendChild(placeholderLine);
                    }
                });

                bookingList.addEventListener('dragleave', function(e) {
                    // Remove placeholder if mouse leaves the container entirely
                    if (placeholderLine && placeholderLine.parentNode && e.target === bookingList && !bookingList.contains(e.relatedTarget)) {
                         placeholderLine.parentNode.removeChild(placeholderLine);
                    }
                });

                bookingList.addEventListener('drop', function (e) {
                    e.preventDefault();
                    if (!draggedItem) return;

                    if (placeholderLine && placeholderLine.parentNode) {
                        // Insert the dragged item where the placeholder is
                        placeholderLine.parentNode.insertBefore(draggedItem, placeholderLine);
                        // Then remove the placeholder
                        placeholderLine.parentNode.removeChild(placeholderLine);
                    }
                    // Note: draggedItem class removal and nullification is handled in dragend
                });
            }

            function getDragAfterElement(container, y) {
                const draggableElements = [...container.querySelectorAll('.booking-container:not(.dragging)')];
                return draggableElements.reduce((closest, child) => {
                    const box = child.getBoundingClientRect();
                    const offset = y - box.top - box.height / 2;
                    if (offset < 0 && offset > closest.offset) {
                        return { offset: offset, element: child };
                    } else {
                        return closest;
                    }
                }, { offset: Number.NEGATIVE_INFINITY }).element;
            }
        });
    </script>
    <script>
        // Generic helper function for horizontal drag and drop
        function getHorizontalDragAfterElement(container, x, draggableSelector) {
            const draggableElements = [...container.querySelectorAll(`.${draggableSelector}:not(.dragging)`)];
            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = x - (box.left + box.width / 2);
                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }

        // Drag and drop for the PMS analytics cards (Guest Nationalities, Total Revenue, Tip Analytics)
        document.addEventListener('DOMContentLoaded', function () {
            const pmsAnalyticsCardsContainer = document.getElementById('pms-analytics-cards-container');
            let draggedPmsItem = null;
            let currentIndicatorTarget = null;
            // let indicatorType = ''; // 'before' or 'after', if needed for complex logic

            if (pmsAnalyticsCardsContainer) {
                pmsAnalyticsCardsContainer.addEventListener('dragstart', function (e) {
                    // Ensure the event target is one of the draggable cards
                    if (e.target.classList.contains('pms-draggable-card')) {
                        draggedPmsItem = e.target;
                        setTimeout(() => {
                            if (draggedPmsItem) draggedPmsItem.classList.add('dragging');
                        }, 0);
                        e.dataTransfer.effectAllowed = 'move';
                        e.dataTransfer.setData('text/plain', e.target.id || 'dragged-pms-card');
                    }
                });

                pmsAnalyticsCardsContainer.addEventListener('dragend', function (e) {
                    if (draggedPmsItem) {
                        draggedPmsItem.classList.remove('dragging');
                    }
                    if (currentIndicatorTarget) {
                        currentIndicatorTarget.classList.remove('pms-drop-indicator-before', 'pms-drop-indicator-after');
                    }
                    draggedPmsItem = null;
                    currentIndicatorTarget = null;
                    // indicatorType = '';
                });

                pmsAnalyticsCardsContainer.addEventListener('dragover', function (e) {
                    e.preventDefault(); // Necessary to allow dropping
                    if (!draggedPmsItem) return;

                    const afterElement = getHorizontalDragAfterElement(pmsAnalyticsCardsContainer, e.clientX, 'pms-draggable-card');

                    // Clear previous indicator
                    if (currentIndicatorTarget) {
                        currentIndicatorTarget.classList.remove('pms-drop-indicator-before', 'pms-drop-indicator-after');
                        currentIndicatorTarget = null;
                        // indicatorType = '';
                    }

                    // Apply new indicator
                    if (afterElement && afterElement !== draggedPmsItem) {
                        afterElement.classList.add('pms-drop-indicator-before');
                        currentIndicatorTarget = afterElement;
                        // indicatorType = 'before';
                    } else if (!afterElement) { // Dropping at the end
                        const draggables = [...pmsAnalyticsCardsContainer.querySelectorAll('.pms-draggable-card:not(.dragging)')];
                        if (draggables.length > 0) {
                            const lastElement = draggables[draggables.length - 1];
                            if (lastElement !== draggedPmsItem) {
                                lastElement.classList.add('pms-drop-indicator-after');
                                currentIndicatorTarget = lastElement;
                                // indicatorType = 'after';
                            }
                        }
                    }
                });

                pmsAnalyticsCardsContainer.addEventListener('drop', function (e) {
                    e.preventDefault();
                    if (!draggedPmsItem) {
                        return;
                    }

                    // Clear indicator before dropping
                    if (currentIndicatorTarget) {
                        currentIndicatorTarget.classList.remove('pms-drop-indicator-before', 'pms-drop-indicator-after');
                        // currentIndicatorTarget = null; // Will be reset in dragend
                        // indicatorType = '';
                    }

                    // Use the same logic as dragover to find the final drop position
                    const finalAfterElement = getHorizontalDragAfterElement(pmsAnalyticsCardsContainer, e.clientX, 'pms-draggable-card');
                    if (finalAfterElement) {
                        if (draggedPmsItem !== finalAfterElement) { // Avoid inserting before itself
                            pmsAnalyticsCardsContainer.insertBefore(draggedPmsItem, finalAfterElement);
                        }
                    } else {
                        pmsAnalyticsCardsContainer.appendChild(draggedPmsItem);
                    }
                    // draggedPmsItem.classList.remove('dragging'); // Handled in dragend
                });
            }
        });

        // Drag and drop for the PMS metric cards (Lifetime Revenue, Total Reservations, etc.)
        document.addEventListener('DOMContentLoaded', function () {
            const pmsMetricCardsContainer = document.getElementById('pms-metric-cards-row-container');
            let draggedPmsMetricItem = null;
            let currentMetricIndicatorTarget = null;

            if (pmsMetricCardsContainer) {
                pmsMetricCardsContainer.addEventListener('dragstart', function (e) {
                    if (e.target.classList.contains('pms-metric-card')) {
                        draggedPmsMetricItem = e.target;
                        setTimeout(() => {
                            if (draggedPmsMetricItem) draggedPmsMetricItem.classList.add('dragging');
                        }, 0);
                        e.dataTransfer.effectAllowed = 'move';
                        e.dataTransfer.setData('text/plain', e.target.id || 'dragged-pms-metric-card');
                    }
                });

                pmsMetricCardsContainer.addEventListener('dragend', function (e) {
                    if (draggedPmsMetricItem) {
                        draggedPmsMetricItem.classList.remove('dragging');
                    }
                    if (currentMetricIndicatorTarget) {
                        currentMetricIndicatorTarget.classList.remove('pms-metric-drop-indicator-before', 'pms-metric-drop-indicator-after');
                    }
                    draggedPmsMetricItem = null;
                    currentMetricIndicatorTarget = null;
                });

                pmsMetricCardsContainer.addEventListener('dragover', function (e) {
                    e.preventDefault();
                    if (!draggedPmsMetricItem) return;

                    const afterElement = getHorizontalDragAfterElement(pmsMetricCardsContainer, e.clientX, 'pms-metric-card');

                    if (currentMetricIndicatorTarget) {
                        currentMetricIndicatorTarget.classList.remove('pms-metric-drop-indicator-before', 'pms-metric-drop-indicator-after');
                        currentMetricIndicatorTarget = null;
                    }

                    if (afterElement && afterElement !== draggedPmsMetricItem) {
                        afterElement.classList.add('pms-metric-drop-indicator-before');
                        currentMetricIndicatorTarget = afterElement;
                    } else if (!afterElement) {
                        const draggables = [...pmsMetricCardsContainer.querySelectorAll('.pms-metric-card:not(.dragging)')];
                        if (draggables.length > 0) {
                            const lastElement = draggables[draggables.length - 1];
                            if (lastElement !== draggedPmsMetricItem) {
                                lastElement.classList.add('pms-metric-drop-indicator-after');
                                currentMetricIndicatorTarget = lastElement;
                            }
                        }
                    }
                });

                pmsMetricCardsContainer.addEventListener('drop', function (e) {
                    e.preventDefault();
                    if (!draggedPmsMetricItem) return;

                    if (currentMetricIndicatorTarget) {
                        currentMetricIndicatorTarget.classList.remove('pms-metric-drop-indicator-before', 'pms-metric-drop-indicator-after');
                    }
                    const finalAfterElement = getHorizontalDragAfterElement(pmsMetricCardsContainer, e.clientX, 'pms-metric-card');
                    if (finalAfterElement) {
                        if (draggedPmsMetricItem !== finalAfterElement) {
                            pmsMetricCardsContainer.insertBefore(draggedPmsMetricItem, finalAfterElement);
                        }
                    } else {
                        pmsMetricCardsContainer.appendChild(draggedPmsMetricItem);
                    }
                });
            }
        });
    </script>
</body>

</html>