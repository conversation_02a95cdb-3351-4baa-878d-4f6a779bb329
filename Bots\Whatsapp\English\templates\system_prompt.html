<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI System Prompt Editor</title>
    <style>
        :root {
            --primary-color: #3a6ea5;
            --secondary-color: #004e98;
            --accent-color: #ff6b6b;
            --background-color: #f8f9fa;
            --text-color: #333;
            --border-color: #ddd;
            --success-color: #28a745;
            --error-color: #dc3545;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        header {
            margin-bottom: 30px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 20px;
        }

        h1 {
            color: var(--primary-color);
            font-size: 28px;
            margin-bottom: 10px;
        }

        .description {
            color: #666;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--secondary-color);
        }

        textarea {
            width: 100%;
            min-height: 400px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
        }

        textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(58, 110, 165, 0.2);
        }

        .button-group {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
        }

        .btn-secondary {
            background-color: #f1f1f1;
            color: #333;
        }

        .btn-secondary:hover {
            background-color: #e1e1e1;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
            display: none;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .alert-error {
            background-color: rgba(220, 53, 69, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        footer {
            margin-top: 40px;
            text-align: center;
            color: #777;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>AI System Prompt Editor</h1>
            <p class="description">
                Edit the system prompt used by the AI agent to control its behavior and responses.
                Changes will be applied after clicking the "Save Changes" button.
            </p>
        </header>

        <div id="alert-success" class="alert alert-success">
            Changes saved successfully! The AI agent will now use the updated system prompt.
        </div>
        
        <div id="alert-error" class="alert alert-error">
            An error occurred while saving changes. Please try again.
        </div>

        <form id="promptForm">
            <div class="form-group">
                <label for="systemPrompt">System Prompt:</label>
                <textarea id="systemPrompt" name="systemPrompt">{{ system_prompt }}</textarea>
            </div>

            <div class="button-group">
                <button type="button" id="resetBtn" class="btn-secondary">Reset</button>
                <button type="submit" id="saveBtn" class="btn-primary">Save Changes</button>
            </div>
        </form>

        <footer>
            <p>Guest Genius AI System Configuration</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('promptForm');
            const systemPromptTextarea = document.getElementById('systemPrompt');
            const resetBtn = document.getElementById('resetBtn');
            const alertSuccess = document.getElementById('alert-success');
            const alertError = document.getElementById('alert-error');
            
            // Store the original prompt for reset functionality
            const originalPrompt = systemPromptTextarea.value;
            
            // Reset button functionality
            resetBtn.addEventListener('click', function() {
                systemPromptTextarea.value = originalPrompt;
                hideAlerts();
            });
            
            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                hideAlerts();
                
                const promptText = systemPromptTextarea.value;
                
                // Send the updated prompt to the server
                fetch('/update_system_prompt', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ system_prompt: promptText }),
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        showAlert(alertSuccess);
                    } else {
                        showAlert(alertError);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert(alertError);
                });
            });
            
            function hideAlerts() {
                alertSuccess.style.display = 'none';
                alertError.style.display = 'none';
            }
            
            function showAlert(alertElement) {
                alertElement.style.display = 'block';
                setTimeout(() => {
                    alertElement.style.display = 'none';
                }, 5000);
            }
        });
    </script>
</body>
</html>
