<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../static/styles/custom.css">
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    {%include 'imports.html'%}
    <style>
        .sidebar-transition {
            transition: width 0.3s ease, padding 0.3s ease;
        }
        .menu-item-transition {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .sidebar-label {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .hidden {
            transform: translateX(-10px);
        }
        .opacity-0 {
            opacity: 0;
        }

        /* Submenu styles */
        .submenu {
            margin-left: 28px;
            display: none;
        }
        
        .submenu.active {
            display: block;
        }
        
        .submenu-item {
            padding: 6px 12px;
            font-size: 14px;
            display: block;
            transition: color 0.2s ease;
        }
        
        .submenu-item:hover {
            color: var(--accent-foreground);
        }
        
        /* Category header styles */
        .category-header {
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
            margin-top: 16px;
            margin-bottom: 8px;
            padding-left: 12px;
        }
        
        /* Chevron icon styles */
        .chevron {
            transition: transform 0.3s ease;
        }
        
        .chevron.down {
            transform: rotate(90deg);
        }

        /* Center icons when sidebar is collapsed */
        .w-20 .sidebar-link {
            justify-content: center;
        }
        /* Adjust the logo container */
        .logo-container {
            transition: all 0.3s ease;
        }
        .w-20 .logo-container {
            justify-content: center;
        }
        
        .grid {
            transition: grid-template-columns 0.3s ease;
        }
        .grid.collapsed {
            grid-template-columns: 60px 1fr;
        }
    
        /* Hide scrollbar for sidebar */
        #sidebar::-webkit-scrollbar {
            display: none;
        }
        #sidebar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        #logo-img {
            min-width: 40px !important;
            min-height: 40px !important;
        }
        .logo-image.collapsed {
            width: 20px;
            height: 20px;
        }

        /* Custom styles for toggle button positioning */
        #toggle-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            /* padding: 0.8rem; */
        }

        /* Update label color */
        .text-gray-500 {
            color: rgb(107 114 128) !important;
            font-size: 0.75rem !important;
        }

        /* Ensure existing scrollbar hiding styles are working */
        #sidebar::-webkit-scrollbar,
        .flex-1::-webkit-scrollbar {
            display: none;
            width: 0;
        }

        /* Sidebar link styles */
        .sidebar-link {
            position: relative;
            transition: background-color 0.2s ease;
        }

        .sidebar-link:hover {
            background-color: var(--dropdown-hover-bg);
            color: var(--accent-foreground);
        }

        .sidebar-link.active {
            background-color: var(--dropdown-hover-bg);
            color: var(--accent-foreground);
            font-weight: 500;
            box-shadow: inset 3px 0 0 var(--primary); /* subtle left border */
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
            transform: scale(1.02);
        }

        .sidebar-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background-color: var(--primary);
            border-radius: 0 4px 4px 0;
        }

        /* Adjust spacing around section headers */
        nav .flex-col > div:not(:first-child) {
            margin-top: 8px;
        }
        
        
        #sidebar {
            position: sticky;
            top: 0;
            height: calc(100vh - 20px);
            /* Force full viewport height */
            overflow-y: auto;
            /* Allow scrolling within the sidebar */
            -ms-overflow-style: none;
            scrollbar-width: none;
            padding: 10px;
            background-color: transparent;
            border-right: 1px solid #e5e7eb;
        }

        .sidebar-transition {
            transition: width 0.3s ease, padding 0.3s ease;
        }
        .menu-item-transition {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .sidebar-label {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .hidden {
            transform: translateX(-10px);
        }
        .opacity-0 {
            opacity: 0;
        }

        /* Submenu styles */
        .submenu {
            margin-left: 28px;
            display: none;
            position: relative;
        }
        
/* Add vertical divider for submenu */
.submenu.active::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    width: 1px;
    background-color: var(--dropdown-text);
    opacity: 0.3;
    border-radius: 2px;
    z-index: 1;
}

/* Hover effect for the divider */
    </style>
</head>
<body class="bg-background text-foreground">
    <div id="sidebar" class="border-r card sidebar-transition w-full">
        <div class="flex h-full flex-col">
            <!-- Header Section -->
            <div class="flex h-[50px] items-center px-2 justify-between group mb-4">
                <a class="flex items-center gap-2 font-semibold logo-container" href="#">
                    <img src="../static/images/logo.png" class="logo-image w-5" alt="Guest Genius Logo" id="logo-img">
                    <span class="text-base sidebar-label" id="brand-text">Guest Genius</span>
                </a>
            </div>
            
            <!-- Navigation -->
            <div class="flex-1 overflow-auto">
                <nav class="flex flex-col">
                    <!-- Platform Section -->
                    <div class="category-header">Platform</div>
                    
                    <!-- Playground with submenu -->
                    <div class="menu-group">
                        <a class="sidebar-link flex items-center justify-between rounded-md px-3 py-2 text-sm" href="#" onclick="toggleSubmenu('playground-submenu', event)">
                            <div class="flex items-center gap-1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                    <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                    <polyline points="21 15 16 10 5 21"></polyline>
                                </svg>
                                <span>Playground</span>
                            </div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>
                        </a>
                        <div id="playground-submenu" class="submenu">
                            <a href="#" class="submenu-item">History</a>
                            <a href="#" class="submenu-item">Starred</a>
                            <a href="#" class="submenu-item">Settings</a>
                        </div>
                    </div>
                    
                    <!-- Models with submenu -->
                    <div class="menu-group">
                        <a class="sidebar-link flex items-center justify-between rounded-md px-3 py-2 text-sm" href="#" onclick="toggleSubmenu('models-submenu', event)">
                            <div class="flex items-center gap-1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                                    <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                                    <line x1="12" y1="22.08" x2="12" y2="12"></line>
                                </svg>
                                <span>Models</span>
                            </div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>
                        </a>
                        <div id="models-submenu" class="submenu">
                            <a href="#" class="submenu-item">Genesis</a>
                            <a href="#" class="submenu-item">Explorer</a>
                            <a href="#" class="submenu-item">Quantum</a>
                        </div>
                    </div>
                    
                    <!-- Documentation -->
                    <a class="sidebar-link flex items-center justify-between rounded-md px-3 py-2 text-sm" href="#">
                        <div class="flex items-center gap-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
                                <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
                            </svg>
                            <span>Documentation</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </a>
                    
                    <!-- Settings -->
                    <a class="sidebar-link flex items-center justify-between rounded-md px-3 py-2 text-sm" href="#">
                        <div class="flex items-center gap-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="3"></circle>
                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                            </svg>
                            <span>Settings</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </a>
                    
                    <!-- Projects Section -->
                    <div class="category-header">Projects</div>
                    
                    <!-- Design Engineering -->
                    <a class="sidebar-link flex items-center gap-1 rounded-md px-3 py-2 text-sm" href="#">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 3v3"></path>
                            <path d="M18.5 8.5l-2.1 2.1"></path>
                            <path d="M21 12h-3"></path>
                            <path d="M3 12h3"></path>
                            <path d="M5.5 8.5l2.1 2.1"></path>
                            <path d="M12 21v-3"></path>
                            <path d="M18.5 15.5l-2.1-2.1"></path>
                            <path d="M5.5 15.5l2.1-2.1"></path>
                            <circle cx="12" cy="12" r="4"></circle>
                        </svg>
                        <span>Design Engineering</span>
                    </a>
                    
                    <!-- Sales & Marketing -->
                    <a class="sidebar-link flex items-center gap-1 rounded-md px-3 py-2 text-sm" href="#">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 2v4"></path>
                            <path d="M12 18v4"></path>
                            <path d="M4.93 4.93l2.83 2.83"></path>
                            <path d="M16.24 16.24l2.83 2.83"></path>
                            <path d="M2 12h4"></path>
                            <path d="M18 12h4"></path>
                            <path d="M4.93 19.07l2.83-2.83"></path>
                            <path d="M16.24 7.76l2.83-2.83"></path>
                        </svg>
                        <span>Sales & Marketing</span>
                    </a>
                    
                    <!-- Travel -->
                    <a class="sidebar-link flex items-center gap-1 rounded-md px-3 py-2 text-sm" href="#">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                        </svg>
                        <span>Travel</span>
                    </a>
                    
                    <!-- More -->
                    <a class="sidebar-link flex items-center gap-1 rounded-md px-3 py-2 text-sm" href="#">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="1"></circle>
                            <circle cx="19" cy="12" r="1"></circle>
                            <circle cx="5" cy="12" r="1"></circle>
                        </svg>
                        <span>More</span>
                    </a>
                </nav>
            </div>
        </div>
    </div>
    
    <script>
        function toggleSubmenu(submenuId, event) {
            event.preventDefault();
            const submenu = document.getElementById(submenuId);
            const chevron = event.currentTarget.querySelector('.chevron');
            
            if (submenu.classList.contains('active')) {
                submenu.classList.remove('active');
                chevron.classList.remove('down');
            } else {
                submenu.classList.add('active');
                chevron.classList.add('down');
            }
        }
    </script>
</body>