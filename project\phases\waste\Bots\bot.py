from flask import Flask, request, jsonify
from twilio.twiml.messaging_response import MessagingResponse
from twilio.rest import Client
import json
import re
from datetime import datetime
from time import sleep
from prompt import system_prompt  # Import the system prompt
from groq import Groq  # Import Groq for AI responses
import os

# ------------------------------ Constants ------------------------------

# Twilio Configuration
ACCOUNT_SID = "**********************************"
AUTH_TOKEN = "005d910f85546392a91f58a3878c437c"
FROM_WHATSAPP_NUMBER = "whatsapp:+***********"
TO_WHATSAPP_NUMBER = "whatsapp:+************"

# Twilio Content SIDs
STARTER_OPTION_SELECTOR_SID = "HX8401c4d930c7e9759d04dc1b6ffbee1b"
FOOD_ORDER_OPTION_SID = "HX7a25af2c3272d417594751769843eb10"
BEVERAGE_ORDER_OPTION_SID = "HX2c677e28821bc1a644f994b406724995"
MASSAGE_SERVICE_OPTION_SID = "HX1cd65c8409a60ae217d719c486818ce6"
SPA_BOOKING_SID = "HX480d34248555c778b10e0634e0996eec"
SWEET_WINE_OPTION_SID = "HX97b6cceff9200fa4a10c5a938f8224fb"
ALCOHOLIC_BEVERAGES_OPTION_SID = "HXea619ac8554b6d334b0eb7799135c7ba"
SOFT_DRINKS_OPTION_SID = "HXe7d674074cb5b5532e6c19c28b2bb826"
BEER_OPTIONS_SID = "HXf6fb658a9b49e08edca17af78a22a1c2"
SIGNATURE_MOCKTAILS_OPTION_SID = "HXaa8ce591a5dbdb661f578f84c4b1cf30"
SPA_DATE_TIME_SELECTOR_SID = "HX291b469365c8cbeac916dcd50261a0ea"
DATE_TIME_SELECTOR_SID = "HXd842a9216b1b3aad23dccfe555ba87d6"
ROOM_BOOKING_CONFIRMATION_SID = 'HX4ade4714876d858178ab241d5bd6a34e'
MASSAGE_ORDER_CONFIRMATION_SID = "HX747bc731f2315a49d178d755aac67866"

# File Paths
CHATS_FILE_PATH = 'chats.json'
USERS_FILE_PATH = 'users.json'

# Messages
WELCOME_MESSAGE = "Welcome to Can Marques! How may I assist you today?"
REQUEST_ROOM_MESSAGE = (
    "Hello, welcome to Can Marques!\nPlease enter your room number before proceeding."
)
ROOM_NUMBER_SAVED_MESSAGE = "Your room number has been saved. How may I assist you today?"

# Order Confirmations
FOOD_ORDER_CONFIRMATION = (
    "Good choice. Your order has been registered.\n"
    "Please make the payment here: https://guestgenius.es/foodorder.\n"
    "Your dish will be delivered shortly once you complete the payment."
)
BEVERAGE_ORDER_CONFIRMATION = (
    "Good choice. Your order has been registered.\n"
    "Please make the payment here: https://guestgenius.es/beverageorder.\n"
    "Your drink will be delivered shortly once you complete the payment."
)
MASSAGE_ORDER_CONFIRMATION = (
    "Good choice. Your order has been registered.\n"
    "Please make the payment here: https://guestgenius.es/massageorder.\n"
    "Your massage will be scheduled shortly once you complete the payment."
)
SPA_ORDER_CONFIRMATION = (
    "Good choice. Your order has been registered.\n"
    "Please make the payment here: https://guestgenius.es/spaorder.\n"
    "Your spa will be scheduled shortly once you complete the payment."
)
ROOM_BOOKING_CONFIRMATION = (
    "Good choice. Your order has been registered.\n"
    "Please make the payment here: https://guestgenius.es/roomorder.\n"
    "Your room will be reserved shortly once you complete the payment."
)

# Allergy Messages
BURRATA_ALLERGY_MESSAGE = """Here are the allergies for: 

🧀 Burrata
* Dairy (burrata cheese)
* Tree Nuts (almonds)
* Fungus (truffle)
* Pork (jamon ibérico)"""

PUMPKIN_ALLERGY_MESSAGE = """Here are the allergies for: 

🎃 Roasted Pumpkin
* Tree Nuts (possible coconut allergy)
* Dairy (if cream used in soup)
* Celery (if used in soup base)
* Sulfites (if used in preservation)
* Soy (if soy sauce used in seasoning)"""

LOBSTER_ROLL_ALLERGY_MESSAGE = """Here are the allergies for: 

🦞 Lobster Roll
* Shellfish (lobster)
* Eggs (mayonnaise)
* Dairy (butter)
* Gluten (brioche)
* Citrus (lemon, lime)"""

SIRLOIN_ALLERGY_MESSAGE = """Here are the allergies for: 

🥩 Sirloin
* Beef
* Dairy (if butter used)
* Soy (if used in marinade)
* Sulfites (if used in preparation)
* Celery (if used in seasoning)"""

CHICKEN_ALLERGY_MESSAGE = """Here are the allergies for: 

🍗 Cornfed Chicken
* Poultry
* Dairy (if used in preparation)
* Fungus (morels)
* Sulfites (if used in preservation)
* Soy (if used in marinade)"""

TBONE_ALLERGY_MESSAGE = """Here are the allergies for: 

🥩 T-bone
* Beef
* Dairy (in sauces)
* Soy (in sauces)
* Eggs (if used in sauce preparation)
* Mustard (if used in sauces)"""

STEAK_LOBSTER_ALLERGY_MESSAGE = """Here are the allergies for: 

🦞 Steak & Lobster
* Beef
* Shellfish (lobster)
* Dairy (butter)
* Gluten (if flour used in preparation)
* Sulfites (if used in preparation)"""

SALMON_ALLERGY_MESSAGE = """Here are the allergies for: 

🐟 Grilled Salmon
* Fish (salmon)
* Fish roe (salmon roe)
* Soy (miso)
* Sesame (if used in seasoning)
* Gluten (in miso)"""

# Order Messages
BURRATA_ORDER_MESSAGE = """Your order for Burrata has been confirmed

Your order details:
* Burrata with Roasted figs
* Includes jamon ibérico & truffle
* Served with watercress & toasted almonds

Estimated preparation time: 15-20 minutes"""

PUMPKIN_ORDER_MESSAGE = """Your order for our Roasted Pumpkin has been confirmed

Your order details:
* Roasted pumpkin
* Served with coconut soup

Estimated preparation time: 15-20 minutes"""

LOBSTER_ROLL_ORDER_MESSAGE = """Your order for our Lobster Roll has been confirmed

Your order details:
* Fresh lobster in buttered brioche
* With lime mayo & leek
* Served with fresh lemon

Estimated preparation time: 20-25 minutes"""

SIRLOIN_ORDER_MESSAGE = """Your order for our Sirloin has been confirmed

Your order details:
* 300g Sirloin steak
* Tender cut with juicy crackling
* Cooked to your preference

Estimated preparation time: 25-30 minutes"""

CORNFEED_CHICKEN_ORDER_MESSAGE = """Your order for our Cornfed Chicken has been confirmed

Your order details:
* Cornfed chicken breast
* Served with truffled potato
* Accompanied by spring onion and morels

Estimated preparation time: 25-30 minutes"""

TBONE_ORDER_MESSAGE = """Your order for our T-bone has been confirmed

Your order details:
* 900g T-bone steak
* Perfect for sharing
* Includes all sauces & fries

Estimated preparation time: 30-35 minutes"""

STEAK_LOBSTER_ORDER_MESSAGE = """Your order for our Steak & Lobster has been confirmed

Your order details:
* Marbled ribeye steak
* 1/2 Fresh lobster
* Served with fries, garlic butter & lemon

Estimated preparation time: 30-35 minutes"""

SALMON_ORDER_MESSAGE = """Your order for our Grilled Salmon has been confirmed

Your order details:
* Miso & honey glazed salmon
* Served with watercress & spinach
* Topped with salmon roe

Estimated preparation time: 20-25 minutes"""

# ------------------------------ Initialization ------------------------------

# Initialize Flask app
app = Flask(__name__)

# Initialize Twilio client
client = Client(ACCOUNT_SID, AUTH_TOKEN)

# Initialize Groq client
groq_client = Groq(api_key='********************************************************')  # Initialize Groq client

# ------------------------------ Helper Functions ------------------------------

def load_chats():
    """Load chats from the JSON file."""
    if not os.path.exists(CHATS_FILE_PATH):
        with open(CHATS_FILE_PATH, 'w') as file:
            json.dump({"users": [], "chats": {}}, file, indent=4)
    with open(CHATS_FILE_PATH, 'r') as file:
        return json.load(file)


def save_chats(data):
    """Save chats to the JSON file."""
    with open(CHATS_FILE_PATH, 'w') as file:
        json.dump(data, file, indent=4)


def load_users():
    """Load users from the JSON file."""
    if not os.path.exists(USERS_FILE_PATH):
        with open(USERS_FILE_PATH, 'w') as file:
            json.dump({"guests": []}, file, indent=4)
    with open(USERS_FILE_PATH, 'r') as file:
        return json.load(file)


def save_users(data):
    """Save users to the JSON file."""
    with open(USERS_FILE_PATH, 'w') as file:
        json.dump(data, file, indent=4)


def find_guest(phone_number, users_data):
    """Find a guest by phone number."""
    for guest in users_data.get('guests', []):
        if guest.get('phone_number') == phone_number:
            return guest
    return None


def is_valid_room_number(room_number):
    """Validate if the room number is a valid integer."""
    return re.fullmatch(r'\d+', room_number) is not None


def send_message(to_number, body=None, content_sid=None):
    """Send a message via Twilio."""
    try:
        if content_sid:
            message = client.messages.create(
                from_=FROM_WHATSAPP_NUMBER,
                to=to_number,
                content_sid=content_sid
            )
            print(f"Message sent to {to_number} with SID: {message.sid}")
        else:
            message = client.messages.create(
                from_=FROM_WHATSAPP_NUMBER,
                to=to_number,
                body=body
            )
            print(f"Message sent to {to_number}: {body}")
    except Exception as e:
        print(f"Error sending message to {to_number}: {str(e)}")

# ------------------------------ Service Handlers ------------------------------

# ----------- Food Service Handlers -----------

def handle_food_options(sender, button_payload):
    if button_payload == "order_food_option":
        send_message(sender, content_sid=FOOD_ORDER_OPTION_SID)
    elif button_payload in [
        "order_burrata", "order_pumpkin", "order_lobsterroll",
        "order_sirloin", "order_chicken", "order_tbone",
        "order_steaklobster", "order_salmon"
    ]:
        order_messages = {
            "order_burrata": BURRATA_ORDER_MESSAGE,
            "order_pumpkin": PUMPKIN_ORDER_MESSAGE,
            "order_lobsterroll": LOBSTER_ROLL_ORDER_MESSAGE,
            "order_sirloin": SIRLOIN_ORDER_MESSAGE,
            "order_chicken": CORNFEED_CHICKEN_ORDER_MESSAGE,
            "order_tbone": TBONE_ORDER_MESSAGE,
            "order_steaklobster": STEAK_LOBSTER_ORDER_MESSAGE,
            "order_salmon": SALMON_ORDER_MESSAGE
        }
        message_to_send = order_messages.get(button_payload)
        if message_to_send:
            send_message(sender, body=message_to_send)
            # Removed the second confirmation message to ensure only one message is sent
        else:
            print(f"No order message found for payload: {button_payload}")

def handle_food_menu_go_back(sender):
    send_message(sender, content_sid=STARTER_OPTION_SELECTOR_SID)

# ----------- Beverage Service Handlers -----------

def handle_beverage_options(sender, button_payload):
    beverage_confirmations = {
        "order_coronita": BEVERAGE_ORDER_CONFIRMATION,
        "order_mr_ginger": BEVERAGE_ORDER_CONFIRMATION,
        "order_berries_mojito": BEVERAGE_ORDER_CONFIRMATION,
        "order_summer_cooler": BEVERAGE_ORDER_CONFIRMATION,
        "order_estrella_galicia": BEVERAGE_ORDER_CONFIRMATION,
        "order_estrella_galicia_0_0": BEVERAGE_ORDER_CONFIRMATION,
        "order_peroni": BEVERAGE_ORDER_CONFIRMATION,
        "order_creative_tonic_water": BEVERAGE_ORDER_CONFIRMATION,
        "order_exotic_yuzu_sensation": BEVERAGE_ORDER_CONFIRMATION,
        "order_fever_tree_tonic": BEVERAGE_ORDER_CONFIRMATION,
        "order_zero_azucar_tonic": BEVERAGE_ORDER_CONFIRMATION
    }
    confirmation_message = beverage_confirmations.get(button_payload)
    if confirmation_message:
        send_message(sender, body=confirmation_message)

def handle_beverage_menu_go_back(sender):
    send_message(sender, content_sid=STARTER_OPTION_SELECTOR_SID)

# ----------- Spa Service Handlers -----------

def handle_spa_options(sender, button_payload):
    spa_actions = {
        "book_spa_option": SPA_DATE_TIME_SELECTOR_SID,
        "book_junior_suite": ROOM_BOOKING_CONFIRMATION_SID,
        "book_the_raid": ROOM_BOOKING_CONFIRMATION_SID,
        "book_junior_suite_deluxe": ROOM_BOOKING_CONFIRMATION_SID,
        "book_executive_suite": ROOM_BOOKING_CONFIRMATION_SID
    }
    content_sid = spa_actions.get(button_payload)
    if content_sid:
        send_message(sender, content_sid=content_sid)

def handle_spa_booking_go_back(sender):
    send_message(sender, content_sid=STARTER_OPTION_SELECTOR_SID)

# ----------- Massage Service Handlers -----------

def handle_massage_options(sender, button_payload):
    if button_payload == "massage_service_option":
        send_message(sender, content_sid=MASSAGE_SERVICE_OPTION_SID)
    elif button_payload == "massage_order_confirmation":
        send_message(sender, body=MASSAGE_ORDER_CONFIRMATION)

def handle_massage_go_back(sender):
    send_message(sender, content_sid=STARTER_OPTION_SELECTOR_SID)

# ----------- General Service Handlers -----------

def handle_general_options(sender, button_payload):
    general_actions = {
        "go_back": STARTER_OPTION_SELECTOR_SID,
        "go_back_massage": STARTER_OPTION_SELECTOR_SID,
        "spa_go_back": STARTER_OPTION_SELECTOR_SID
    }
    content_sid = general_actions.get(button_payload)
    if content_sid:
        send_message(sender, content_sid=content_sid)

# ----------- Allergy Service Handlers -----------

def handle_allergy_options(sender, button_payload):
    allergy_messages = {
        "burrata_allergies": BURRATA_ALLERGY_MESSAGE,
        "pumpkin_allergies": PUMPKIN_ALLERGY_MESSAGE,
        "lobsterroll_allergies": LOBSTER_ROLL_ALLERGY_MESSAGE,
        "sirloin_allergies": SIRLOIN_ALLERGY_MESSAGE,
        "cornfed_chicken_allergies": CHICKEN_ALLERGY_MESSAGE,
        "tbone_allergies": TBONE_ALLERGY_MESSAGE,
        "steaklobster_allergies": STEAK_LOBSTER_ALLERGY_MESSAGE,
        "salmon_allergies": SALMON_ALLERGY_MESSAGE
    }
    message_to_send = allergy_messages.get(button_payload)
    if message_to_send:
        send_message(sender, body=message_to_send)
    else:
        print(f"No allergy message found for payload: {button_payload}")

# ----------- Chat Service Handlers -----------

def handle_chat_with_staff(sender, guest, chats_data):
    send_message(sender, body="Please tell us your request.")
    user_id = str(guest.get('id'))
    if user_id not in chats_data['chats']:
        chats_data['chats'][user_id] = []
        save_chats(chats_data)
    print(f"Initialized chat with staff for {sender}")

# ------------------------------ Request Handler ------------------------------

@app.route("/", methods=['POST'])
def handle_incoming_message():
    # Get the incoming data from the request
    incoming_data = request.get_json() if request.is_json else request.form

    # Log the entire received API request
    print("Received API Request:")
    print(json.dumps(incoming_data, indent=4))

    # Extract relevant fields from the incoming data
    sender = incoming_data.get('From', '')
    message_body = incoming_data.get('Body', '').strip()
    message_type = incoming_data.get('MessageType', '').lower()
    button_payload = incoming_data.get('ButtonPayload', '')
    list_title = incoming_data.get('ListTitle', '')
    list_id = incoming_data.get('ListId', '')

    # Prevent responding to messages sent from the bot's own number
    if sender == FROM_WHATSAPP_NUMBER:
        print("Received message from the bot itself. Ignoring to prevent loop.")
        return '', 204  # No Content

    # Load users and chats data
    users_data = load_users()
    chats_data = load_chats()

    # Find the guest by phone number
    guest = find_guest(sender, users_data)

    # Initialize chats for the user if needed
    if guest:
        user_id = str(guest.get('id')) if guest.get('id') else None
        if user_id and user_id not in chats_data['chats']:
            chats_data['chats'][user_id] = []
            save_chats(chats_data)

    # ------------------------------ New User Handling ------------------------------
    if not guest:
        # Add new guest
        new_guest = {
            "phone_number": sender,
            "room_number": None
        }
        users_data['guests'].append(new_guest)
        save_users(users_data)
        # Send welcome messages
        send_message(sender, body=WELCOME_MESSAGE)
        send_message(sender, content_sid=STARTER_OPTION_SELECTOR_SID)
        return '', 204

    # ------------------------------ Room Number Handling ------------------------------
    room_number = guest.get('room_number')
    if not room_number:
        if is_valid_room_number(message_body):
            guest['room_number'] = int(message_body)
            save_users(users_data)
            send_message(sender, body=ROOM_NUMBER_SAVED_MESSAGE)
            send_message(sender, content_sid=STARTER_OPTION_SELECTOR_SID)
        else:
            send_message(sender, body=REQUEST_ROOM_MESSAGE)
        return '', 204

    # ------------------------------ Interactive Message Handling ------------------------------

    if message_type == "interactive":
        if message_body in [
            "room_service_option", "massage_service_option",
            "spa_booking_option", "sweet_wine_option",
            "alcoholic_beverages_option", "soft_drinks_option",
            "beer_options", "signature_mocktails_option",
            "book_spa_option", "room_booking_option"
        ]:
            interactive_actions = {
                "room_service_option": lambda: send_message(
                    sender,
                    content_sid="HXb9b169a221a86232e0491f9e190710bc"
                ),
                "massage_service_option": lambda: send_message(
                    sender,
                    content_sid=MASSAGE_SERVICE_OPTION_SID
                ),
                "spa_booking_option": lambda: send_message(
                    sender,
                    content_sid=SPA_BOOKING_SID
                ),
                "sweet_wine_option": lambda: send_message(
                    sender,
                    content_sid=SWEET_WINE_OPTION_SID
                ),
                "alcoholic_beverages_option": lambda: send_message(
                    sender,
                    content_sid=ALCOHOLIC_BEVERAGES_OPTION_SID
                ),
                "soft_drinks_option": lambda: send_message(
                    sender,
                    content_sid=SOFT_DRINKS_OPTION_SID
                ),
                "beer_options": lambda: send_message(
                    sender,
                    content_sid=BEER_OPTIONS_SID
                ),
                "signature_mocktails_option": lambda: send_message(
                    sender,
                    content_sid=SIGNATURE_MOCKTAILS_OPTION_SID
                ),
                "book_spa_option": lambda: send_message(
                    sender,
                    content_sid=SPA_DATE_TIME_SELECTOR_SID
                ),
                "room_booking_option": lambda: send_message(
                    sender,
                    content_sid="HXc87a9790fe9ea31f5929e49ee0785b7b"
                ),
            }
            action = interactive_actions.get(message_body)
            if action:
                action()
                print(f"Handled interactive message: {message_body}")
                return '', 204

    # ------------------------------ Button Message Handling ------------------------------
    if message_type == "button":
        if button_payload.startswith("order_"):
            # Determine the service type based on the payload
            if "food" in button_payload or button_payload in [
                "order_burrata", "order_pumpkin", "order_lobsterroll",
                "order_sirloin", "order_cornfed_chicken", "order_tbone",
                "order_steaklobster", "order_salmon"
            ]:
                handle_food_options(sender, button_payload)
            elif "beverage" in button_payload or button_payload in [
                "order_coronita", "order_mr_ginger", "order_berries_mojito",
                "order_summer_cooler", "order_estrella_galicia",
                "order_estrella_galicia_0_0", "order_peroni",
                "order_creative_tonic_water", "order_exotic_yuzu_sensation",
                "order_fever_tree_tonic", "order_zero_azucar_tonic"
            ]:
                handle_beverage_options(sender, button_payload)
            elif "spa" in button_payload or button_payload in [
                "book_junior_suite", "book_the_raid",
                "book_junior_suite_deluxe", "book_executive_suite"
            ]:
                handle_spa_options(sender, button_payload)
            else:
                # General button actions
                handle_general_options(sender, button_payload)
            return '', 204
        elif button_payload.endswith("_allergies"):
            handle_allergy_options(sender, button_payload)
            return '', 204
        elif button_payload.startswith("go_back"):
            handle_general_options(sender, button_payload)
            return '', 204

    # ------------------------------ Booking Confirmation Handling ------------------------------
    if message_type == "interactive":
        if list_title in [
            "22-10-24 (4-6 PM)", "22-10-24 (10-12 AM)",
            "21-10-24 (10-12 AM)", "22-10-24 (4-6 PM)"
        ]:
            send_message(sender, body=MASSAGE_ORDER_CONFIRMATION)
            print(f"Sent massage order confirmation to {sender}")
            return '', 204
        elif list_title in [
            "22-10-24 (2-3 PM)", "22-10-24 (3-4 PM)",
            "21-10-24 (2-3 PM)", "21-10-24 (3-4 PM)"
        ]:
            send_message(sender, body=SPA_ORDER_CONFIRMATION)
            print(f"Sent spa order confirmation to {sender}")
            return '', 204
        elif list_title in [
            "21-10-2024", "22-10-2024", "23-10-2024",
            "24-10-2024", "25-10-2024"
        ]:
            send_message(sender, content_sid=MASSAGE_ORDER_CONFIRMATION_SID)
            print(f"Sent date selection confirmation to {sender}")
            return '', 204

    if message_type == "interactive" and message_body in [
        "1_nights", "2_nights", "3_nights",
        "4_nights", "5_nights", "6_nights"
    ]:
        send_message(sender, body=ROOM_BOOKING_CONFIRMATION)
        print(f"Sent room booking confirmation to {sender}")
        return '', 204

    # ------------------------------ Chat with Staff Handling ------------------------------
    if list_id == "chat_with_staff_option" and message_body == "chat_with_staff_option":
        handle_chat_with_staff(sender, guest, chats_data)
        return '', 204

    # ------------------------------ AI Response Handling ------------------------------
    if room_number:
        if message_body.upper() == "GGTEST":
            send_message(sender, body=WELCOME_MESSAGE)
            send_message(sender, content_sid=STARTER_OPTION_SELECTOR_SID)
            print(f'Sent WELCOME_MESSAGE to {sender} due to GGTEST.')
            return '', 204
        else:
            try:
                # Prepare the messages for the AI model
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": message_body}
                ]

                # Get the AI response
                response = groq_client.chat.completions.create(
                    model="llama3-70b-8192",
                    messages=messages,
                    max_tokens=2048,
                    temperature=0.7
                )

                assistant_response = response.choices[0].message.content

                # Send the AI-generated response to the user
                send_message(sender, body=assistant_response)
                print(f'Sent AI response to {sender}.')

            except Exception as e:
                print(f"Error sending AI response to {sender}: {str(e)}")

            return '', 204

    # ------------------------------ Staff Chat Logging ------------------------------
    if guest:
        user_id = str(guest.get('id')) if guest.get('id') else None
        if user_id and user_id in chats_data['chats']:
            # Log the customer's message
            chats_data['chats'][user_id].append({
                "sender": guest.get('name'),
                "message": message_body,
                "timestamp": datetime.now().strftime("%H:%M"),
                "customer": True
            })
            save_chats(chats_data)

            # Example agent response
            agent_response = "Thank you for your request. Our staff will get back to you shortly."
            send_message(sender, body=agent_response)

            # Log agent's response
            chats_data['chats'][user_id].append({
                "sender": "Agent",
                "message": agent_response,
                "timestamp": datetime.now().strftime("%H:%M"),
                "customer": False
            })
            save_chats(chats_data)
            return '', 204

    # ------------------------------ Default Response ------------------------------
    send_message(sender, body="I'm sorry, I didn't understand that. Please choose an option from the menu.")
    return '', 204

# ------------------------------ Run Server ------------------------------

if __name__ == '__main__':
    app.run(debug=False, port=6000)