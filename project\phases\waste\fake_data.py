import os
import requests
from faker import Faker
from datetime import datetime
import random

SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY')

def fetch_users():
    try:
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}"
        }
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/Users",
            headers=headers
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error fetching users: {e}")
        return None

def generate_fake_user():
    fake = Faker('en_IN')  # Using Indian locale for names and phone numbers
    current_time = datetime.now().strftime("%d/%m | %I:%M %p")
    
    return {
        "Name": fake.name(),
        "phone": f"+91{fake.msisdn()[3:]}",  # Indian format mobile number
        "room_no": str(random.randint(100, 999)),
        "platform": "Whatsapp",
        "language": "English",
        "time": current_time
    }

def insert_user(user_data):
    try:
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/Users",
            headers=headers,
            json=user_data
        )
        response.raise_for_status()
        return True
    except Exception as e:
        print(f"Error inserting user: {e}")
        return False

if __name__ == "__main__":
    # Insert 20 fake users
    for _ in range(20):
        user_data = generate_fake_user()
        if insert_user(user_data):
            print(f"Successfully inserted user: {user_data['Name']}")
    
    # Fetch and display all users
    users = fetch_users()
    if users:
        print("\nAll users:")
        print(users)