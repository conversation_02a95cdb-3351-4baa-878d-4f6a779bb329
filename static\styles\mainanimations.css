
  /* Container Animation on Load */
.animate-container {
  opacity: 0;
  transform: translateY(20px);
  animation: containerFadeIn 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  will-change: opacity, transform;
}

/* Staggered delay for multiple containers */
.animate-container:nth-child(1) { animation-delay: 0.1s; }
.animate-container:nth-child(2) { animation-delay: 0.2s; }
.animate-container:nth-child(3) { animation-delay: 0.3s; }
.animate-container:nth-child(4) { animation-delay: 0.4s; }
.animate-container:nth-child(5) { animation-delay: 0.5s; }

@keyframes containerFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Element Animations Inside Containers */
.animate-title {
  opacity: 0;
  transform: translateY(-8px);
  animation: titleFadeIn 0.4s ease-out 0.2s forwards;
}

.animate-text {
  opacity: 0;
  animation: textFadeIn 0.4s ease-out 0.3s forwards;
}

.animate-item {
  opacity: 0;
  transform: translateX(-10px);
  animation: itemSlideIn 0.3s ease-out forwards;
}

/* Staggered delay for multiple items */
.animate-item:nth-child(1) { animation-delay: 0.1s; }
.animate-item:nth-child(2) { animation-delay: 0.15s; }
.animate-item:nth-child(3) { animation-delay: 0.2s; }
.animate-item:nth-child(4) { animation-delay: 0.25s; }
.animate-item:nth-child(5) { animation-delay: 0.3s; }

@keyframes titleFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes textFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes itemSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Pulse animation for highlighting elements */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Theme-aware shimmer effect for content loading */
.shimmer {
  position: relative;
  overflow: hidden;
  background: var(--theme-selector-bg);
}

.shimmer::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background: linear-gradient(
    90deg, 
    transparent, 
    var(--theme-option-hover-bg, rgba(255, 255, 255, 0.2)), 
    transparent
  );
  animation: shimmer 1.5s infinite;
  content: '';
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Add these classes to your custom.css file */

/* Container animation that triggers when visible */
.animate-container-visible {
  animation: containerFadeIn 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

/* Title animation that triggers when visible */
.animate-title-visible {
  animation: titleFadeIn 0.4s ease-out forwards;
}

/* Text animation that triggers when visible */
.animate-text-visible {
  animation: textFadeIn 0.4s ease-out forwards;
}

/* Item animation that triggers when visible */
.animate-item-visible {
  animation: itemSlideIn 0.3s ease-out forwards;
}

/* Staggered delay for items is still applied */
.animate-item-visible:nth-child(1) { animation-delay: 0.1s; }
.animate-item-visible:nth-child(2) { animation-delay: 0.15s; }
.animate-item-visible:nth-child(3) { animation-delay: 0.2s; }
.animate-item-visible:nth-child(4) { animation-delay: 0.25s; }
.animate-item-visible:nth-child(5) { animation-delay: 0.3s; }