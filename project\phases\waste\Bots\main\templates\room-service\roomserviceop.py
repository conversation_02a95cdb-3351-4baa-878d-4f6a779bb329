import requests
import json

# Your Twilio account details (replace with your actual Account SID and Auth Token)
ACCOUNT_SID = '**********************************'
AUTH_TOKEN = '005d910f85546392a91f58a3878c437c'

# Twilio API endpoint for content creation
url = 'https://content.twilio.com/v1/Content'

# Payload data for the carousel with updated beverage menu items and descriptions
payload = {
    "friendly_name": "v4_food_or_bev",
    "language": "en",
    "types": {
        "twilio/carousel": {
            "body": "Savor exquisite food and beverages served with unparalleled hospitality!",
            "cards": [
                {
                    "title": "Order Food",
                    "body": "Discover our delicious menu options.",
                    "media": "https://dixith-dev.github.io/food_menu/images/burrata.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Food",
                            "id": "order_food_option"
                        }
                    ]
                },
                {
                    "title": "Order Beverages",
                    "body": "Refresh yourself with our drink selection.",
                    "media": "https://dixith-dev.github.io/food_menu/images/drinks.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Beverages",
                            "id": "order_beverage_option"
                        }
                    ]
                }
            ]
        }
    }
}

# Make the POST request to the Twilio API
response = requests.post(
    url,
    auth=(ACCOUNT_SID, AUTH_TOKEN),
    headers={'Content-Type': 'application/json'},
    data=json.dumps(payload)
)

# Check the response status
if response.status_code == 201:
    print("Carousel created successfully!")
    print(response.json())
else:
    print(f"Failed to create carousel: {response.status_code}")
    print(response.text)