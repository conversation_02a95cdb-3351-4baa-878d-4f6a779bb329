document.addEventListener('DOMContentLoaded', function() {
    const themeOptions = document.querySelectorAll('.theme-options');
    const darkModeToggler = document.getElementById('darkModeToggler');
    const sunIcon = document.getElementById('sunIcon');
    const moonIcon = document.getElementById('moonIcon');

    // Function to apply theme
    function applyTheme(themeName) {
        document.body.className = themeName;
        localStorage.setItem('selectedTheme', themeName);
        
        // Toggle icons
        if (themeName === 'pure-black') {
            sunIcon.classList.remove('hidden');
            moonIcon.classList.add('hidden');
        } else {
            sunIcon.classList.add('hidden');
            moonIcon.classList.remove('hidden');
        }
    }

    // Check if there's a saved theme in localStorage
    const savedTheme = localStorage.getItem('selectedTheme');

    // Apply saved theme or default to 'light'
    if (savedTheme) {
        applyTheme(savedTheme);
    } else {
        applyTheme('light');
    }

    // Add click event listener for the dark mode toggler
    if (darkModeToggler) {
        darkModeToggler.addEventListener('click', function() {
            const currentTheme = localStorage.getItem('selectedTheme');
            const newTheme = currentTheme === 'pure-black' ? 'light' : 'pure-black';
            applyTheme(newTheme);
        });
    }
});