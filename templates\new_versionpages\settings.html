<html class="dark uk-theme-zinc uk-radii-md uk-shadows-sm uk-font-sm" lang="en">

<head>
    <meta charset="utf-8">
    <link rel="icon" href="https://frames.franken-ui.dev/favicon.png">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="preload" href="/fonts/Geist/Sans.woff2" as="font" type="font/woff2" crossorigin="">
    <link rel="preload" href="/fonts/Geist/Mono.woff2" as="font" type="font/woff2" crossorigin="">
    <link rel="stylesheet" href="/fonts/Geist/style.css">
    {%include 'imports.html'%}
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Franken UI Core -->
    <link rel="stylesheet" href="https://unpkg.com/franken-ui@1.1.0/dist/css/core.min.css" />
    <script src="https://unpkg.com/franken-ui@1.1.0/dist/js/core.iife.js" type="module"></script>
    <script src="https://unpkg.com/franken-ui@1.1.0/dist/js/icon.iife.js" type="module"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- UIkit Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit-icons.min.js"></script>

    <!-- Custom Styles -->
    <link rel="stylesheet" href="../static/styles/custom.css">
    <link rel="stylesheet" href="../static/styles/loadinganimations.css">
    <link rel="stylesheet" href="../static/styles/scrollbar.css">

    <!-- Custom Scripts -->
    <script src="../static/js/themes.js" defer></script>
    <script src="../static/js/sidebarcollapse.js" defer></script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://rsms.me/" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />

    <style>
        .pure-black .uk-dropdown.themer-icon uk-icon {
            color: white !important;
        }

        .light .uk-dropdown.themer-icon uk-icon {
            color: black !important;
        }

        .light .uk-dropdown .themer-input {
            background-color: transparent !important;
            color: black !important;
        }

        .pure-black .uk-dropdown .themer-input {
            background-color: transparent !important;
            color: white !important;
            border: transparent !important;
        }
    </style>
    <script>
        const htmlElement = document.documentElement;

        const __FRANKEN__ = JSON.parse(localStorage.getItem('__FRANKEN__') || '{}');

        if (
            __FRANKEN__.mode === 'dark' ||
            (!__FRANKEN__.mode && window.matchMedia('(prefers-color-scheme: dark)').matches)
        ) {
            htmlElement.classList.add('dark');
        } else {
            htmlElement.classList.remove('dark');
        }

        htmlElement.classList.add(__FRANKEN__.theme || 'uk-theme-zinc');
        htmlElement.classList.add(__FRANKEN__.radii || 'uk-radii-md');
        htmlElement.classList.add(__FRANKEN__.shadows || 'uk-shadows-sm');
        htmlElement.classList.add(__FRANKEN__.font || 'uk-font-sm');

        window.addEventListener('message', function (event) {
            const config = {};
            const classList = event.data.classList;

            if (classList.findIndex((a) => a === 'dark') === -1) {
                config['mode'] = 'light';
            } else {
                config['mode'] = 'dark';
            }

            ['theme', 'radii', 'shadows'].forEach((a) => {
                const current = classList.find((cls) => cls.startsWith(`uk-${a}-`));

                config[a] = current;
            });

            htmlElement.className = classList.join(' ');

            localStorage.setItem('__FRANKEN__', JSON.stringify(config));
        });
    </script>

    <link href="../_app/immutable/assets/0.CKeztTNO.css" rel="stylesheet">
    <link rel="modulepreload" href="../_app/immutable/entry/start.Vst7ySbz.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/entry.9QVIkRbF.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/runtime.TiHKIKXM.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/index.BFqC5wTN.js">
    <link rel="modulepreload" href="../_app/immutable/entry/app.D3_5gIyu.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/render.DjbNRGoE.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/events.CtNZkgeR.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/svelte-head.C7r8nZFU.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/template.BeAEtOma.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/disclose-version.Bg9kRutz.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/if.DqNexOy9.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/proxy.BxnfUy7H.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/store.CFH9Spb4.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/index-client.DKBMzo6l.js">
    <link rel="modulepreload" href="../_app/immutable/nodes/0.DdOE4ss_.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/attributes.BPlYHI2u.js">
    <link rel="modulepreload" href="../_app/immutable/nodes/5.CTdZAOzx.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/class.BFWW4W6B.js">
    <link rel="modulepreload" href="../_app/immutable/chunks/demo.CcBFghhe.js"><!--[-->
    <script type="module" src="/_app/immutable/assets/core.iife.DJHF22JU.js"></script>
    <script type="module" src="/_app/immutable/assets/icon.iife.CRfRcP5P.js"></script><!--]-->
    <link rel="modulepreload" as="script" crossorigin=""
        href="https://frames.franken-ui.dev/_app/immutable/nodes/1.HX1CEMLl.js">
    <link rel="modulepreload" as="script" crossorigin=""
        href="https://frames.franken-ui.dev/_app/immutable/chunks/legacy.CtaTdtmd.js">
    <style type="text/css">
        @font-face {
            font-family: 'Atlassian Sans';
            font-style: normal;
            font-weight: 400 653;
            font-display: swap;
            src:
                local('AtlassianSans'),
                local('Atlassian Sans Text'),
                url('chrome-extension://liecbddmkiiihnedobmlmillhodjkdmb/fonts/AtlassianSans-latin.woff2') format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
                U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }
    </style>
</head>

<body class="bg-background font-geist-sans text-sm text-foreground" data-sveltekit-preload-data="hover">
    <div style="display: contents"><!--[--><!--[--><!----><!---->
        <div class="hidden p-6 lg:p-10 xl:block">
            <div class="space-y-0.5">
                <h2 class="text-2xl font-bold tracking-tight">Settings</h2>
                <p class="text-muted-foreground">Manage your account settings and set e-mail preferences.</p>
            </div>
            <div class="my-6 border-t border-border"></div>
            <div class="flex gap-x-12">
                <aside class="w-1/5">
                    <ul class="uk-nav uk-nav-secondary"
                        data-uk-switcher="connect: #component-nav; animation: uk-anmt-fade" role="tablist"
                        aria-orientation="vertical">
                        <li class="uk-active" role="presentation"><a href="#" aria-selected="true" role="tab"
                                id="uk-switcher-1" aria-controls="uk-switcher-2">Profile</a></li>
                        <li role="presentation" class=""><a href="#" aria-selected="false" role="tab" id="uk-switcher-3"
                                aria-controls="uk-switcher-4" tabindex="-1">Account</a></li>
                        <li role="presentation" class=""><a href="#" aria-selected="false" role="tab" id="uk-switcher-5"
                                aria-controls="uk-switcher-6" tabindex="-1">Appearance</a></li>
                        <li role="presentation" class=""><a href="#" aria-selected="false" role="tab" id="uk-switcher-7"
                                aria-controls="uk-switcher-8" tabindex="-1">Notifications</a></li>
                        <li role="presentation" class=""><a href="#" aria-selected="false" role="tab" id="uk-switcher-9"
                                aria-controls="uk-switcher-10" tabindex="-1">Display</a></li>
                        <li role="presentation" class=""><a href="#" aria-selected="false" role="tab"
                                id="uk-switcher-11" aria-controls="uk-switcher-12" tabindex="-1">Staff Members</a></li>
                    </ul>
                </aside>
                <div class="flex-1">
                    <ul id="component-nav" class="uk-switcher max-w-2xl" role="presentation"
                        style="touch-action: pan-y pinch-zoom;">
                        <li class="space-y-6 uk-active" id="uk-switcher-2" role="tabpanel"
                            aria-labelledby="uk-switcher-1" style="">
                            <div>
                                <h3 class="text-lg font-medium">Profile</h3>
                                <p class="text-sm text-muted-foreground">This is how others will see you on the site.
                                </p>
                            </div>
                            <div class="border-t border-border"></div>
                            <div class="space-y-2"><label class="uk-form-label" for="username">Username</label> <input
                                    class="uk-input" id="username" type="text" placeholder="sveltecult">
                                <div class="uk-form-help text-muted-foreground">This is your public display name. It can
                                    be your real name or a pseudonym. You can
                                    only change this once every 30 days.</div>
                            </div>
                            <div class="space-y-2">
                                <label class="uk-form-label" for="email">Email</label>
                                <div class="h-9">
                                    <div class="uk-position-relative">
                                        <button type="button" id="email-dropdown-btn"
                                            class="uk-input-fake w-full flex justify-between items-center"
                                            aria-haspopup="true" style="background-color: transparent;">
                                            <EMAIL>
                                            <uk-icon class="" icon="chevrons-up-down">
                                            </uk-icon>
                                        </button>
                                        <div id="email-dropdown" uk-dropdown="mode: click; pos: bottom-justify"
                                            class="uk-dropdown w-full">
                                            <ul class="uk-nav uk-dropdown-nav uk-overflow-auto uk-cs-options">
                                                <li class="uk-active">
                                                    <a class="email-option" data-value="<EMAIL>">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text"><EMAIL></span>
                                                        </div>
                                                        <uk-icon class="uk-cs-check" icon="check">

                                                        </uk-icon>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="email-option" data-value="<EMAIL>">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text"><EMAIL></span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="email-option" data-value="<EMAIL>">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text"><EMAIL></span>
                                                        </div>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="uk-form-help text-muted-foreground">
                                    You can manage verified email addresses in your email settings.
                                </div>
                            </div>

                            <script>
                                document.addEventListener('DOMContentLoaded', function () {
                                    // Make sure UIkit is loaded first
                                    if (typeof UIkit !== 'undefined') {
                                        // Initialize the email dropdown specifically
                                        const emailDropdown = document.getElementById('email-dropdown');
                                        if (emailDropdown) {
                                            const dropdown = UIkit.dropdown(emailDropdown, {
                                                mode: 'click',
                                                pos: 'bottom-justify'
                                            });

                                            // Store the dropdown instance on the element
                                            emailDropdown.ukDropdown = dropdown;
                                        }

                                        // Get all email options
                                        const emailOptions = document.querySelectorAll('.email-option');
                                        emailOptions.forEach(option => {
                                            option.addEventListener('click', function (event) {
                                                event.preventDefault();

                                                // Update the button text with selected value
                                                const selectedValue = this.getAttribute('data-value');
                                                document.getElementById('email-dropdown-btn').innerText = selectedValue;

                                                // Update active state
                                                emailOptions.forEach(opt => {
                                                    opt.parentElement.classList.remove('uk-active');
                                                });
                                                this.parentElement.classList.add('uk-active');

                                                // Add the check icon to the selected option
                                                emailOptions.forEach(opt => {
                                                    const checkIcon = opt.querySelector('.uk-cs-check');
                                                    if (checkIcon) {
                                                        checkIcon.style.display = 'none';
                                                    }
                                                });

                                                const selectedCheckIcon = this.querySelector('.uk-cs-check');
                                                if (selectedCheckIcon) {
                                                    selectedCheckIcon.style.display = 'block';
                                                }

                                                // Close dropdown
                                                if (emailDropdown.ukDropdown) {
                                                    emailDropdown.ukDropdown.hide();
                                                }
                                            });
                                        });
                                    } else {
                                        console.error('UIkit is not loaded. Make sure it\'s loaded before this script runs.');
                                    }
                                });
                            </script>

                            <div class="space-y-2"><label class="uk-form-label text-destructive" for="bio">Bio</label>
                                <textarea class="uk-textarea uk-form-destructive" id="bio"
                                    placeholder="Tell us a little bit about yourself"></textarea>
                                <div class="uk-form-help text-muted-foreground">You can @mention other users and
                                    organizations to link to them.</div>
                                <div class="uk-form-help text-destructive">String must contain at least 4 character(s)
                                </div>
                            </div>
                            <div class="space-y-2"><span class="uk-form-label">URLs</span>
                                <div class="uk-form-help text-muted-foreground">Add links to your website, blog, or
                                    social media profiles.</div> <input class="uk-input" readonly="" type="text"
                                    value="https://www.franken-ui.dev"> <input class="uk-input" readonly="" type="text"
                                    value="https://github.com/sveltecult/franken-ui"> <button
                                    class="uk-button uk-button-default">Add URL</button>
                            </div>
                            <div><button class="uk-button uk-button-primary">Update profile</button></div>
                        </li>
                        <li class="space-y-6" id="uk-switcher-4" role="tabpanel" aria-labelledby="uk-switcher-3"
                            style="">
                            <div>
                                <h3 class="text-lg font-medium">Account</h3>
                                <p class="text-sm text-muted-foreground">Update your account settings. Set your
                                    preferred language and timezone.</p>
                            </div>
                            <div class="border-t border-border"></div>
                            <div class="space-y-2"><label class="uk-form-label" for="name">Name</label> <input
                                    class="uk-input" id="name" type="text" placeholder="Your name">
                                <div class="uk-form-help text-muted-foreground">This is the name that will be displayed
                                    on your profile and in emails.</div>
                            </div>
                            <div class="space-y-2"><label class="uk-form-label block" for="date_of_birth">Date of
                                    Birth</label> <input class="uk-input w-60" id="date_of_birth" type="date"
                                    placeholder="Pick a date">
                                <div class="uk-form-help text-muted-foreground">Your date of birth is used to calculate
                                    your age.</div>
                            </div>
                            <div class="space-y-2">
                                <label class="uk-form-label block" for="language">Language</label>
                                <div class="h-9">
                                    <div class="uk-position-relative">
                                        <button type="button" id="language-dropdown-btn"
                                            class="uk-input-fake w-full flex justify-between items-center"
                                            aria-haspopup="true" style="background-color: transparent;">
                                            English
                                            <uk-icon class="" icon="chevrons-up-down">

                                            </uk-icon>
                                        </button>
                                        <div id="language-dropdown" uk-dropdown="mode: click; pos: bottom-justify"
                                            class="uk-dropdown w-full">
                                            <ul class="uk-nav uk-dropdown-nav uk-overflow-auto uk-cs-options">
                                                <li class="uk-active">
                                                    <a class="language-option" data-value="English">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">English</span>
                                                        </div>
                                                        <uk-icon class="uk-cs-check" icon="check">

                                                        </uk-icon>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="language-option" data-value="French">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">French</span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="language-option" data-value="German">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">German</span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="language-option" data-value="Spanish">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">Spanish</span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="language-option" data-value="Portuguese">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">Portuguese</span>
                                                        </div>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="uk-form-help text-muted-foreground">This is the language that will be used
                                    in the dashboard.</div>
                            </div>

                            <script>
                                document.addEventListener('DOMContentLoaded', function () {
                                    // Make sure UIkit is loaded first
                                    if (typeof UIkit !== 'undefined') {
                                        // Initialize the language dropdown specifically
                                        const languageDropdown = document.getElementById('language-dropdown');
                                        if (languageDropdown) {
                                            const dropdown = UIkit.dropdown(languageDropdown, {
                                                mode: 'click',
                                                pos: 'bottom-justify'
                                            });

                                            // Store the dropdown instance on the element
                                            languageDropdown.ukDropdown = dropdown;
                                        }

                                        // Get all language options
                                        const languageOptions = document.querySelectorAll('.language-option');
                                        languageOptions.forEach(option => {
                                            option.addEventListener('click', function (event) {
                                                event.preventDefault();

                                                // Update the button text with selected value
                                                const selectedValue = this.getAttribute('data-value');
                                                document.getElementById('language-dropdown-btn').innerText = selectedValue;

                                                // Update active state
                                                languageOptions.forEach(opt => {
                                                    opt.parentElement.classList.remove('uk-active');
                                                });
                                                this.parentElement.classList.add('uk-active');

                                                // Add the check icon to the selected option
                                                languageOptions.forEach(opt => {
                                                    const checkIcon = opt.querySelector('.uk-cs-check');
                                                    if (checkIcon) {
                                                        checkIcon.style.display = 'none';
                                                    }
                                                });

                                                const selectedCheckIcon = this.querySelector('.uk-cs-check');
                                                if (selectedCheckIcon) {
                                                    selectedCheckIcon.style.display = 'block';
                                                } else {
                                                    // Create check icon if it doesn't exist
                                                    const checkIcon = document.createElement('uk-icon');
                                                    checkIcon.className = 'uk-cs-check';
                                                    checkIcon.setAttribute('icon', 'check');


                                                    this.appendChild(checkIcon);
                                                }

                                                // Close dropdown
                                                if (languageDropdown.ukDropdown) {
                                                    languageDropdown.ukDropdown.hide();
                                                }
                                            });
                                        });
                                    } else {
                                        console.error('UIkit is not loaded. Make sure it\'s loaded before this script runs.');
                                    }
                                });
                            </script>
                            <div><button class="uk-button uk-button-primary">Update profile</button></div>
                        </li>
                        <li class="space-y-6" id="uk-switcher-6" role="tabpanel" aria-labelledby="uk-switcher-5"
                            style="">
                            <div>
                                <h3 class="text-lg font-medium">Appearance</h3>
                                <p class="text-sm text-muted-foreground">Customize the appearance of the app.
                                    Automatically switch between day and night
                                    themes.</p>
                            </div>
                            <div class="border-t border-border"></div>
                            <div class="space-y-2">
                                <label class="uk-form-label block" for="font-family">Font Family</label>
                                <div class="h-9">
                                    <div class="uk-position-relative">
                                        <button type="button" id="font-dropdown-btn"
                                            class="uk-input-fake w-full flex justify-between items-center"
                                            aria-haspopup="true" style="background-color: transparent;">
                                            Geist
                                            <uk-icon class="" icon="chevrons-up-down">

                                            </uk-icon>
                                        </button>
                                        <div id="font-dropdown" uk-dropdown="mode: click; pos: bottom-justify"
                                            class="uk-dropdown w-full">
                                            <ul class="uk-nav uk-dropdown-nav uk-overflow-auto uk-cs-options">
                                                <li>
                                                    <a class="font-option" data-value="Inter">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">Inter</span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li class="uk-active">
                                                    <a class="font-option" data-value="Geist">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">Geist</span>
                                                        </div>
                                                        <uk-icon class="uk-cs-check" icon="check">

                                                        </uk-icon>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="font-option" data-value="Open Sans">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">Open Sans</span>
                                                        </div>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="uk-form-help text-muted-foreground">Set the font you want to use in the
                                    dashboard.</div>
                            </div>

                            <script>
                                document.addEventListener('DOMContentLoaded', function () {
                                    // Make sure UIkit is loaded first
                                    if (typeof UIkit !== 'undefined') {
                                        // Initialize the font dropdown specifically
                                        const fontDropdown = document.getElementById('font-dropdown');
                                        if (fontDropdown) {
                                            const dropdown = UIkit.dropdown(fontDropdown, {
                                                mode: 'click',
                                                pos: 'bottom-justify'
                                            });

                                            // Store the dropdown instance on the element
                                            fontDropdown.ukDropdown = dropdown;
                                        }

                                        // Get all font options
                                        const fontOptions = document.querySelectorAll('.font-option');
                                        fontOptions.forEach(option => {
                                            option.addEventListener('click', function (event) {
                                                event.preventDefault();

                                                // Update the button text with selected value
                                                const selectedValue = this.getAttribute('data-value');
                                                document.getElementById('font-dropdown-btn').innerText = selectedValue;

                                                // Update active state
                                                fontOptions.forEach(opt => {
                                                    opt.parentElement.classList.remove('uk-active');
                                                });
                                                this.parentElement.classList.add('uk-active');

                                                // Add the check icon to the selected option
                                                fontOptions.forEach(opt => {
                                                    const checkIcon = opt.querySelector('.uk-cs-check');
                                                    if (checkIcon) {
                                                        checkIcon.style.display = 'none';
                                                    }
                                                });

                                                const selectedCheckIcon = this.querySelector('.uk-cs-check');
                                                if (selectedCheckIcon) {
                                                    selectedCheckIcon.style.display = 'block';
                                                } else {
                                                    // Create check icon if it doesn't exist
                                                    const checkIcon = document.createElement('uk-icon');
                                                    checkIcon.className = 'uk-cs-check';
                                                    checkIcon.setAttribute('icon', 'check');




                                                    checkIcon.appendChild(svg);
                                                    this.appendChild(checkIcon);
                                                }

                                                // Close dropdown
                                                if (fontDropdown.ukDropdown) {
                                                    fontDropdown.ukDropdown.hide();
                                                }

                                                // Optional: Actually apply the selected font to the page
                                                document.body.style.fontFamily = selectedValue + ", sans-serif";
                                            });
                                        });
                                    } else {
                                        console.error('UIkit is not loaded. Make sure it\'s loaded before this script runs.');
                                    }
                                });
                            </script>
                            <div class="space-y-2"><span class="uk-form-label">Theme</span>
                                <div class="uk-form-help text-muted-foreground">Select the theme for the dashboard.
                                </div>
                                <div class="grid max-w-md grid-cols-2 gap-8"><a
                                        class="block cursor-pointer items-center rounded-md border-2 border-muted p-1 ring-ring ">
                                        <div class="space-y-2 rounded-sm bg-[#ecedef] p-2">
                                            <div class="space-y-2 rounded-md bg-white p-2 shadow-sm">
                                                <div class="h-2 w-[80px] rounded-lg bg-[#ecedef]"></div>
                                                <div class="h-2 w-[100px] rounded-lg bg-[#ecedef]"></div>
                                            </div>
                                            <div class="flex items-center space-x-2 rounded-md bg-white p-2 shadow-sm">
                                                <div class="h-4 w-4 rounded-full bg-[#ecedef]"></div>
                                                <div class="h-2 w-[100px] rounded-lg bg-[#ecedef]"></div>
                                            </div>
                                            <div class="flex items-center space-x-2 rounded-md bg-white p-2 shadow-sm">
                                                <div class="h-4 w-4 rounded-full bg-[#ecedef]"></div>
                                                <div class="h-2 w-[100px] rounded-lg bg-[#ecedef]"></div>
                                            </div>
                                        </div>
                                    </a> <a
                                        class="block cursor-pointer items-center rounded-md border-2 border-muted bg-popover p-1 ring-ring ring-2">
                                        <div class="space-y-2 rounded-sm bg-slate-950 p-2">
                                            <div class="space-y-2 rounded-md bg-slate-800 p-2 shadow-sm">
                                                <div class="h-2 w-[80px] rounded-lg bg-slate-400"></div>
                                                <div class="h-2 w-[100px] rounded-lg bg-slate-400"></div>
                                            </div>
                                            <div
                                                class="flex items-center space-x-2 rounded-md bg-slate-800 p-2 shadow-sm">
                                                <div class="h-4 w-4 rounded-full bg-slate-400"></div>
                                                <div class="h-2 w-[100px] rounded-lg bg-slate-400"></div>
                                            </div>
                                            <div
                                                class="flex items-center space-x-2 rounded-md bg-slate-800 p-2 shadow-sm">
                                                <div class="h-4 w-4 rounded-full bg-slate-400"></div>
                                                <div class="h-2 w-[100px] rounded-lg bg-slate-400"></div>
                                            </div>
                                        </div>
                                    </a></div>
                            </div>
                            <div><button class="uk-button uk-button-primary">Update preferences</button></div>
                        </li>
                        <li class="space-y-6" id="uk-switcher-8" role="tabpanel" aria-labelledby="uk-switcher-7"
                            style="">
                            <div>
                                <h3 class="text-lg font-medium">Notifications</h3>
                                <p class="text-sm text-muted-foreground">Configure how you receive notifications.</p>
                            </div>
                            <div class="border-t border-border"></div>
                            <div class="space-y-2"><span class="uk-form-label">Notify me about</span> <!--[--><label
                                    class="block text-sm" for="notification_0"><input id="notification_0"
                                        class="uk-radio mr-2" name="notification" type="radio"> All new
                                    messages</label><label class="block text-sm" for="notification_1"><input
                                        id="notification_1" class="uk-radio mr-2" name="notification" type="radio">
                                    Direct messages and mentions</label><label class="block text-sm"
                                    for="notification_2"><input id="notification_2" class="uk-radio mr-2"
                                        name="notification" type="radio"> Nothing</label><!--]--></div>
                            <div>
                                <h3 class="mb-4 text-lg font-medium">Email Notifications</h3>
                                <div class="space-y-4"><!--[-->
                                    <div class="flex items-center justify-between rounded-lg border border-border p-4">
                                        <div class="space-y-0.5"><label class="text-base font-medium"
                                                for="email_notification_0">Communication emails</label>
                                            <div class="uk-form-help text-muted-foreground">Receive emails about your
                                                account activity.</div>
                                        </div> <input class="uk-toggle-switch uk-toggle-switch-primary"
                                            id="email_notification_0" type="checkbox">
                                    </div>
                                    <div class="flex items-center justify-between rounded-lg border border-border p-4">
                                        <div class="space-y-0.5"><label class="text-base font-medium"
                                                for="email_notification_1">Marketing emails</label>
                                            <div class="uk-form-help text-muted-foreground">Receive emails about new
                                                products, features, and more.</div>
                                        </div> <input class="uk-toggle-switch uk-toggle-switch-primary"
                                            id="email_notification_1" type="checkbox">
                                    </div>
                                    <div class="flex items-center justify-between rounded-lg border border-border p-4">
                                        <div class="space-y-0.5"><label class="text-base font-medium"
                                                for="email_notification_2">Social emails</label>
                                            <div class="uk-form-help text-muted-foreground">Receive emails for friend
                                                requests, follows, and more.</div>
                                        </div> <input class="uk-toggle-switch uk-toggle-switch-primary"
                                            id="email_notification_2" type="checkbox">
                                    </div>
                                    <div class="flex items-center justify-between rounded-lg border border-border p-4">
                                        <div class="space-y-0.5"><label class="text-base font-medium"
                                                for="email_notification_3">Security emails</label>
                                            <div class="uk-form-help text-muted-foreground">Receive emails about your
                                                account activity and security.</div>
                                        </div> <input class="uk-toggle-switch uk-toggle-switch-primary"
                                            id="email_notification_3" type="checkbox" disabled="">
                                    </div><!--]-->
                                </div>
                            </div>
                            <div class="flex gap-x-3"><input class="uk-checkbox mt-1" id="notification_mobile"
                                    type="checkbox" checked="">
                                <div class="space-y-1"><label class="uk-form-label" for="notification_mobile">Use
                                        different settings for my mobile devices</label>
                                    <div class="uk-form-help text-muted-foreground">You can manage your mobile
                                        notifications in the mobile settings page.</div>
                                </div>
                            </div>
                            <div><button class="uk-button uk-button-primary">Update notifications</button></div>
                        </li>
                        <li class="space-y-6" id="uk-switcher-10" role="tabpanel" aria-labelledby="uk-switcher-9">
                            <div>
                                <h3 class="text-lg font-medium">Display</h3>
                                <p class="text-sm text-muted-foreground">Turn items on or off to control what's
                                    displayed in the app.</p>
                            </div>
                            <div class="border-t border-border"></div>
                            <div class="space-y-2">
                                <div class="mb-4"><span class="text-base font-medium">Sidebar</span>
                                    <div class="uk-form-help text-muted-foreground">Select the items you want to display
                                        in the sidebar.</div>
                                </div> <!--[--><label class="block text-sm" for="display_0"><input
                                        class="uk-checkbox mr-2" type="checkbox"> Recents</label><label
                                    class="block text-sm" for="display_1"><input class="uk-checkbox mr-2"
                                        type="checkbox"> Home</label><label class="block text-sm" for="display_2"><input
                                        class="uk-checkbox mr-2" type="checkbox"> Applications</label><label
                                    class="block text-sm" for="display_3"><input class="uk-checkbox mr-2"
                                        type="checkbox"> Desktop</label><label class="block text-sm"
                                    for="display_4"><input class="uk-checkbox mr-2" type="checkbox">
                                    Downloads</label><label class="block text-sm" for="display_5"><input
                                        class="uk-checkbox mr-2" type="checkbox"> Documents</label><!--]-->
                            </div>
                            <div><button class="uk-button uk-button-primary">Update display</button></div>
                        </li>
                        <li class="space-y-6" id="uk-switcher-12" role="tabpanel" aria-labelledby="uk-switcher-11">
                            <div>
                                <h3 class="text-lg font-medium">Staff Members</h3>
                                <p class="text-sm text-muted-foreground">View and manage staff members with access to
                                    this system.</p>
                            </div>
                            <div class="border-t border-border"></div>
                            <div id="staff-members" class="uk-card card shadow-fixer">
                                <div class="uk-card-header">
                                    <h3 class="font-semibold leading-none tracking-tight">
                                        Add staff members
                                    </h3>
                                    <p class="mt-1.5 text-sm text-muted-foreground">
                                        Anyone with the link can view this document.
                                    </p>
                                </div>
                                <div class="uk-card-body pt-0">
                                    <div class="card flex gap-x-2">
                                        <div class="flex-1"> <input class="uk-input card" readonly=""
                                                value="http://guestgenius.es/hotelname/#/04" id="share-link"> </div>
                                        <div class="flex-none"> <button class="uk-button border card default"
                                                onclick="copyToClipboard()" id="copy-button"
                                                style="display: flex; align-items: center; gap: 8px; padding: 8px 12px; min-width: 110px; justify-content: center;">
                                                <span>Copy Link</span>
                                            </button>
                                        </div>
                                    </div>
                                    <hr class="card my-4">
                                    <div class="space-y-4">
                                        <h4 class="text-sm font-medium">Staff members with access : </h4>
                                        <div class="flex items-center space-x-4">
                                            <span
                                                class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                                                <img class="aspect-square h-full w-full"
                                                    src="https://api.dicebear.com/8.x/lorelei/svg?seed=Olivia Martin">
                                            </span>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium leading-none">Dixith Mediga</p>
                                                <p class="text-sm text-muted-foreground"><EMAIL></p>
                                            </div>
                                            <div class="h-9 w-[200px] relative">
                                                <div
                                                    class="theme-responsive-button card theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer universal-hover">
                                                    <span>Select Permissions</span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M19 9l-7 7-7-7">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <div class="uk-drop uk-dropdown w-[252px] hidden themer-icon"
                                                    uk-drop="mode: click; pos: bottom-right">
                                                    <div class="m-1 flex items-center px-2 py-1.5">
                                                        <uk-icon class="opacity-50" icon="search"></uk-icon>
                                                        <input
                                                            class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                                            placeholder="Select a new role" type="text">
                                                    </div>
                                                    <ul class="uk-dropdown-nav">
                                                        <li class="uk-nav-divider"></li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Viewer</div>
                                                                    <div class="text-sm text-muted-foreground ">Can view
                                                                        and comment.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Developer</div>
                                                                    <div class="text-sm text-muted-foreground">Can view,
                                                                        comment and edit.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Billing</div>
                                                                    <div class="text-sm text-muted-foreground ">Can
                                                                        view, comment and manage billing.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Owner</div>
                                                                    <div class="text-sm text-muted-foreground ">
                                                                        Admin-level to all resources.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <span
                                                class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                                                <img class="aspect-square h-full w-full"
                                                    src="https://api.dicebear.com/8.x/lorelei/svg?seed=Isabella Nguyen">
                                            </span>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium leading-none">Harsh Jadhav</p>
                                                <p class="text-sm text-muted-foreground"><EMAIL></p>
                                            </div>
                                            <div class="h-9 w-[200px] relative">
                                                <div
                                                    class="theme-responsive-button card theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer universal-hover">
                                                    <span>Select Permissions</span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M19 9l-7 7-7-7">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <div class="uk-drop uk-dropdown w-[252px] hidden themer-icon"
                                                    uk-drop="mode: click; pos: bottom-right">
                                                    <div class="m-1 flex items-center px-2 py-1.5">
                                                        <uk-icon class="opacity-50" icon="search"></uk-icon>
                                                        <input
                                                            class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                                            placeholder="Select a new role" type="text">
                                                    </div>
                                                    <ul class="uk-dropdown-nav">
                                                        <li class="uk-nav-divider"></li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Viewer</div>
                                                                    <div class="text-sm text-muted-foreground ">Can view
                                                                        and comment.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Developer</div>
                                                                    <div class="text-sm text-muted-foreground">Can view,
                                                                        comment and edit.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Billing</div>
                                                                    <div class="text-sm text-muted-foreground ">Can
                                                                        view, comment and manage billing.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Owner</div>
                                                                    <div class="text-sm text-muted-foreground ">
                                                                        Admin-level to all resources.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <span
                                                class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                                                <img class="aspect-square h-full w-full"
                                                    src="https://api.dicebear.com/8.x/lorelei/svg?seed=Sofia Davis">
                                            </span>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium leading-none">George Garriga</p>
                                                <p class="text-sm text-muted-foreground"><EMAIL></p>
                                            </div>
                                            <div class="h-9 w-[200px] relative">
                                                <div
                                                    class="theme-responsive-button card theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer universal-hover">
                                                    <span>Select Permissions</span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M19 9l-7 7-7-7">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <div class="uk-drop uk-dropdown w-[252px] hidden themer-icon"
                                                    uk-drop="mode: click; pos: bottom-right">
                                                    <div class="m-1 flex items-center px-2 py-1.5">
                                                        <uk-icon class="opacity-50" icon="search"></uk-icon>
                                                        <input
                                                            class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                                            placeholder="Select a new role" type="text">
                                                    </div>
                                                    <ul class="uk-dropdown-nav">
                                                        <li class="uk-nav-divider"></li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Viewer</div>
                                                                    <div class="text-sm text-muted-foreground ">Can view
                                                                        and comment.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Developer</div>
                                                                    <div class="text-sm text-muted-foreground">Can view,
                                                                        comment and edit.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Billing</div>
                                                                    <div class="text-sm text-muted-foreground ">Can
                                                                        view, comment and manage billing.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Owner</div>
                                                                    <div class="text-sm text-muted-foreground ">
                                                                        Admin-level to all resources.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Insert or replace Jackon Lee's block with the following -->
                                        <div class="flex items-center space-x-4">
                                            <span
                                                class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                                                <img class="aspect-square h-full w-full"
                                                    src="https://api.dicebear.com/8.x/lorelei/svg?seed=Jackon Lee">
                                            </span>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium leading-none">Jackon Lee</p>
                                                <p class="text-sm text-muted-foreground"><EMAIL></p>
                                            </div>
                                            <div class="h-9 w-[200px] relative">
                                                <div id="jackon-permissions-selector"
                                                    class="theme-responsive-button card theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer universal-hover">
                                                    <span id="jackon-selected-permission">Select Permissions</span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M19 9l-7 7-7-7">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <div id="jackon-permission-dropdown"
                                                    class="uk-drop uk-dropdown dropdown-content w-[252px] hidden themer-icon"
                                                    uk-drop="mode: click; pos: bottom-right">
                                                    <div class="m-1 flex items-center px-2 py-1.5">
                                                        <uk-icon class="opacity-50" icon="search">

                                                        </uk-icon>
                                                        <input
                                                            class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                                            placeholder="Select a new role" type="text">
                                                    </div>
                                                    <ul class="uk-dropdown-nav">
                                                        <li class="uk-nav-divider"></li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Viewer</div>
                                                                    <div class="text-sm text-muted-foreground ">Can view
                                                                        and comment.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Developer</div>
                                                                    <div class="text-sm text-muted-foreground">Can view,
                                                                        comment and edit.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Billing</div>
                                                                    <div class="text-sm text-muted-foreground ">Can
                                                                        view, comment and manage billing.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Owner</div>
                                                                    <div class="text-sm text-muted-foreground ">
                                                                        Admin-level to all resources.</div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <script>
                                            document.addEventListener('DOMContentLoaded', function () {
                                                let currentOpenDropdown = null;

                                                // Attach handlers to each .theme-dropdown trigger
                                                document.querySelectorAll('.theme-dropdown').forEach(trigger => {
                                                    const container = trigger.closest('.relative');
                                                    if (!container) return;
                                                    const dropdown = container.querySelector('.uk-dropdown');
                                                    if (!dropdown) return;

                                                    // Add the dropdown-content class if it doesn't exist
                                                    if (!dropdown.classList.contains('dropdown-content')) {
                                                        dropdown.classList.add('dropdown-content');
                                                    }

                                                    trigger.addEventListener('click', e => {
                                                        e.preventDefault();
                                                        e.stopPropagation();

                                                        if (dropdown.classList.contains('hidden')) {
                                                            if (currentOpenDropdown && currentOpenDropdown !== dropdown) {
                                                                closeDropdown(currentOpenDropdown);
                                                            }
                                                            openDropdown(dropdown);
                                                            currentOpenDropdown = dropdown;
                                                        } else {
                                                            closeDropdown(dropdown);
                                                            currentOpenDropdown = null;
                                                        }
                                                    });

                                                    // Close dropdown if a .uk-drop-close link is clicked
                                                    dropdown.querySelectorAll('.uk-drop-close').forEach(item => {
                                                        item.addEventListener('click', e => {
                                                            e.preventDefault();
                                                            closeDropdown(dropdown);
                                                            if (currentOpenDropdown === dropdown) {
                                                                currentOpenDropdown = null;
                                                            }
                                                        });
                                                    });

                                                    // Prevent dropdown from closing when clicking inside
                                                    dropdown.addEventListener('click', e => e.stopPropagation());

                                                    // Search filter logic
                                                    const searchInput = dropdown.querySelector('input[type="text"]');
                                                    if (searchInput) {
                                                        const listItems = dropdown.querySelectorAll('ul.uk-dropdown-nav > li:not(.uk-nav-divider)');
                                                        searchInput.addEventListener('input', e => {
                                                            e.stopPropagation();
                                                            const query = e.target.value.toLowerCase();
                                                            listItems.forEach(li => {
                                                                const optionEl = li.querySelector('.dropdown-option-3');
                                                                const text = (optionEl ? optionEl.textContent : '').toLowerCase();
                                                                li.style.display = text.includes(query) ? '' : 'none';
                                                            });
                                                        });
                                                    }
                                                });

                                                // Close any open dropdown if clicking outside
                                                document.addEventListener('click', () => {
                                                    if (currentOpenDropdown) {
                                                        closeDropdown(currentOpenDropdown);
                                                        currentOpenDropdown = null;
                                                    }
                                                });

                                                function openDropdown(dropdown) {
                                                    // Set transform origin for proper animation
                                                    dropdown.style.transformOrigin = 'top right';
                                                    // Remove hidden class first
                                                    dropdown.classList.remove('hidden');
                                                    // Use requestAnimationFrame to ensure the browser processes the unhide before animating
                                                    requestAnimationFrame(() => {
                                                        dropdown.setAttribute('data-state', 'open');
                                                    });
                                                }

                                                function closeDropdown(dropdown) {
                                                    // Start the fade out animation
                                                    dropdown.setAttribute('data-state', '');
                                                    // Wait for animation to complete before hiding
                                                    setTimeout(() => {
                                                        dropdown.classList.add('hidden');
                                                    }, 200); // Match this to your animation duration
                                                }
                                            });
                                        </script>
                                        <script>
                                            function copyLink() {
                                                const linkInput = document.getElementById('share-link');
                                                linkInput.select();
                                                linkInput.setSelectionRange(0, 99999); // For mobile devices

                                                navigator.clipboard.writeText(linkInput.value)
                                                    .then(() => {
                                                        const button = event.target;
                                                        const originalText = button.textContent;
                                                        button.textContent = 'Copied!';

                                                        setTimeout(() => {
                                                            button.textContent = originalText;
                                                        }, 2000);
                                                    })
                                                    .catch(err => {
                                                        console.error('Failed to copy:', err);
                                                        alert('Failed to copy link');
                                                    });
                                            }

                                            function copyToClipboard() {
                                                // Get the text field and button
                                                const copyText = document.getElementById("share-link");
                                                const copyButton = document.getElementById("copy-button");

                                                // Try the modern clipboard API first
                                                if (navigator.clipboard && window.isSecureContext) {
                                                    navigator.clipboard.writeText(copyText.value)
                                                        .then(() => {
                                                            copyButton.textContent = "Copied!";
                                                            setTimeout(() => {
                                                                copyButton.textContent = "Copy Link";
                                                            }, 2000);
                                                        })
                                                        .catch(() => {
                                                            // Fallback to older method if clipboard API fails
                                                            fallbackCopyToClipboard(copyText, copyButton);
                                                        });
                                                } else {
                                                    // Use fallback for non-HTTPS or unsupported browsers
                                                    fallbackCopyToClipboard(copyText, copyButton);
                                                }
                                            }

                                            function fallbackCopyToClipboard(copyText, copyButton) {
                                                try {
                                                    // Select the text
                                                    copyText.select();
                                                    copyText.setSelectionRange(0, 99999); // For mobile devices

                                                    // Execute copy command
                                                    document.execCommand('copy');

                                                    // Update button text
                                                    copyButton.textContent = "Copied!";
                                                    setTimeout(() => {
                                                        copyButton.textContent = "Copy Link";
                                                    }, 2000);
                                                } catch (err) {
                                                    console.error('Failed to copy:', err);
                                                    copyButton.textContent = "Failed to copy";
                                                    setTimeout(() => {
                                                        copyButton.textContent = "Copy Link";
                                                    }, 2000);
                                                }
                                            }

                                        </script>
                                    </div>
                                </div>
                            </div>
                            <div><button class="uk-button uk-button-primary">Manage Staff</button></div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>



</body>

</html>