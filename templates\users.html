<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Users</title>
    <!-- Tailwind CSS -->
    {% include 'imports.html' %}
    <style>
        body {
            visibility: hidden;
            margin: 0;
            overflow-x: hidden;
            width: 100vw;
        }

        .grid {
            display: grid;
            grid-template-columns: 280px 1fr;
            transition: grid-template-columns 0.3s ease-in-out;
            overflow: hidden !important;
        }

        .grid.side-panel-active {
            grid-template-columns: 280px 1fr 25%;
        }

        .grid.collapsed.side-panel-active {
            grid-template-columns: 60px 1fr 25%;
        }

        .relative.w-full.overflow-auto.h-full {
            overflow-x: hidden !important;
        }

        /* Side Panel Styles */
        .side-panel {
            display: none;
            /* background-color: #fefefe; */
            /* box-shadow: -1px 0 5px rgba(0,0,0,0.1); */
            z-index: 1000;
            overflow-y: auto;
            padding: 32px;
            padding-left: 5px;
            padding-right: 5px;
            border-radius: 0 12px 12px 0;
            transition: transform 5s ease-in-out;
        }



        /* Add this to the existing <style> section */

        .side-panel {
            /* Existing styles... */
            scrollbar-width: none;
            /* Firefox */
            -ms-overflow-style: none;
            /* IE/Edge */
        }

        .side-panel::-webkit-scrollbar {
            display: none;
            /* Chrome, Safari, Opera */
        }

        /* Also hide scrollbar for the sales grid if needed */
        .sales-grid {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .sales-grid::-webkit-scrollbar {
            display: none;
        }

        .side-panel.active {
            display: block;
            transform: translateX(0);
        }

        @media (min-width: 890px) {
            .side-panel {
                position: relative;
                transform: translateX(100%);
            }
        }

        .user-profile-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding-bottom: 24px;
        }

        .user-avatar {
            width: 120px;
            height: 144px;
            background-color: #f3f4f6;
            border-radius: 8px;
            margin-bottom: 16px;
            object-fit: cover;
            border: 1px solid #e5e7eb;
        }

        .user-name {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .user-phone {
            margin-bottom: 16px;
            font-size: 0.95rem;
        }

        .platform-language-icons {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .icon-divider {
            width: 1px;
            height: 20px;
            background-color: #e5e7eb;
        }

        .platform-language-icons img {
            width: 24px;
            height: 24px;
            object-fit: contain;
        }

        .room-number {
            background-color: var(--dropdown-text);
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            color: var(--dropdown-bg);
        }

        .joined-at {
            font-size: 0.875rem;
        }

        .user-details-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 24px;
            margin-top: 32px;
            padding: 0 16px;
        }

        .detail-container {
            border-radius: 12px;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 12px;

        }

        .stats-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .stats-list li {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            color: #4b5563;
            font-size: 0.95rem;
        }

        .stats-value {
            font-weight: 500;
            color: #111827;
        }

        .sales-history {
            max-height: 400px;
            overflow-y: auto;
        }

        .sale-item {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .sale-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 1rem;
            color: #111827;
        }

        .sale-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 0.95rem;
            color: #4b5563;
        }

        .sale-details div {
            display: flex;
            justify-content: space-between;
        }

        .no-sales {
            color: #6b7280;
            text-align: center;
            padding: 24px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            font-size: 1rem;
        }

        .close-panel {
            position: absolute;
            left: 16px;
            top: 16px;
            font-size: 24px;
            cursor: pointer;
            padding: 8px;
            background: none;
            border: none;
            line-height: 1;
            color: #6b7280;
            border-radius: 6px;
        }

        .close-panel:hover {
            background-color: #f3f4f6;
            color: #374151;
        }

        .sales-grid {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .sale-card {
            border-radius: 0.5rem;
            padding: 1.25rem;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .sale-main {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .sale-title {
            font-weight: 600;
            font-size: 0.95rem;
        }

        .sale-price {
            font-weight: 600;
            color: #059669;
            font-size: 0.95rem;
        }

        .sale-info {
            display: flex;
            justify-content: space-between;
            /* Push items to ends */
            align-items: center;
            font-size: 0.875rem;
            padding-top: 0.75rem;
        }

        .whatsapp-icon {
            width: 1.125rem;
            height: 1.125rem;
            opacity: 0.9;
            position: absolute;
            /* Removed absolute positioning styles */
        }

        /* Table scroll styles */
        .table-container {
            max-height: calc(100vh - 150px);
            overflow-y: auto;
            position: relative;
            border-radius: inherit;
        }

        /* Simple fix for rounded corners */
        .rounded-lg {
            overflow: hidden;
        }



        /* Add this style for the header row to ensure complete coverage */
        .table-container thead tr {
            background-color: var(--card);
        }

        /* Smooth scrolling for table */
        .table-container {
            scroll-behavior: smooth;
            scrollbar-width: thin;
        }

        /* Scrollbar styling for webkit browsers */
        .table-container::-webkit-scrollbar {
            width: 6px;
        }

        .table-container::-webkit-scrollbar-track {
            background: var(--card);
        }

        .table-container::-webkit-scrollbar-thumb {
            background-color: var(--muted);
            border-radius: 3px;
        }

        .table-container thead tr.card {
            background-color: var(--card) !important;
        }

        /* Make the table header sticky with a solid bottom border */
        /* Make the table header sticky with a solid bottom border */
        /* Ensure the search bar row always sticks at the top */
        /* Ensure the search bar row always sticks at the top with a simulated bottom border */

        .light {
            --color-vala-border: #e5e7eb;
        }

        .pure-black {
            --color-vala-border: #38393b;
        }

        /* Ensure the search bar row always sticks at the top with a simulated bottom border using the card variable */
        .table-container thead tr.search-row {
            position: sticky;
            top: 0;
            z-index: 1;
            /* higher than other header rows */
            background-color: var(--card);
            box-shadow: 0 1px 0 0 var(--color-vala-border);
        }

        .table-container {
            overflow-y: auto;
            -ms-overflow-style: none;
            /* Hide scrollbar in IE 10+ */
            scrollbar-width: none;
            /* Hide scrollbar in Firefox */
        }

        .table-container::-webkit-scrollbar {
            display: none;
            /* Hide scrollbar in Chrome, Safari, Opera */
        }

        #sidebar {
            position: sticky;
            top: 0;
            height: 100vh;
            /* Force full viewport height */
            overflow-y: auto;
            /* Allow scrolling within the sidebar */
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        /* Animation for the side panel */
        .side-panel {
            transform: translateX(100%);
            transition: transform 0.3s ease-out, opacity 0.3s ease-out;
            display: none;
            opacity: 0;
        }

        .side-panel.active {
            transform: translateX(0);
            opacity: 1;
            display: block;
        }

        /* Animation for sales cards */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .sale-card {
            opacity: 0;
            animation: fadeInUp 0.5s ease-out forwards;
        }

        /* Create staggered animation delay for multiple cards */
        .sale-card:nth-child(1) {
            animation-delay: 0.1s;
        }

        .sale-card:nth-child(2) {
            animation-delay: 0.2s;
        }

        .sale-card:nth-child(3) {
            animation-delay: 0.3s;
        }

        .sale-card:nth-child(4) {
            animation-delay: 0.4s;
        }

        .sale-card:nth-child(5) {
            animation-delay: 0.5s;
        }

        .sale-card:nth-child(6) {
            animation-delay: 0.6s;
        }

        .sale-card:nth-child(7) {
            animation-delay: 0.7s;
        }

        .sale-card:nth-child(8) {
            animation-delay: 0.8s;
        }

        /* Animation for the section title */
        .section-title {
            opacity: 0;
            animation: fadeInUp 0.4s ease-out forwards;
        }

        /* Animation for the "No sales history available" message */
        .sale-card p.text-center {
            opacity: 0;
            animation: fadeIn 0.6s ease-out 0.3s forwards;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        #searchInput {
            text-indent: 4px;
            font-weight: 600;
        }
    </style>
    <style>
        /* Remove outline and border from dropdown search inputs */
        .uk-dropdown .themer-input,
        .uk-drop .themer-input {
            outline: none !important;
            border: none !important;
            box-shadow: none !important;
        }

        .uk-dropdown .themer-input:focus,
        .uk-drop .themer-input:focus,
        .uk-dropdown .themer-input:active,
        .uk-drop .themer-input:active {
            outline: none !important;
            border: none !important;
            box-shadow: none !important;
        }

        /* Theme-specific styles */
        .light .uk-dropdown .themer-input,
        .light .uk-drop .themer-input {
            background-color: transparent !important;
            color: black !important;
            border: transparent !important;
            outline: none !important;
            box-shadow: none !important;
        }

        .pure-black .uk-dropdown .themer-input,
        .pure-black .uk-drop .themer-input {
            background-color: transparent !important;
            color: white !important;
            border: transparent !important;
            outline: none !important;
            box-shadow: none !important;
        }

        /* Make main element a flex container to distribute space properly */
        main.hide-scrollbar {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 60px);
        }

        /* Summary cards and filters container */
        .grid.grid-cols-4,
        .flex.items-center.justify-between {
            flex-shrink: 0;
            /* Prevent shrinking */
        }

        /* Make the table card grow to fill remaining space */
        .border.shadow-sm.rounded-lg.h-full.card {
            flex-grow: 1;
            min-height: 0;
            /* Important for proper flex behavior */
            display: flex;
            flex-direction: column;
        }

        /* Table container should fill its parent */
        .relative.w-full.h-full.table-container {
            flex-grow: 1;
            overflow-y: auto;
            min-height: 0;
            /* Important for proper scrolling */
        }

        /* Ensure the table fills the container width */
        .table-container table {
            width: 100%;
        }

        /* Hide scrollbar utility for main content */
        .hide-scrollbar {
            scrollbar-width: none;
            /* Firefox */
            -ms-overflow-style: none;
            /* IE/Edge */
        }

        .hide-scrollbar::-webkit-scrollbar {
            display: none;
            /* Chrome, Safari, Opera */
        }

        /* Add these styles to your existing <style> section */
        .activity-timeline {
            padding: 0 0.5rem;
        }

        .activity-item {
            position: relative;
            transition: all 0.2s ease;
        }

        .activity-icon {
            transition: all 0.2s ease;
        }

        #viewSelector {
            background-color: var(--card);
            color: var(--foreground);
            border-color: var(--border-color);
        }

        /* Activity log animations */
        .activity-timeline .absolute {
            opacity: 0;
            animation: fadeIn 0.8s ease-out forwards;
        }

        .activity-item {
            opacity: 0;
            animation: fadeInUp 0.5s ease-out forwards;
        }

        /* Staggered animation delay for activity items */
        .mb-6:nth-child(1) .activity-item {
            animation-delay: 0.1s;
        }

        .mb-6:nth-child(2) .activity-item {
            animation-delay: 0.2s;
        }

        .mb-6:nth-child(3) .activity-item {
            animation-delay: 0.3s;
        }

        .mb-6:nth-child(4) .activity-item {
            animation-delay: 0.4s;
        }

        .mb-6:nth-child(5) .activity-item {
            animation-delay: 0.5s;
        }

        /* Timeline line animation */
        .activity-timeline .absolute.left-\[15px\] {
            height: 0;
            animation: growDown 0.8s ease-out forwards;
        }

        @keyframes growDown {
            from {
                height: 0;
            }

            to {
                height: 100%;
            }
        }

        /* View all link animation */
        .activity-timeline .mt-4 {
            opacity: 0;
            animation: fadeIn 0.5s ease-out 0.6s forwards;
        }

        /* Override dropdown hover background to be completely transparent */

        html .pure-black .uk-drop.uk-dropdown .uk-dropdown-nav li:hover,
        body.pure-black .uk-drop.uk-dropdown .uk-dropdown-nav li:hover {
            background-color: transparent !important;
            background: transparent !important;
        }

        html .light .uk-drop.uk-dropdown .uk-dropdown-nav li:hover,
        body .light .uk-drop.uk-dropdown .uk-dropdown-nav li:hover {
            background-color: transparent !important;
            background: transparent !important;
        }

        /* Add these styles to the second <style> section in users.html */
        .chat-dropdown-icon-hover {
            transition: background-color 0.2s ease;
            padding: 6px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .light .chat-dropdown-icon-hover:hover {
            background-color: #f4f4f5 !important;
        }

        .pure-black .chat-dropdown-icon-hover:hover {
            background-color: #27272a !important;
        }

        /* More styles for the activity log can be added here */
    </style>
</head>

<body class="bg-background text-foreground">
    <!-- Loading Overlay -->
    {% include 'components/loading.html' %}

    <div id="mainGrid" class="grid min-h-screen w-full lg:grid-cols-[280px_1fr]  ">
        {% include 'sidebar.html' %}
        <div class="flex flex-col">
            <header
                class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-6 sticky-page-header">
                <div class="flex items-center gap-2 px-4 pl-0">
                    <button id="toggle-btn" class="opacity-100 transition-opacity duration-300 focus:outline-none"
                        style="background-color: transparent !important;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-panel-left">
                            <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                            <path d="M9 3v18"></path>
                        </svg>
                    </button>
                    <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-3 h-4"
                        style="background-color: var(--border-color);"></div>
                    <nav aria-label="breadcrumb">
                        <ol
                            class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
                            <!-- Dashboard link -->
                            <div class="menubar" role="menubar">
                                <div class="menubar-indicator"></div>
                                <a href="/" role="menuitem">Dashboard</a>
                                <a href="/users" role="menuitem" class="active">Users</a>
                                <a href="/products" role="menuitem">Products</a>
                                <a href="/analytics" role="menuitem">Overview</a>
                            </div>
                        </ol>
                    </nav>
                </div>
                {% include 'topright.html' %}
            </header>
            <main class="gap-3 p-4 md:gap-3 md:p-6 hide-scrollbar"
                style="min-height: 0; height: calc(100vh - 60px); overflow-y: auto;">
                <!-- Copied Task Summary Cards and Filters -->
                <div class="grid grid-cols-4 gap-x-4 gap-y-4 ">
                    <!-- Card 1: Total Tasks -->
                    <div class="card rounded-lg border shadow-sm relative">
                        <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Total Users</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-users">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                                <circle cx="9" cy="7" r="4" />
                                <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                                <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                            </svg>
                        </div>
                        <div class="p-6 relative">
                            <div class="text-2xl font-bold" id="total-users-count">0</div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">All registered users</p>
                        </div>
                    </div>
                    <!-- Card 2: Active Users -->
                    <div class="card rounded-lg border shadow-sm relative">
                        <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Active Users</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-user-check">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                                <circle cx="9" cy="7" r="4" />
                                <polyline points="16 11 18 13 22 9" />
                            </svg>
                        </div>
                        <div class="p-6 relative">
                            <div class="text-2xl font-bold" id="active-users-count">0</div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Users active recently</p>
                        </div>
                    </div>
                    <!-- Card 3: New Users Today -->
                    <div class="card rounded-lg border shadow-sm relative">
                        <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">New Users Today</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-user-plus">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                                <circle cx="9" cy="7" r="4" />
                                <line x1="19" x2="19" y1="8" y2="14" />
                                <line x1="22" x2="16" y1="11" y2="11" />
                            </svg>
                        </div>
                        <div class="p-6 relative">
                            <div class="text-2xl font-bold" id="new-users-today-count">0</div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Joined in last 24h</p>
                        </div>
                    </div>
                    <!-- Card 4: Users with Sales -->
                    <div class="card rounded-lg border shadow-sm relative">
                        <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Users with Sales</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-shopping-cart">
                                <circle cx="8" cy="21" r="1" />
                                <circle cx="19" cy="21" r="1" />
                                <path
                                    d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12" />
                            </svg>
                        </div>
                        <div class="p-6 relative">
                            <div class="text-2xl font-bold" id="users-with-sales-count">0</div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Users with purchase history</p>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between ">
                    <div class="flex flex-1 gap-4">
                        <!-- Search input is already part of the table header, so not duplicated here -->
                    </div>
                    <div class="flex items-center gap-2">
                        <button class="uk-button uk-button-default universal-hover theme-text card" aria-haspopup="true"
                            style="background-color: transparent; height: 36px;">
                            <span class="mr-2 size-4">
                                <uk-icon icon="settings-2"></uk-icon>
                            </span>
                            View
                        </button>
                        <div class="uk-drop uk-dropdown " uk-drop="mode: click; pos: bottom-right">
                            <div class="m-1 flex items-center px-2">
                                <uk-icon class="opacity-50" icon="search">
                                </uk-icon>
                                <input class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                    placeholder="Filter View" type="text">
                            </div>
                            <ul class="uk-dropdown-nav">
                                <li class="uk-nav-divider"></li>
                                <li>
                                    <a class="uk-drop-close" href="#" role="button">
                                        All Users
                                    </a>
                                </li>
                                <li>
                                    <a class="uk-drop-close" href="#" role="button">
                                        Active Last 7 Days
                                    </a>
                                </li>
                                <li>
                                    <a class="uk-drop-close" href="#" role="button">
                                        With Recent Sales
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <button
                            class="card inline-flex items-center justify-center rounded-md border border-dashed border-input px-3 text-xs font-medium shadow-sm hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring h-[36px] universal-hover"
                            aria-haspopup="true" style="background: transparent;">
                            <span class="mr-2 size-4">
                                <uk-icon icon="circle-plus">
                                </uk-icon>
                            </span>
                            Platform
                        </button>
                        <div class="uk-drop uk-dropdown" uk-drop="mode: click; pos: bottom-right">
                            <div class="m-1 flex items-center px-2">
                                <uk-icon class="opacity-50" icon="search">
                                </uk-icon>
                                <input class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                    placeholder="Filter Platform" type="text">
                            </div>
                            <ul class="uk-dropdown-nav">
                                <li class="uk-nav-divider"></li>
                                <li>
                                    <a class="uk-drop-close" href="#" role="button">
                                        <div class="flex flex-1 items-center justify-between">
                                            <div class="flex flex-1 items-center gap-x-2">
                                                <img src="../static/icons/whatsapp2.png" alt="WhatsApp"
                                                    class="whatsapp-theme-icon"
                                                    style="width: 16px; height: 16px; margin-right: 8px;">
                                                <span>WhatsApp</span>
                                            </div>
                                            <span class="font-geist-mono text-xs" id="platform-whatsapp-count">0</span>
                                        </div>
                                    </a>
                                </li>
                                <!-- Add other platforms as needed -->
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- End of Copied Content -->

                <div class="border shadow-sm rounded-lg h-full card">
                    <div class="relative w-full h-full table-container">
                        <table class="w-full caption-bottom text-sm">
                            <thead class="[&_tr]:border-b">
                                <tr
                                    class="card search-row border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                                    <th
                                        class="h-11 px-1 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">
                                        <div class="uk-inline max-w-[240px] w-full ml-4">
                                            <span class="uk-form-icon text-muted-foreground">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-calendar-search">
                                                    <path d="M16 2v4" />
                                                    <path
                                                        d="M21 11.75V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7.25" />
                                                    <path d="m22 22-1.875-1.875" />
                                                    <path d="M3 10h18" />
                                                    <path d="M8 2v4" />
                                                    <circle cx="18" cy="18" r="3" />
                                                </svg>
                                            </span>
                                            <div class="uk-inline w-full">
                                                <span class="uk-form-icon text-muted-foreground">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-search">
                                                        <circle cx="11" cy="11" r="8"></circle>
                                                        <path d="m21 21-4.3-4.3"></path>
                                                    </svg>
                                                </span>
                                                <input id="searchInput"
                                                    class="uk-input rounded-md border border-gray-300 p-2 w-fit focus:border-gray-300 focus:outline-none"
                                                    type="text" placeholder="Search">
                                            </div>
                                    </th>
                                    <th
                                        class="h-12 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">
                                        Platform</th>
                                    <th
                                        class="h-12 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">
                                        Language</th>
                                    <th
                                        class="h-12 px-3 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">
                                        Room No</th>
                                    <th
                                        class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">
                                        Joined time</th>
                                    <th
                                        class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">
                                        Total Spend</th>
                                    <th
                                        class="h-12 px-4 text-center align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">
                                        Actions</th>
                                </tr>
                            </thead>
                            <tbody class="[&_tr:last-child]:border-0" id="user-list">
                                <!-- User list will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
        <!-- Updated Side Panel HTML -->
        <div id="userDetailPanel"
            class="side-panel border-l no-overflow-card max-h-screen overflow-y-auto scroll-smooth">
            <button id="closePanel" class="close-panel" aria-label="Close Panel"
                style="background-color: transparent;"><i class="ph ph-x" style="font-size: 20px;"></i></button>
            <div id="panelContent">
                <!-- User details will be populated here -->
            </div>
        </div>
    </div>

    <script>
        let users = [];
        let salesData = []; // New variable to store sales data

        function tryHideLoadingOverlay() {
            const loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
        }

        function fetchAllData() {
            console.log('Fetching user list and sales data...');
            Promise.all([
                fetch('/updateduserslist').then(res => res.json()),
                fetch('/fetch-sales-list').then(res => res.json())
            ])
                .then(([usersData, salesList]) => {
                    console.log('User list fetched:', usersData);
                    console.log('Sales list fetched:', salesList);

                    users = usersData;
                    salesData = salesList;

                    // Enrich users with user_id from salesData
                    users.forEach(user => {
                        const match = salesData.find(sale =>
                            sale.guest_phone_or_id.toString() === user.guest_phone_or_id.toString()
                        );
                        if (match) {
                            user.user_id = match.user_id;
                        }
                    });

                    updateUserList();
                    updateSummaryCards(); // New function call
                    tryHideLoadingOverlay();

                    if (users.length > 0) {
                        showUserDetails(users[0]);
                    }

                    // Set up search functionality after data is loaded
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput) {
                        searchInput.addEventListener('input', filterUsers);
                    }
                })
                .catch(error => {
                    console.error('Error fetching user list or sales data:', error);
                    tryHideLoadingOverlay();
                });
        }

        function updateUserList(filteredUsers = users) {
            const userListContainer = document.getElementById('user-list');
            userListContainer.innerHTML = '';

            filteredUsers.forEach((user, index) => {
                const userElement = document.createElement('tr');
                userElement.className = 'card border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted user-item cursor-pointer relative';
                userElement.innerHTML = `
                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">
                    <div class="flex items-center gap-4 ml-3">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user">
                            <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                        <div>
                            <div class="font-semibold">${sanitizeHTML(user.guest_name)}</div>
                            <div class="text-sm text-gray-500">+${sanitizeHTML(user.guest_phone_or_id.toString())}</div>
                        </div>
                    </div>
                </td>
                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">
                    ${user.guest_platform.toLowerCase() === 'whatsapp' ?
                        `<img src="../static/icons/whatsapp2.png"
                            alt="WhatsApp"
                            title="WhatsApp"
                            class="whatsapp-theme-icon"
                            style="width: 24px; height: 24px;"
                            loading="lazy">`
                        : sanitizeHTML(user.guest_platform)}
                </td>
                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">
                    ${user.guest_language.toLowerCase() === 'english' ?
                        `<img src="https://cdn-icons-png.flaticon.com/128/197/197374.png"
                            alt="English"
                            title="English"
                            style="width: 24px; height: 24px; margin-left: 7px;"
                            loading="lazy">`
                        : sanitizeHTML(user.guest_language)}
                </td>
                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">
                    <span style="margin-left:3px" class="uk-label uk-label-primary">${sanitizeHTML(user.guest_room_number.toString())}</span>
                </td>
                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">${sanitizeHTML(user.joined_time)}</td>
                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0" style="padding-left: 26px;">€${sanitizeHTML(user.guest_total_spend ? user.guest_total_spend.toString() : '0')}</td>
                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 text-center">
                        <div class="inline-block">
                            <ul class="uk-iconnav">
                                <li>
                                    <a href="#" class="admin-required chat-dropdown-icon-hover">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis-vertical">
                                            <circle cx="12" cy="12" r="1"></circle>
                                            <circle cx="12" cy="5" r="1"></circle>
                                            <circle cx="12" cy="19" r="1"></circle>
                                        </svg>
                                    </a>
                                    <div uk-dropdown="mode: click; pos: bottom-right">
                                        <ul class="uk-nav uk-dropdown-nav">
                                            <li><a href="#" class="admin-required">Restrict user</a></li>
                                            <li><a href="#" class="admin-required">Block number</a></li>
                                        </ul>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </td>
                `;
                userElement.addEventListener('click', (e) => {
                    if (!e.target.closest('.admin-required')) {
                        console.log('User row clicked:', user);
                        showUserDetails(user);
                    }
                });
                userListContainer.appendChild(userElement);
            });
        }

        function formatOrderText(orderText) {
            if (orderText.toLowerCase().includes('massage session')) {
                return 'Massage session';
            }
            return orderText
                .replace(/Room '\d+' (ordered|booked) /, '')
                .replace(/\.$/, '');
        }

        function showUserDetails(user) {
            console.log('Showing user details for:', user);
            const mainGrid = document.getElementById('mainGrid');
            const panel = document.getElementById('userDetailPanel');
            const panelContent = document.getElementById('panelContent');

            const userSales = salesData.filter(sale =>
                sale.guest_phone_or_id.toString() === user.guest_phone_or_id.toString()
            );

            // Generate sample activity log data
            const activityLog = [
                { type: 'login', time: '2 hours ago', details: 'User logged in through WhatsApp' },
                { type: 'order', time: '1 day ago', details: 'Placed order for Room Service' },
                { type: 'feedback', time: '3 days ago', details: 'Left a 5-star review' },
                { type: 'inquiry', time: '1 week ago', details: 'Asked about spa services' },
                { type: 'checkin', time: '1 week ago', details: 'Checked in to Room ' + user.room_no }
            ];

            panelContent.innerHTML = `
        <div class="user-profile-header">
            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTtuphMb4mq-EcVWhMVT8FCkv5dqZGgvn_QiA&s"
                 alt="User profile"
                 class="user-avatar" loading="lazy">
            <div class="user-name">${sanitizeHTML(user.guest_name)}</div>
            <div class="user-phone">+${sanitizeHTML(user.guest_phone_or_id.toString())}</div>
            <div class="platform-language-icons">
                ${(user.guest_platform && user.guest_platform.toLowerCase() === 'whatsapp') ?
                    `<img src="../static/icons/whatsapp2.png"
                         alt="WhatsApp"
                         title="WhatsApp"
                         class="whatsapp-theme-icon"
                         style="width: 24px; height: 24px;"
                         loading="lazy">` : ''}
                <div class="icon-divider"></div>
                ${(user.guest_language && user.guest_language.toLowerCase() === 'english') ?
                    `<img src="https://cdn-icons-png.flaticon.com/128/197/197374.png"
                         alt="English"
                         title="English"
                         loading="lazy">` :
                    sanitizeHTML(user.guest_language || '')}
            </div>
            <div class="room-number random-btn">Room : ${sanitizeHTML(user.guest_room_number.toString())}</div>
            <div style="margin-top: 5px;" class="joined-at">Time Joined : ${sanitizeHTML(user.joined_time)}</div>
        </div>

        <!-- Combined Title and Dropdown Container with shorter divider -->
        <div class="px-4 mb-4">
            <div class="flex justify-between items-center border-b card py-3 mx-auto" style="width: calc(90% + 44px);">
                <!-- Title text will be updated by JS -->
                <h3 id="viewTitle" class="section-title !mb-0 !pb-0 !border-none">Sales History</h3>
                <!-- Dropdown -->
                <div class="relative w-2/5">
                    <div id="viewSelector" class="uk-input card w-full flex justify-between items-center cursor-pointer py-2 px-3 rounded-md mainva-selector" aria-haspopup="true" style="background-color: transparent; color: var(--dropdown-text);">
                        <span id="viewSelectorText">Sales History</span>
                        <uk-icon icon="chevrons-up-down"></uk-icon>
                    </div>
                    <div id="viewSelectorDropdown" uk-dropdown="mode: click; pos: bottom-justify" class="uk-dropdown w-full dropdown-content">
                        <ul class="uk-nav uk-dropdown-nav uk-overflow-auto uk-cs-options">
                            <li class="uk-active">
                                <a href="#" class="view-option" data-value="sales">
                                    <span class="uk-cs-item-text">Sales History</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="view-option" data-value="activity">
                                    <span class="uk-cs-item-text">Activity Log</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-details-grid">
            <!-- Sales History View - Title is now above -->
            <div id="salesView" class="detail-container">
                <!-- Static title removed, handled by #viewTitle above -->
                <div class="sales-grid"> <!-- Remove extra padding -->
                    ${userSales.length > 0 ?
                    userSales.map(sale => `
                            <div class="sale-card border card">
                                <div class="sale-main">
                                    <span class="sale-title">${sanitizeHTML(sale.sale_item)}</span>
                                    <span class="sale-price">£${sanitizeHTML(sale.sale_price.toString())}</span>
                                </div>
                                <div class="sale-info border-t card">
                                    <span>Time: ${sanitizeHTML(sale.sale_time)}</span>
                                    ${(sale.guest_platform && sale.guest_platform.toLowerCase() === 'whatsapp') ? `
                                        <img src="../static/icons/whatsapp2.png"
                                             alt="WhatsApp"
                                             title="WhatsApp"
                                             class="whatsapp-theme-icon"
                                             style="width: 24px; height: 24px;">
                                    ` : `
                                        <span>${sanitizeHTML(sale.guest_platform || '')}</span>
                                    `}
                                </div>
                            </div>
                        `).join('') :
                    '<div class="sale-card border card"><p class="text-center">No sales history available</p></div>'
                }
                </div>
            </div>

            <div id="activityView" class="detail-container hidden">
                <div class="activity-timeline relative">
                    <div class="absolute left-[15px] top-1 h-full w-0.5 bg-black opacity-70"></div>
                    ${activityLog.map((activity, index) => `
                        <div class="mb-6 pl-8 relative ">
                <div class="absolute left-0 top-1 w-[25px] h-[25px] activity-line-colors rounded-full flex items-center justify-center z-10">    <div class="activity-icon" style="transform: scale(0.9);">
                        ${getActivityIcon(activity.type)}
                    </div>
                </div>
                            <div class="activity-item border card rounded-lg p-3 shadow-sm transition-all">
                                <p class="text-gray-400 text-xs mb-1 flex items-center">
                                    <span class="font-medium">${getActivityTitle(activity.type)}</span>
                                    <span class="mx-1">•</span>
                                    <span>${sanitizeHTML(activity.time)}</span>
                                </p>
                                <p class="text-sm font-medium">${sanitizeHTML(activity.details)}</p>
                            </div>
                        </div>
                    `).join('')}
                    <div class="mt-4 pl-8">
                        <a href="#" class="text-blue-500 text-sm hover:text-blue-600 dark:hover:text-blue-400 flex items-center transition-colors font-medium">
                            View full activity log
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
                `;
            initializeViewSelector();

            setTimeout(() => {
                mainGrid.classList.add('side-panel-active');
                panel.classList.add('active');
            }, 10);
            // Add event listener to the view selector
            const viewSelector = document.getElementById('viewSelector');
            if (viewSelector) {
                viewSelector.addEventListener('change', function () {
                    const salesView = document.getElementById('salesView');
                    const activityView = document.getElementById('activityView');

                    if (this.value === 'sales') {
                        salesView.classList.remove('hidden');
                        activityView.classList.add('hidden');
                    } else {
                        salesView.classList.add('hidden');
                        activityView.classList.remove('hidden');
                    }
                });
            }

            mainGrid.classList.add('side-panel-active');
            panel.classList.add('active');
        }

        // Helper functions for activity log
        function getActivityIconClass(type) {
            const classes = {
                'login': 'bg-blue-100 text-blue-600',
                'order': 'bg-green-100 text-green-600',
                'feedback': 'bg-yellow-100 text-yellow-600',
                'inquiry': 'bg-purple-100 text-purple-600',
                'checkin': 'bg-indigo-100 text-indigo-600'
            };
            return classes[type] || 'bg-gray-100 text-gray-600';
        }

        // Find the getActivityIcon function and update the SVG sizes

        function getActivityIcon(type) {
            const icons = {
                'login': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M15 12H3"></path></svg>',
                'order': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>',
                'feedback': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m2 4 3 12h14l3-12-6 4-4-8-4 8-6-4zm3 16h14"></path></svg>',
                'inquiry': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><path d="M12 17h.01"></path></svg>',
                'checkin': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 10V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v3"></path><path d="M20 14v3a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-3"></path><path d="M12 12a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"></path><path d="M12 21v-9"></path></svg>'
            };
            return icons[type] || '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle></svg>';
        }

        function getActivityTitle(type) {
            const titles = {
                'login': 'User Login',
                'order': 'New Order',
                'feedback': 'Feedback',
                'inquiry': 'Inquiry',
                'checkin': 'Check-in'
            };
            return titles[type] || 'Activity';
        }
        function hideUserDetails() {
            const mainGrid = document.getElementById('mainGrid');
            const panel = document.getElementById('userDetailPanel');
            panel.classList.remove('active');
            mainGrid.classList.remove('side-panel-active');
            document.body.style.overflow = '';
        }

        function filterUsers() {
            const searchInput = document.getElementById('searchInput');
            if (!searchInput) return;

            const searchValue = searchInput.value.toLowerCase();
            const filteredUsers = users.filter(user => {
                // Simplified debug logging with proper syntax
                const nameMatch = user.guest_name && user.guest_name.toLowerCase().includes(searchValue);
                const phoneMatch = user.guest_phone_or_id && user.guest_phone_or_id.toString().toLowerCase().includes(searchValue);
                const roomMatch = user.guest_room_number && user.guest_room_number.toString().includes(searchValue);
                const matches = nameMatch || phoneMatch || roomMatch;

                console.log('Search debug:', { user: user, matches: matches });
                return matches;
            });
            updateUserList(filteredUsers);
        }

        function sanitizeHTML(str) {
            if (!str) return '';
            return str.replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }

        function updateSummaryCards() {
            const totalUsersCountEl = document.getElementById('total-users-count');
            const activeUsersCountEl = document.getElementById('active-users-count');
            const newUsersTodayCountEl = document.getElementById('new-users-today-count');
            const usersWithSalesCountEl = document.getElementById('users-with-sales-count');
            const platformWhatsappCountEl = document.getElementById('platform-whatsapp-count');

            if (totalUsersCountEl) totalUsersCountEl.textContent = users.length;

            // Placeholder for active users - Can be refined with more specific logic if available
            // For now, let's assume all fetched users are potentially active or use total as a base
            if (activeUsersCountEl) activeUsersCountEl.textContent = users.length;

            let newToday = 0, usersWithSales = 0, whatsappUsers = 0;
            const todayDateStr = new Date().toISOString().slice(0, 10); // YYYY-MM-DD format

            users.forEach(user => {
                // Assuming user.joined_time is a string like "YYYY-MM-DD HH:MM:SS" or "YYYY-MM-DD"
                if (user.joined_time && user.joined_time.startsWith(todayDateStr)) {
                    newToday++;
                }
                if (user.guest_total_spend && parseFloat(user.guest_total_spend) > 0) {
                    usersWithSales++;
                }
                if (user.guest_platform && user.guest_platform.toLowerCase() === 'whatsapp') {
                    whatsappUsers++;
                }
            });

            if (newUsersTodayCountEl) newUsersTodayCountEl.textContent = newToday;
            if (usersWithSalesCountEl) usersWithSalesCountEl.textContent = usersWithSales;
            if (platformWhatsappCountEl) platformWhatsappCountEl.textContent = whatsappUsers;
        }

        document.addEventListener('DOMContentLoaded', () => {
            console.log('Document loaded');
            fetchAllData();

            const closePanelButton = document.getElementById('closePanel');
            if (closePanelButton) {
                closePanelButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    hideUserDetails();
                });
            }

            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape') {
                    hideUserDetails();
                }
            });

            const panelContent = document.getElementById('panelContent');
            if (panelContent) {
                panelContent.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
            }
        });
    </script>
    <script>
        // Add this script after the showUserDetails function
        function initializeViewSelector() {
            const viewSelector = document.getElementById('viewSelector');
            const viewSelectorDropdown = document.getElementById('viewSelectorDropdown');
            const viewOptions = document.querySelectorAll('.view-option');
            const salesView = document.getElementById('salesView');
            const activityView = document.getElementById('activityView');

            // Initialize UIkit dropdown if not already done
            if (typeof UIkit !== 'undefined' && viewSelectorDropdown) {
                const dropdown = UIkit.dropdown(viewSelectorDropdown, {
                    mode: 'click',
                    pos: 'bottom-justify',
                    animation: false,
                    duration: 0,
                    hideDelay: 0,
                    offset: 0
                });

                // Add animation classes
                viewSelectorDropdown.classList.add('dropdown-content');

                // Add animation handlers
                UIkit.util.on(viewSelectorDropdown, 'beforeshow', function () {
                    this.style.transformOrigin = 'top center';
                    this.setAttribute('data-state', '');

                    requestAnimationFrame(() => {
                        this.setAttribute('data-state', 'open');
                    });
                });

                // Replace the problematic beforehide handler with a simpler version
                UIkit.util.on(viewSelectorDropdown, 'beforehide', function () {
                    this.setAttribute('data-state', '');
                    // No timeout, no prevention of default behavior
                });
            }

            // Handle view option selection
            viewOptions.forEach(option => {
                option.addEventListener('click', function (e) {
                    e.preventDefault();

                    // Force immediate closing of the dropdown
                    if (typeof UIkit !== 'undefined' && viewSelectorDropdown) {
                        UIkit.dropdown(viewSelectorDropdown).hide(0); // Force immediate hide
                    }

                    // Get the display text and value
                    const selectedValue = this.getAttribute('data-value');
                    const selectedText = this.querySelector('.uk-cs-item-text').textContent;

                    // Update button text (optional, might remove later if redundant)
                    document.getElementById('viewSelectorText').textContent = selectedText;
                    // Update the main view title
                    const viewTitle = document.getElementById('viewTitle');
                    if (viewTitle) {
                        viewTitle.textContent = selectedText;
                    }

                    // Add this function to restore animations while keeping the timeline line visible
                    function animateActivityView() {
                        // Reset animations for individual elements but preserve the line
                        const activityItems = document.querySelectorAll('.activity-item');
                        const timelineLine = document.querySelector('.activity-timeline .absolute.left-\\[15px\\]');
                        const viewAllLink = document.querySelector('.activity-timeline .mt-4');
                        const sectionTitle = document.querySelector('#activityView .section-title');

                        if (sectionTitle) {
                            sectionTitle.style.animation = 'none';
                            sectionTitle.offsetHeight; // Force reflow
                            sectionTitle.style.animation = '';
                        }

                        // Preserve the timeline line but restart its animation
                        if (timelineLine) {
                            // Make sure the line is visible even during animation reset
                            timelineLine.style.height = '100%';
                            timelineLine.style.opacity = '0.7';

                            // Reset and restart the grow animation
                            timelineLine.style.animation = 'none';
                            timelineLine.offsetHeight;
                            timelineLine.style.animation = 'growDown 0.8s ease-out forwards';
                        }

                        activityItems.forEach((item) => {
                            item.style.animation = 'none';
                            item.offsetHeight; // Force reflow
                            item.style.animation = '';
                        });

                        if (viewAllLink) {
                            viewAllLink.style.animation = 'none';
                            viewAllLink.offsetHeight;
                            viewAllLink.style.animation = '';
                        }
                    }

                    // Update active state and show/hide sections
                    if (selectedValue === 'sales') {
                        salesView.classList.remove('hidden');
                        activityView.classList.add('hidden');
                        // No need to animate sales here as it's shown by default
                    } else {
                        salesView.classList.add('hidden');
                        activityView.classList.remove('hidden');
                        animateActivityView(); // Add this line to trigger activity animations
                    }

                    // Update active state in dropdown
                    viewOptions.forEach(opt => {
                        const parentLi = opt.closest('li');
                        if (parentLi) {
                            parentLi.classList.remove('uk-active');

                            // Remove check icon if exists
                            const checkIcon = opt.querySelector('.uk-cs-check');
                            if (checkIcon) {
                                checkIcon.remove();
                            }
                        }
                    });

                    // Add active state to selected item
                    const parentLi = this.closest('li');
                    if (parentLi) {
                        parentLi.classList.add('uk-active');
                    }
                });
            });
        }


    </script>

</body>

</html>