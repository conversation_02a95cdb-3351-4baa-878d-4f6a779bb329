<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
  <title>Issues</title>
  <!-- Tailwind CSS -->
  {% include 'imports.html' %}

  <script>
    tailwind.config = {
      darkMode: 'class',
    }
  </script>
  <!-- Add Confetti.js -->
  <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.4.0/dist/confetti.browser.min.js"></script>

  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      padding: 0;
      /* Removed overflow: hidden to allow page scrolling */
    }

    body {
      visibility: hidden;
    }

    #app {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    @keyframes messageIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .divider {
      margin: 7px 0 0 0;
      padding: 0;
    }

    #dropdown-menu {
      display: none;
    }

    #dropdown-button {
      color: #4a5568;
    }

    #dropdown-button:hover,
    #dropdown-button:focus {
      color: #2d3748;
    }

    #dropdown-button.clicked+#dropdown-menu {
      display: block;
    }

    main {
      flex-grow: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;
      /* Changed overflow from hidden to auto to allow scrolling */
      overflow: auto;
    }

    #chat-messages {
      flex-grow: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }

    * {
      scrollbar-width: none;
      -ms-overflow-style: none;
    }

    *::-webkit-scrollbar {
      display: none;
    }

    .user-item {
      cursor: pointer;
      border-bottom: 1px solid #e5e7eb;
    }

    .user-item:last-child {
      border-bottom: none;
    }

    #chat-container {
      height: 600px;
      /* Fixed height for comfortable viewing */
      overflow-y: auto;
    }

    .message-container {
      display: flex;
      align-items: flex-start;
      margin-bottom: 10px;
    }

    .message-container.left {
      animation: slideInLeft 0.3s ease-out;
    }

    .message-container.right {
      animation: slideInRight 0.3s ease-out;
    }

    @keyframes slideInLeft {
      from {
        opacity: 0;
        transform: translateX(-20px);
      }

      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    @keyframes slideInRight {
      from {
        opacity: 0;
        transform: translateX(20px);
      }

      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .message-icon {
      width: 24px;
      height: 24px;
      flex-shrink: 0;
      background-size: cover;
      background-position: center;
    }

    .message-icon.user {
      background-image: url('https://cdn-icons-png.flaticon.com/128/17487/17487660.png');
      margin-right: 15px;
    }

    .message-icon.bot {
      background-image: url('https://cdn-icons-png.flaticon.com/128/17487/17487660.png');
      margin-left: 15px;
    }

    .message-left,
    .message-right {
      max-width: 70%;
      padding: 10px;
      border-radius: 8px;
      word-wrap: break-word;
    }

    .message-left {
      background-color: #f0f0f0;
      color: black;
      margin-right: auto;
    }

    .message-right {
      background-color: #f0f0f0;
      color: black;
      margin-left: auto;
    }

    .timestamp {
      cursor: pointer;
    }

    #message-input-container {
      display: flex;
      align-items: center;
      border: 1px solid #ccc;
      border-radius: 0px;
      padding: 10px;
      flex-grow: 1;
    }

    #message-input {
      flex: 1;
      border: none;
      resize: none;
      font-size: 14px;
      outline: none;
    }

    #send-button svg {
      width: 32px;
      height: 32px;
    }

    .user-row {
      padding: 15px;
      transition: background-color 0.3s ease;
      border-bottom: 1px solid #e5e7eb;
    }

    .user-row:hover,
    .user-row.bg-gray-200 {
      background-color: #f3f4f6;
    }

    .user-item:last-child .user-row {
      border-bottom: none;
      margin-bottom: -8px;
    }

    #user-list {
      padding: 0;
    }

    .rounded-lg {
      border-radius: 1rem;
    }

    .chat-input-container {
      position: sticky;
      bottom: 0;
      z-index: 10;
    }

    #user-list {
      padding-top: 2rem !important;
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }

    #user-list {
      max-height: calc(100vh - 180px);
      /* Adjust this value as needed */
      overflow-y: auto;
      padding-top: 1rem !important;
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }

    .tag-mail {
      margin-bottom: 10px;
    }

    .uk-label {
      background: transparent;
      color: currentColor;
      border: 1px solid currentColor;
      padding: 2px 6px;
      font-size: 0.75rem;
      border-radius: 4px;
    }

    .uk-label-platform {
      background: transparent;
      color: currentColor;
      border: 1px solid currentColor;
      padding: 2px 6px;
      font-size: 0.75rem;
      border-radius: 4px;
      text-transform: none;
    }

    .flex-grow {
      flex-grow: 1;
    }

    .grid-cols-custom {
      display: grid;
      grid-template-columns: 360px 1fr;

    }

    /* Width of the user list */

    #chat-container {
      min-height: 0;
      /* This allows the container to grow within the flex layout */
    }

    /* Additional Styles to Match Live Chat */
    /* Ensure the chat interface is properly displayed */
    #chat-interface {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    /* Style adjustments for chat messages */
    .message-bubble {
      padding: 10px;
      border-radius: 8px;
      word-wrap: break-word;
    }

    .message-bubble p {
      margin: 0;
    }

    /* Add these styles */
    #area-options li:hover,
    #priority-options li:hover,
    #urgency-options li:hover {
      background-color: #f3f4f6;
    }

    .dropdown-option {
      transition: background-color 0.2s ease;
    }

    .dropdown-option:hover {
      background-color: #f3f4f6;
    }

    /* Add styles for popup */
    .popup-overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.2);
      backdrop-filter: blur(4px);
      z-index: 1000;
    }

    .popup {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #ffffff;
      padding: 24px;
      border-radius: 12px;
      max-width: 500px;
      width: 90%;
      z-index: 1001;
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
    }

    .popup h2 {
      font-weight: 600;
      font-size: 1.25rem;
      color: #000000;
      margin-bottom: 0.5rem;
    }

    .popup-divider {
      height: 1px;
      background: #e5e7eb;
      margin: 1rem 0;
      width: 100%;
    }

    .success-message {
      display: none;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(255, 255, 255, 0.95);
      padding: 28px 36px;
      border-radius: 12px;
      text-align: center;
      z-index: 1002;
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      /* For Safari support */
      border: 1px solid rgba(229, 231, 235, 0.3);
      /* Made border more subtle */
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      font-weight: 500;
    }

    /* Add this new style for the background blur when success message appears */
    body.success-blur::after {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.2);
      backdrop-filter: blur(4px);
      -webkit-backdrop-filter: blur(4px);
      z-index: 1001;
      animation: fadeIn 0.3s ease-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }

    .popup-content strong {
      font-weight: 600;
      color: #000000;
    }

    .popup-content p {
      font-size: 0.9375rem;
      line-height: 1.6;
      margin-bottom: 0.75rem;
      color: #1f2937;
    }

    .estimated-time {
      font-size: 0.9375rem;
      color: #374151;
      margin-top: 1rem;
      font-weight: 500;
    }

    .confirm-button {
      padding: 0.625rem 1.25rem;
      background-color: black;
      color: white;
      border-radius: 8px;
      font-weight: 500;
      font-size: 0.9375rem;
      transition: all 0.2s ease;
    }

    /* Update animations */
    @keyframes fadeInScale {
      from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.98);
      }

      to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }
    }

    @keyframes fadeOut {
      from {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }

      to {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.98);
      }
    }

    /* Add this sidebar CSS to match dashboard.html */
    #sidebar {
      position: sticky;
      top: 0;
      height: 100vh;
      /* Force full viewport height */
      overflow-y: auto;
      /* Allow scrolling within the sidebar */
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  </style>
</head>

<body class="light">
  <!-- Loading Overlay -->
  {% include 'components/loading.html' %}

  <!-- Application Container -->
  <div id="app">
    <!-- Main Grid Layout -->
    <div class="grid min-h-screen w-full  lg:grid-cols-[280px_1fr] ">
      <!-- Sidebar -->
      {% include 'sidebar.html' %}

      <!-- Main Content -->
      <div class="flex flex-col">
        <header
          class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
          <div class="flex items-center gap-2 px-4 pl-0">
            <button id="toggle-btn" style="margin-left: 8px;"
              class="opacity-100 transition-opacity duration-300 focus:outline-none"
              style="background-color: transparent !important;">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-panel-left">
                <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                <path d="M9 3v18"></path>
              </svg>
            </button>
            <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-2 h-4"
              style="background-color: var(--border-color);"></div>
            <nav aria-label="breadcrumb">
              <ol class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
                <!-- Tasks link -->
                <div class="menubar" role="menubar">
                    <div class="menubar-indicator"></div>
                    <a href="/tasks" role="menuitem">Tasks</a>
                    <a href="/issue" role="menuitem" class="active">Issues</a>
                </div>
              </ol>
            </nav>
          </div>
          {% include 'topright.html' %}
        </header>

        <!-- Main Area -->
        <main class="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6 overflow-auto">
          <!-- Submit an Issue Section -->
          <div class="grid gap-4 md:grid-cols-1 lg:grid-cols-1">
            <div class="rounded-lg border shadow-sm card" data-v0-t="card">
              <div class="flex flex-col space-y-1.5 p-6">
                <h3 class="whitespace-nowrap text-2xl font-semibold leading-none tracking-tight">Submit an Issue</h3>
                <p class="text-sm text-muted-foreground">
                  Please provide details about the issue you are experiencing.
                </p>
              </div>
              <div class="p-6">
                <form class="grid gap-4">
                  <div class="grid grid-cols-3 gap-4">
                    <!-- Area Dropdown -->
                    <div class="grid gap-2">
                      <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="area">
                        Which area are you experiencing issues with ?
                      </label>
                      <div class="relative w-full">
                        <div id="area-selector"
                          class="theme-responsive-button theme-dropdown flex card items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer">
                          <span id="selected-area">Analytics</span>
                          <svg class="opacity-50" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <path d="m7 15 5 5 5-5"></path>
                            <path d="m7 9 5-5 5 5"></path>
                          </svg>
                        </div>
                        <ul id="area-options"
                          class="absolute z-50 hidden w-full py-1 mt-1 overflow-auto text-sm bg-background rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none dropdown-content">
                          <li class="px-3 py-2 cursor-pointer dropdown-option" data-value="Bots">Bots</li>
                          <li class="px-3 py-2 cursor-pointer dropdown-option" data-value="Analytics">Analytics</li>
                          <li class="px-3 py-2 cursor-pointer dropdown-option" data-value="Voice-AI">Voice assistants
                          </li>
                          <li class="px-3 py-2 cursor-pointer dropdown-option" data-value="AI and responses">AI and
                            responses</li>
                          <li class="px-3 py-2 cursor-pointer dropdown-option" data-value="Other">Other</li>
                        </ul>

                      </div>
                    </div>

                    <!-- Priority Dropdown -->
                    <div class="grid gap-2">
                      <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="priority">
                        What type of issue are you facing ?
                      </label>
                      <div class="relative w-full">
                        <div id="priority-selector"
                          class="theme-responsive-button theme-dropdown flex card items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer">
                          <span id="selected-priority">Backend (Data)</span>
                          <svg class="opacity-50" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <path d="m7 15 5 5 5-5"></path>
                            <path d="m7 9 5-5 5 5"></path>
                          </svg>
                        </div>
                        <ul id="priority-options"
                          class="absolute z-50 hidden w-full py-1 mt-1 overflow-auto text-sm bg-background rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none dropdown-content">
                          <li class="px-3 py-2 cursor-pointer dropdown-option" data-value="Frontend">Frontend (UI - UX)
                          </li>
                          <li class="px-3 py-2 cursor-pointer dropdown-option" data-value="Backend">Backend (Data)</li>
                          <li class="px-3 py-2 cursor-pointer dropdown-option" data-value="Both">Frontend and Backend
                          </li>
                          <li class="px-3 py-2 cursor-pointer dropdown-option" data-value="Unsure">Not sure</li>
                        </ul>

                      </div>
                    </div>

                    <!-- Urgency Dropdown -->
                    <div class="grid gap-2">
                      <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="urgency">
                        How urgent is this issue for you ?
                      </label>
                      <div class="relative w-full">
                        <div id="urgency-selector"
                          class="theme-responsive-button theme-dropdown flex card items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer">
                          <span id="selected-option">Medium</span>
                          <svg class="opacity-50" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <path d="m7 15 5 5 5-5"></path>
                            <path d="m7 9 5-5 5 5"></path>
                          </svg>
                        </div>
                        <ul id="urgency-options" <ul id="urgency-options"
                          class="absolute z-50 hidden w-full py-1 mt-1 overflow-auto text-sm bg-background rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none dropdown-content">
                          <li class="px-3 py-2 cursor-pointer dropdown-option" data-value="Low">Low</li>
                          <li class="px-3 py-2 cursor-pointer dropdown-option" data-value="Medium">Medium</li>
                          <li class="px-3 py-2 cursor-pointer dropdown-option" data-value="High">High</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div class="grid gap-2">
                    <label
                      class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      for="description">
                      Description
                    </label>
                    <textarea
                      class="card flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                      id="description" placeholder="Enter a detailed description of the issue"
                      style="height: 170px;"></textarea>
                  </div>
                  <div class="flex justify-end">
                    <button class="uk-button border card default mt-2" type="submit" aria-haspopup="true"
                      style="display: flex; align-items: center; gap: 8px; padding: 8px 12px; min-width: 110px; justify-content: center;">
                      <span>Submit Issue</span>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  </div>

  <!-- Add popup elements before closing body tag -->
  <div id="popup-overlay" class="popup-overlay">
    <div class="popup card">
      <h2 class="text-xl">Confirm Issue Submission</h2>
      <div class="popup-divider"></div>
      <div id="popup-content"></div>
      <div class="estimated-time mt-4 text-gray-600"></div>
      <div class="flex justify-end mt-6">
        <button onclick="submitIssue()" class="confirm-button">Confirm Submission</button>
      </div>
    </div>
  </div>

  <div id="success-message" class="success-message card">
    <h2>Issue Submitted Successfully 🎉</h2>
    <p>Our team will get back to you within the estimated time.</p>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const dropdowns = [
        { selector: 'area-selector', options: 'area-options', selected: 'selected-area' },
        { selector: 'priority-selector', options: 'priority-options', selected: 'selected-priority' },
        { selector: 'urgency-selector', options: 'urgency-options', selected: 'selected-option' }
      ];

      // Close all dropdowns
      function closeAllDropdowns() {
        dropdowns.forEach(({ options }) => {
          const optionsEl = document.getElementById(options);
          if (!optionsEl.classList.contains('hidden')) {
            startCloseAnimation(optionsEl);
          }
        });
      }

      // Start the close animation
      function startCloseAnimation(element) {
        // Don't animate if already transitioning
        if (element.classList.contains('transitioning')) return;

        element.classList.add('transitioning');
        element.setAttribute('data-state', '');

        // Wait for animation to finish before hiding
        setTimeout(() => {
          element.classList.add('hidden');
          element.classList.remove('transitioning');
        }, 200); // Match animation duration
      }

      // Start the open animation
      function startOpenAnimation(element) {
        // Set initial state
        element.classList.remove('hidden');
        element.style.transformOrigin = 'top left';
        element.setAttribute('data-state', '');

        // Use requestAnimationFrame to ensure browser processes the DOM before animation
        requestAnimationFrame(() => {
          element.setAttribute('data-state', 'open');
        });
      }

      // Initialize each dropdown
      dropdowns.forEach(({ selector, options, selected }) => {
        const selectorEl = document.getElementById(selector);
        const optionsEl = document.getElementById(options);
        const selectedEl = document.getElementById(selected);

        // Toggle dropdown
        selectorEl.addEventListener('click', (e) => {
          e.stopPropagation();
          const isHidden = optionsEl.classList.contains('hidden');
          closeAllDropdowns();
          if (isHidden) {
            startOpenAnimation(optionsEl);
          }
        });

        // Handle option selection
        optionsEl.querySelectorAll('li').forEach(option => {
          option.addEventListener('click', (e) => {
            e.stopPropagation();
            selectedEl.textContent = option.textContent;
            startCloseAnimation(optionsEl);
          });
        });
      });

      // Close dropdowns when clicking outside
      document.addEventListener('click', closeAllDropdowns);
    });

    // Add this after the existing script
    document.querySelector('form').addEventListener('submit', function (e) {
      e.preventDefault();
      showPopup();
    });

    function showPopup() {
      const area = document.getElementById('selected-area').textContent;
      const priority = document.getElementById('selected-priority').textContent;
      const urgency = document.getElementById('selected-option').textContent;
      const description = document.getElementById('description').value;

      // Calculate estimated response time based on urgency
      let estimatedTime;
      switch (urgency) {
        case 'High':
          estimatedTime = '2-4 hours';
          break;
        case 'Medium':
          estimatedTime = '7 hours';
          break;
        case 'Low':
          estimatedTime = '14 hours';
          break;
        default:
          estimatedTime = '7 hours';
      }

      const content = `
  <div class="grid gap-2">
    <p><strong>Area:</strong> ${area}</p>
    <p><strong>Issue Type:</strong> ${priority}</p>
    <p><strong>Urgency:</strong> ${urgency}</p>
    <p><strong>Description:</strong> ${description}</p>
  </div>
  `;

      document.getElementById('popup-content').innerHTML = content;
      document.querySelector('.estimated-time').textContent = `Estimated Response Time: ${estimatedTime}`;
      document.getElementById('popup-overlay').style.display = 'block';
    }

    function closePopup() {
      document.getElementById('popup-overlay').style.display = 'none';
    }

    function submitIssue() {
      closePopup();

      document.body.classList.add('success-blur'); // Add this line
      const successMessage = document.getElementById('success-message');
      successMessage.style.display = 'block';
      successMessage.style.animation = 'fadeInScale 0.3s ease-out';

      // Trigger confetti
      confetti({
        particleCount: 150,
        spread: 80,
        origin: { y: 0.6 },
        colors: ['#000', '#333', '#666']
      });

      // Hide success message with fade out animation
      setTimeout(() => {
        successMessage.style.animation = 'fadeOut 0.3s ease-out';
        document.body.classList.remove('success-blur'); // Add this line
        setTimeout(() => {
          successMessage.style.display = 'none';
          // Reset form
          document.querySelector('form').reset();
          document.getElementById('selected-area').textContent = 'Analytics';
          document.getElementById('selected-priority').textContent = 'Backend (Data)';
          document.getElementById('selected-option').textContent = 'Medium';
        }, 300);
      }, 2700);
    }

    // Close popup when clicking outside
    document.getElementById('popup-overlay').addEventListener('click', function (e) {
      if (e.target === this) {
        closePopup();
      }
    });
  </script>
</body>

</html>