<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Test</title>

    <!-- Inter Font -->
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />

    <!-- Tailwind CSS (assuming this is part of your imports.html) -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        background: 'hsl(var(--background))',
                        foreground: 'hsl(var(--foreground))',
                        card: 'hsl(var(--card))',
                        'card-foreground': 'hsl(var(--card-foreground))',
                        border: 'hsl(var(--border))',
                        accent: {
                            DEFAULT: 'hsl(var(--accent))',
                            foreground: 'hsl(var(--accent-foreground))',
                        }
                    }
                }
            }
        }
    </script>

    <!-- UIkit CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.19.2/dist/css/uikit.min.css" />

    <!-- UIkit JS -->
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.19.2/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.19.2/dist/js/uikit-icons.min.js"></script>

    <style>
        /* Base CSS variables - these would be in your main CSS or theme setup */
        body {
            font-family: 'Inter', sans-serif;
            padding: 20px;
        }

        .light {
            --background: 0 0% 100%; /* white */
            --foreground: 0 0% 3.9%; /* black */
            --card: 0 0% 100%; /* white */
            --card-foreground: 0 0% 3.9%; /* black */
            --border: 0 0% 89.8%; /* #e5e7eb */
            --border-color: #e5e7eb; /* Specific for notification if not using HSL */
            --accent: 0 0% 96.1%;
            --accent-foreground: 0 0% 9%;
        }

        .pure-black {
            --background: 0 0% 3.9%; /* #09090b */
            --foreground: 0 0% 98%; /* white */
            --card: 0 0% 3.9%; /* #09090b */
            --card-foreground: 0 0% 98%; /* white */
            --border: 0 0% 14.9%; /* #27272a */
            --border-color: #27272a; /* Specific for notification if not using HSL */
            --accent: 0 0% 14.9%;
            --accent-foreground: 0 0% 98%;
        }

        /* Apply Tailwind base styles for background and text */
        body.light {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }
        body.pure-black {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }


        /* Style UIkit success notifications to look like cards (copied from tasks.html) */
        .uk-notification-message.uk-notification-message-success {
            background-color: hsl(var(--card)); /* Use HSL variable */
            color: hsl(var(--card-foreground));    /* Use HSL variable */
            border: 1px solid var(--border-color); /* Using the direct hex value for simplicity or ensure --border maps correctly */
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
            background-image: none;
            padding: 12px 16px;
        }

        /* Ensure notification close button is always visible (copied from tasks.html) */
        .uk-notification-message .uk-notification-close {
            opacity: 0.6 !important;
        }

        /* Basic button styling to make it visible */
        #showNotificationBtn {
            background-color: #1e87f0; /* UIkit primary blue */
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
        }
        #showNotificationBtn:hover {
            background-color: #106ebe;
        }

    </style>
</head>
<body class="light"> {/* Change to "pure-black" to test the dark theme */}

    <button id="showNotificationBtn" class="uk-button uk-button-primary">Show Completion Notification</button>
    <button onclick="toggleTheme()" class="uk-button uk-button-default uk-margin-left">Toggle Theme</button>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const showNotificationButton = document.getElementById('showNotificationBtn');

            if (showNotificationButton) {
                showNotificationButton.addEventListener('click', function () {
                    const taskId = 'TEST123'; // Example Task ID
                    UIkit.notification({
                        message: `Task #${taskId} completed.`,
                        status: 'success',
                        pos: 'bottom-right',
                        timeout: 4000
                    });
                });
            }
        });

        function toggleTheme() {
            const body = document.body;
            if (body.classList.contains('light')) {
                body.classList.remove('light');
                body.classList.add('pure-black');
            } else {
                body.classList.remove('pure-black');
                body.classList.add('light');
            }
        }
    </script>

</body>
</html>