import asyncio
from typing import Optional
from realtime import Async<PERSON>ealtimeClient, RealtimeSubscribeStates

async def main():
    REALTIME_URL = "wss://nuqxdjuaoccswunhqixz.supabase.co/realtime/v1"
    API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im51cXhkanVhb2Njc3d1bmhxaXh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTcxMjI2OTcsImV4cCI6MjAzMjY5ODY5N30.sHkkzEb5oCTlLB3MQ0420XtJpURXW1DIHuHm4M9kDPI"

    socket = AsyncRealtimeClient(REALTIME_URL, API_KEY)
    channel = socket.channel("Task_list")

    def on_subscribe(status: RealtimeSubscribeStates, err: Optional[Exception]):
        if status == RealtimeSubscribeStates.SUBSCRIBED:
            print("Connected!")
        elif err:
            print(f"Error: {err}")

    def handle_db_changes(payload):
        print("Change received:", payload)

    subscription = channel.on_postgres_changes(
        event='*',
        schema='public',
        table='Task_list',
        callback=handle_db_changes
    )

    await channel.subscribe(on_subscribe)

    try:
        while True:
            await asyncio.sleep(1)
    except asyncio.CancelledError:
        await socket.close()

if __name__ == "__main__":
    asyncio.run(main())