/**
 * <PERSON>ubar Component
 * 
 * This script handles the interactive menubar with sliding indicator.
 * It automatically positions the indicator under the active menu item
 * and animates it when hovering over other menu items.
 */

document.addEventListener('DOMContentLoaded', function () {
    // Find all menubar elements on the page
    const menubarElements = document.querySelectorAll('.menubar');

    menubarElements.forEach(menubar => {
        const indicator = menubar.querySelector('.menubar-indicator');
        const links = Array.from(menubar.querySelectorAll('a[role="menuitem"]'));

        // Skip initialization if required elements are missing
        if (!indicator || links.length === 0) return;

        /**
         * Moves the indicator to the target element
         * @param {HTMLElement} target - The target element to move the indicator to
         */
        function moveIndicator(target) {
            if (!target || !indicator) return;
            // Use offsetLeft and offsetWidth as they are relative to the offsetParent (menubar)
            indicator.style.width = target.offsetWidth + 'px';
            indicator.style.left = target.offsetLeft + 'px';
        }

        // Find the active link and position the indicator
        let activeLink = menubar.querySelector('a[role="menuitem"].active');
        
        if (activeLink) {
            // Needs a slight delay for accurate measurement if page is complex or fonts are loading
            setTimeout(() => moveIndicator(activeLink), 100);
        }

        // Add event listeners to each link
        links.forEach(link => {
            // Move indicator on hover
            link.addEventListener('mouseenter', () => moveIndicator(link));
            
            // Move indicator on focus (for accessibility)
            link.addEventListener('focus', () => moveIndicator(link));
            
            // Handle click - indicator moves before navigation
            link.addEventListener('click', function(e) {
                // Allow default navigation. Indicator moves on mouseenter/focus.
                // For server-rendered pages, the new page will set its active link.
                moveIndicator(link); // Ensure indicator moves on click before navigation
            });
        });

        // Return indicator to active link when mouse leaves menubar
        menubar.addEventListener('mouseleave', () => {
            const currentActiveLink = menubar.querySelector('a[role="menuitem"].active');
            if (currentActiveLink) {
                moveIndicator(currentActiveLink);
            }
        });

        // Reposition indicator on window resize
        window.addEventListener('resize', () => {
            const currentActiveLink = menubar.querySelector('a[role="menuitem"].active');
            if (currentActiveLink) {
                moveIndicator(currentActiveLink);
            }
        });
    });
});
