<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
  <title>Guest Genius AI</title>
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  
  <!-- Custom Styles -->
  <link rel="stylesheet" href="../static/styles/custom.css">
  <link rel="stylesheet" href="../static/styles/loadinganimations.css">
  
  <!-- UIkit Styles -->
  <link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/yellow.min.css"/>
  
  <!-- Preloaded Fonts -->
  <link rel="preload" href="/fonts/geist-font/fonts/GeistVariableVF.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="/fonts/geist-font/fonts/GeistMonoVariableVF.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="stylesheet" href="/fonts/geist-font/style.css">
  
  <!-- UIkit Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit-icons.min.js"></script>
  
  <!-- Additional Scripts -->
  <script src="/js/htmx@2.0.0/htmx.min.js"></script>
  <script src="https://unpkg.com/@phosphor-icons/web"></script>
  <script type="module" src="/js/franken-wc@0.0.6/wc.iife.js"></script>
  
  <!-- Astro Styles -->
  <link rel="stylesheet" href="/_astro/master.CZ5-T1HD.css">
  
  <!-- Loading Animations -->
  <link rel="stylesheet" href="../static/styles/loadinganimations.css">
  <link rel="stylesheet" href="../static/styles/scrollbar.css">
  <script src="../static/js/loading.js" defer></script>
  <script src="../static/js/themes.js" defer></script>
  <script>
    tailwind.config = {
      darkMode: 'class',
    }
  </script>
  
  <style>
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
    }
    #app {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    @keyframes messageIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .divider {
      margin: 7px 0 0 0;
      padding: 0;
    }
    #dropdown-menu {
      display: none;
    }
    #dropdown-button {
      color: #4a5568;
    }
    #dropdown-button:hover,
    #dropdown-button:focus {
      color: #2d3748;
    }
    #dropdown-button.clicked + #dropdown-menu {
      display: block;
    }
    main {
      flex-grow: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
    #chat-messages {
      flex-grow: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }
    * {
      scrollbar-width: none;
      -ms-overflow-style: none;
    }
    *::-webkit-scrollbar {
      display: none;
    }
    .user-item {
      cursor: pointer;
      border-bottom: 1px solid #e5e7eb;
    }
    .user-item:last-child {
      border-bottom: none;
    }
    #chat-container {
      height: 600px;
      overflow-y: auto;
    }
    .message-container {
      display: flex;
      align-items: flex-start;
      margin-bottom: 10px;
    }
    .message-container.left {
      animation: slideInLeft 0.3s ease-out;
    }
    .message-container.right {
      animation: slideInRight 0.3s ease-out;
    }
    @keyframes slideInLeft {
      from {
        opacity: 0;
        transform: translateX(-20px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }
    @keyframes slideInRight {
      from {
        opacity: 0;
        transform: translateX(20px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }
    .message-icon {
      width: 24px;
      height: 24px;
      flex-shrink: 0;
      background-size: cover;
      background-position: center;
    }
    .message-icon.user {
      background-image: url('https://cdn-icons-png.flaticon.com/128/17487/17487660.png');
      margin-right: 15px;
    }
    .message-icon.bot {
      background-image: url('https://cdn-icons-png.flaticon.com/128/17487/17487660.png');
      margin-left: 15px;
    }
    .message-left, .message-right {
      max-width: 70%;
      padding: 10px;
      border-radius: 8px;
      word-wrap: break-word;
    }
    .message-left {
      background-color: #f0f0f0;
      color: black;
      margin-right: auto;
    }
    .message-right {
      background-color: #f0f0f0;
      color: black;
      margin-left: auto;
    }
    .timestamp {
      cursor: pointer;
    }
    #message-input-container {
      display: flex;
      align-items: center;
      border: 1px solid #ccc;
      border-radius: 0px;
      padding: 10px;
      flex-grow: 1;
    }
    #message-input {
      flex: 1;
      border: none;
      resize: none;
      font-size: 14px;
      outline: none;
    }
    #send-button svg {
      width: 32px;
      height: 32px;
    }
    .user-row {
      padding: 15px;
      transition: background-color 0.3s ease;
      border-bottom: 1px solid #e5e7eb;
    }
    .user-row:hover, .user-row.bg-gray-200 {
      background-color: #f3f4f6;
    }
    .user-item:last-child .user-row {
      border-bottom: none;
      margin-bottom: -8px;
    }
    #user-list {
      padding: 0;
    }
    .rounded-lg {
      border-radius: 1rem;
    }
    .chat-input-container {
      position: sticky;
      bottom: 0;
      z-index: 10;
    }
    #user-list {
      padding-top : 2rem !important;
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }
    #user-list {
      max-height: calc(100vh - 180px);
      overflow-y: auto;
      padding-top: 1rem !important;
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }

    .tag-mail {
      margin-bottom: 10px;
    }

    .uk-label {
      background: transparent;
      color: currentColor;
      border: 1px solid currentColor;
      padding: 2px 6px;
      font-size: 0.75rem;
      border-radius: 4px;
    }
    .uk-label-platform{
      background: transparent;
      color: currentColor;
      border: 1px solid currentColor;
      padding: 2px 6px;
      font-size: 0.75rem;
      border-radius: 4px;
      text-transform: none;
    }
    .flex-grow {
      flex-grow: 1;
    }

    .grid-cols-custom {
      display: grid;
      grid-template-columns: 550px 1fr;
    }

    #chat-container {
      min-height: 0;
    }

    #chat-interface {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .message-bubble {
      padding: 10px;
      border-radius: 8px;
      word-wrap: break-word;
    }

    .message-bubble p {
      margin: 0;
    }
  </style>
</head>
<body class="light">
  <!-- Loading Overlay -->
  <div id="loading-overlay" class="loading-overlay">
    <div class="typing-indicator">
      <div class="typing-circle"></div>
      <div class="typing-circle"></div>
      <div class="typing-circle"></div>
      <div class="typing-shadow"></div>
      <div class="typing-shadow"></div>
      <div class="typing-shadow"></div>
    </div>
  </div>

  <!-- Application Container -->
  <div id="app">
    <!-- Main Grid Layout -->
    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr]">
      <!-- Sidebar -->
      {% include 'sidebar.html' %}
      
      <!-- Main Content -->
      <div class="flex flex-col">
        <!-- Header -->
        <header class="flex h-14 lg:h-[60px] items-center gap-4 border-b px-6 card">
          <a class="lg:hidden" href="#">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                  <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                  <path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9"></path>
                  <path d="M12 3v6"></path>
              </svg>
              <span class="sr-only">Home</span>
          </a>
          <h1 class="font-semibold text-lg">Guest Genius AI</h1>                
        </header>
        
        <!-- Main Area -->
        <main class="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6 overflow-auto">
          <!-- Live Chat Integration -->
          <div class="grid gap-4 md:grid-cols-1 lg:grid-cols-1">
            <div class="rounded-lg border text-card-foreground shadow-sm card" data-v0-t="chat-card">
              <div class="flex flex-col min-h-screen">
                <div class="flex divide-x divide-border flex-grow">
                  <div class="grid flex-1 grid-cols-custom divide-x divide-border">
                    <div class="flex flex-col " uk-filter="target: .js-filter">
                      <div class="flex h-14 flex-none items-center border-b border-border px-4 py-2 card justify-between">
                        <div class="p-4">
                          <div class="uk-inline w-full">
                            <span class="uk-form-icon text-muted-foreground">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.3-4.3"></path>
                              </svg>
                            </span>
                            <input class="uk-input" type="text" placeholder="Search">
                          </div>
                        </div>
                        <ul class="uk-tab-alt ml-auto max-w-40">
                          <!-- Tabs can be added here if needed -->
                        </ul>
                      </div>
                      <div class="flex flex-1 flex-col">
                        <div class="max-h-[100vh] flex-1 overflow-y-auto">
                          <ul class="js-filter space-y-2 p-4 pt-0" id="user-list">
                            <!-- User list items will be populated here -->
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="flex flex-col flex-grow overflow-hidden relative">
                    <div id="initial-message" class="flex items-center justify-center h-full">
                      <h2 class="text-2xl font-semibold text-gray-500">Select a user to start the chat :)</h2>
                    </div>
                    <div id="chat-interface" class="flex flex-col h-full" style="display: none;">
                      <div class="flex h-14 flex-none items-center border-b border-border p-2 card">
                        <!-- Container for the title and buttons -->
                        <div class="flex w-full justify-between items-center">
                          <!-- Title taking up full space -->
                          <h3 id="chat-title" class="text-lg font-semibold flex-grow ps-1">Chat Title</h3>
                          <!-- Buttons taking up only required space -->
                          <div class="flex gap-x-2 divide-x divide-border">
                            <ul class="uk-iconnav">
                              <li>
                                <a href="#demo">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2">
                                    <path d="M3 6h18"></path>
                                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                    <line x1="10" x2="10" y1="11" y2="17"></line>
                                    <line x1="14" x2="14" y1="11" y2="17"></line>
                                  </svg>
                                </a>
                              </li>
                              <li>
                                <a href="#demo">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12 6 12 12 16 14"></polyline>
                                  </svg>
                                </a>
                              </li>
                              <ul class="uk-iconnav pl-2 b-l border-left">
                                <li>
                                  <a href="#demo" aria-haspopup="true">
                                    <span class="sr-only">Menu</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis-vertical">
                                      <circle cx="12" cy="12" r="1"></circle>
                                      <circle cx="12" cy="5" r="1"></circle>
                                      <circle cx="12" cy="19" r="1"></circle>
                                    </svg>
                                  </a>
                                  <div class="uk-dropdown uk-drop" uk-dropdown="pos: bottom-right; mode: click">
                                    <ul class="uk-dropdown-nav uk-nav">
                                      <li>
                                        <a class="uk-drop-close" href="#demo" uk-toggle="" role="button">
                                          Restrict user
                                        </a>
                                      </li>
                                      <li>
                                        <a class="uk-drop-close" href="#demo" uk-toggle="" role="button">
                                          Block number
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </li>
                              </ul>
                            </ul>
                          </div>
                        </div>
                      </div>
                      <div class="flex flex-col flex-grow overflow-hidden relative">
                        <div class="flex-1 overflow-y-auto p-4 absolute top-0 left-0 right-0 bottom-16" id="chat-container">
                          <!-- Chat messages will be displayed here -->
                        </div>
                        <div class="chat-input-container py-4 border-t border-border px-4 absolute bottom-0 left-0 right-0 card">
                          <div class="flex">
                            <input id="message-input" 
                              class="uk-input flex-1 rounded-md focus:outline-none focus:ring-2 focus:ring-black-50"
                              style="border: 1px solid #e7e5e4 !important; padding: 0.5rem 1rem;" 
                              type="text" 
                              placeholder="Type a message...">
                            <button id="send-button" class="uk-button ml-2 bg-blue-500 text-white rounded-md hover:bg-blue-600" style="padding: 10px 20px;">Send</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  </div>
      
  <!-- Live Chat Scripts -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const userList = document.getElementById('user-list');
      const chatContainer = document.getElementById('chat-container');
      const messageInput = document.getElementById('message-input');
      const sendButton = document.getElementById('send-button');
      const chatTitle = document.getElementById('chat-title');
      const loadingOverlay = document.getElementById('loading-overlay');
      let selectedUser = null;
      let chatData = null;

      // Sample Data
      const sampleChatData = {
        users: [
          { id: 1, name: 'Harsh Jadhav', phone_number: '************', room_number: '201', platform: 'Slack', avatar: 'https://randomuser.me/api/portraits/men/1.jpg' },
          { id: 2, name: 'Dixith Mediga', phone_number: '************', room_number: '202', platform: 'Discord', avatar: 'https://randomuser.me/api/portraits/men/2.jpg' }
        ],
        chats: {
          '1': [
            { sender: 'Harsh Jadhav', message: 'Hey team, can someone help me with the new deployment process?', timestamp: '09:00 AM', customer: true },
            { sender: 'Agent', message: 'Sure Harsh, I can walk you through it.', timestamp: '09:05 AM', customer: false },
          ],
          '2': [
            { sender: 'Dixith Mediga', message: 'Is the database migration completed?', timestamp: '10:15 AM', customer: true },
            { sender: 'Agent', message: 'Yes, all migration scripts have been executed successfully.', timestamp: '10:20 AM', customer: false },
          ]
        }
      };

      // Initialize the chat interface with sample data
      chatData = sampleChatData;
      updateUserList();

      function updateUserList() {
        userList.innerHTML = '';
        chatData.users.forEach(user => {
          const userChats = chatData.chats[user.id] || [];
          const latestMessage = userChats[userChats.length - 1];
          const latestMessageTime = latestMessage ? latestMessage.timestamp : 'No messages';

          const li = document.createElement('li');
          li.className = 'tag-mail relative rounded-lg border border-border p-3 text-sm hover:bg-accent card';
          li.setAttribute('data-user', user.id);
          li.innerHTML = `
            <div class="flex w-full flex-col gap-1">
              <div class="flex items-center">
                <div class="flex items-center gap-2">
                  <div class="font-semibold">${user.name}</div>
                </div>
                <div class="ml-auto text-xs ">${latestMessageTime}</div>
              </div>
              <div class="text-xs font-medium">
                ${user.phone_number}
              </div>
              <div class="flex items-center gap-2 mt-2">
                <span class="uk-label-platform">Room  : ${user.room_number}</span>
                ${user.platform.toLowerCase() === 'whatsapp' 
                  ? `<img src="static/images/whatsapp.png" alt="WhatsApp" class="w-5 h-5">
                     <img src="https://flagpedia.net/data/flags/emoji/twitter/256x256/gb.png" alt="UK Flag" class="w-5 h-5">` 
                  : `<span class="uk-label-platform">${user.platform}</span>`}
              </div>
            </div>
          `;
          userList.appendChild(li);
        });
      }

      userList.addEventListener('click', function(e) {
        const userItem = e.target.closest('[data-user]');
        if (userItem) {
          selectedUser = userItem.getAttribute('data-user');
          // Ensure selectedUser is a string
          selectedUser = selectedUser.toString();
          const user = chatData.users.find(u => u.id.toString() === selectedUser);
          if (user) {
            chatTitle.innerHTML = `
              <div class="flex items-center">
                <img src="${user.avatar}" alt="${user.name}" class="w-8 h-8 rounded-full mr-2">
                <h3 class="text-lg font-semibold">${user.name}</h3>
              </div>
            `;
            loadChatHistory(selectedUser);
            showChatInterface(true);
          }
        }
      });

      function loadChatHistory(userId) {
        chatContainer.innerHTML = '';
        const messages = chatData.chats[userId] || [];
        messages.forEach(msg => displayMessage(msg.sender, msg.message, msg.customer));
      }

      function sendMessage() {
        if (!selectedUser) {
          alert("Please select a developer to chat with first.");
          return;
        }
        const message = messageInput.value.trim();
        if (message) {
          // Optimistic UI update
          displayMessage('Agent', message, false);
          messageInput.value = '';

          // Simulate sending message
          setTimeout(() => {
            // Optionally, add the message to chatData.chats
            if (!chatData.chats[selectedUser]) {
              chatData.chats[selectedUser] = [];
            }
            chatData.chats[selectedUser].push({
              sender: 'Agent',
              message: message,
              timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),
              customer: false
            });
            chatContainer.scrollTop = chatContainer.scrollHeight;
          }, 500);
        }
      }

      function displayMessage(sender, text, isCustomer) {
        const messageElement = document.createElement('div');
        messageElement.className = `message-container ${isCustomer ? 'justify-start' : 'justify-end'} flex items-end mb-4`;

        messageElement.innerHTML = `
          <div class="${isCustomer ? 'mr-3' : 'order-2 ml-3'}">
            <span class="flex h-10 w-10 items-center justify-center rounded-full bg-black text-white">${sender.charAt(0).toUpperCase()}</span>
          </div>
          <div class="message-bubble ${isCustomer ? 'bg-gray-200 text-black' : 'bg-black text-white'} p-3 rounded-lg max-w-[80%]">
            <p class="text-sm">${text}</p>
          </div>
        `;
        
        chatContainer.appendChild(messageElement);
        
        // Force a reflow to ensure the animation plays
        void messageElement.offsetWidth;
        
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }

      messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          e.preventDefault(); // Prevent default to avoid form submission
          sendMessage();
        }
      });

      sendButton.addEventListener('click', function() {
        sendMessage();
      });

      function showChatInterface(show) {
        const initialMessage = document.getElementById('initial-message');
        const chatInterface = document.getElementById('chat-interface');
        
        initialMessage.style.display = show ? 'none' : 'flex';
        chatInterface.style.display = show ? 'flex' : 'none';
      }
    });
  </script>
</body>
</html>