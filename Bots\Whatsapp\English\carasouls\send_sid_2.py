# Download the helper library from https://www.twilio.com/docs/python/install
import os
from twilio.rest import Client
import json

# Find your Account SID and Auth Token at twilio.com/console
# and set the environment variables. See http://twil.io/secure
TWILIO_ACCOUNT_SID="**********************************"
TWILIO_AUTH_TOKEN="005d910f85546392a91f58a3878c437c"

# --- Block for the new SID ---
try:
    # Initialize client with correct credentials
    client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

    new_sid = "HX50460e90855bb0fd7417eccb26e4e63a"

    message = client.messages.create(
        content_sid=new_sid,
        # No content_variables needed as per request
        to="whatsapp:+************",
        from_="whatsapp:+***********"
    )

    print(f"Message sent using SID {new_sid}. Message SID: {message.sid}")

except Exception as e:
    print(f"Error sending message: {str(e)}")