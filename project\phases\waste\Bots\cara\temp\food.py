import requests
import json

# Your Twilio account details (replace with your actual Account SID and Auth Token)
ACCOUNT_SID = '**********************************'
AUTH_TOKEN = '005d910f85546392a91f58a3878c437c'

# Twilio API endpoint for content creation
url = 'https://content.twilio.com/v1/Content'

# Payload data for the carousel with updated menu items and descriptions
payload = {
    "friendly_name": "v6_food_menu_carousel",
    "language": "en",
    "types": {
        "twilio/carousel": {
            "body": "Savor exceptional cuisine with our outstanding food services!",
            "cards": [
                {
                    "title": "Burrata",
                    "body": "Roasted figs, jamon ibérico, watercress, truffle & toasted almonds\nPrice : €21.50",
                    "media": "https://dixith-dev.github.io/food_menu/images/burrata.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_burrata"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "burrata_allergies"
                        }
                    ]
                },
                {
                    "title": "Roasted Pumpkin Dish",
                    "body": "Roasted pumpkin with coconut soup\nPrice: €16.00",
                    "media": "https://dixith-dev.github.io/food_menu/images/pumpkinwithcoconut.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_pumpkin"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "pumpkin_allergies"
                        }
                    ]
                },
                {
                    "title": "Lobster Roll",
                    "body": "Buttered brioche, lime mayo, leek, lemon\nPrice: €28.00",
                    "media": "https://dixith-dev.github.io/food_menu/images/lobsterroll.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_lobsterroll"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "lobsterroll_allergies"
                        }
                    ]
                },
                {
                    "title": "Sirloin",
                    "body": "300g - Tender yet succulent with a strip of juicy crackling\nPrice: €32.00",
                    "media": "https://dixith-dev.github.io/food_menu/images/filletsteak.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_sirloin"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "sirloin_allergies"
                        }
                    ]
                },
                {
                    "title": "Cornfed Chicken",
                    "body": "Cornfed chicken breast with truffled potato, spring onion and morels\nPrice: €28.00",
                    "media": "https://dixith-dev.github.io/food_menu/images/chicken.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_chicken"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "chicken_allergies"
                        }
                    ]
                },
                {
                    "title": "T-bone",
                    "body": "900g - Ideal to share, all the sauces & fries\nPrice: €110.00",
                    "media": "https://dixith-dev.github.io/food_menu/images/tbone.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_tbone"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "tbone_allergies"
                        }
                    ]
                },
                {
                    "title": "Steak & Lobster",
                    "body": "Ribeye marbled for flavour 1/2 Lobster with fries, garlic butter, lemon\nPrice: €79",
                    "media": "https://dixith-dev.github.io/food_menu/images/steakandlobster.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_steaklobster"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "steaklobster_allergies"
                        }
                    ]
                },
                {
                    "title": "Grilled Salmon",
                    "body": "Miso, honey, lemon, watercress, spinach, salmon roe\nPrice: €31.00",
                    "media": "https://dixith-dev.github.io/food_menu/images/grilledsalmon.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_salmon"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "salmon_allergies"
                        }
                    ]
                }
            ]
        }
    }
}

# Make the POST request to the Twilio API
response = requests.post(
    url,
    auth=(ACCOUNT_SID, AUTH_TOKEN),
    headers={'Content-Type': 'application/json'},
    data=json.dumps(payload)
)

# Check the response status
if response.status_code == 201:
    print("Carousel created successfully!")
    print(response.json())
else:
    print(f"Failed to create carousel: {response.status_code}")
    print(response.text)