<!-- Staff Assignment Modal -->
<div id="staff-assignment-modal" class="fixed inset-0 z-50 hidden" style="z-index: 2000;">
  <!-- Backdrop with blur effect -->
  <div class="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>

  <!-- Modal container -->
  <div class="relative flex items-center justify-center min-h-screen p-4">
    <!-- Using exact same markup as isolated.html -->
    <div class="universal-background-color max-w-2xl w-full rounded-lg shadow-lg">
      <div id="staff-members" class="uk-card card">
        <div class="uk-card-header pb-0">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold leading-none tracking-tight">
              Assign Staff members
            </h3>
            <button id="close-staff-modal" class="text-muted-foreground hover:text-foreground" style="background-color: transparent;">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
              </svg>
            </button>
          </div>
          <p class="mt-1.5 text-sm">
            Assign staff members to this hotel. Staff members will be able to access the hotel using the link below.
          </p>
        </div>
        <div class="uk-card-body pt-0">
          <hr class="card my-4">
          <div class="space-y-4" id="staff-container">
            <h4 class="text-sm font-medium">Staff members with access : </h4>
            <!-- Staff members will be dynamically rendered here -->
          </div>
        </div>
      </div>
      <!-- Modal footer -->
      <div class="p-4 border-t card border-border flex justify-end gap-2">
        <button id="cancel-staff-assignment" class="uk-button uk-button-default card" >Cancel</button>
        <button id="confirm-staff-assignment" class="uk-button uk-button-primary card">Save Staff Configuration</button>
      </div>
    </div>
  </div>
</div>

<style>
  /* Modal styles */
  #staff-assignment-modal {
    transition: opacity 0.3s ease;
  }

  #staff-assignment-modal.hidden {
    opacity: 0;
    pointer-events: none;
  }

  #staff-assignment-modal:not(.hidden) {
    opacity: 1;
    pointer-events: auto;
  }

  /* Backdrop blur effect */
  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }

  /* Modal appearance animation */
  #staff-members {
    background-color: var(--card-background);
    transition: transform 0.3s ease, opacity 0.3s ease;
    transform: translateY(0);
    opacity: 1;
  }

  #staff-assignment-modal.hidden #staff-members {
    transform: translateY(20px);
    opacity: 0;
  }

  .light .themer-input {
    background-color: transparent !important;
    color: black !important;
    border: transparent !important;
    outline: none !important;
    /* Removes the focus border */
    box-shadow: none !important;
  }

  .pure-black .themer-input {
    background-color: transparent !important;
    color: white !important;
    border: transparent !important;
    border: transparent !important;
    outline: none !important;
    /* Removes the focus border */
    box-shadow: none !important;
  }

  .uk-label {
    color: var(--dropdown-text);
    background-color: var(--background);
  }

  .uk-drop.uk-dropdown {
    color: var(--dropdown-text);
  }

  .light .uk-table-divider>tr:not(:first-child),
  .uk-table-divider>:not(:first-child)>tr,
  .uk-table-divider>:first-child>tr:not(:first-child) {
    border-color: #e5e7eb;
  }

  .pure-black .uk-table-divider>tr:not(:first-child),
  .pure-black .uk-table-divider>:not(:first-child)>tr,
  .pure-black .uk-table-divider>:first-child>tr:not(:first-child) {
    border-color: #27272a;
  }

  .light .uk-label {
    background-color: white;
  }

  .pure-black .uk-label {
    background-color: #09090b;
  }
</style>