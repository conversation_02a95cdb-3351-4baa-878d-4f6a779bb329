from flask import Blueprint, request, jsonify
import os
import json
from datetime import datetime

# Create a Blueprint for settings
settings_bp = Blueprint('settings', __name__)

# Create settings folder path
SETTINGS_FOLDER = os.path.join(os.path.dirname(__file__), 'settings_data')

# Ensure settings folder exists
os.makedirs(SETTINGS_FOLDER, exist_ok=True)

# Configuration file paths
PMS_CONFIG_FILE = os.path.join(SETTINGS_FOLDER, 'pms_config.json')
LANGUAGE_CONFIG_FILE = os.path.join(SETTINGS_FOLDER, 'language_settings.json')
STAFF_CONFIG_FILE = os.path.join(SETTINGS_FOLDER, 'staff_settings.json')
NOTIFICATIONS_CONFIG_FILE = os.path.join(SETTINGS_FOLDER, 'notifications_settings.json')
KNOWLEDGE_BASE_FILE = os.path.join(SETTINGS_FOLDER, 'knowledge_base.txt')


# ==================== PMS Configuration Routes ====================

@settings_bp.route('/api/pms/config/save', methods=['POST'])
def save_pms_config():
    try:
        print("Saving PMS config data")
        config_data = request.json
        
        # Add server-side timestamp
        config_data['serverTimestamp'] = datetime.now().isoformat()
        
        # Ensure settings folder exists
        os.makedirs(SETTINGS_FOLDER, exist_ok=True)
            
        # Save to JSON file with more explicit error handling
        with open(PMS_CONFIG_FILE, 'w') as f:
            json.dump(config_data, f, indent=2)
            print(f"Successfully wrote config data to {PMS_CONFIG_FILE}")
            
        return jsonify({"status": "success", "message": "Configuration saved successfully"})
    except Exception as e:
        print(f"ERROR saving PMS config: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@settings_bp.route('/api/pms/config/load', methods=['GET'])
def load_pms_config():
    try:
        print("FETCHING THE DATA PMS")
        if os.path.exists(PMS_CONFIG_FILE):
            with open(PMS_CONFIG_FILE, 'r') as f:
                config_data = json.load(f)
            return jsonify(config_data)
        else:
            # Return empty config if file doesn't exist yet
            return jsonify({
                "pmsProvider": "",
                "apiKey": "",
                "apiEndpoint": "",
                "permissionLevel": "read",
                "lastUpdated": ""
            })
    except Exception as e:
        print(f"ERROR loading PMS config: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

# ==================== Language Settings Routes ====================

@settings_bp.route('/api/settings/language', methods=['POST'])
def save_language_settings():
    try:
        print("Saving language settings data")
        settings_data = request.get_json()
        print("Received language settings:", settings_data)
        
        # Ensure guestLanguages exists and is a list
        if 'guestLanguages' not in settings_data or not isinstance(settings_data['guestLanguages'], list):
            settings_data['guestLanguages'] = []
          
        # Add server-side timestamp
        settings_data['serverTimestamp'] = datetime.now().isoformat()
        
        # Ensure settings folder exists
        os.makedirs(SETTINGS_FOLDER, exist_ok=True)
        
        # Save to JSON file
        with open(LANGUAGE_CONFIG_FILE, 'w') as f:
            json.dump(settings_data, f, indent=2)
            print(f"Successfully wrote language settings to {LANGUAGE_CONFIG_FILE}")
            
        return jsonify({"status": "success", "message": "Language settings saved successfully"})
    except Exception as e:
        print(f"ERROR saving language settings: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500
    
@settings_bp.route('/api/settings/language', methods=['GET'])
def load_language_settings():
    try:
        print("Loading language settings data")
        if os.path.exists(LANGUAGE_CONFIG_FILE):
            with open(LANGUAGE_CONFIG_FILE, 'r') as f:
                settings_data = json.load(f)
            return jsonify(settings_data)
        else:
            # Return default settings if file doesn't exist yet
            return jsonify({
                "primaryLanguage": "English",
                "guestLanguages": ["en"],
                "timezone": "UTC+05:30",
                "currency": "INR",
                "dateFormat": "DD/MM/YYYY",
                "lastUpdated": ""
            })
    except Exception as e:
        print(f"ERROR loading language settings: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

# ==================== Staff Settings Routes ====================

@settings_bp.route('/api/settings/staff/save', methods=['POST'])
def save_staff_settings():
    try:
        print("Saving staff settings data")
        settings_data = request.json
        
        # Add server-side timestamp
        settings_data['serverTimestamp'] = datetime.now().isoformat()
        
        # Ensure settings folder exists (for redundancy)
        os.makedirs(SETTINGS_FOLDER, exist_ok=True)
            
        # Save to JSON file
        with open(STAFF_CONFIG_FILE, 'w') as f:
            json.dump(settings_data, f, indent=2)
            print(f"Successfully wrote staff settings to {STAFF_CONFIG_FILE}")
            
        return jsonify({"status": "success", "message": "Staff settings saved successfully"})
    except Exception as e:
        print(f"ERROR saving staff settings: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@settings_bp.route('/api/settings/staff/load', methods=['GET'])
def load_staff_settings():
    try:
        print("Loading staff settings data")
        if os.path.exists(STAFF_CONFIG_FILE):
            with open(STAFF_CONFIG_FILE, 'r') as f:
                settings_data = json.load(f)
            return jsonify(settings_data)
        else:
            # Return default settings if file doesn't exist yet
            return jsonify({
                "shareLink": "http://guestgenius.es/hotelname/#/04",
                "staffMembers": [
                    {
                        "name": "John Doe",
                        "email": "<EMAIL>",
                        "permission": "Admin",
                        "isActive": True
                    },
                    {
                        "name": "Jane Smith",
                        "email": "<EMAIL>",
                        "permission": "Editor", 
                        "isActive": True
                    }
                ],
                "lastUpdated": ""
            })
    except Exception as e:
        print(f"ERROR loading staff settings: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

# ==================== Notifications Settings Routes ====================

@settings_bp.route('/api/settings/notifications/save', methods=['POST'])
def save_notifications_settings():
    try:
        print("Saving notification settings data")
        settings_data = request.json
        
        # Add server-side timestamp
        settings_data['serverTimestamp'] = datetime.now().isoformat()
        
        # Ensure settings folder exists (for redundancy)
        os.makedirs(SETTINGS_FOLDER, exist_ok=True)
            
        # Save to JSON file
        with open(NOTIFICATIONS_CONFIG_FILE, 'w') as f:
            json.dump(settings_data, f, indent=2)
            print(f"Successfully wrote notification settings to {NOTIFICATIONS_CONFIG_FILE}")
            
        return jsonify({"status": "success", "message": "Notification settings saved successfully"})
    except Exception as e:
        print(f"ERROR saving notification settings: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

# ==================== Knowledge Base Routes ====================

@settings_bp.route('/api/settings/knowledge_base/upload', methods=['POST'])
def upload_knowledge_base():
    try:
        print("Uploading knowledge base file")
        if 'knowledge_base_file' not in request.files:
            return jsonify({"status": "error", "message": "No file part in the request"}), 400

        file = request.files['knowledge_base_file']

        if file.filename == '':
            return jsonify({"status": "error", "message": "No selected file"}), 400

        if file and file.filename.endswith('.txt'):
            # Ensure settings folder exists
            os.makedirs(SETTINGS_FOLDER, exist_ok=True)

            file_content = file.read().decode('utf-8')
            with open(KNOWLEDGE_BASE_FILE, 'w', encoding='utf-8') as f:
                f.write(file_content)
            print(f"Successfully wrote knowledge base content to {KNOWLEDGE_BASE_FILE}")
            return jsonify({"status": "success", "message": "Knowledge base uploaded successfully"})
        else:
            return jsonify({"status": "error", "message": "Invalid file type. Only .txt files are allowed."}), 400

    except Exception as e:
        print(f"ERROR uploading knowledge base: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@settings_bp.route('/api/settings/knowledge_base/load_text', methods=['GET'])
def load_knowledge_base_text():
    try:
        print("Loading knowledge base text")
        if os.path.exists(KNOWLEDGE_BASE_FILE):
            with open(KNOWLEDGE_BASE_FILE, 'r', encoding='utf-8') as f:
                text_content = f.read()
            return jsonify({"text": text_content})
        else:
            # Return empty text if file doesn't exist yet
            return jsonify({"text": ""})
    except Exception as e:
        print(f"ERROR loading knowledge base text: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@settings_bp.route('/api/settings/knowledge_base/save_text', methods=['POST'])
def save_knowledge_base_text():
    try:
        print("Saving knowledge base text")
        settings_data = request.get_json()
        text_content = settings_data.get('text', '')

        # Ensure settings folder exists
        os.makedirs(SETTINGS_FOLDER, exist_ok=True)

        with open(KNOWLEDGE_BASE_FILE, 'w', encoding='utf-8') as f:
            f.write(text_content)
        print(f"Successfully wrote knowledge base text to {KNOWLEDGE_BASE_FILE}")

        return jsonify({"status": "success", "message": "Knowledge base saved successfully"})
    except Exception as e:
        print(f"ERROR saving knowledge base text: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@settings_bp.route('/api/settings/notifications/load', methods=['GET'])
def load_notifications_settings():
    try:
        print("Loading notification settings data")
        if os.path.exists(NOTIFICATIONS_CONFIG_FILE):
            with open(NOTIFICATIONS_CONFIG_FILE, 'r') as f:
                settings_data = json.load(f)
            return jsonify(settings_data)
        else:
            # Return default settings if file doesn't exist yet
            return jsonify({
                "notificationType": "all_messages",  # Default to "All new messages"
                "emailNotifications": {
                    "communication": True,
                    "marketing": False,
                    "social": True
                },
                "lastUpdated": ""
            })
    except Exception as e:
        print(f"ERROR loading notification settings: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500