<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hotel Booking Management</title>
  <style>
    :root {
      /* Refined color palette */
      --background: 0 0% 100%;
      --foreground: 224 71% 4%;
      --card: 0 0% 100%;
      --card-foreground: 224 71% 4%;
      --popover: 0 0% 100%;
      --popover-foreground: 224 71% 4%;
      --primary: 220 91% 54%;
      --primary-foreground: 0 0% 100%;
      --secondary: 220 14% 96%;
      --secondary-foreground: 220 80% 30%;
      --muted: 220 14% 96%;
      --muted-foreground: 220 8% 46%;
      --accent: 220 14% 96%;
      --accent-foreground: 220 91% 54%;
      --destructive: 0 84% 60%;
      --destructive-foreground: 0 0% 100%;
      --border: 220 13% 91%;
      --input: 220 13% 91%;
      --ring: 220 91% 54%;
      --radius: 0.5rem;
      
      /* Booking type colors - more elegant and vibrant */
      --room-color: 220 91% 54%;
      --spa-color: 142 72% 45%;
      --massage-color: 330 82% 60%;
      
      /* Shadow values */
      --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.04);
      --shadow-md: 0 3px 12px rgba(0, 0, 0, 0.07);
      --shadow-lg: 0 8px 30px rgba(0, 0, 0, 0.12);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    body {
      background-color: hsl(var(--background));
      color: hsl(var(--foreground));
      font-feature-settings: "rlig" 1, "calt" 1;
      line-height: 1.5;
      min-height: 100vh;
    }

    .container {
      max-width: 1280px;
      margin: 0 auto;
      padding: 2rem;
    }

    header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1.75rem;
    }

    h1 {
      font-size: 1.75rem;
      font-weight: 600;
      color: hsl(var(--foreground));
      letter-spacing: -0.02em;
    }

    h2 {
      font-size: 1.25rem;
      font-weight: 600;
      color: hsl(var(--foreground));
      letter-spacing: -0.02em;
      margin-bottom: 1.25rem;
    }

    .calendar-nav {
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    #current-month-display {
      font-weight: 500;
      font-size: 1rem;
      padding: 0 0.5rem;
      min-width: 140px;
      text-align: center;
    }

    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--radius);
      font-weight: 500;
      font-size: 0.875rem;
      height: 2.25rem;
      padding-left: 1rem;
      padding-right: 1rem;
      transition: all 0.15s ease;
      cursor: pointer;
      border: none;
      white-space: nowrap;
    }

    .btn-secondary {
      background-color: hsl(var(--secondary));
      color: hsl(var(--secondary-foreground));
    }

    .btn-secondary:hover {
      filter: brightness(0.95);
    }

    .btn-outline {
      border: 1px solid hsl(var(--border));
      background-color: transparent;
      color: hsl(var(--foreground));
    }

    .btn-outline:hover {
      background-color: hsl(var(--secondary));
    }

    .btn-primary {
      background-color: hsl(var(--primary));
      color: hsl(var(--primary-foreground));
    }

    .btn-primary:hover {
      filter: brightness(0.9);
    }

    .btn-icon {
      height: 2rem;
      width: 2rem;
      padding: 0;
      border-radius: 0.375rem;
    }

    .calendar-wrapper {
      background-color: hsl(var(--card));
      border-radius: var(--radius);
      overflow: hidden;
      box-shadow: var(--shadow-md);
      border: 1px solid hsl(var(--border));
    }

    .calendar {
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed;
    }

    .calendar th {
      text-align: center;
      padding: 0.75rem 0.5rem;
      font-weight: 500;
      font-size: 0.813rem;
      color: hsl(var(--muted-foreground));
      background-color: hsl(var(--card));
      border-bottom: 1px solid hsl(var(--border));
      text-transform: uppercase;
      letter-spacing: 0.03em;
    }

    .calendar td {
      border-right: 1px solid hsl(var(--border));
      border-bottom: 1px solid hsl(var(--border));
      height: 110px;
      vertical-align: top;
      width: calc(100% / 7);
    }

    .calendar td:last-child {
      border-right: none;
    }

    .calendar tr:last-child td {
      border-bottom: none;
    }

    .day {
      height: 100%;
      padding: 0.5rem;
      cursor: pointer;
      transition: background-color 0.15s ease;
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .day:hover {
      background-color: hsl(var(--secondary));
    }

    .day.selected {
      background-color: hsl(var(--primary) / 0.08);
    }

    .day-number {
      font-size: 0.875rem;
      font-weight: 500;
      height: 1.5rem;
      width: 1.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 0.375rem;
    }

    .booking-indicator {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
      font-size: 0.688rem;
      margin-top: auto;
    }

    .booking-type {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.2rem 0.375rem;
      border-radius: 0.25rem;
      color: white;
      font-weight: 500;
    }

    .booking-type-room {
      background-color: hsl(var(--room-color));
    }

    .booking-type-spa {
      background-color: hsl(var(--spa-color));
    }

    .booking-type-massage {
      background-color: hsl(var(--massage-color));
    }

    .booking-count {
      font-weight: 600;
    }

    .day.other-month {
      color: hsl(var(--muted-foreground));
      background-color: hsl(var(--secondary) / 0.3);
    }

    .day.today .day-number {
      background-color: hsl(var(--primary));
      color: hsl(var(--primary-foreground));
      border-radius: 50%;
    }

    .booking-details {
      position: fixed;
      top: 0;
      right: 0;
      width: 100%;
      max-width: 30rem;
      height: 100vh;
      background-color: hsl(var(--card));
      border-left: 1px solid hsl(var(--border));
      padding: 1.75rem;
      overflow-y: auto;
      transform: translateX(100%);
      transition: transform 0.3s ease-in-out;
      z-index: 50;
      box-shadow: var(--shadow-lg);
    }

    .booking-details.open {
      transform: translateX(0);
    }

    .booking-details-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1.75rem;
      padding-bottom: 0.75rem;
      border-bottom: 1px solid hsl(var(--border));
    }

    .booking-details-date {
      font-size: 1.375rem;
      font-weight: 600;
      color: hsl(var(--foreground));
    }

    .close-btn {
      background: none;
      border: none;
      cursor: pointer;
      width: 2rem;
      height: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: hsl(var(--muted-foreground));
      transition: all 0.15s ease;
    }

    .close-btn:hover {
      background-color: hsl(var(--secondary));
      color: hsl(var(--foreground));
    }

    .booking-summary {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 0.75rem;
      margin-bottom: 1.75rem;
    }

    .booking-summary-item {
      padding: 1rem 0.75rem;
      border-radius: var(--radius);
      background-color: hsl(var(--card));
      box-shadow: var(--shadow-sm);
      border: 1px solid hsl(var(--border));
      text-align: center;
    }

    .booking-summary-item.room {
      border-top: 3px solid hsl(var(--room-color));
    }

    .booking-summary-item.spa {
      border-top: 3px solid hsl(var(--spa-color));
    }

    .booking-summary-item.massage {
      border-top: 3px solid hsl(var(--massage-color));
    }

    .booking-summary-label {
      font-size: 0.688rem;
      color: hsl(var(--muted-foreground));
      margin-bottom: 0.375rem;
      text-transform: uppercase;
      letter-spacing: 0.04em;
      font-weight: 500;
    }

    .booking-summary-value {
      font-size: 1.75rem;
      font-weight: 600;
      line-height: 1;
      color: hsl(var(--foreground));
    }

    .booking-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }

    .booking-item {
      padding: 1rem;
      border-radius: var(--radius);
      background-color: hsl(var(--card));
      box-shadow: var(--shadow-sm);
      border: 1px solid hsl(var(--border));
      transition: all 0.15s ease;
    }

    .booking-item:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }

    .booking-item.room {
      border-left: 3px solid hsl(var(--room-color));
    }

    .booking-item.spa {
      border-left: 3px solid hsl(var (--spa-color));
    }

    .booking-item.massage {
      border-left: 3px solid hsl(var(--massage-color));
    }

    .booking-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.75rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid hsl(var(--border));
    }

    .booking-item-type {
      font-weight: 500;
      font-size: 0.938rem;
      color: hsl(var(--foreground));
      display: flex;
      align-items: center;
      gap: 0.375rem;
    }

    .booking-item-type-room {
      color: hsl(var(--room-color));
    }

    .booking-item-type-spa {
      color: hsl(var(--spa-color));
    }

    .booking-item-type-massage {
      color: hsl(var(--massage-color));
    }

    .booking-item-type-icon {
      width: 1.125rem;
      height: 1.125rem;
    }

    .booking-item-time {
      font-size: 0.75rem;
      color: hsl(var(--foreground));
      font-weight: 500;
      background-color: hsl(var(--secondary));
      padding: 0.188rem 0.5rem;
      border-radius: 1rem;
    }

    .booking-item-details {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0.625rem 1rem;
      font-size: 0.813rem;
    }

    .booking-item-label {
      color: hsl(var(--muted-foreground));
      font-weight: 500;
    }
    
    .booking-item-value {
      text-align: right;
      font-weight: 500;
      color: hsl(var(--foreground));
    }

    .overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.4);
      z-index: 40;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.3s ease-in-out;
      backdrop-filter: blur(2px);
    }

    .overlay.open {
      opacity: 1;
      pointer-events: auto;
    }

    @media (max-width: 768px) {
      .booking-details {
        max-width: 100%;
      }
      
      .container {
        padding: 1rem;
      }
      
      header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
      }
      
      .calendar-nav {
        width: 100%;
        justify-content: space-between;
      }
      
      .booking-summary {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>Booking Management</h1>
      <div class="calendar-nav">
        <button class="btn btn-outline btn-icon" id="prev-month">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="15 18 9 12 15 6"></polyline></svg>
        </button>
        <span id="current-month-display">March 2025</span>
        <button class="btn btn-outline btn-icon" id="next-month">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
        </button>
        <button class="btn btn-primary" id="today-btn">Today</button>
      </div>
    </header>

    <div class="calendar-wrapper">
      <table class="calendar">
        <thead>
          <tr>
            <th>Sun</th>
            <th>Mon</th>
            <th>Tue</th>
            <th>Wed</th>
            <th>Thu</th>
            <th>Fri</th>
            <th>Sat</th>
          </tr>
        </thead>
        <tbody id="calendar-body">
          <!-- Calendar days will be generated by JavaScript -->
        </tbody>
      </table>
    </div>
  </div>

  <div class="booking-details" id="booking-details">
    <div class="booking-details-header">
      <div class="booking-details-date" id="selected-date">March 23, 2025</div>
      <button class="close-btn" id="close-details">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
      </button>
    </div>

    <div class="booking-summary">
      <div class="booking-summary-item room">
        <div class="booking-summary-label">Rooms</div>
        <div class="booking-summary-value" id="room-count">4</div>
      </div>
      <div class="booking-summary-item spa">
        <div class="booking-summary-label">Spa</div>
        <div class="booking-summary-value" id="spa-count">2</div>
      </div>
      <div class="booking-summary-item massage">
        <div class="booking-summary-label">Massage</div>
        <div class="booking-summary-value" id="massage-count">3</div>
      </div>
    </div>

    <h2>All Bookings</h2>
    <div class="booking-list" id="booking-list">
      <!-- Booking items will be generated by JavaScript -->
    </div>
  </div>

  <div class="overlay" id="overlay"></div>

  <script>
    // Sample booking data
    const bookingData = {
      "2025-03-20": {
        rooms: [
          { guestName: "James Smith", platform: "Direct", phone: "+****************", time: "3:00 PM" },
          { guestName: "Sarah Johnson", platform: "Booking.com", phone: "+****************", time: "2:30 PM" },
          { guestName: "Robert Brown", platform: "Expedia", phone: "+****************", time: "1:00 PM" }
        ],
        spa: [
          { guestName: "Emma Wilson", platform: "Direct", phone: "+****************", time: "10:00 AM" }
        ],
        massage: [
          { guestName: "Michael Davis", platform: "Hotels.com", phone: "+****************", time: "11:30 AM" },
          { guestName: "Jennifer Miller", platform: "Direct", phone: "+****************", time: "4:00 PM" }
        ]
      },
      "2025-03-21": {
        rooms: [
          { guestName: "David Wilson", platform: "Expedia", phone: "+****************", time: "2:00 PM" },
          { guestName: "Lisa Taylor", platform: "Direct", phone: "+****************", time: "3:30 PM" }
        ],
        spa: [
          { guestName: "Richard Moore", platform: "Booking.com", phone: "+****************", time: "9:00 AM" },
          { guestName: "Patricia White", platform: "Direct", phone: "+****************", time: "1:30 PM" }
        ],
        massage: [
          { guestName: "Thomas Anderson", platform: "Hotels.com", phone: "+****************", time: "10:30 AM" }
        ]
      },
      "2025-03-22": {
        rooms: [
          { guestName: "Charles Harris", platform: "Direct", phone: "+****************", time: "1:00 PM" },
          { guestName: "Mary Robinson", platform: "Booking.com", phone: "+****************", time: "4:00 PM" },
          { guestName: "Daniel Lewis", platform: "Expedia", phone: "+****************", time: "3:00 PM" }
        ],
        spa: [
          { guestName: "Elizabeth Walker", platform: "Direct", phone: "+****************", time: "11:00 AM" },
          { guestName: "Joseph Young", platform: "Hotels.com", phone: "+****************", time: "2:30 PM" },
          { guestName: "Susan Allen", platform: "Direct", phone: "+****************", time: "9:30 AM" }
        ],
        massage: [
          { guestName: "Paul Scott", platform: "Booking.com", phone: "+****************", time: "10:00 AM" },
          { guestName: "Nancy Green", platform: "Direct", phone: "+****************", time: "4:30 PM" }
        ]
      },
      "2025-03-23": {
        rooms: [
          { guestName: "Kenneth Hall", platform: "Expedia", phone: "+****************", time: "2:00 PM" },
          { guestName: "Margaret Baker", platform: "Direct", phone: "+****************", time: "3:30 PM" },
          { guestName: "Steven Nelson", platform: "Booking.com", phone: "+****************", time: "1:30 PM" },
          { guestName: "Carol Carter", platform: "Hotels.com", phone: "+****************", time: "4:00 PM" }
        ],
        spa: [
          { guestName: "Edward Mitchell", platform: "Direct", phone: "+****************", time: "9:00 AM" },
          { guestName: "Helen Perez", platform: "Booking.com", phone: "+****************", time: "11:30 AM" }
        ],
        massage: [
          { guestName: "Ronald Roberts", platform: "Direct", phone: "+****************", time: "10:30 AM" },
          { guestName: "Donna Cook", platform: "Expedia", phone: "+****************", time: "1:00 PM" },
          { guestName: "Kevin Morgan", platform: "Direct", phone: "+****************", time: "3:00 PM" }
        ]
      }
    };

    // Calendar functionality
    document.addEventListener('DOMContentLoaded', function() {
      const calendarBody = document.getElementById('calendar-body');
      const currentMonthDisplay = document.getElementById('current-month-display');
      const prevMonthBtn = document.getElementById('prev-month');
      const nextMonthBtn = document.getElementById('next-month');
      const todayBtn = document.getElementById('today-btn');
      const bookingDetails = document.getElementById('booking-details');
      const selectedDateElement = document.getElementById('selected-date');
      const closeDetailsBtn = document.getElementById('close-details');
      const overlay = document.getElementById('overlay');
      const roomCount = document.getElementById('room-count');
      const spaCount = document.getElementById('spa-count');
      const massageCount = document.getElementById('massage-count');
      const bookingList = document.getElementById('booking-list');

      // Set current date
      let currentDate = new Date();
      let selectedDate = currentDate;
      
      // Generate calendar for current month
      generateCalendar(currentDate);

      // Automatically show today's bookings when page loads
      const todayStr = formatDateToString(new Date());
      setTimeout(() => {
        showBookingDetails(todayStr);
      }, 300);

      // Event listeners
      prevMonthBtn.addEventListener('click', function() {
        currentDate.setMonth(currentDate.getMonth() - 1);
        generateCalendar(currentDate);
      });

      nextMonthBtn.addEventListener('click', function() {
        currentDate.setMonth(currentDate.getMonth() + 1);
        generateCalendar(currentDate);
      });

      todayBtn.addEventListener('click', function() {
        currentDate = new Date();
        generateCalendar(currentDate);
      });

      closeDetailsBtn.addEventListener('click', function() {
        bookingDetails.classList.remove('open');
        overlay.classList.remove('open');
      });

      overlay.addEventListener('click', function() {
        bookingDetails.classList.remove('open');
        overlay.classList.remove('open');
      });
      
      // Helper function to format date to YYYY-MM-DD string
      function formatDateToString(date) {
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
      }

      // Generate calendar
      function generateCalendar(date) {
        // Clear previous calendar
        calendarBody.innerHTML = '';
        
        // Display current month and year
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        currentMonthDisplay.textContent = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
        
        // Get first day of the month
        const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
        const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
        
        // Get day of the week for first day (0 = Sunday, 6 = Saturday)
        const firstDayOfWeek = firstDay.getDay();
        
        // Calculate days from previous month to display
        const prevMonthLastDay = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
        
        // Create calendar grid
        let dayCount = 1;
        let nextMonthDayCount = 1;
        
        for (let i = 0; i < 6; i++) { // 6 rows max
          // Create row
          const row = document.createElement('tr');
          
          for (let j = 0; j < 7; j++) { // 7 days per week
            // Create cell
            const cell = document.createElement('td');
            const dayDiv = document.createElement('div');
            dayDiv.className = 'day';
            
            // Calculate date to display
            if (i === 0 && j < firstDayOfWeek) {
              // Previous month
              const prevMonthDay = prevMonthLastDay - (firstDayOfWeek - j - 1);
              const dayNumber = document.createElement('span');
              dayNumber.className = 'day-number';
              dayNumber.textContent = prevMonthDay;
              dayDiv.appendChild(dayNumber);
              dayDiv.classList.add('other-month');
              
              // Add date data attribute
              const prevMonth = date.getMonth() === 0 ? 11 : date.getMonth() - 1;
              const prevYear = date.getMonth() === 0 ? date.getFullYear() - 1 : date.getFullYear();
              const dateStr = `${prevYear}-${(prevMonth + 1).toString().padStart(2, '0')}-${prevMonthDay.toString().padStart(2, '0')}`;
              dayDiv.setAttribute('data-date', dateStr);
            } else if (dayCount > lastDay.getDate()) {
              // Next month
              const dayNumber = document.createElement('span');
              dayNumber.className = 'day-number';
              dayNumber.textContent = nextMonthDayCount;
              dayDiv.appendChild(dayNumber);
              dayDiv.classList.add('other-month');
              
              // Add date data attribute
              const nextMonth = date.getMonth() === 11 ? 0 : date.getMonth() + 1;
              const nextYear = date.getMonth() === 11 ? date.getFullYear() + 1 : date.getFullYear();
              const dateStr = `${nextYear}-${(nextMonth + 1).toString().padStart(2, '0')}-${nextMonthDayCount.toString().padStart(2, '0')}`;
              dayDiv.setAttribute('data-date', dateStr);
              
              nextMonthDayCount++;
            } else {
              // Current month
              const dayNumber = document.createElement('span');
              dayNumber.className = 'day-number';
              dayNumber.textContent = dayCount;
              dayDiv.appendChild(dayNumber);
              
              // Add date data attribute
              const dateStr = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${dayCount.toString().padStart(2, '0')}`;
              dayDiv.setAttribute('data-date', dateStr);
              
              // Check if it's today
              const today = new Date();
              if (date.getFullYear() === today.getFullYear() && 
                  date.getMonth() === today.getMonth() && 
                  dayCount === today.getDate()) {
                dayDiv.classList.add('today');
                
                // Store reference to today's element for later use
                window.todayElement = dayDiv;
              }
              
              // Add booking indicators if data exists
              if (bookingData[dateStr]) {
                const bookingIndicator = document.createElement('div');
                bookingIndicator.className = 'booking-indicator';
                
                // Room bookings
                if (bookingData[dateStr].rooms && bookingData[dateStr].rooms.length > 0) {
                  const roomType = document.createElement('div');
                  roomType.className = 'booking-type booking-type-room';
                  roomType.innerHTML = `<span>Rooms</span><span class="booking-count">${bookingData[dateStr].rooms.length}</span>`;
                  bookingIndicator.appendChild(roomType);
                }
                
                // Spa bookings
                if (bookingData[dateStr].spa && bookingData[dateStr].spa.length > 0) {
                  const spaType = document.createElement('div');
                  spaType.className = 'booking-type booking-type-spa';
                  spaType.innerHTML = `<span>Spa</span><span class="booking-count">${bookingData[dateStr].spa.length}</span>`;
                  bookingIndicator.appendChild(spaType);
                }
                
                // Massage bookings
                if (bookingData[dateStr].massage && bookingData[dateStr].massage.length > 0) {
                  const massageType = document.createElement('div');
                  massageType.className = 'booking-type booking-type-massage';
                  massageType.innerHTML = `<span>Massage</span><span class="booking-count">${bookingData[dateStr].massage.length}</span>`;
                  bookingIndicator.appendChild(massageType);
                }
                
                dayDiv.appendChild(bookingIndicator);
              }
              
              dayCount++;
            }
            
            // Add click event to show booking details
            dayDiv.addEventListener('click', function() {
              const dateStr = this.getAttribute('data-date');
              showBookingDetails(dateStr);
              
              // Remove selected class from all days
              document.querySelectorAll('.day').forEach(day => {
                day.classList.remove('selected');
              });
              
              // Add selected class to clicked day
              this.classList.add('selected');
            });
            
            cell.appendChild(dayDiv);
            row.appendChild(cell);
          }
          
          calendarBody.appendChild(row);
          
          // Stop if we've reached the end of the next month
          if (dayCount > lastDay.getDate() && nextMonthDayCount > 7) {
            break;
          }
        }
      }

      // Show booking details
      function showBookingDetails(dateStr) {
        const date = new Date(dateStr);
        const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        selectedDateElement.textContent = date.toLocaleDateString('en-US', options);
        
        const data = bookingData[dateStr] || { rooms: [], spa: [], massage: [] };
        
        // Update booking counts
        roomCount.textContent = data.rooms ? data.rooms.length : 0;
        spaCount.textContent = data.spa ? data.spa.length : 0;
        massageCount.textContent = data.massage ? data.massage.length : 0;
        
        // Generate booking list
        bookingList.innerHTML = '';
        
        // Add rooms
        if (data.rooms && data.rooms.length > 0) {
          data.rooms.forEach(booking => {
            addBookingItem('Room', booking, 'room');
          });
        }
        
        // Add spa
        if (data.spa && data.spa.length > 0) {
          data.spa.forEach(booking => {
            addBookingItem('Spa', booking, 'spa');
          });
        }
        
        // Add massage
        if (data.massage && data.massage.length > 0) {
          data.massage.forEach(booking => {
            addBookingItem('Massage', booking, 'massage');
          });
        }
        
        // Show details panel
        bookingDetails.classList.add('open');
        overlay.classList.add('open');
      }
      
      // Add booking item to list
      function addBookingItem(type, booking, cssClass) {
        const item = document.createElement('div');
        item.className = `booking-item ${cssClass}`;
        
        const header = document.createElement('div');
        header.className = 'booking-item-header';
        
        const typeElement = document.createElement('div');
        typeElement.className = `booking-item-type booking-item-type-${cssClass.toLowerCase()}`;
        
        // Add appropriate icon based on booking type
        let typeIcon = '';
        if (type === 'Room') {
          typeIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="booking-item-type-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 7v11m0-7h18m0-7v14m-9-7v7m4.5-10h-9a2 2 0 0 0-2 2v.5H17"></path></svg>';
        } else if (type === 'Spa') {
          typeIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="booking-item-type-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="M8 14s1.5 2 4 2 4-2 4-2"></path><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line></svg>';
        } else if (type === 'Massage') {
          typeIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="booking-item-type-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z"></path></svg>';
        }
        
        typeElement.innerHTML = typeIcon + type;
        
        const timeElement = document.createElement('div');
        timeElement.className = 'booking-item-time';
        timeElement.textContent = booking.time;
        
        header.appendChild(typeElement);
        header.appendChild(timeElement);
        
        const details = document.createElement('div');
        details.className = 'booking-item-details';
        
        // Guest name
        const nameLabel = document.createElement('div');
        nameLabel.className = 'booking-item-label';
        nameLabel.textContent = 'Guest';
        
        const nameValue = document.createElement('div');
        nameValue.className = 'booking-item-value';
        nameValue.textContent = booking.guestName;
        
        // Platform
        const platformLabel = document.createElement('div');
        platformLabel.className = 'booking-item-label';
        platformLabel.textContent = 'Platform';
        
        const platformValue = document.createElement('div');
        platformValue.className = 'booking-item-value';
        platformValue.textContent = booking.platform;
        
        // Phone
        const phoneLabel = document.createElement('div');
        phoneLabel.className = 'booking-item-label';
        phoneLabel.textContent = 'Phone';
        
        const phoneValue = document.createElement('div');
        phoneValue.className = 'booking-item-value';
        phoneValue.textContent = booking.phone;
        
        details.appendChild(nameLabel);
        details.appendChild(nameValue);
        details.appendChild(platformLabel);
        details.appendChild(platformValue);
        details.appendChild(phoneLabel);
        details.appendChild(phoneValue);
        
        item.appendChild(header);
        item.appendChild(details);
        
        bookingList.appendChild(item);
      }
    });
  </script>
</body>
</html>