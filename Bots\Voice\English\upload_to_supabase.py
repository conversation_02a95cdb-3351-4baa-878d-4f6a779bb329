import os
import json
import requests
import glob
import re
import time
from datetime import datetime
from dotenv import load_dotenv
import uuid

# Load environment variables from .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), ".env"))

# Supabase API credentials
SUPABASE_URL = os.environ.get("SUPABASE_URL")
SUPABASE_ANON_KEY = os.environ.get("SUPABASE_ANON_KEY")

# Supabase API headers
headers = {
    "apikey": SUPABASE_ANON_KEY,
    "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=minimal"
}

def format_time(timestamp_str):
    """Convert ISO timestamp to HH:MM AM/PM format"""
    if not timestamp_str:
        return None
    
    try:
        # Parse the ISO timestamp
        dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        # Format to HH:MM AM/PM
        return dt.strftime('%I:%M %p')
    except Exception as e:
        print(f"Error formatting time: {e}")
        return None

def calculate_call_ratio(call_data):
    """Calculate the ratio of AI to user talk time"""
    if not call_data or 'messages' not in call_data:
        return "0:0"
    
    ai_time = 0
    user_time = 0
    
    for msg in call_data['messages']:
        if msg.get('role') == 'bot' and msg.get('duration'):
            ai_time += msg.get('duration', 0)
        elif msg.get('role') == 'user' and msg.get('duration'):
            user_time += msg.get('duration', 0)
    
    # Convert to seconds and format as ratio
    ai_seconds = int(ai_time / 1000) if ai_time else 0
    user_seconds = int(user_time / 1000) if user_time else 0
    
    return f"{ai_seconds}:{user_seconds}"

def analyze_sentiment(transcript):
    """Simple sentiment analysis based on keywords"""
    if not transcript:
        return "5/10 (Neutral)"
    
    positive_words = ['thank', 'thanks', 'good', 'great', 'excellent', 'happy', 'appreciate', 'helpful', 'perfect', 'wonderful']
    negative_words = ['bad', 'poor', 'terrible', 'unhappy', 'disappointed', 'issue', 'problem', 'wrong', 'not working', 'complaint']
    
    # Count occurrences of positive and negative words
    positive_count = sum(1 for word in positive_words if word.lower() in transcript.lower())
    negative_count = sum(1 for word in negative_words if word.lower() in transcript.lower())
    
    # Calculate sentiment score (1-10 scale)
    base_score = 5  # Neutral starting point
    sentiment_score = base_score + positive_count - negative_count
    
    # Ensure score is within 1-10 range
    sentiment_score = max(1, min(10, sentiment_score))
    
    # Determine sentiment label
    if sentiment_score >= 8:
        sentiment_label = "Positive"
    elif sentiment_score <= 3:
        sentiment_label = "Negative"
    elif sentiment_score < 5:
        sentiment_label = "Slightly Negative"
    elif sentiment_score > 5:
        sentiment_label = "Slightly Positive"
    else:
        sentiment_label = "Neutral"
    
    return f"{sentiment_score}/10 ({sentiment_label})"

def calculate_call_time(call_data):
    """Calculate the total call time in minutes"""
    if not call_data:
        return "0 minutes"
    
    start_time = call_data.get('startedAt')
    end_time = call_data.get('endedAt')
    
    if not start_time or not end_time:
        return "Unknown"
    
    try:
        # Parse ISO timestamps
        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        
        # Calculate duration in minutes
        duration_seconds = (end_dt - start_dt).total_seconds()
        duration_minutes = int(duration_seconds / 60)
        
        if duration_minutes == 0:
            return f"{int(duration_seconds)} seconds"
        elif duration_minutes == 1:
            return "1 minute"
        else:
            return f"{duration_minutes} minutes"
    except Exception as e:
        print(f"Error calculating call time: {e}")
        return "Unknown"

def extract_phone_number(call_data):
    """Extract the phone number from call data"""
    if not call_data or 'customer' not in call_data:
        return None
    
    # Get the phone number from the customer field
    phone_number = call_data.get('customer', {}).get('number', '')
    
    # Remove the '+' prefix if present
    if phone_number.startswith('+'):
        phone_number = phone_number[1:]
    
    return phone_number

def upload_to_supabase(transcript_file):
    """Upload transcript data to Supabase voicebot_data table"""
    try:
        # Check if file exists
        if not os.path.exists(transcript_file):
            print(f"File not found: {transcript_file}")
            return False
        
        # Load JSON data
        with open(transcript_file, 'r', encoding='utf-8') as f:
            call_data = json.load(f)
        
        # Extract necessary data
        call_id = call_data.get('id')
        
        # Generate a numeric call_id for the database (using last 8 chars of UUID)
        numeric_call_id = int(call_id.replace('-', '')[-8:], 16) % 100000000
        
        # Extract phone number
        guest_phone = extract_phone_number(call_data)
        
        # Format timestamps
        call_start_time = format_time(call_data.get('startedAt'))
        call_end_time = format_time(call_data.get('endedAt'))
        
        # Get transcript
        call_transcript = call_data.get('transcript', '')
        
        # Calculate call ratio (AI:User talk time)
        call_ratio = calculate_call_ratio(call_data)
        
        # Analyze sentiment
        sentiment_analysis = analyze_sentiment(call_transcript)
        
        # Determine if call is outgoing or incoming
        call_type = call_data.get('type', '')
        out_or_in = 'outgoing' if call_type == 'outboundPhoneCall' else 'incoming'
        
        # Generate call summary
        call_summary = call_data.get('summary', 'No summary available')
        
        # Calculate total call time
        call_time = calculate_call_time(call_data)
        
        # Determine call status
        call_status = 'Completed' if call_data.get('status') == 'ended' else call_data.get('status', 'Unknown')
        
        # Prepare data for Supabase
        supabase_data = {
            "call_id": numeric_call_id,
            "guest_phone": guest_phone,
            "call_start_time": call_start_time,
            "call_end_time": call_end_time,
            "call_transcript": call_transcript,
            "call_ratio": call_ratio,
            "sentiment_analysis": sentiment_analysis,
            "out_or_in": out_or_in,
            "call_summary": call_summary,
            "call_time": call_time,
            "call_status": call_status,
            "room_no": None  # This could be updated later if room information is available
        }
        
        # Upload to Supabase
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/voicebot_data",
            headers=headers,
            json=supabase_data
        )
        
        if response.status_code == 201:
            print(f"Successfully uploaded data for call {call_id} to Supabase")
            return True
        else:
            print(f"Failed to upload data: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"Error uploading to Supabase: {e}")
        return False

def process_all_transcripts():
    """Process all transcript files in the transcripts directory"""
    # Get all JSON transcript files
    transcript_dir = os.path.join(os.path.dirname(__file__), 'transcripts')
    json_files = glob.glob(os.path.join(transcript_dir, '*.json'))
    
    if not json_files:
        print("No transcript files found")
        return
    
    print(f"Found {len(json_files)} transcript files")
    
    # Process each file
    for file_path in json_files:
        print(f"Processing {os.path.basename(file_path)}...")
        success = upload_to_supabase(file_path)
        if success:
            print(f"Successfully processed {os.path.basename(file_path)}")
        else:
            print(f"Failed to process {os.path.basename(file_path)}")

def process_single_transcript(file_path):
    """Process a single transcript file"""
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return False
    
    print(f"Processing {os.path.basename(file_path)}...")
    success = upload_to_supabase(file_path)
    if success:
        print(f"Successfully processed {os.path.basename(file_path)}")
        return True
    else:
        print(f"Failed to process {os.path.basename(file_path)}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Upload voice call transcripts to Supabase')
    parser.add_argument('--file', type=str, help='Path to a specific transcript file to process')
    parser.add_argument('--all', action='store_true', help='Process all transcript files in the transcripts directory')
    
    args = parser.parse_args()
    
    if args.file:
        process_single_transcript(args.file)
    elif args.all:
        process_all_transcripts()
    else:
        print("Please specify either --file or --all")
        print("Example: python upload_to_supabase.py --all")
        print("Example: python upload_to_supabase.py --file transcripts/transcript_123456.json")
