import requests
import json

# Your Twilio account details (replace with your actual Account SID and Auth Token)
ACCOUNT_SID = 'your_account_sid'
AUTH_TOKEN = 'your_auth_token'

# Twilio API endpoint for content creation
url = 'https://content.twilio.com/v1/Content'

# Updated payload with improved titles and corrected grammar
payload = {
    "friendly_name": "roomserviceoptions2",
    "language": "en",
    "types": {
        "twilio/carousel": {
            "body": "",
            "cards": [
                {
                    "title": "Room Service",
                    "body": "Discover our delicious menu and beverage options.",
                    "media": "https://jadhavharshh.github.io/food_menu/images/roomservice.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_room_service"
                        }
                    ]
                },
                {
                    "title": "Book a Spa Session",
                    "body": "Relax and rejuvenate at our spa.",
                    "media": "https://satxnishere.github.io/HOSTEDIMAGES/static/essentials.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Book a spa session",
                            "id": "spa_booking_option"
                        }
                    ]
                },
                {
                    "title": "Schedule a Massage",
                    "body": "Experience our therapeutic massage services.",
                    "media": "https://dixith-dev.github.io/food_menu/images/mas.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Book a Massage Session",
                            "id": "massage_service_option"
                        }
                    ]
                },
                {
                    "title": "Book a Room",
                    "body": "Stay in our luxurious accommodations.",
                    "media": "https://satxnishere.github.io/HOSTEDIMAGES/static/deluxe.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Reserve a Room",
                            "id": "room_booking_option"
                        }
                    ]
                },
                {
                    "title": "Speak with our receptionist",
                    "body": "Connect with our friendly staff.",
                    "media": "https://dixith-dev.github.io/food_menu/images/istockphoto-*********-612x612.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Chat with Staff",
                            "id": "chat_with_staff_option"
                        }
                    ]
                }
            ]
        }
    }
}

# Make the POST request to the Twilio API
response = requests.post(
    url,
    auth=(ACCOUNT_SID, AUTH_TOKEN),
    headers={'Content-Type': 'application/json'},
    data=json.dumps(payload)
)

# Check the response status
if response.status_code == 201:
    print("Carousel created successfully!")
    print(response.json())
else:
    print(f"Failed to create carousel: {response.status_code}")
    print(response.text)