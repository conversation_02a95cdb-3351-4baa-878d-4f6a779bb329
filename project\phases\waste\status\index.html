<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Status</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        }
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-white min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Header -->
        <div class="flex items-center justify-between mb-10">
            <div>
                <h1 class="text-3xl font-bold text-primary-800">System Status</h1>
                <p class="text-primary-500 mt-1">Last updated: <span id="last-updated" class="font-medium"></span></p>
            </div>
            <div class="bg-green-100 text-green-800 px-4 py-2 rounded-lg flex items-center">
                <div class="h-3 w-3 bg-green-500 rounded-xl mr-2 animate-pulse"></div>
                <span class="font-medium">All Systems Operational</span>
            </div>
        </div>

        <!-- Status Overview -->
        <div class="mb-12">
            <!-- Added more vertical spacing after the heading -->
            <h2 class="text-xl font-semibold text-primary-800 mb-8">Current Status</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                <!-- Dashboard Status - Updated with 4-day history and improved spacing -->
                <div class="bg-white rounded-xl border border-[#e5e7eb] overflow-hidden transition-all">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <div class="h-10 w-10 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <h3 class="font-medium text-primary-800">Dashboard</h3>
                            </div>
                            <div class="bg-green-100 text-green-800 px-3 py-1 rounded text-sm">Operational</div>
                        </div>
                        <!-- Added more bottom margin after this section -->
                        <div class="mt-2 mb-6">
                            <div class="bg-gray-100 rounded-full h-2 mb-2">
                                <div class="bg-green-500 h-2 rounded-full w-[99%]"></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500">
                                <span>99.9% uptime</span>
                                <span>24h monitoring</span>
                            </div>
                        </div>
                        
                        <!-- Added 4-day history with real-time dates -->
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Last 4 Days Status</h4>
                        <div class="space-y-3">
                            <!-- Dynamic dates will be filled by JavaScript -->
                            <div class="flex items-center justify-between" id="dashboard-day1">
                                <span class="text-xs text-gray-500 w-16">Today</span>
                                <div class="flex-1 mx-2">
                                    <div class="bg-gray-100 rounded-full h-1.5">
                                        <div class="bg-green-500 h-1.5 rounded-full w-[100%]"></div>
                                    </div>
                                </div>
                                <div class="h-2 w-2 bg-green-500 rounded-full"></div>
                            </div>
                            
                            <div class="flex items-center justify-between" id="dashboard-day2">
                                <span class="text-xs text-gray-500 w-16"></span>
                                <div class="flex-1 mx-2">
                                    <div class="bg-gray-100 rounded-full h-1.5">
                                        <div class="bg-green-500 h-1.5 rounded-full w-[98%]"></div>
                                    </div>
                                </div>
                                <div class="h-2 w-2 bg-green-500 rounded-full"></div>
                            </div>
                            
                            <div class="flex items-center justify-between" id="dashboard-day3">
                                <span class="text-xs text-gray-500 w-16"></span>
                                <div class="flex-1 mx-2">
                                    <div class="bg-gray-100 rounded-full h-1.5">
                                        <div class="bg-yellow-500 h-1.5 rounded-full w-[88%]"></div>
                                    </div>
                                </div>
                                <div class="h-2 w-2 bg-yellow-500 rounded-full"></div>
                            </div>
                            
                            <div class="flex items-center justify-between" id="dashboard-day4">
                                <span class="text-xs text-gray-500 w-16"></span>
                                <div class="flex-1 mx-2">
                                    <div class="bg-gray-100 rounded-full h-1.5">
                                        <div class="bg-red-500 h-1.5 rounded-full w-[62%]"></div>
                                    </div>
                                </div>
                                <div class="h-2 w-2 bg-red-500 rounded-full"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mobile App Status - Updated with 4-day history and improved spacing -->
                <div class="bg-white rounded-xl border border-[#e5e7eb] overflow-hidden transition-all">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <div class="h-10 w-10 bg-purple-100 text-purple-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <h3 class="font-medium text-primary-800">Mobile App</h3>
                            </div>
                            <div class="bg-green-100 text-green-800 px-3 py-1 rounded text-sm">Operational</div>
                        </div>
                        <div class="mt-2 mb-6">
                            <div class="bg-gray-100 rounded-full h-2 mb-2">
                                <div class="bg-green-500 h-2 rounded-full w-[97%]"></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500">
                                <span>97.8% uptime</span>
                                <span>24h monitoring</span>
                            </div>
                        </div>
                        
                        <!-- Added 4-day history with real-time dates -->
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Last 4 Days Status</h4>
                        <div class="space-y-3">
                            <!-- Dynamic dates will be filled by JavaScript -->
                            <div class="flex items-center justify-between" id="mobile-day1">
                                <span class="text-xs text-gray-500 w-16">Today</span>
                                <div class="flex-1 mx-2">
                                    <div class="bg-gray-100 rounded-full h-1.5">
                                        <div class="bg-green-500 h-1.5 rounded-full w-[97%]"></div>
                                    </div>
                                </div>
                                <div class="h-2 w-2 bg-green-500 rounded-full"></div>
                            </div>
                            
                            <div class="flex items-center justify-between" id="mobile-day2">
                                <span class="text-xs text-gray-500 w-16"></span>
                                <div class="flex-1 mx-2">
                                    <div class="bg-gray-100 rounded-full h-1.5">
                                        <div class="bg-green-500 h-1.5 rounded-full w-[99%]"></div>
                                    </div>
                                </div>
                                <div class="h-2 w-2 bg-green-500 rounded-full"></div>
                            </div>
                            
                            <div class="flex items-center justify-between" id="mobile-day3">
                                <span class="text-xs text-gray-500 w-16"></span>
                                <div class="flex-1 mx-2">
                                    <div class="bg-gray-100 rounded-full h-1.5">
                                        <div class="bg-yellow-500 h-1.5 rounded-full w-[85%]"></div>
                                    </div>
                                </div>
                                <div class="h-2 w-2 bg-yellow-500 rounded-full"></div>
                            </div>
                            
                            <div class="flex items-center justify-between" id="mobile-day4">
                                <span class="text-xs text-gray-500 w-16"></span>
                                <div class="flex-1 mx-2">
                                    <div class="bg-gray-100 rounded-full h-1.5">
                                        <div class="bg-blue-500 h-1.5 rounded-full w-[75%]"></div>
                                    </div>
                                </div>
                                <div class="h-2 w-2 bg-blue-500 rounded-full"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chatbot Parent Card - Improved spacing -->
                <div class="bg-white rounded-xl border border-[#e5e7eb] overflow-hidden transition-all">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <div class="h-10 w-10 bg-indigo-100 text-indigo-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <h3 class="font-medium text-primary-800">Chatbot Services</h3>
                            </div>
                            <div class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded text-sm">Minor Issues</div>
                        </div>

                        <!-- Changed mt-3 to mt-6 to add more space -->
                        <div class="space-y-4 mt-6">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">WhatsApp</span>
                                <div class="flex items-center">
                                    <div class="h-2 w-2 bg-green-500 rounded-full mr-1"></div>
                                    <span class="text-xs text-green-600">Operational</span>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Messenger</span>
                                <div class="flex items-center">
                                    <div class="h-2 w-2 bg-green-500 rounded-full mr-1"></div>
                                    <span class="text-xs text-green-600">Operational</span>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Instagram</span>
                                <div class="flex items-center">
                                    <div class="h-2 w-2 bg-yellow-500 rounded-full mr-1"></div>
                                    <span class="text-xs text-yellow-600">Degraded</span>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Voice</span>
                                <div class="flex items-center">
                                    <div class="h-2 w-2 bg-green-500 rounded-full mr-1"></div>
                                    <span class="text-xs text-green-600">Operational</span>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Web</span>
                                <div class="flex items-center">
                                    <div class="h-2 w-2 bg-blue-500 rounded-full mr-1"></div>
                                    <span class="text-xs text-blue-600">In Development</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Other</span>
                                <div class="flex items-center">
                                    <div class="h-2 w-2 bg-blue-500 rounded-full mr-1"></div>
                                    <span class="text-xs text-blue-600">In Development</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Updates -->
        <div>
            <h2 class="text-xl font-semibold text-primary-800 mb-8 flex items-center gap-2">
                <i class="fas fa-history text-primary-600"></i>
                Recent Updates
            </h2>
            
            <div class="space-y-6">
                <!-- Timeline Item 1 -->
                <div class="bg-white rounded-xl border border-[#e5e7eb] p-6 transition-all">
                    <div class="flex items-start gap-4">
                        <div>
                            <div class="h-10 w-10 bg-green-100 text-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center mb-1">
                                <h3 class="font-medium text-primary-800">Issue resolved: Instagram Chatbot</h3>
                                <span class="ml-3 text-xs text-gray-500">30 minutes ago</span>
                            </div>
                            <p class="text-sm text-gray-600">The connectivity issue with Instagram Chatbot service has been resolved. All services are now operating normally.</p>
                            <div class="flex items-center mt-2 text-sm text-gray-500">
                                <span class="flex items-center"><i class="fas fa-calendar mr-1"></i> <span id="update-date-1"></span></span>
                                <span class="flex items-center ml-4"><i class="fas fa-clock mr-1"></i> <span id="update-time-1"></span></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Timeline Item 2 -->
                <div class="bg-white rounded-xl border border-[#e5e7eb] p-6 transition-all">
                    <div class="flex items-start gap-4">
                        <div>
                            <div class="h-10 w-10 bg-yellow-100 text-yellow-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation"></i>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center mb-1">
                                <h3 class="font-medium text-primary-800">Investigating: Instagram Chatbot</h3>
                                <span class="ml-3 text-xs text-gray-500">2 hours ago</span>
                            </div>
                            <p class="text-sm text-gray-600">We're investigating reports of intermittent connectivity issues with the Instagram Chatbot service. Some users may experience delayed responses.</p>
                            <div class="flex items-center mt-2 text-sm text-gray-500">
                                <span class="flex items-center"><i class="fas fa-calendar mr-1"></i> <span id="update-date-2"></span></span>
                                <span class="flex items-center ml-4"><i class="fas fa-clock mr-1"></i> <span id="update-time-2"></span></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Timeline Item 3 -->
                <div class="bg-white rounded-xl border border-[#e5e7eb] p-6 transition-all">
                    <div class="flex items-start gap-4">
                        <div>
                            <div class="h-10 w-10 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-info"></i>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center mb-1">
                                <h3 class="font-medium text-primary-800">Maintenance completed: Dashboard</h3>
                                <span class="ml-3 text-xs text-gray-500">1 day ago</span>
                            </div>
                            <p class="text-sm text-gray-600">Scheduled maintenance on the Dashboard has been completed successfully. Performance improvements and security updates have been applied.</p>
                            <div class="flex items-center mt-2 text-sm text-gray-500">
                                <span class="flex items-center"><i class="fas fa-calendar mr-1"></i> <span id="update-date-3"></span></span>
                                <span class="flex items-center ml-4"><i class="fas fa-clock mr-1"></i> <span id="update-time-3"></span></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="mt-16 border-t border-gray-200 pt-8 text-center text-gray-500 text-sm">
            <p>&copy; 2023 Your Company. All rights reserved.</p>
            <div class="flex justify-center space-x-4 mt-2">
                <a href="#" class="text-gray-400 hover:text-gray-600">Terms</a>
                <a href="#" class="text-gray-400 hover:text-gray-600">Privacy</a>
                <a href="#" class="text-gray-400 hover:text-gray-600">Contact</a>
            </div>
        </footer>
    </div>

    <script>
        // Update the last updated time and all date-related elements
        const updateTime = () => {
            const now = new Date();
            const options = { 
                month: 'short', 
                day: 'numeric', 
                year: 'numeric', 
                hour: '2-digit', 
                minute: '2-digit',
                second: '2-digit'
            };
            document.getElementById('last-updated').textContent = now.toLocaleDateString('en-US', options);
        };
        
        // Generate real dates for the last 4 days
        const generateDates = () => {
            const today = new Date();
            const dateFormat = { month: 'short', day: 'numeric' };
            
            // Set dates for Dashboard history
            for (let i = 2; i <= 4; i++) {
                const pastDate = new Date();
                pastDate.setDate(today.getDate() - (i-1));
                const dateString = pastDate.toLocaleDateString('en-US', dateFormat);
                
                document.querySelector(`#dashboard-day${i} span`).textContent = dateString;
                document.querySelector(`#mobile-day${i} span`).textContent = dateString;
            }
            
            // Set dates for the updates
            const updateTimes = [
                { hours: 0, minutes: 30 }, // 30 minutes ago
                { hours: 2, minutes: 0 },  // 2 hours ago
                { hours: 24, minutes: 0 }  // 1 day ago
            ];
            
            updateTimes.forEach((timeDiff, index) => {
                const updateTime = new Date();
                updateTime.setHours(updateTime.getHours() - timeDiff.hours);
                updateTime.setMinutes(updateTime.getMinutes() - timeDiff.minutes);
                
                const dateString = updateTime.toLocaleDateString('en-US', dateFormat);
                const timeString = updateTime.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                }) + ' UTC';
                
                document.getElementById(`update-date-${index+1}`).textContent = dateString;
                document.getElementById(`update-time-${index+1}`).textContent = timeString;
            });
        };
        
        // Initial updates
        updateTime();
        generateDates();
        
        // Update every minute
        setInterval(updateTime, 60000);
    </script>
</body>
</html>
