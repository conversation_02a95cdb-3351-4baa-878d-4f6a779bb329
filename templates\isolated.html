<html lang="en" class="">

<head>
    <meta charset="utf-8">
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    {%include 'imports.html'%}
    <style>
        .light .themer-input {
            background-color: transparent !important;
            color: black !important;
            border: transparent !important;
            outline: none !important;
            /* Removes the focus border */
            box-shadow: none !important;
        }

        .pure-black .themer-input {
            background-color: transparent !important;
            color: white !important;
            border: transparent !important;
            border: transparent !important;
            outline: none !important;
            /* Removes the focus border */
            box-shadow: none !important;
        }

        .uk-label {
            color: var(--dropdown-text);
            background-color: var(--background);
        }

        .uk-drop.uk-dropdown {
            color: var(--dropdown-text);
        }

        .light .uk-table-divider>tr:not(:first-child),
        .uk-table-divider>:not(:first-child)>tr,
        .uk-table-divider>:first-child>tr:not(:first-child) {
            border-color: #e5e7eb;
        }

        .pure-black .uk-table-divider>tr:not(:first-child),
        .pure-black .uk-table-divider>:not(:first-child)>tr,
        .pure-black .uk-table-divider>:first-child>tr:not(:first-child) {
            border-color: #27272a;
        }

        .light .uk-label {
            background-color: white;
        }

        .pure-black .uk-label {
            background-color: #09090b;
        }
    </style>
</head>

<body class="light">
    <div class="main max-w-2xl mx-auto">
        <div id="staff-members" class="uk-card card shadow-fixer mt-4">
            <div class="uk-card-header pb-0">
                <h3 class="font-semibold leading-none tracking-tight">
                    Assign Staff members
                </h3>
                <p class="mt-1.5 text-sm ">
                    Assign staff members to this hotel. Staff members will be able to access the hotel using the link
                    below.
                </p>
            </div>
            <div class="uk-card-body pt-0">
                <hr class="card my-4">
                <div class="space-y-4" id="staff-container">
                    <h4 class="text-sm font-medium">Staff members with access : </h4>
                    <!-- Staff members will be dynamically rendered here -->
                </div>
            </div>
        </div>

        <div class="mt-4">
            <button class="uk-button uk-button-default" id="save-staff">Save Staff Configuration</button>
        </div>
    </div>
    <!-- Add this script before the closing body tag -->
    <script>
        // Sample staff data structure
        const staffMembers = [
            {
                id: 1,
                name: "Dixith Mediga",
                email: "<EMAIL>",
                avatar: "https://api.dicebear.com/8.x/lorelei/svg?seed=Olivia Martin",
                permission: null
            },
            {
                id: 2,
                name: "Harsh Jadhav",
                email: "<EMAIL>",
                avatar: "https://api.dicebear.com/8.x/lorelei/svg?seed=Isabella Nguyen",
                permission: null
            },
            {
                id: 3,
                name: "George Garriga",
                email: "<EMAIL>",
                avatar: "https://api.dicebear.com/8.x/lorelei/svg?seed=Sofia Davis",
                permission: null
            },
            {
                id: 4,
                name: "Jackon Lee",
                email: "<EMAIL>",
                avatar: "https://api.dicebear.com/8.x/lorelei/svg?seed=Jackon Lee",
                permission: null
            }
        ];

        function renderStaffMembers() {
            const container = document.getElementById('staff-container');

            // Keep the heading
            const heading = container.querySelector('h4');

            // Clear existing staff members, but keep the heading
            container.innerHTML = '';
            container.appendChild(heading);

            // Add each staff member
            staffMembers.forEach(staff => {
                const staffElement = createStaffElement(staff);
                container.appendChild(staffElement);
            });
        }

        function createStaffElement(staff) {
            const element = document.createElement('div');
            element.className = 'flex items-center space-x-4';
            element.innerHTML = `
                <span class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                    <img class="aspect-square h-full w-full" src="${staff.avatar}">
                </span>
                <div class="flex-1">
                    <p class="text-sm font-medium leading-none">${staff.name}</p>
                    <p class="text-sm ">${staff.email}</p>
                </div>
                <div class="h-9 w-[200px] relative">
                    <div id="staff-${staff.id}-permissions-selector" 
                        class="theme-responsive-button card theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer universal-hover">
                        <span id="staff-${staff.id}-selected-permission">Select Permissions</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                    <div id="staff-${staff.id}-permission-dropdown"
                        class="uk-drop uk-dropdown dropdown-content w-[252px] hidden themer-icon"
                        uk-drop="mode: click; pos: bottom-right">
                        <div class="m-1 flex items-center px-2 ">
                            <uk-icon class="opacity-50" icon="search"></uk-icon>
                            <input class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                style="background-color: transparent;" placeholder="Select a new role" type="text">
                        </div>
                        <ul class="uk-dropdown-nav">
                            <li class="uk-nav-divider"></li>
                            <li>
                                <a class="uk-drop-close" href="#demo" role="button" data-permission="Viewer">
                                    <div>
                                        <div class="dropdown-option-3">Viewer</div>
                                        <div class="text-sm" style="color: var(--dropdown-text);">Can view and comment.</div>
                                    </div>
                                </a>
                            </li>
                            <li>
                                <a class="uk-drop-close" href="#demo" role="button" data-permission="Developer">
                                    <div>
                                        <div class="dropdown-option-3">Developer</div>
                                        <div class="text-sm" style="color: var(--dropdown-text);">Can view, comment and edit.</div>
                                    </div>
                                </a>
                            </li>
                            <li>
                                <a class="uk-drop-close" href="#demo" role="button" data-permission="Billing">
                                    <div>
                                        <div class="dropdown-option-3">Billing</div>
                                        <div class="text-sm" style="color: var(--dropdown-text);">Can view, comment and manage billing.</div>
                                    </div>
                                </a>
                            </li>
                            <li>
                                <a class="uk-drop-close" href="#demo" role="button" data-permission="Owner">
                                    <div>
                                        <div class="dropdown-option-3">Owner</div>
                                        <div class="text-sm" style="color: var(--dropdown-text);">Admin-level to all resources.</div>
                                    </div>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            `;

            return element;
        }

        document.addEventListener('DOMContentLoaded', function () {
            // Render the staff members first
            renderStaffMembers();

            let currentOpenDropdown = null;

            // Attach handlers to each .theme-dropdown trigger
            document.querySelectorAll('.theme-dropdown').forEach(trigger => {
                const container = trigger.closest('.relative');
                if (!container) return;
                const dropdown = container.querySelector('.uk-dropdown');
                if (!dropdown) return;

                // Add the dropdown-content class if it doesn't exist
                if (!dropdown.classList.contains('dropdown-content')) {
                    dropdown.classList.add('dropdown-content');
                }

                trigger.addEventListener('click', e => {
                    e.preventDefault();
                    e.stopPropagation();

                    if (dropdown.classList.contains('hidden')) {
                        if (currentOpenDropdown && currentOpenDropdown !== dropdown) {
                            closeDropdown(currentOpenDropdown);
                        }
                        openDropdown(dropdown);
                        currentOpenDropdown = dropdown;
                    } else {
                        closeDropdown(dropdown);
                        currentOpenDropdown = null;
                    }
                });

                // Close dropdown if a .uk-drop-close link is clicked and update permission
                dropdown.querySelectorAll('.uk-drop-close').forEach(item => {
                    item.addEventListener('click', e => {
                        e.preventDefault();

                        // Update permission if data-permission attribute exists
                        const permission = item.getAttribute('data-permission');
                        if (permission) {
                            const staffId = dropdown.id.replace('-permission-dropdown', '');
                            const selectedElement = document.getElementById(`${staffId}-selected-permission`);
                            if (selectedElement) {
                                selectedElement.textContent = permission;

                                // Update the permission in the staffMembers array
                                const idNum = parseInt(staffId.replace('staff-', ''));
                                const staffMember = staffMembers.find(s => s.id === idNum);
                                if (staffMember) {
                                    staffMember.permission = permission;
                                    console.log(`Updated ${staffMember.name}'s permission to ${permission}`);
                                }
                            }
                        }

                        closeDropdown(dropdown);
                        if (currentOpenDropdown === dropdown) {
                            currentOpenDropdown = null;
                        }
                    });
                });

                // Prevent dropdown from closing when clicking inside
                dropdown.addEventListener('click', e => e.stopPropagation());

                // Search filter logic
                const searchInput = dropdown.querySelector('input[type="text"]');
                if (searchInput) {
                    const listItems = dropdown.querySelectorAll('ul.uk-dropdown-nav > li:not(.uk-nav-divider)');
                    searchInput.addEventListener('input', e => {
                        e.stopPropagation();
                        const query = e.target.value.toLowerCase();
                        listItems.forEach(li => {
                            const optionEl = li.querySelector('.dropdown-option-3');
                            const text = (optionEl ? optionEl.textContent : '').toLowerCase();
                            li.style.display = text.includes(query) ? '' : 'none';
                        });
                    });
                }
            });

            // Close any open dropdown if clicking outside
            document.addEventListener('click', () => {
                if (currentOpenDropdown) {
                    closeDropdown(currentOpenDropdown);
                    currentOpenDropdown = null;
                }
            });

            function openDropdown(dropdown) {
                // Set transform origin for proper animation
                dropdown.style.transformOrigin = 'top right';
                // Remove hidden class first
                dropdown.classList.remove('hidden');
                // Use requestAnimationFrame to ensure the browser processes the unhide before animating
                requestAnimationFrame(() => {
                    dropdown.setAttribute('data-state', 'open');
                });
            }

            function closeDropdown(dropdown) {
                // Start the fade out animation
                dropdown.setAttribute('data-state', '');
                // Wait for animation to complete before hiding
                setTimeout(() => {
                    dropdown.classList.add('hidden');
                }, 200); // Match this to your animation duration
            }

            // Save button handler
            document.getElementById('save-staff').addEventListener('click', function () {
                console.log('Staff Configuration:', staffMembers);
                alert('Staff configuration saved!');
            });
        });
</script>
</body>

</html>