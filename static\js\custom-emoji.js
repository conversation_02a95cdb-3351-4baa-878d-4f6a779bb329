document.addEventListener('DOMContentLoaded', function() {
  console.log('Custom Emoji Script Loaded'); // Debugging Log

  const emojiButton = document.getElementById('emoji-button');
  const messageInput = document.getElementById('message-input');

  if (!emojiButton) {
    console.error('Emoji Button not found');
    return;
  }

  if (!messageInput) {
    console.error('Message Input not found');
    return;
  }

  // Initialize Emoji Button
  const picker = new EmojiButton({
    position: 'top-start',
    style: {
      height: '300px',
      width: '350px',
    }
  });

  picker.on('emoji', emoji => {
    console.log('Emoji selected:', emoji); // Debugging Log
    const start = messageInput.selectionStart;
    const end = messageInput.selectionEnd;
    const text = messageInput.value;
    messageInput.value = text.slice(0, start) + emoji + text.slice(end);
    messageInput.selectionStart = messageInput.selectionEnd = start + emoji.length;
    messageInput.focus();
  });

  emojiButton.addEventListener('click', function(e) {
    e.preventDefault();
    console.log('Emoji Button Clicked'); // Debugging Log
    picker.togglePicker(emojiButton);
  });
});