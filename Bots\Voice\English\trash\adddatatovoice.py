import os
import sys
import json
import time
from datetime import datetime
import glob
from Bots.Voice.English.upload_to_supabase import upload_to_supabase, process_single_transcript

def monitor_transcripts_directory():
    """
    Monitor the transcripts directory for new files and upload them to Supabase.
    """
    print("Starting transcript monitoring service...")
    
    # Get the transcripts directory
    transcripts_dir = os.path.join(os.path.dirname(__file__), 'transcripts')
    
    # Ensure the directory exists
    if not os.path.exists(transcripts_dir):
        os.makedirs(transcripts_dir, exist_ok=True)
        print(f"Created transcripts directory: {transcripts_dir}")
    
    # Get initial list of files
    processed_files = set()
    initial_files = glob.glob(os.path.join(transcripts_dir, '*.json'))
    
    # Add existing files to processed list
    for file_path in initial_files:
        processed_files.add(os.path.basename(file_path))
    
    print(f"Found {len(processed_files)} existing transcript files")
    
    try:
        while True:
            # Get current list of files
            current_files = glob.glob(os.path.join(transcripts_dir, '*.json'))
            
            # Check for new files
            for file_path in current_files:
                file_name = os.path.basename(file_path)
                
                # If this is a new file
                if file_name not in processed_files:
                    print(f"New transcript file detected: {file_name}")
                    
                    # Wait a moment to ensure file is completely written
                    time.sleep(1)
                    
                    # Process the file
                    success = process_single_transcript(file_path)
                    
                    if success:
                        print(f"Successfully uploaded {file_name} to Supabase")
                    else:
                        print(f"Failed to upload {file_name} to Supabase")
                    
                    # Add to processed files
                    processed_files.add(file_name)
            
            # Sleep before checking again
            time.sleep(5)
            
    except KeyboardInterrupt:
        print("\nStopping transcript monitoring service...")
        sys.exit(0)

def process_specific_transcript(file_path):
    """
    Process a specific transcript file and upload it to Supabase.
    """
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return False
    
    print(f"Processing transcript file: {os.path.basename(file_path)}")
    success = process_single_transcript(file_path)
    
    if success:
        print(f"Successfully uploaded {os.path.basename(file_path)} to Supabase")
        return True
    else:
        print(f"Failed to upload {os.path.basename(file_path)} to Supabase")
        return False

def process_latest_transcript():
    """
    Find and process the most recent transcript file.
    """
    transcripts_dir = os.path.join(os.path.dirname(__file__), 'transcripts')
    
    # Ensure the directory exists
    if not os.path.exists(transcripts_dir):
        print(f"Transcripts directory not found: {transcripts_dir}")
        return False
    
    # Get all JSON files
    json_files = glob.glob(os.path.join(transcripts_dir, '*.json'))
    
    if not json_files:
        print("No transcript files found")
        return False
    
    # Find the most recent file
    latest_file = max(json_files, key=os.path.getmtime)
    
    print(f"Found latest transcript file: {os.path.basename(latest_file)}")
    return process_specific_transcript(latest_file)

def process_all_transcripts():
    """
    Process all transcript files in the directory.
    """
    transcripts_dir = os.path.join(os.path.dirname(__file__), 'transcripts')
    
    # Ensure the directory exists
    if not os.path.exists(transcripts_dir):
        print(f"Transcripts directory not found: {transcripts_dir}")
        return False
    
    # Get all JSON files
    json_files = glob.glob(os.path.join(transcripts_dir, '*.json'))
    
    if not json_files:
        print("No transcript files found")
        return False
    
    print(f"Found {len(json_files)} transcript files")
    
    success_count = 0
    for file_path in json_files:
        if process_specific_transcript(file_path):
            success_count += 1
    
    print(f"Successfully processed {success_count} out of {len(json_files)} files")
    return success_count > 0

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Process voice call transcripts and upload to Supabase')
    parser.add_argument('--monitor', action='store_true', help='Monitor the transcripts directory for new files')
    parser.add_argument('--file', type=str, help='Process a specific transcript file')
    parser.add_argument('--latest', action='store_true', help='Process the most recent transcript file')
    parser.add_argument('--all', action='store_true', help='Process all transcript files')
    
    args = parser.parse_args()
    
    if args.monitor:
        monitor_transcripts_directory()
    elif args.file:
        process_specific_transcript(args.file)
    elif args.latest:
        process_latest_transcript()
    elif args.all:
        process_all_transcripts()
    else:
        # Default to processing the latest transcript
        process_latest_transcript()
