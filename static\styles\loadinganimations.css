.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--theme-overlay-bg);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0; /* Prevent any visible flicker */
  transition: opacity 0.4s ease; /* Smoother transition */
  pointer-events: none; /* Ensure it doesn't block interactions */
}

.loader {
  width: 50px;
  height: 50px;
  position: relative;
  z-index: 1;
  transform: rotate(15deg);
  will-change: transform;
}

.loader::before, 
.loader::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  mix-blend-mode: difference;
  animation: rotate92523 2.5s infinite cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, left;
}

.loader::before {
  background-color: #000000;
}

.loader::after {
  background-color: #ffffff;
  animation-delay: 1.25s;
}

@keyframes rotate92523 {
  0%, 100% {
    left: 35px;
    transform: scale(1);
  }
  25% {
    transform: scale(0.3);
  }
  50% {
    left: 0%;
  }
  75% {
    transform: scale(1);
  }
}

/* Pulsating effect */
@keyframes pulse {
  0%, 100% {
    transform: scale(1) rotate(15deg);
  }
  50% {
    transform: scale(1.07) rotate(15deg);
  }
}

.loader {
  animation: pulse 1.8s infinite ease-in-out; /* Quicker and smoother */
}
