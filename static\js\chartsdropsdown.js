document.addEventListener('DOMContentLoaded', function() {
    setupChartDropdown('chart1-dropdown-btn', 'chart1-dropdown-menu');
    setupChartDropdown('chart2-dropdown-btn', 'chart2-dropdown-menu');
    setupChartDropdown('chart3-dropdown-btn', 'chart3-dropdown-menu');
    setupChartDropdown('salesOverviewChart-dropdown-btn', 'salesOverviewChart-dropdown-menu');
});

function setupChartDropdown(buttonId, menuId) {
    const button = document.getElementById(buttonId);
    const menu = document.getElementById(menuId);

    if (!button || !menu) {
        console.error(`Button or menu not found for ${buttonId}`);
        return;
    }

    button.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        const isExpanded = button.getAttribute('aria-expanded') === 'true';
        button.setAttribute('aria-expanded', !isExpanded);
        menu.classList.toggle('hidden');
    });

    menu.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        if (e.target.classList.contains('chart-dropdown-item')) {
            const selectedOption = e.target.querySelector('i').nextSibling.textContent.trim();
            updateButtonContent(button, selectedOption);
            menu.classList.add('hidden');
            button.setAttribute('aria-expanded', 'false');
        }
    });

    document.addEventListener('click', function(e) {
        if (!button.contains(e.target) && !menu.contains(e.target)) {
            menu.classList.add('hidden');
            button.setAttribute('aria-expanded', 'false');
        }
    });
}

function updateButtonContent(button, selectedOption) {
    const span = button.querySelector('span');
    if (span) {
        span.textContent = selectedOption;
    }
    
    // Update icon if needed
    const icon = button.querySelector('i');
    if (icon) {
        icon.className = ''; // Clear existing classes
        switch(selectedOption) {
            case 'Facebook':
                icon.className = 'fab fa-facebook mr-1';
                break;
            case 'Instagram':
                icon.className = 'fab fa-instagram mr-1';
                break;
            case 'Twitter':
                icon.className = 'fab fa-twitter mr-1';
                break;
            case 'LinkedIn':
                icon.className = 'fab fa-linkedin mr-1';
                break;
            case 'Bar':
                icon.className = 'fas fa-chart-bar mr-2';
                break;
            case 'Line':
                icon.className = 'fas fa-chart-line mr-2';
                break;
            // Add cases for other dropdowns if needed
        }
    }
}