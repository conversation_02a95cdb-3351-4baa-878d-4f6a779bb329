<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Products</title>
  {% include 'imports.html' %}
</head>
<style>
  body {
    visibility: hidden;
  }

  #sidebar {
    position: sticky;
    top: 0;
    height: 100vh;
    /* Force full viewport height */
    overflow-y: auto;
    /* Allow scrolling within the sidebar */
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Custom close button - no hover effects */
  #custom-close-btn {
    cursor: pointer;
    outline: none;
  }

  #custom-close-btn:hover,
  #custom-close-btn:focus {
    opacity: 1;
    transform: none;
    background-color: transparent;
  }

  #custom-close-btn svg {
    pointer-events: none;
  }

  #custom-close-btn:hover svg,
  #custom-close-btn:focus svg {
    opacity: 1;
    transform: none;
  }
</style>

<body class="light">
  {% include 'components/loading.html' %}

  <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] ">
    {% include 'sidebar.html' %}

    <div class="flex flex-col">
      <header
        class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
        <div style="margin-left: 8px;" class="flex items-center gap-2 px-4 pl-0">
          <button id="toggle-btn" class="opacity-100 transition-opacity duration-300 focus:outline-none"
            style="background-color: transparent !important;">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-panel-left">
              <rect width="18" height="18" x="3" y="3" rx="2"></rect>
              <path d="M9 3v18"></path>
            </svg>
          </button>
          <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-3 h-4"
            style="background-color: var(--border-color);"></div>
          <nav aria-label="breadcrumb">
            <ol class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
              <!-- Dashboard link -->
              <div class="menubar" role="menubar">
                  <div class="menubar-indicator"></div>
                  <a href="/" role="menuitem">Dashboard</a>
                  <a href="/users" role="menuitem">Users</a>
                  <a href="/products" role="menuitem" class="active">Products</a>
                  <a href="/analytics" role="menuitem">Overview</a>
              </div>
            </ol>
          </nav>
        </div>
        {% include 'topright.html' %}
      </header>
      <main class="card flex flex-1 flex-col gap-2 p-4 md:gap-2 md:p-6">
        <div class="">
          <div class="mx-auto max-w-2xl px-2 py-4 sm:px-3 sm:py-6 lg:max-w-7xl lg:px-2">
            <!-- Food Menu Section -->
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-xl font-medium leading-none tracking-tight">
                Food Menu
              </h2>
              <button class="uk-button border card default" type="button" uk-toggle="target: #add-product-modal"
                style="display: flex; align-items: center; gap: 8px; padding: 8px 12px; min-width: 110px; justify-content: center;">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" style="margin-left: -6px;" height="16"
                  viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" class="lucide lucide-plus">
                  <path d="M5 12h14" />
                  <path d="M12 5v14" />
                </svg>
                <span>Add Item</span>
              </button>
            </div>
            <div
              class="grid grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 xl:gap-x-8 mb-12">
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/foodmenu/burrata.png" alt="Burrata"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Burrata</h3>
                <p class="mt-1 text-lg font-medium">$25</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/foodmenu/chicken.png" alt="Chicken"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Chicken</h3>
                <p class="mt-1 text-lg font-medium">$32</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/foodmenu/filletsteak.png" alt="Fillet Steak"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Fillet Steak</h3>
                <p class="mt-1 text-lg font-medium">$45</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/foodmenu/grilledsalmon.png" alt="Grilled Salmon"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Grilled Salmon</h3>
                <p class="mt-1 text-lg font-medium">$38</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/foodmenu/lobsterroll.png" alt="Lobster Roll"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Lobster Roll</h3>
                <p class="mt-1 text-lg font-medium">$42</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/foodmenu/pumpkinwithcoconut.png" alt="Pumpkin with Coconut"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Pumpkin with Coconut</h3>
                <p class="mt-1 text-lg font-medium">$28</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/foodmenu/steakandlobster.png" alt="Steak and Lobster"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Steak and Lobster</h3>
                <p class="mt-1 text-lg font-medium">$65</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/foodmenu/tbone.png" alt="T-Bone Steak"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">T-Bone Steak</h3>
                <p class="mt-1 text-lg font-medium">$55</p>
              </a>
            </div>

            <!-- Divider -->
            <div style="margin-bottom: 5pc;" class="partition lines mb-8"></div>

            <!-- Beverages Section -->
            <h2 class="text-xl font-medium leading-none tracking-tight mb-6">Beverages</h2>
            <div
              class="grid grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 xl:gap-x-8 mb-12">
              <!-- Update beverage image URLs -->
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/beveragemenu/cocktail.jpg" alt="Cocktail"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Cocktail</h3>
                <p class="mt-1 text-lg font-medium">$48</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/beveragemenu/drinks.jpeg" alt="Drinks"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Drinks</h3>
                <p class="mt-1 text-lg font-medium">$89</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/beveragemenu/redwine.jpg" alt="Red Wine"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Red Wine</h3>
                <p class="mt-1 text-lg font-medium">$40</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/beveragemenu/beers.jpg" alt="Beers"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Beers</h3>
                <p class="mt-1 text-lg font-medium">$10</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/beveragemenu/soda.jpg" alt="Soda"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Soda</h3>
                <p class="mt-1 text-lg font-medium">$3</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/beveragemenu/water.jpg" alt="Water"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Water</h3>
                <p class="mt-1 text-lg font-medium">$5</p>
              </a>
            </div>

            <!-- Divider -->
            <div style="margin-bottom: 5pc;" class="partition lines mb-8"></div>

            <!-- Room Bookings Section -->
            <h2 class="text-xl font-medium leading-none tracking-tight mb-6">Room Bookings</h2>
            <div
              class="grid grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 xl:gap-x-8 mb-12">
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/roombooking/executivesuite.jpeg" alt="Executive Suite"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Executive Suite</h3>
                <p class="mt-1 text-lg font-medium">$150</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/roombooking/juniorsuite.jpeg" alt="Junior Suite"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Junior Suite</h3>
                <p class="mt-1 text-lg font-medium">$55</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/roombooking/juniorsuitedeluxe.jpeg" alt="Junior Suite Deluxe"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Junior Suite Deluxe</h3>
                <p class="mt-1 text-lg font-medium">$65</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/roombooking/raid.jpeg" alt="Royal Suite"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Royal Suite</h3>
                <p class="mt-1 text-lg font-medium">$75</p>
              </a>
            </div>

            <!-- Divider -->
            <div style="margin-bottom: 5pc;" class="partition lines mb-8"></div>

            <!-- Spa & Massage Section -->
            <h2 class="text-xl font-medium leading-none tracking-tight mb-6">Spa & Massage</h2>
            <div class="grid grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 xl:gap-x-8">
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/starterimages/spa.png" alt="Spa Services"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Spa Services</h3>
                <p class="mt-1 text-lg font-medium">$120</p>
              </a>
              <a href="#" class="group">
                <img src="https://images-new.vercel.app/starterimages/massage.png" alt="Massage"
                  class="aspect-square w-full rounded-lg bg-transparent object-cover xl:aspect-[7/8]">
                <h3 class="mt-4 text-sm">Massage</h3>
                <p class="mt-1 text-lg font-medium">$90</p>
              </a>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- Add Product Modal -->
  <div id="add-product-modal" uk-modal>
    <div class="uk-modal-dialog uk-modal-body card rounded-lg">
      <button class="absolute top-6 right-3 p-0 border-0 bg-transparent" style="background-color: transparent;"
        type="button" id="custom-close-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
      <h2 class="uk-modal-title mb-4 text-lg font-semibold">Add New Product</h2>

      <form>
        <div class="mb-4">
          <label class="uk-form-label mb-1 block" for="product-name">Product Name</label>
          <div class="uk-form-controls">
            <input class="uk-input card rounded-md" id="product-name" type="text" placeholder="Enter product name">
          </div>
        </div>

        <!-- Price and Type Row -->
        <div class="flex items-end gap-4 mb-4">
          <!-- Price Input -->
          <div class="flex-1">
            <label class="uk-form-label mb-1 block" for="product-price">Price</label>
            <div class="uk-form-controls">
              <input class="uk-input card rounded-md" id="product-price" type="text" placeholder="Enter price"
                inputmode="numeric">
            </div>
          </div>
          <!-- Using the structure from tasks.html View dropdown -->
          <div class="product-type-container">
            <button id="product-type-button" class="uk-button uk-button-default universal-hover theme-text card"
              type="button" aria-haspopup="true" style="background-color: transparent; height: 36px; min-width: 150px;">
              <span class="mr-2 size-4">
                <!-- Using a category-like icon -->
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="lucide lucide-layout-grid">
                  <rect width="18" height="18" x="3" y="3" rx="2" />
                  <path d="M3 12h18" />
                  <path d="M12 3v18" />
                </svg>
              </span>
              <span id="product-type-button-text">Select Type</span>
            </button>
            <div id="product-type-dropdown" class="uk-dropdown card dropdown-content"
              uk-dropdown="mode: click; pos: bottom-right; animation: false; duration: 0">
              <ul class="uk-nav uk-dropdown-nav">
                <li><a href="#" class="product-type-option" data-value="Food Menu"
                    onclick="selectProductType(event, 'Food Menu')">Food Menu</a></li>
                <li><a href="#" class="product-type-option" data-value="Beverages"
                    onclick="selectProductType(event, 'Beverages')">Beverages</a></li>
                <li><a href="#" class="product-type-option" data-value="Room Bookings"
                    onclick="selectProductType(event, 'Room Bookings')">Room Bookings</a></li>
                <li><a href="#" class="product-type-option" data-value="Spa & Massage"
                    onclick="selectProductType(event, 'Spa & Massage')">Spa & Massage</a></li>
              </ul>
            </div>
          </div>
        </div>

        <div class="mb-6"> <!-- Changed mb-4 to mb-6 for spacing -->
          <label class="uk-form-label mb-1 block">Product Image</label>
          <div class="uk-form-controls">
            <label for="product-image-upload"
              class="card border border-dashed rounded-md p-4 flex flex-col items-center justify-center cursor-pointer h-32 w-full hover:border-primary transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-upload-cloud mb-2 text-gray-400">
                <path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242" />
                <path d="M12 12v9" />
                <path d="m16 16-4-4-4 4" />
              </svg>
              <span class="text-sm text-gray-500" id="upload-text">Click or drag file to upload</span>
              <img id="image-preview" src="#" alt="Image Preview"
                class="hidden max-h-full max-w-full object-contain mt-2" />
            </label>
            <input class="hidden" id="product-image-upload" type="file" accept="image/*">
          </div>
        </div>

        <div class="text-right">
          <button class="uk-button uk-button-default uk-modal-close card" type="button">Cancel</button>
          <button class="uk-button uk-button-default uk-modal-close card" type="button">Save Product</button>
        </div>
      </form>
    </div>
  </div>

  <script>
    // Function to handle product type selection
    function selectProductType(event, value) {
      // Ensure these are the first lines to prevent default behavior immediately
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }

      // Update the button text
      const buttonText = document.getElementById('product-type-button-text');
      if (buttonText) {
        buttonText.textContent = value;
      }

      // Hide the dropdown using UIkit
      if (typeof UIkit !== 'undefined') {
        const dropdown = UIkit.dropdown('#product-type-dropdown');
        if (dropdown) {
          dropdown.hide(false);
        }
      }

      // Return false to further ensure no navigation happens
      return false;
    }
    // Initialize UIkit dropdown when DOM is loaded
    document.addEventListener('DOMContentLoaded', function () {
      // Set up custom close button functionality
      const customCloseBtn = document.getElementById('custom-close-btn');
      if (customCloseBtn) {
        customCloseBtn.addEventListener('click', function () {
          UIkit.modal('#add-product-modal').hide();
        });
      }
      if (typeof UIkit !== 'undefined') {
        const productTypeDropdown = document.getElementById('product-type-dropdown');
        if (productTypeDropdown) {
          // Initialize the dropdown with UIkit
          const dropdown = UIkit.dropdown(productTypeDropdown, {
            mode: 'click',
            pos: 'bottom-right',
            animation: false,
            duration: 0
          });

          // Store the dropdown instance on the element for easier access
          productTypeDropdown.ukDropdown = dropdown;

          // Add animation handlers similar to other dropdowns in the project
          UIkit.util.on(productTypeDropdown, 'beforeshow', function () {
            this.style.transformOrigin = 'top right';
            this.setAttribute('data-state', 'open');
          });
        }
      }

      // Price input formatting
      const priceInput = document.getElementById('product-price');
      if (priceInput) {
        // Format the price with pound symbol and .00
        function formatPrice(value) {
          // Remove any non-numeric characters
          let numericValue = value.replace(/[^0-9]/g, '');

          // If empty, return empty string
          if (numericValue === '') return '';

          // Convert to number and format with 2 decimal places
          const price = parseInt(numericValue, 10) / 100;
          return `£${price.toFixed(2)}`;
        }

        // Update the input value with formatted price
        function updateInputValue() {
          const rawValue = priceInput.value.replace(/[^0-9]/g, '');
          if (rawValue) {
            priceInput.value = formatPrice(rawValue);
          }
        }

        // Handle input events
        priceInput.addEventListener('input', function (e) {
          const cursorPosition = this.selectionStart;
          const originalLength = this.value.length;

          // Store the raw numeric value
          const rawValue = this.value.replace(/[^0-9]/g, '');

          // Format and update the input
          if (rawValue) {
            this.value = formatPrice(rawValue);

            // Adjust cursor position after formatting
            const newLength = this.value.length;
            const newPosition = cursorPosition + (newLength - originalLength);
            this.setSelectionRange(newPosition, newPosition);
          }
        });

        // Handle focus events
        priceInput.addEventListener('focus', function () {
          // If the field is empty, add the pound symbol and .00
          if (!this.value) {
            this.value = '£0.00';
            this.setSelectionRange(1, 1);
          }
        });

        // Handle blur events
        priceInput.addEventListener('blur', function () {
          // Ensure proper formatting when leaving the field
          updateInputValue();

          // If the value is just the pound symbol, clear it
          if (this.value === '£0.00') {
            this.value = '';
          }
        });

        // Handle keydown to only allow numeric input
        priceInput.addEventListener('keydown', function (e) {
          // Allow: backspace, delete, tab, escape, enter, and numeric keys
          if ([46, 8, 9, 27, 13].indexOf(e.keyCode) !== -1 ||
            // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
            (e.keyCode === 65 && e.ctrlKey === true) ||
            (e.keyCode === 67 && e.ctrlKey === true) ||
            (e.keyCode === 86 && e.ctrlKey === true) ||
            (e.keyCode === 88 && e.ctrlKey === true) ||
            // Allow: home, end, left, right
            (e.keyCode >= 35 && e.keyCode <= 39)) {
            return;
          }

          // Block non-numeric keys
          if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) &&
            (e.keyCode < 96 || e.keyCode > 105)) {
            e.preventDefault();
          }
        });
      }
    });

    // Image Upload Preview Script
    const fileInput = document.getElementById('product-image-upload');
    const imagePreview = document.getElementById('image-preview');
    const uploadText = document.getElementById('upload-text');
    const uploadIcon = document.querySelector('#add-product-modal label[for="product-image-upload"] svg'); // Select the icon inside the label

    fileInput.addEventListener('change', function (event) {
      const file = event.target.files[0];
      if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();

        reader.onload = function (e) {
          imagePreview.src = e.target.result;
          imagePreview.classList.remove('hidden'); // Show the image preview
          uploadText.classList.add('hidden');     // Hide the placeholder text
          if (uploadIcon) uploadIcon.classList.add('hidden'); // Hide the icon
        }
        reader.readAsDataURL(file);
      }
    });
  </script>
</body>

</html>