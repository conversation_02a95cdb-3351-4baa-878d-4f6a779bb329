<style>
    /* styles.css */
    .no-hover-bg:hover {
      background-color: transparent !important;
    }
  </style>
  <script src="https://unpkg.com/franken-ui@1.1.0/dist/js/core.iife.js" type="module"></script>
  <script src="https://unpkg.com/franken-ui@1.1.0/dist/js/icon.iife.js" type="module"></script>
  <link rel="stylesheet" href="../static/styles/custom.css">
  
  
  <div class="relative card">
    <div class="uk-navbar-item flex items-center card">
      <!-- Divider -->
      
      <button onclick="window.location.reload()"
          class="uk-button border card flex items-center gap-2 ">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-refresh-ccw"><path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/><path d="M3 3v5h5"/><path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"/><path d="M16 16h5v5"/></svg>
          Reload
      </button>
      <ul style="margin-right: 5px;" class="uk-iconnav uk-iconnav-outline">
        <li>
          <a href="javascript:void(0);" uk-toggle="target: #language-dropdown" id="languageToggler" style="background-color: transparent;" class="flex items-center justify-center card universal-hover" aria-label="Select Language">
            <uk-icon icon="languages"></uk-icon>
          </a>
          <!-- Dropdown Menu -->
          <div class="card dropdown-content" id="language-dropdown" uk-dropdown="mode: click; pos: bottom-right; offset: 10">
            <ul class="uk-nav uk-dropdown-nav card">
              <li><a href="#" class="language-option" data-no-translate data-lang="en">English</a></li>
              <li><a href="#" class="language-option" data-no-translate data-lang="es">Spanish</a></li>
              <li><a href="#" class="language-option" data-no-translate data-lang="fr">French</a></li>
              <li><a href="#" class="language-option" data-no-translate data-lang="it">Italian</a></li>
              <li><a href="#" class="language-option" data-no-translate data-lang="sv">Swedish</a></li>
              <li><a href="#" class="language-option" data-no-translate data-lang="de">German</a></li>
              <li><a href="#" class="language-option" data-no-translate data-lang="nl">Dutch</a></li>
            </ul>
          </div>
        </li>
        <li>
          <a href="javascript:void(0);" id="darkModeToggler" class="card flex items-center justify-center universal-hover" style="background-color: transparent;" aria-label="Toggle Dark Mode">
            <uk-icon id="moonIcon" icon="moon-star"></uk-icon>
            <uk-icon id="sunIcon" icon="sun" class="hidden"></uk-icon>
          </a>
        </li>
      </ul>
      
      <!-- Profile Icon with Dropdown -->
      <a class="inline-flex h-8 w-8 items-center justify-center rounded-full  ring-ring border-2  focus:outline-none " href="javascript:void(0);" role="button" aria-haspopup="true" aria-label="User Profile"> 
        <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-full"> 
          <img class="aspect-square h-full w-full" alt="User Avatar" src="https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult"> 
        </span> 
      </a> 
      <div class="uk-drop uk-dropdown dropdown-content" uk-dropdown="mode: click; pos: bottom-right">
        <ul class="uk-dropdown-nav uk-nav">
          <li class="px-2 py-1.5 text-sm">
            <div class="flex flex-col space-y-1">
              <p class="text-sm font-medium leading-none">Guest Genius</p>
              <p class="text-xs leading-none text-muted-foreground">
                <EMAIL>
              </p>
            </div>
          </li>
          <li class="uk-nav-divider"></li>
          <li> 
            <a class="justify-between language-option" href="#demo" role="button" data-lang="en">
              Profile 
            </a> 
          </li>
          <li> 
            <a class="justify-between language-option" href="/issue" role="button" data-lang="en">
              Contact Support 
            </a> 
          </li>
          <li class="uk-nav-divider"></li>
          <li> 
            <a class="uk-drop-close justify-between language-option" href="/logout" role="button" data-lang="en">
              Logout
            </a> 
          </li>
        </ul>
      </div>
    </div>
    
    <script>
      // Prevent default behavior for all links with href="#" to avoid page scrolling to top
      document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('a[href="#"]').forEach(function(link) {
          link.addEventListener('click', function(e) {
            e.preventDefault();
          });
        });
  
        // Initialize shadcn-style animations for dropdowns
        if (typeof UIkit !== 'undefined') {
          // Add event listeners for UIkit dropdown events
          UIkit.util.on('.dropdown-content', 'beforeshow', function() {
            // Before showing the dropdown, set initial state
            this.style.transformOrigin = 'top right';
            this.setAttribute('data-state', '');
            
            // Use requestAnimationFrame to ensure browser processes the DOM before animation
            requestAnimationFrame(() => {
              this.setAttribute('data-state', 'open');
            });
          });
  
          UIkit.util.on('.dropdown-content', 'beforehide', function() {
            // Start the fade out animation
            this.setAttribute('data-state', '');
            
            // Prevent the default hide behavior to allow our animation to finish
            const dropdown = this;
            const component = UIkit.dropdown(dropdown) || UIkit.drop(dropdown);
            
            if (component) {
              const originalHide = component.hide;
              component.hide = function() {
                // Do nothing, waiting for animation
              };
              
              // After animation completes, restore original hide method and call it
              setTimeout(() => {
                component.hide = originalHide;
                component.hide();
              }, 200); // Match animation duration
            }
            
            return false; // Prevent default hide
          });
        }
      });
    </script>
  </div>