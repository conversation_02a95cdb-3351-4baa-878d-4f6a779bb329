<!-- TESTING -->
<!--Dix testing-->
<!--(The UI is done and perfect, Backend is done and perfect)-->

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, viewport-fit=cover">
  <title>Live Chat</title>
  {% include 'imports.html' %}

  <!-- WebSocket for real-time updates -->
  <script>
    // We'll use the native WebSocket API instead of Supabase client
    console.log("Using WebSocket for real-time updates");
  </script>

  <script>
    tailwind.config = {
      darkMode: 'class',
    }
  </script>
  <style>
    /* High-priority realtime message styling */
    .realtime-new {
      animation: none !important;
      /* Override existing animations */
      transition: none !important;
      /* Override transitions */
      opacity: 1 !important;
    }

    /* Flash animation for new messages */
    @keyframes flashNew {

      0%,
      100% {
        background-color: transparent;
      }

      50% {
        background-color: rgba(59, 130, 246, 0.15);
      }
    }

    .message-highlight {
      animation: flashNew 0.8s ease-out;
    }
  </style>
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      padding: 0;
      overflow: hidden;
    }

    #app {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    @keyframes messageIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .divider {
      margin: 7px 0 0 0;
      padding: 0;
    }

    #dropdown-menu {
      display: none;
    }

    #dropdown-button {
      color: #4a5568;
    }

    #dropdown-button:hover,
    #dropdown-button:focus {
      color: #2d3748;
    }

    #dropdown-button.clicked+#dropdown-menu {
      display: block;
    }

    main {
      flex-grow: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;
    }

    #chat-messages {
      flex-grow: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
      padding: 10px;
    }

    * {
      scrollbar-width: none;
      -ms-overflow-style: none;
    }

    *::-webkit-scrollbar {
      display: none;
    }

    .user-item {
      cursor: pointer;
      border-bottom: 1px solid #e5e7eb;
    }

    .user-item:last-child {
      border-bottom: none;
    }

    #chat-container {
      height: calc(100vh - 180px);
      overflow-y: auto;
    }

    .message-container {
      display: flex;
      align-items: flex-start;
      margin-bottom: 10px;
    }

    .message-container.left {
      animation: slideInLeft 0.3s ease-out;
    }

    .message-container.right {
      animation: slideInRight 0.3s ease-out;
    }

    @keyframes slideInLeft {
      from {
        opacity: 0;
        transform: translateX(-20px);
      }

      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    @keyframes slideInRight {
      from {
        opacity: 0;
        transform: translateX(20px);
      }

      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    #emoji-container {
      position: absolute;
      bottom: 60px;
      /* Position above the input field */
      left: 0;
      display: none;
      /* Hidden by default */
      z-index: 1000;
      /* Ensure it appears above other elements */
      width: 300px;
      /* Adjust width as needed */
    }

    /* Optional: Adjust the emoji button styles */
    .emoji-button {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      margin-right: 10px;
      /* Space between button and input */
    }

    /* Ensure the chat-input-container is positioned relative */
    .chat-input-container {
      position: relative;
      /* To position #emoji-container absolutely within it */
    }

    .message-icon {
      width: 24px;
      height: 24px;
      flex-shrink: 0;
      background-size: cover;
      background-position: center;
    }

    .message-icon.user {
      background-image: url('https://cdn-icons-png.flaticon.com/128/17487/17487660.png');
      margin-right: 15px;
    }

    .message-icon.bot {
      background-image: url('https://cdn-icons-png.flaticon.com/128/17487/17487660.png');
      margin-left: 15px;
    }

    .message-left,
    .message-right {
      max-width: 70%;
      padding: 10px;
      border-radius: 8px;
      word-wrap: break-word;
    }

    .message-left {
      background-color: #f0f0f0;
      color: black;
      margin-right: auto;
    }

    .message-right {
      background-color: #f0f0f0;
      color: black;
      margin-left: auto;
    }

    .timestamp {
      cursor: pointer;
    }

    #message-input-container {
      display: flex;
      align-items: center;
      border: 1px solid #ccc;
      border-radius: 0px;
      padding: 10px;
      flex-grow: 1;
    }

    #message-input {
      flex: 1;
      border: none;
      resize: none;
      font-size: 14px;
      outline: none;
      line-height: 1.5;
      padding: 10px;
      box-sizing: border-box;
    }

    /* Message input styling with theme-aware border */
    .message-input {
      border: 1px solid var(--border-color) !important;
    }

    #message-input:focus {
      outline: none !important;
      box-shadow: none !important;
      border-color: var(--border-color) !important;
    }

    #send-button svg {
      width: 32px;
      height: 32px;
    }

    .user-row {
      padding: 15px;
      transition: background-color 0.3s ease;
      border-bottom: 1px solid #e5e7eb;
    }

    .user-row:hover,
    .user-row.bg-gray-200 {
      background-color: #f3f4f6;
    }

    /* Add this CSS rule */
    #emoji-picker-button {
      background-color: #ffffff;
    }

    .user-item:last-child .user-row {
      border-bottom: none;
      margin-bottom: -8px;
    }

    #user-list {
      padding: 0;
    }

    .rounded-lg {
      border-radius: 1rem;
    }

    .chat-input-container {
      position: sticky;
      bottom: 0;
      z-index: 10;
    }

    #user-list {
      padding-top: 2rem !important;
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }

    #user-list {
      max-height: calc(100vh - 140px);
      overflow-y: auto;
      padding-top: 1rem !important;
      padding-left: 1rem !important;
      padding-right: 1rem !important;
      padding-bottom: 3rem !important;
      /* Add bottom padding */
      margin-bottom: 1rem;
    }

    .tag-mail {
      margin-bottom: 10px;
      transition: all 0.2s ease;
      background-color: transparent;
      border: 1px solid #e5e7eb;
      position: relative;
      z-index: 1;
    }

    .tag-mail:hover,
    .tag-mail.hover-persistent {
      background-color: #f4f4f5 !important;
      cursor: pointer;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .tag-mail.active {
      background-color: #f4f4f5;
      border-color: #e2e2e2;
    }

    .uk-label {
      background: transparent;
      color: currentColor;
      border: 1px solid currentColor;
      padding: 2px 6px;
      font-size: 0.75rem;
      border-radius: 4px;
    }

    .uk-label-platform {
      background: transparent;
      color: currentColor;
      border: 1px solid currentColor;
      padding: 2px 6px;
      font-size: 0.75rem;
      border-radius: 4px;
      text-transform: none;
    }

    .flex-grow {
      flex-grow: 1;
    }

    .grid-cols-custom {
      display: grid;
      grid-template-columns: 350px 1fr;
    }

    /* Width of the user list */

    #chat-container {
      min-height: 0;
      /* This allows the container to grow within the flex layout */
    }

    /* Message bubble theme-based styling */
    body.light .message-bubble {
      background-color: #18181b !important;
      color: white !important;
    }

    /* If you want to keep original styling in dark mode, add this */
    body:not(.light) .message-bubble {
      background-color: #e5e7eb;
      color: black;
    }

    .light .chat-dropdown-icon-hover:hover {
      background-color: #f4f4f5 !important;
    }

    .pure-black .chat-dropdown-icon-hover:hover {
      background-color: #27272a !important;
    }
  </style>
  <style>
    /* Emoji Picker Styles */
    #emoji-container {
      position: absolute;
      bottom: 60px;
      /* Position above the input field */
      left: 0;
      display: none;
      /* Hidden by default */
      z-index: 1000;
      /* Ensure it appears above other elements */
      width: fit-content;
      /* Adjust width as needed */
      max-height: 300px;
      /* Set desired max height */
      overflow: hidden;
    }

    /* Adjust the scroll area within the picker */
    #emoji-container .emoji-mart-scroll {
      max-height: 180px !important;
      /* Further constrain the scrollable area */
    }

    /* Optional: Adjust the emoji button styles */
    .emoji-button {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      margin-right: 10px;
      /* Space between button and input */
    }

    /* Ensure the chat-input-container is positioned relative */
    .chat-input-container {
      position: relative;
      /* To position #emoji-container absolutely within it */
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
      #emoji-container {
        width: 250px;
        /* Adjust width for smaller screens */
        max-height: 150px;
        /* Adjust height for smaller screens */
        bottom: 60px;
        /* Adjust positioning if necessary */
      }

      #emoji-container .emoji-mart {
        max-height: 150px !important;
      }

      #emoji-container .emoji-mart-scroll {
        max-height: 130px !important;
      }

      .emoji-button {
        font-size: 20px;
        /* Slightly smaller on mobile */
      }
    }
  </style>
</head>

<body class="light">
  {% include 'components/loading.html' %}
  <!-- Template Add Modal -->
  <div id="add-template-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
    <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm" id="modal-backdrop"></div>
    <div class="card universal-background-color rounded-lg shadow-lg w-[500px] z-10 relative border border-gray-300">
      <!-- Header with divider -->
      <div class="flex justify-between items-center p-4 border-b card">
        <h3 class="text-lg font-medium" id="modal-title">Add New Template</h3>
        <!-- X icon matching the toggle button style -->
        <button id="close-template-modal"
          style="background-color: transparent; height: 34px; border: none; cursor: pointer; position: relative;"
          class="flex items-center justify-center card" aria-label="Close Modal">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      <div class="p-4">
        <div class="mb-4">
          <label for="template-name" class="block text-sm font-medium mb-1">Template Name</label>
          <input type="text" id="template-name"
            class="uk-input card w-full rounded-md focus:outline-none focus:border-blue-500 message-input"
            placeholder="Enter template name">
        </div>
        <div class="mb-4">
          <label for="template-content" class="block text-sm font-medium mb-1">Template Message</label>
          <textarea id="template-content"
            class="uk-input card w-full rounded-md focus:outline-none focus:border-blue-500 message-input"
            style="min-height: 120px; resize: none;" placeholder="Enter your template message here..."></textarea>
        </div>
        <!-- Hidden template ID for edit mode -->
        <input type="hidden" id="template-id" value="">
        <input type="hidden" id="edit-mode" value="false">
      </div>
      <!-- Footer with divider -->
      <div class="flex justify-end p-4 border-t card">
        <button id="cancel-template" class="uk-button uk-button-default mr-2 card universal-hover"
          style="border-radius: 8px; height: 36px; line-height: 30px;">Cancel</button>
        <button id="delete-template" class="uk-button uk-button-danger mr-2"
          style="border-radius: 8px; height: 36px; display: none; align-items: center; justify-content: center;">
          Delete Template
        </button>
        <button id="save-template" class="uk-button uk-button-primary"
          style="border-radius: 8px; height: 36px; line-height: 30px;">
          <span id="save-button-text">Save Template</span>
        </button>
      </div>
    </div>
  </div>

  <div id="app">
    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] ">
      {% include 'sidebar.html' %}
      <div class="flex flex-1 flex-col">
        <header
          class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
          <div class="flex items-center gap-2 px-4 pl-0">
            <button id="toggle-btn" style="margin-left: 8px;"
              class="opacity-100 transition-opacity duration-300 focus:outline-none"
              style="background-color: transparent !important;">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-panel-left">
                <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                <path d="M9 3v18"></path>
              </svg>
            </button>
            <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-2 h-4"
              style="background-color: var(--border-color);"></div>
            <nav aria-label="breadcrumb">
              <ol class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
                <!-- Live Chat (active) -->
                <div class="menubar" role="menubar">
                  <div class="menubar-indicator"></div>
                  <a href="/livechat" role="menuitem">Omni Channel</a>
                  <a href="/testlive" role="menuitem" class="active">Test Omni Channel</a>
                  <a href="/aichat" role="menuitem">Guest Genius</a>
                  <a href="/voicebot" role="menuitem">Voice Agents</a>
                </div>
              </ol>
            </nav>
          </div>
          {% include 'topright.html' %}
        </header>
        <main class="flex-1 flex flex-col overflow-hidden">
          <div class="flex divide-x divide-border flex-grow">
            <div class="grid flex-1 grid-cols-custom divide-x divide-border">
              <div class="flex flex-col px-29" uk-filter="target: .js-filter">
                <div class="flex h-14 flex-none items-center border-b border-border px-l-4 py-2 card justify-between">
                  <div class="p-4 pr-0 w-full">
                    <div class="uk-inline w-full">
                      <span class="uk-form-icon text-muted-foreground">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                          class="lucide lucide-search">
                          <circle cx="11" cy="11" r="8"></circle>
                          <path d="m21 21-4.3-4.3"></path>
                        </svg>
                      </span>
                      <input class="uk-input w-full rounded-lg border border-gray-200 p-2" type="text"
                        placeholder="Search">
                    </div>
                  </div>
                  <div class="p-4 pl-1">
                    <ul class="uk-iconnav uk-iconnav-outline uk-iconnav-small">
                      <li>
                        <a href="#" style="background-color: transparent; height: 34px;"
                          class="flex items-center justify-center card" aria-label="Select Language">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M20 7h-9"></path>
                            <path d="M14 17H5"></path>
                            <circle cx="17" cy="17" r="3"></circle>
                            <circle cx="7" cy="7" r="3"></circle>
                          </svg>
                        </a>

                        <!-- Dropdown Menu - Added dropdown-content class -->
                        <div class="card dropdown-content" id="language-dropdown"
                          uk-dropdown="mode: click; pos: bottom-right; offset: 10">
                          <ul class="uk-nav uk-dropdown-nav card">
                            <li><a href="#">Open chats</a></li>
                            <li><a href="#">Closed chats</a></li>
                            <li><a href="#">Checked in</a></li>
                            <li><a href="#">Checked out</a></li>
                          </ul>
                        </div>
                      </li>
                  </div>
                </div>
                <div class="flex flex-1 flex-col">
                  <div class="max-h-[100vh] flex-1 overflow-y-auto">
                    <ul class="js-filter space-y-2 p-4 pt-0" id="user-list">
                      <!-- Add more email list items as needed -->
                    </ul>
                  </div>
                </div>
              </div>
              <div class="flex flex-col h-full card relative-container">
                <div class="flex flex-row h-full">
                  <div id="initial-message" class="flex items-center justify-center h-full w-full">
                    <h2 class="text-2xl font-semibold text-gray-500">Select a user to start the chat :) </h2>
                  </div>
                  <div id="chat-interface" class="flex flex-col h-full" style="display: none;">
                    <div class="flex h-14 flex-none items-center border-b border-border p-2 card">
                      <!-- Container for the title and buttons -->
                      <div class="flex w-full justify-between items-center">
                        <!-- Title taking up full space -->
                        <h3 id="chat-title" class="text-lg font-semibold flex-grow ps-1">Chat Title</h3>
                        <!-- Buttons taking up only required space -->
                        <div class="flex gap-x-2 divide-x divide-border">
                          <ul class="uk-iconnav flex items-center space-x-3">
                            <ul class="uk-iconnav pl-2 b-l border-left">
                              <li>
                                <a href="#demo" aria-haspopup="true"
                                  class="inline-flex items-center justify-center h-8 w-8 rounded chat-dropdown-icon-hover transition-colors duration-150">
                                  <span class="sr-only">Menu</span>
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-ellipsis-vertical">
                                    <circle cx="12" cy="12" r="1"></circle>
                                    <circle cx="12" cy="5" r="1"></circle>
                                    <circle cx="12" cy="19" r="1"></circle>
                                  </svg>
                                </a>
                                <div class="uk-dropdown uk-drop dropdown-content"
                                  uk-dropdown="pos: bottom-right; mode: click">
                                  <ul class="uk-dropdown-nav uk-nav">
                                    <li><a class="uk-drop-close" href="#demo" role="button">
                                        Assign Staff
                                      </a></li>
                                    <li><a class="uk-drop-close" href="#demo" uk-toggle="" role="button">
                                        Close chat
                                      </a></li>
                                    <li><a class="uk-drop-close" href="#demo" uk-toggle="" role="button">
                                        Delete chat
                                      </a></li>
                                  </ul>
                                </div>
                              </li>
                            </ul>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div class="flex flex-col flex-grow overflow-hidden relative">
                      <div class="flex-1 overflow-y-auto p-4 absolute top-0 left-0 right-0 bottom-[160px]"
                        id="chat-container">
                        <!-- Chat messages here -->
                      </div>
                      <!-- Input Container with Divider -->
                      <div
                        class="chat-input-container py-4 border-t border-border px-4 absolute bottom-0 left-0 right-0 card universal-background-color">

                        <!-- Template Messages Container - Positioned above the input area -->
                        <div id="template-messages-container"
                          class="absolute left-0 right-0 bottom-full z-50 px-4 py-3 card universal-background-color border-t border-border hidden">
                          <!-- Template Header with Template Count and Add Button -->
                          <div class="flex justify-between items-center mb-3">
                            <!-- Template Count Button on Left -->
                            <div
                              class="template-count-container uk-button uk-button-default flex items-center justify-center card"
                              style="padding: 0 12px; height: 36px; line-height: 30px; border-radius: 8px; border: 1px solid var(--border-color);">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-list mr-2">
                                <line x1="8" x2="21" y1="6" y2="6"></line>
                                <line x1="8" x2="21" y1="12" y2="12"></line>
                                <line x1="8" x2="21" y1="18" y2="18"></line>
                                <line x1="3" x2="3.01" y1="6" y2="6"></line>
                                <line x1="3" x2="3.01" y1="12" y2="12"></line>
                                <line x1="3" x2="3.01" y1="18" y2="18"></line>
                              </svg>
                              <span id="template-count" style="font-size: 14px; white-space: nowrap;">x Message
                                Templates</span>
                            </div>

                            <!-- Add Template Button on Right -->
                            <button id="add-template-button"
                              class="uk-button uk-button-default flex items-center justify-center card universal-hover"
                              style="padding: 0 12px; height: 36px; line-height: 30px; border-radius: 8px; border: 1px solid var(--border-color);">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-plus mr-2">
                                <path d="M12 5v14M5 12h14"></path>
                              </svg>
                              <span style="font-size: 14px; white-space: nowrap;">Add</span>
                            </button>
                          </div>
                          <div>
                            <!-- Template Messages Grid -->
                            <div class="grid grid-cols-4 gap-1 mb-1">
                              <!-- These template buttons will be replaced by the dynamic ones from JSON -->
                              <!-- We're keeping them in the HTML as a fallback, but they'll be hidden by the renderTemplates function -->
                            </div>

                            <!-- Additional template rows will be added dynamically by the renderTemplates function -->
                          </div>

                          <!-- Divider between Message Templates and Interactive Templates -->
                          <div class="border-t card border-border my-3"></div>

                          <!-- Interactive Templates Section -->
                          <div class="flex justify-between items-center mb-3">
                            <!-- Interactive Template Count Button on Left -->
                            <div
                              class="template-count-container uk-button uk-button-default flex items-center justify-center card"
                              style="padding: 0 12px; height: 36px; line-height: 30px; border-radius: 8px; border: 1px solid var(--border-color);">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-list mr-2">
                                <line x1="8" x2="21" y1="6" y2="6"></line>
                                <line x1="8" x2="21" y1="12" y2="12"></line>
                                <line x1="8" x2="21" y1="18" y2="18"></line>
                                <line x1="3" x2="3.01" y1="6" y2="6"></line>
                                <line x1="3" x2="3.01" y1="12" y2="12"></line>
                                <line x1="3" x2="3.01" y1="18" y2="18"></line>
                              </svg>
                              <span style="font-size: 14px; white-space: nowrap;">Interactive Templates</span>
                            </div>
                          </div>

                          <!-- Interactive Templates Grid -->
                          <div class="grid grid-cols-4 gap-1 mb-2">
                            <!-- Food Template Button -->
                            <button id="food-template-button"
                              class="template-message uk-button uk-button-default flex items-center justify-center card universal-hover"
                              style="background: transparent; border: 1px solid var(--border-color); border-radius: 8px; padding: 0 8px; height: 36px; line-height: 30px; flex: 1;">
                              <span style="font-size: 14px; white-space: nowrap;">Food</span>
                            </button>
                            <!-- Beverage Template Button -->
                            <button id="" class=" template-message uk-button uk-button-default flex
                              items-center justify-center card universal-hover"
                              style="background: transparent; border: 1px solid var(--border-color); border-radius: 8px; padding: 0 8px; height: 36px; line-height: 30px; flex: 1;">
                              <span style="font-size: 14px; white-space: nowrap;">Beverage</span>
                            </button>

                            <!-- Spa Template Button -->
                            <!-- Modified Spa Template Button -->
                            <button id="spa-template-button"
                              class="template-message uk-button uk-button-default flex items-center justify-center card universal-hover"
                              style="background: transparent; border: 1px solid var(--border-color); border-radius: 8px; padding: 0 8px; height: 36px; line-height: 30px; flex: 1;">
                              <span style="font-size: 14px; white-space: nowrap;">Spa</span>
                            </button>

                            <!-- Massage Template Button -->
                            <!-- Massage Template Button -->
                            <button id="massage-template-button"
                              class="template-message uk-button uk-button-default flex items-center justify-center card universal-hover"
                              style="background: transparent; border: 1px solid var(--border-color); border-radius: 8px; padding: 0 8px; height: 36px; line-height: 30px; flex: 1;">
                              <span style="font-size: 14px; white-space: nowrap;">Massage</span>
                            </button>
                          </div>

                          <!-- Second row of Interactive Templates -->
                          <div class="grid grid-cols-4 gap-1 mb-1">
                            <!-- Room Bookings Template Button -->
                            <button id="room-template-button"
                              class="template-message uk-button uk-button-default flex items-center justify-center card universal-hover"
                              style="background: transparent; border: 1px solid var(--border-color); border-radius: 8px; padding: 0 8px; height: 36px; line-height: 30px; flex: 1;">
                              <span style="font-size: 14px; white-space: nowrap;">Room Bookings</span>
                            </button>

                            <!-- Review Template Button -->
                            <button
                              class="template-message uk-button uk-button-default flex items-center justify-center card universal-hover"
                              style="background: transparent; border: 1px solid var(--border-color); border-radius: 8px; padding: 0 8px; height: 36px; line-height: 30px; flex: 1;">
                              <span style="font-size: 14px; white-space: nowrap;">Review</span>
                            </button>

                            <!-- Spa Template Button (duplicate as requested) -->
                            <button
                              class="template-message uk-button uk-button-default flex items-center justify-center card universal-hover"
                              style="background: transparent; border: 1px solid var(--border-color); border-radius: 8px; padding: 0 8px; height: 36px; line-height: 30px; flex: 1;">
                              <span style="font-size: 14px; white-space: nowrap;">Tip</span>
                            </button>
                          </div>
                        </div>

                        <!-- Input and Send Button -->
                        <div class="flex relative items-start">
                          <!-- Message Input Container with padding for both buttons -->
                          <div class="relative flex flex-col flex-grow">
                            <!-- Scrollable Textarea for Message Input with padding on both sides -->
                            <div class="pl-[40px] pr-[80px]">
                              <!-- Increased left padding to accommodate moved toggle button -->
                              <textarea id="message-input"
                                class="uk-input card flex-1 rounded-md focus:outline-none focus:border-none message-input"
                                style="width: 100%; min-height: 38px; max-height: 120px; resize: none; overflow-y: auto; padding-top: 10px; padding-bottom: 10px; padding-right: 25px; margin-left: 0px;"
                                placeholder="Type a message..." rows="1"></textarea>
                            </div>

                            <!-- Toggle Button - Positioned absolutely to stay at the bottom left -->
                            <button id="toggle-action-buttons"
                              class="flex items-center justify-center card toggle-button"
                              aria-label="Toggle Action Buttons">
                              <div style="position: relative; width: 16px; height: 16px;">
                                <svg id="toggle-icon-up" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                  viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                  stroke-linecap="round" stroke-linejoin="round">
                                  <path d="m5 12 7-7 7 7" />
                                  <path d="M12 19V5" />
                                </svg>
                                <svg id="toggle-icon-down" class="hidden" xmlns="http://www.w3.org/2000/svg" width="16"
                                  height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                  stroke-linecap="round" stroke-linejoin="round">
                                  <path d="M12 5v14" />
                                  <path d="m19 12-7 7-7-7" />
                                </svg>
                              </div>
                            </button>

                            <!-- Send Button - Positioned absolutely to stay at the bottom right -->
                            <button id="send-button"
                              class="uk-button ml-2 bg-blue-500 text-white rounded-md hover:scale-105 active:scale-95 transition-transform duration-150 absolute right-0 bottom-0"
                              style="padding: 10px 20px; margin-bottom: 2px;">
                              Send
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Overlay (Optional: You can move this outside if needed) -->
                  <div class="panel-overlay"></div>
                </div>
              </div>
            </div>
        </main>
      </div>
    </div>
  </div>
  <style>
    .user-icon {
      position: relative;
      display: inline-block;
      border: 1px solid #e5e7eb;
      /* Dark gray color for a modern look */
      border-radius: 50%;
      padding: 2px;
      /* Minimal padding */
      margin: 0 5px;
      /* Add horizontal space */
    }

    .active-language {
      background-color: #f0f0f0;
      /* Light gray background */
      font-weight: bold;
    }

    /* Optional: Style translation loading indicator */
    .translation-loading {
      font-size: 0.8em;
      color: #888;
      margin-left: 5px;
    }

    /* Template messages container transitions */
    #template-messages-container {
      transition: opacity 0.2s ease, transform 0.2s ease;
      opacity: 0;
      transform: translateY(10px);
      pointer-events: none;
      /* Position it above the input area */
      bottom: 100%;
      margin-bottom: 1px;
      border-bottom: 1px solid var(--border-color);
      box-shadow: 0 -2px 4px -1px rgba(0, 0, 0, 0.1);
      /* Add max-height and scrolling for many templates */
      max-height: 400px;
      overflow-y: auto;
    }

    #template-messages-container.visible {
      opacity: 1;
      transform: translateY(0);
      pointer-events: auto;
    }

    .toggle-button {
      transition: transform 0.1s ease;
    }

    .toggle-button:active {
      transform: scale(1.1);
    }

    /* When visible class is added, show the container */
    #template-messages-container.visible {
      opacity: 1;
      transform: translateY(0);
      pointer-events: auto;
      display: block !important;
      /* Force display when visible */
    }

    /* Ensure it's hidden by default */
    #template-messages-container:not(.visible) {
      visibility: hidden !important;
      opacity: 0;
    }

    /* Template header buttons styling */
    .template-count-container {
      border: 1px solid var(--border-color);
      /* Add border for template buttons */
      transition: all 0.3s ease;
      position: relative;
      overflow: visible;
      background: transparent;
      z-index: 1;
      /* Ensure button content stays above ripple */
    }

    /* Add template button styling */
    #add-template-button {
      transition: all 0.3s ease;
      position: relative;
      overflow: visible;
      z-index: 1;
      /* Ensure button content stays above ripple */
    }

    /* Template buttons styling */
    .template-message {
      transition: all 0.3s ease;
      position: relative;
      overflow: visible;
      background: transparent !important;
      border: 1px solid var(--border-color) !important;
    }





    /* Hover effect for buttons */
    .template-count-container:hover {
      background-color: rgba(0, 0, 0, 0.05);
      z-index: 1;
    }

    /* Hover effect for add template button */
    #add-template-button:hover {
      background-color: rgba(0, 0, 0, 0.05);
      z-index: 1;
    }

    /* Hover effect for template buttons */
    .template-message:hover {
      background-color: rgba(0, 0, 0, 0.05) !important;
      z-index: 1;
    }



    /* Animation for clicked state */
    #add-template-button.clicked {
      position: relative;
      overflow: hidden;
    }

    /* Ripple effect for clicked state */
    @keyframes ripple {
      0% {
        transform: scale(0);
        opacity: 0.8;
      }

      100% {
        transform: scale(4);
        opacity: 0;
      }
    }



    #add-template-button.clicked::after {
      content: "";
      position: absolute;
      width: 30px;
      height: 30px;
      background: rgba(59, 130, 246, 0.4);
      border-radius: 50%;
      left: calc(50% - 15px);
      top: calc(50% - 15px);
      animation: ripple 1s ease-out forwards;
      z-index: -1;
    }

    /* Icons inside the buttons */
    .template-count-container svg {
      transition: all 0.3s ease;
    }

    /* Add template button icon */
    #add-template-button svg {
      transition: all 0.3s ease;
    }

    .template-count-container:hover svg {
      transform: scale(1.2);
    }

    #add-template-button:hover svg {
      transform: scale(1.2);
    }

    /* Text inside the buttons */
    #template-count {
      transition: all 0.3s ease;
      font-weight: 500;
      color: var(--text-color);
    }

    /* Add template button text */
    #add-template-button span {
      transition: all 0.3s ease;
      font-weight: 500;
      color: var(--text-color);
    }

    .template-count-container:hover #template-count {
      letter-spacing: 0.03em;
      font-weight: 600;
    }

    #add-template-button:hover span {
      letter-spacing: 0.03em;
      font-weight: 600;
    }







    /* Simple icon transitions */
    #toggle-icon-up,
    #toggle-icon-down {
      position: absolute;
      top: 0;
      left: 0;
      transition: opacity 0.2s ease;
    }

    #toggle-icon-up.hidden,
    #toggle-icon-down.hidden {
      opacity: 0;
    }

    /* Modal animations and styling */
    #add-template-modal {
      transition: opacity 0.3s ease;
    }

    #add-template-modal.hidden {
      opacity: 0;
      pointer-events: none;
    }

    #add-template-modal:not(.hidden) {
      opacity: 1;
      pointer-events: auto;
    }

    #add-template-modal .card {
      transform: translateY(20px);
      opacity: 0;
      transition: transform 0.3s ease, opacity 0.3s ease;
    }

    #add-template-modal:not(.hidden) .card {
      transform: translateY(0);
      opacity: 1;
    }

    /* Staff Assignment Modal animations and styling */
    #staff-assignment-modal {
      transition: opacity 0.3s ease;
    }

    #staff-assignment-modal.hidden {
      opacity: 0;
      pointer-events: none;
    }

    #staff-assignment-modal:not(.hidden):not(.modal-closing) {
      opacity: 1;
      /* Backdrop visible */
      pointer-events: auto;
    }

    /* The actual modal content card */
    #staff-assignment-modal .modal-card-animated {
      transition: transform 0.3s ease, opacity 0.3s ease;
    }

    /* Card initial state for opening: when modal is hidden, card is down and transparent */
    #staff-assignment-modal.hidden .modal-card-animated {
      transform: translateY(20px);
      opacity: 0;
    }

    /* Card state when modal is opening/open */
    #staff-assignment-modal:not(.hidden):not(.modal-closing) .modal-card-animated {
      transform: translateY(0);
      opacity: 1;
    }

    /* State for closing animation */
    #staff-assignment-modal.modal-closing {
      opacity: 0;
      /* Fade out the backdrop */
    }

    #staff-assignment-modal.modal-closing .modal-card-animated {
      transform: translateY(20px);
      /* Slide down the card */
      opacity: 0;
      /* Fade out the card */
    }


    /* Style the form elements */
    #template-name,
    #template-content,
    #template-category {
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    #template-name:focus,
    #template-content:focus,
    #template-category:focus {
      border-color: rgba(59, 130, 246, 0.5) !important;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
    }

    /* Style the save button */
    #save-template {
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    #save-template:hover {
      transform: translateY(-2px);
    }

    #save-template:active {
      transform: translateY(0);
    }

    /* Message input scrollbar styling */
    #message-input {
      scrollbar-width: thin;
      scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
    }

    #message-input::-webkit-scrollbar {
      width: 6px;
    }

    #message-input::-webkit-scrollbar-track {
      background: transparent;
    }

    #message-input::-webkit-scrollbar-thumb {
      background-color: rgba(156, 163, 175, 0.5);
      border-radius: 20px;
    }

    /* Toggle button styling */
    #toggle-action-buttons.card {
      height: 40px;
      width: 40px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background: transparent;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      margin: 0 2px 0;
      position: absolute;
      left: -8px;
      /* Moved 10px to the left */
      bottom: 2px;
      /* Adjusted position */
    }

    #toggle-action-buttons.card:hover {
      background: rgba(0, 0, 0, 0.05);
      border-color: var(--border-color);
    }

    /* Dark mode adaptation */
    .dark #toggle-action-buttons.card {
      border-color: var(--border-color);
      background: transparent;
    }
  </style>
  <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script>
  <script>
    let selectedUser = null;
    let currentLanguage = 'original';
    let originalMessages = {};
    let chatData = { users: [], chats: {} };
    let translationCache = JSON.parse(localStorage.getItem('translationCache')) || {}; // Cache translations
    const MAX_CACHE_ENTRIES = 1000;
    let previousData = null; // Store previous data to compare changes
    let isInitialLoad = true; // Flag for initial page load
    let fetchInProgress = false; // Prevent overlapping fetch operations

    function showChatInterface(show) {
      const initialMessage = document.getElementById('initial-message');
      const chatInterface = document.getElementById('chat-interface');

      initialMessage.style.display = show ? 'none' : 'flex';
      chatInterface.style.display = show ? 'flex' : 'none';
    }

    document.addEventListener('DOMContentLoaded', function () {
      const userList = document.getElementById('user-list');
      const chatContainer = document.getElementById('chat-container');
      const messageInput = document.getElementById('message-input');
      const sendButton = document.getElementById('send-button');
      const chatTitle = document.getElementById('chat-title');
      const languageDropdown = document.getElementById('language-dropdown');
      const searchInput = document.querySelector('.uk-input[placeholder="Search"]'); // Get the search input element
      const realtimeStatus = document.createElement('div');
      realtimeStatus.id = 'realtime-status';
      realtimeStatus.className = 'fixed bottom-2 right-2 px-2 py-1 text-xs rounded bg-gray-200 text-gray-700';
      realtimeStatus.textContent = 'Realtime: Initializing...';
      document.body.appendChild(realtimeStatus);

      // Initialize UI elements
      showChatInterface(false);

      // Load templates from JSON file
      loadTemplates();

      // Initialize interactive template buttons
      initializeInteractiveTemplates();

      // Initialize template messages container
      const templateMessagesContainer = document.getElementById('template-messages-container');
      const toggleUpIcon = document.getElementById('toggle-icon-up');
      const toggleDownIcon = document.getElementById('toggle-icon-down');
      const templateCountElement = document.getElementById('template-count');
      const addTemplateButton = document.getElementById('add-template-button');
      const templateCountContainer = document.querySelector('.template-count-container');

      // Add event listener to prevent clicks on the template count container from closing the popup
      templateCountContainer.addEventListener('click', function (e) {
        e.stopPropagation(); // Prevent event from bubbling up to document
      });

      // Count templates and update the display
      function updateTemplateCount() {
        // Use the templatesData array length for accurate count
        const count = templatesData.length;
        templateCountElement.textContent = count + (count === 1 ? ' Message Template' : ' Message Templates');
      }

      // Call initially to set the count (will be updated when templates are loaded)
      templateCountElement.textContent = 'x Message Templates';

      // Add event listener for the Add Template button
      addTemplateButton.addEventListener('click', function (e) {
        e.stopPropagation(); // Prevent event from bubbling up to document

        // Add the clicked class to trigger the float animation
        this.classList.add('clicked');

        // Remove the class after the animation completes
        setTimeout(() => {
          this.classList.remove('clicked');
        }, 1000);

        // Show the template modal
        document.getElementById('add-template-modal').classList.remove('hidden');

        // Focus on the template name input
        setTimeout(() => {
          document.getElementById('template-name').focus();
        }, 300);
      });

      // Close modal when clicking the close button
      document.getElementById('close-template-modal').addEventListener('click', function () {
        document.getElementById('add-template-modal').classList.add('hidden');
        clearTemplateForm();
      });

      // Close modal when clicking the cancel button
      document.getElementById('cancel-template').addEventListener('click', function () {
        document.getElementById('add-template-modal').classList.add('hidden');
        clearTemplateForm();
      });

      // Close modal when clicking outside the modal content
      document.getElementById('modal-backdrop').addEventListener('click', function () {
        document.getElementById('add-template-modal').classList.add('hidden');
        clearTemplateForm();
      });

      // Save template when clicking the save button
      // Add delete template handler
      document.getElementById('delete-template').addEventListener('click', async function () {
        const templateId = document.getElementById('template-id').value;
        if (!templateId) return;

        if (!confirm('Are you sure you want to permanently delete this template?')) return;

        try {
          templatesData = templatesData.filter(t => t.id !== templateId);
          await saveTemplatesData();
          renderTemplates();
          clearTemplateForm();
          document.getElementById('add-template-modal').classList.add('hidden');
        } catch (error) {
          console.error('Delete failed:', error);
          alert('Failed to delete template. Please try again.');
        }
      });

      document.getElementById('save-template').addEventListener('click', saveTemplate);

      // Function to clear the template form
      function clearTemplateForm() {
        document.getElementById('template-name').value = '';
        document.getElementById('template-content').value = '';
        document.getElementById('template-id').value = '';
        document.getElementById('edit-mode').value = 'false';

        // Reset modal title and button text
        document.getElementById('modal-title').textContent = 'Add New Template';
        document.getElementById('save-button-text').textContent = 'Save Template';
        // Hide delete button when creating new template
        document.getElementById('delete-template').style.display = 'none';
      }

      // Templates data
      let templatesData = [];

      // Load templates from JSON file
      async function loadTemplates() {
        try {
          const response = await fetch('/data/templates.json');
          if (!response.ok) {
            throw new Error('Failed to load templates');
          }
          const data = await response.json();
          templatesData = data.templates || [];

          // Update the template count immediately after loading
          updateTemplateCount();

          // Then render the templates
          renderTemplates();

          // Keep templates hidden by default, but ensure they're ready to be shown when toggled
          // No need to show them automatically
        } catch (error) {
          console.error('Error loading templates:', error);
          // Set templates to empty array in case of error
          templatesData = [];
          updateTemplateCount();
        }
      }

      // Save templates to JSON file
      async function saveTemplatesData() {
        try {
          const response = await fetch('/save_templates', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ templates: templatesData })
          });

          if (!response.ok) {
            throw new Error('Failed to save templates');
          }

          return await response.json();
        } catch (error) {
          console.error('Error saving templates:', error);
          alert('Failed to save template. Please try again.');
        }
      }

      // Render all templates
      function renderTemplates() {
        // Clear existing templates
        const templateContainer = document.querySelector('#template-messages-container > div:nth-child(2)');

        // Clear existing content
        templateContainer.innerHTML = '';

        // If no templates, show a message
        if (templatesData.length === 0) {
          const noTemplatesMessage = document.createElement('div');
          noTemplatesMessage.className = 'text-center p-4';
          noTemplatesMessage.textContent = 'No templates available. Click "Add" to create one.';
          templateContainer.appendChild(noTemplatesMessage);
          return;
        }

        // Process templates in batches of 4 for rows
        for (let i = 0; i < templatesData.length; i += 4) {
          // Create a new row for every 4 templates
          const gridContainer = document.createElement('div');
          gridContainer.className = 'grid grid-cols-4 gap-1 mb-2';

          // Add up to 4 templates to this row
          const endIndex = Math.min(i + 4, templatesData.length);
          for (let j = i; j < endIndex; j++) {
            const template = templatesData[j];
            const templateButton = createTemplateButton(template);
            gridContainer.appendChild(templateButton);
          }

          // Add this row to the container
          templateContainer.appendChild(gridContainer);
        }

        // Update template count
        updateTemplateCount();
      }

      // Create a template button
      function createTemplateButton(template) {
        const templateButton = document.createElement('button');
        templateButton.className = 'template-message uk-button uk-button-default flex items-center justify-center card universal-hover';
        templateButton.setAttribute('data-id', template.id);
        templateButton.setAttribute('data-message', template.content);
        templateButton.setAttribute('data-name', template.name);

        // Add styling to the template button
        templateButton.style.background = 'transparent';
        templateButton.style.border = '1px solid var(--border-color)';
        templateButton.style.borderRadius = '8px';
        templateButton.style.padding = '0 8px';
        templateButton.style.height = '36px';
        templateButton.style.lineHeight = '30px';
        templateButton.style.flex = '1';

        // Add the template name
        templateButton.innerHTML = `
          <span style="font-size: 14px; white-space: nowrap;">${template.name}</span>
        `;

        // Add click event listener
        templateButton.addEventListener('click', function (e) {
          e.stopPropagation(); // Prevent event from bubbling up to document
          const templateMessage = this.getAttribute('data-message');
          if (templateMessage && messageInput) {
            // Set the template message in the input field
            messageInput.value = templateMessage;
            // Trigger auto-resize to adjust height based on the template content
            autoResizeTextarea();
            // Focus on the input field
            messageInput.focus();
            // Hide the template container after selecting a template
            hideTemplateMessages();
          }
        });

        // Add right-click (context menu) event listener
        templateButton.addEventListener('contextmenu', function (e) {
          e.preventDefault();
          e.stopPropagation(); // Prevent event from bubbling up to document
          const templateId = this.getAttribute('data-id');
          const templateName = this.getAttribute('data-name');
          const templateContent = this.getAttribute('data-message');

          // Set edit mode
          document.getElementById('edit-mode').value = 'true';
          document.getElementById('template-id').value = templateId;
          document.getElementById('template-name').value = templateName;
          document.getElementById('template-content').value = templateContent;
          // Show delete button in edit mode
          document.getElementById('delete-template').style.display = 'block';

          // Update modal title and button text
          document.getElementById('modal-title').textContent = 'Edit Template';
          document.getElementById('save-button-text').textContent = 'Update Template';

          // Show the modal
          document.getElementById('add-template-modal').classList.remove('hidden');
          // Show delete button in edit mode
          document.getElementById('delete-template').style.display = 'block';

          // Focus on the template name input
          setTimeout(() => {
            document.getElementById('template-name').focus();
          }, 300);
        });

        return templateButton;
      }

      // Function to save the template
      async function saveTemplate() {
        const templateName = document.getElementById('template-name').value.trim();
        const templateContent = document.getElementById('template-content').value.trim();
        const isEditMode = document.getElementById('edit-mode').value === 'true';
        const templateId = document.getElementById('template-id').value;

        if (!templateName || !templateContent) {
          alert('Please fill in all required fields');
          return;
        }

        if (isEditMode) {
          // Update existing template
          const templateIndex = templatesData.findIndex(t => t.id === templateId);
          if (templateIndex !== -1) {
            templatesData[templateIndex].name = templateName;
            templatesData[templateIndex].content = templateContent;
          }
        } else {
          // Add new template
          const newTemplate = {
            id: 'template-' + (Date.now()),
            name: templateName,
            content: templateContent
          };

          templatesData.push(newTemplate);
        }

        // Save templates to server
        await saveTemplatesData();

        // Update the template count immediately
        updateTemplateCount();

        // Render updated templates
        renderTemplates();

        // Close the modal
        document.getElementById('add-template-modal').classList.add('hidden');

        // Clear the form
        clearTemplateForm();

        console.log(`Template saved. Total templates: ${templatesData.length}`);
      }

      // Set initial state - up arrow visible (templates hidden)
      toggleUpIcon.classList.remove('hidden');
      toggleDownIcon.classList.add('hidden');

      // Auto-resize textarea based on content
      function autoResizeTextarea() {
        // Save scroll position
        const scrollTop = messageInput.scrollTop;

        // Reset height to auto to get the correct scrollHeight
        messageInput.style.height = 'auto';

        // Set the height to match the content (scrollHeight)
        // Clamp between min-height and max-height (defined in the style attribute)
        const newHeight = Math.min(Math.max(messageInput.scrollHeight, 40), 120);
        messageInput.style.height = newHeight + 'px';

        // Enable scrolling when content exceeds max height
        if (messageInput.scrollHeight > 120) {
          messageInput.style.overflowY = 'auto';
        } else {
          messageInput.style.overflowY = 'hidden';
        }

        // Restore scroll position
        messageInput.scrollTop = scrollTop;

        // Ensure both buttons stay at the bottom of the expanded textarea
        const sendButton = document.getElementById('send-button');
        const toggleButton = document.getElementById('toggle-action-buttons');

        if (sendButton) {
          // Position the send button at the bottom of the textarea
          sendButton.style.bottom = '0';
        }

        if (toggleButton) {
          // Position the toggle button at the bottom of the textarea
          toggleButton.style.bottom = '0';
        }
      }

      // Initialize textarea height
      autoResizeTextarea();

      // Add input event listener to auto-resize as user types
      messageInput.addEventListener('input', autoResizeTextarea);

      // Also resize when content is pasted or when template messages are inserted
      messageInput.addEventListener('paste', () => {
        // Use setTimeout to ensure content is pasted before measuring
        setTimeout(autoResizeTextarea, 0);
      });

      // Initialize interactive template buttons
      function initializeInteractiveTemplates() {
        // Get all interactive template buttons
        const interactiveButtons = document.querySelectorAll('.grid-cols-4 .template-message');

        // Add click event listeners to each button
        interactiveButtons.forEach(button => {
          button.addEventListener('click', function (e) {
            e.stopPropagation(); // Prevent event from bubbling up

            // Get the template text from the button
            const templateText = this.querySelector('span').textContent;

            // Set the template text in the input field
            if (messageInput) {
              messageInput.value = `I'd like to inquire about ${templateText.toLowerCase()} options.`;

              // Trigger auto-resize to adjust height based on the template content
              autoResizeTextarea();

              // Focus on the input field
              messageInput.focus();

              // Hide the template container after selecting a template
              hideTemplateMessages();
            }
          });
        });
      }

      // Helper function to find the most recent chat
      function findMostRecentChat(chatData) {
        if (!chatData.users || chatData.users.length === 0) {
          return null;
        }

        let mostRecentUserId = null;
        let mostRecentTimestamp = 0;

        // Loop through all users and their chats
        for (const user of chatData.users) {
          const userId = user.id.toString();
          const userChats = chatData.chats[userId] || [];

          if (userChats.length > 0) {
            // Get the last message for this user
            const lastMessage = userChats[userChats.length - 1];
            // Convert timestamp string to Date object for comparison
            // Extract the original timestamp from the chat data
            const originalTimestamp = lastMessage.originalTimestamp || new Date().getTime();

            // If this is the most recent message we've seen so far
            if (originalTimestamp > mostRecentTimestamp) {
              mostRecentTimestamp = originalTimestamp;
              mostRecentUserId = userId;
            }
          }
        }

        return mostRecentUserId;
      }

      // Function to process chats data from the server
      function processChatsData(chats) {
        if (!chats || !Array.isArray(chats)) {
          console.error("Invalid chats data received:", chats);
          return;
        }

        console.log(`Processing ${chats.length} chats from server`);

        // Group chats by user_id and extract user info from messages
        const users = [];
        const userMap = new Map();
        const chatsByUser = {};

        // Find unique senders from customer messages and create user profiles
        chats.forEach(chat => {
          // Use both user_id and sender as unique identifier to handle cases
          // where same user_id has different sender names
          const userId = chat.user_id.toString();

          // Add chat to user's messages
          if (!chatsByUser[userId]) {
            chatsByUser[userId] = [];
          }

          // Store the original timestamp for sorting
          const originalTimestamp = new Date(chat.timestamp).getTime();

          chatsByUser[userId].push({
            id: chat.id, // Add this line to store the Supabase message ID
            sender: chat.sender,
            message: chat.message,
            timestamp: new Date(chat.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            originalTimestamp: originalTimestamp, // Store original timestamp for sorting
            customer: chat.customer
          });

          // Extract user info from customer messages
          if (chat.customer && !userMap.has(userId)) {
            const roomNumber = 101 + parseInt(userId);

            const userObject = {
              id: userId,
              name: chat.sender,
              status: "Online",
              platform: "WhatsApp",
              phone_number: chat.phone_number || "", // Use phone_number from data without fallback
              room_number: roomNumber
            };

            userMap.set(userId, userObject);
            users.push(userObject);
            console.log("Added user:", userObject);
          }
        });

        console.log("Found", users.length, "unique users");

        // Update our chat data
        chatData.users = users;
        chatData.chats = chatsByUser;

        // If a user is selected, reload their chat history
        if (selectedUser) {
          loadChatHistory(selectedUser, true);
        } else if (users.length > 0) {
          // Auto-select the most recent chat if no user is currently selected
          const mostRecentUserId = findMostRecentChat(chatData);

          if (mostRecentUserId) {
            console.log("Auto-selecting most recent chat with user ID:", mostRecentUserId);

            // Set the selected user
            selectedUser = mostRecentUserId;

            // Find the user object
            const user = chatData.users.find(u => u.id.toString() === selectedUser);

            if (user) {
              // Update the chat title
              updateChatTitle(user);

              // Load the chat history
              loadChatHistory(selectedUser);

              // Show the chat interface
              showChatInterface(true);

              // Update the active state in the UI
              setTimeout(() => {
                const userItem = document.querySelector(`li[data-user="${selectedUser}"]`);
                if (userItem) {
                  userList.querySelectorAll('[data-user]').forEach(item => {
                    item.classList.remove('active');
                  });
                  userItem.classList.add('active');
                }
              }, 100);
            }
          }
        }

        // Update the user list to show all available users
        updateUserList();
      }

      async function fetchChatsFromSupabase() {
        try {
          console.log("Fetching chats from /livechat_chats endpoint...");
          const res = await fetch('/livechat_chats');
          if (!res.ok) throw new Error(res.statusText);
          const chats = await res.json();

          console.log("Received chats data:", chats.length, "messages");

          // Process the chats data
          processChatsData(chats);

        } catch (err) {
          console.error('Error fetching supabase chats:', err);
          document.getElementById('initial-message').innerHTML = `
      <div class="text-center">
        <h2 class="text-xl font-semibold text-red-500">Error Loading Chats</h2>
        <p class="text-gray-500 mt-2">${err.message}</p>
        <button onclick="fetchChatsFromSupabase()" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded">
          Try Again
        </button>
      </div>
    `;
        }
      }

      // Update the sendMessage function to send to Supabase
      function sendMessage() {
        if (!selectedUser) {
          alert("Please select a user to chat with first.");
          return;
        }

        const message = messageInput.value.trim();
        if (message) {
          const timestamp = new Date().toISOString();
          const messageId = `msg-${Date.now()}`;

          // Get the current user's information
          const user = chatData.users.find(u => u.id.toString() === selectedUser);

          // Optimistic UI update
          displayMessage('Agent', message, false, `agent-${messageId}`);
          messageInput.value = '';
          // Reset textarea height after sending
          messageInput.style.height = '40px';
          messageInput.style.overflowY = 'hidden';
          // Make sure both buttons stay at the bottom
          const sendButton = document.getElementById('send-button');
          const toggleButton = document.getElementById('toggle-action-buttons');

          if (sendButton) {
            // Position the send button at the bottom of the textarea
            sendButton.style.bottom = '0';
          }

          if (toggleButton) {
            // Position the toggle button at the bottom of the textarea
            toggleButton.style.bottom = '0';
          }

          // Save to Supabase
          fetch('/supabase_chats', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              user_id: parseInt(selectedUser),
              message: message,
              timestamp: timestamp,
              sender: 'Agent',
              customer: false
            }),
          })
            .then(response => response.json())
            .then(data => {
              console.log('Message saved to Supabase:', data);
            })
            .catch(error => {
              console.error('Error saving to Supabase:', error);
            });
        }
      }


      // Add event listener for search input
      searchInput.addEventListener('input', function (e) {
        const searchQuery = e.target.value.trim().toLowerCase();
        filterUserList(searchQuery);
      });

      // Add event listeners for template message buttons
      document.querySelectorAll('.template-message').forEach(button => {
        button.addEventListener('click', function () {
          const templateMessage = this.getAttribute('data-message');
          if (templateMessage && messageInput) {
            // Set the template message in the input field
            messageInput.value = templateMessage;
            // Trigger auto-resize to adjust height based on the template content
            autoResizeTextarea();
            // Focus on the input field
            messageInput.focus();

            // Optional: Auto-send the message
            // sendMessage();
          }
        });
      });



      // Function to hide template messages
      function hideTemplateMessages() {
        const templateContainer = document.getElementById('template-messages-container');
        const upIcon = document.getElementById('toggle-icon-up');
        const downIcon = document.getElementById('toggle-icon-down');

        if (templateContainer.classList.contains('visible')) {
          // Hide template messages with smooth transition
          templateContainer.classList.remove('visible');

          // Switch icons
          downIcon.classList.add('hidden');
          upIcon.classList.remove('hidden');

          // After transition completes, add hidden class for accessibility
          setTimeout(() => {
            templateContainer.classList.add('hidden');
          }, 200);

          return true; // Indicates the popup was visible and is now hidden
        }

        return false; // Indicates the popup was already hidden
      }

      // Function to show template messages
      function showTemplateMessages() {
        const templateContainer = document.getElementById('template-messages-container');
        const upIcon = document.getElementById('toggle-icon-up');
        const downIcon = document.getElementById('toggle-icon-down');

        if (!templateContainer.classList.contains('visible')) {
          // Show template messages with smooth transition
          templateContainer.classList.remove('hidden');

          // Force reflow to ensure transition plays
          void templateContainer.offsetWidth;

          // Add visible class to trigger transition
          templateContainer.classList.add('visible');

          // Switch icons
          upIcon.classList.add('hidden');
          downIcon.classList.remove('hidden');

          return true; // Indicates the popup was hidden and is now visible
        }

        return false; // Indicates the popup was already visible
      }

      // Add event listener for the toggle template messages button
      document.getElementById('toggle-action-buttons').addEventListener('click', function (e) {
        e.stopPropagation(); // Prevent event from bubbling up to document
        const templateContainer = document.getElementById('template-messages-container');

        // Check if template messages are currently visible
        const isVisible = templateContainer.classList.contains('visible');

        if (isVisible) {
          hideTemplateMessages();
        } else {
          showTemplateMessages();
        }
      });

      // Add event listener to prevent clicks on the template container from closing the popup
      templateMessagesContainer.addEventListener('click', function (e) {
        e.stopPropagation(); // Prevent event from bubbling up to document
      });

      // Close template popup when clicking outside
      document.addEventListener('click', function (e) {
        const templateContainer = document.getElementById('template-messages-container');

        // If the template container is visible and the click is outside the container
        // and not on the toggle button
        if (templateContainer.classList.contains('visible') &&
          !templateContainer.contains(e.target) &&
          !e.target.closest('#toggle-action-buttons')) {
          hideTemplateMessages();
        }
      });

      // Close template popup when pressing Escape key
      document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
          hideTemplateMessages();
        }
      });

      // Filter user list based on search query
      function filterUserList(query) {
        const userItems = userList.querySelectorAll('li[data-user]');

        userItems.forEach(item => {
          // Get the user name from the item (the first div with font-semibold class)
          const nameElement = item.querySelector('.font-semibold');
          const phoneElement = item.querySelector('.text-xs.font-medium');

          if (nameElement) {
            const userName = nameElement.textContent.toLowerCase();
            const userPhone = phoneElement ? phoneElement.textContent.toLowerCase() : '';

            // Check if the name contains the search query
            if (userName.includes(query) || userPhone.includes(query)) {
              item.style.display = ''; // Show the item
            } else {
              item.style.display = 'none'; // Hide the item
            }
          }
        });
      }


      // Add this to your existing script section
      const updateWhatsAppIcons = () => {
        document.querySelectorAll('.whatsapp-icon').forEach(icon => {
          icon.src = `../static/icons/${document.body.classList.contains('dark') ? 'white' : 'black'}-whatsapp-icon.png`;
        });
      };

      // Add observer for theme changes
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.attributeName === 'class') {
            updateWhatsAppIcons();
          }
        });
      });

      const genderCache = {};

      function getGender(fullName) {
        const firstName = fullName.split(' ')[0];
        // If exists in cache, return a resolved promise
        if (genderCache[firstName]) {
          return Promise.resolve(genderCache[firstName]);
        }
        return fetch(`/guess_gender?name=${encodeURIComponent(fullName)}`)
          .then(response => response.json())
          .then(data => {
            const gender = data.gender || 'unknown';
            // Cache the result
            genderCache[firstName] = gender;
            return gender;
          })
          .catch(err => {
            console.error("Gender API error", err);
            return 'unknown';
          });
      }

      // Improved updateUserList function to avoid unnecessary DOM updates
      function updateUserList() {
        // Create a map of existing users in the DOM for quick lookup
        const existingUsers = new Map();
        userList.querySelectorAll('li[data-user]').forEach(item => {
          existingUsers.set(item.getAttribute('data-user'), item);
        });

        // Keep track of elements to add/update in order
        const updatedElements = [];

        // Process each user in the data
        chatData.users.forEach(user => {
          const userId = user.id.toString();
          const userChats = chatData.chats[user.id] || [];
          const latestMessage = userChats[userChats.length - 1];
          const latestMessageTime = latestMessage ? latestMessage.timestamp : 'No messages';
          const isSelected = selectedUser === userId;

          // Check if this user element already exists in the DOM
          const existingElement = existingUsers.get(userId);

          if (existingElement) {
            // Update only necessary parts of existing element to avoid flickering
            const timeEl = existingElement.querySelector('.ml-auto.text-xs');
            if (timeEl && timeEl.textContent !== latestMessageTime) {
              timeEl.textContent = latestMessageTime;
            }

            // Update selected state if needed
            if (isSelected && !existingElement.classList.contains('active')) {
              existingElement.classList.add('active');
            } else if (!isSelected && existingElement.classList.contains('active')) {
              existingElement.classList.remove('active');
            }

            // Keep track of this element for proper order
            updatedElements.push(existingElement);
            existingUsers.delete(userId);
          } else {
            // Create new user element if it doesn't exist
            const li = document.createElement('li');
            li.className = `relative rounded-lg border border-border p-3 text-sm custom-hover-effect card${isSelected ? ' active' : ''}`;
            li.setAttribute('data-user', userId);
            li.innerHTML = `
              <div class="flex w-full flex-col gap-1">
                <div class="flex items-center">
                  <div class="flex items-center gap-2">
                    <div class="font-semibold">${user.name}</div>
                  </div>
                  <div class="ml-auto text-xs ">${latestMessageTime}</div>
                </div>
                <div class="text-xs font-medium">
                  ${user.phone_number}
                </div>
                <div class="flex items-center gap-2 mt-2 ">
                  <span class="uk-label-platform border card">Room : ${user.room_number}</span>
                  ${user.platform.toLowerCase() === 'whatsapp'
                ? `<img src="https://flagpedia.net/data/flags/emoji/twitter/256x256/gb.png" alt="UK Flag" class="w-5 h-5">
                       <div class="flex-grow"></div>
                       <img src="../static/images/whatsapp.png" alt="WhatsApp" class="whatsapp-theme-icon" style="margin-right: 4px; width: 20px; height: 20px;" class="w-5 h-5 ml-auto">`
                : `<span class="uk-label-platform">${user.platform}</span>`}
                </div>
              </div>
            `;

            // Add hover listeners
            li.addEventListener('mouseenter', () => {
              li.classList.add('hover-persistent');
            });

            li.addEventListener('mouseleave', () => {
              li.classList.remove('hover-persistent');
            });

            updatedElements.push(li);
          }
        });

        // If order has changed or elements were added/removed, rebuild the list
        // to ensure proper order, but without flickering (using document fragment)
        if (updatedElements.length !== userList.children.length ||
          existingUsers.size > 0 ||
          !Array.from(userList.children).every((child, i) => child === updatedElements[i])) {

          // Remove any elements that weren't in the updated data
          existingUsers.forEach(element => element.remove());

          // Create a fragment to update the DOM in one operation
          const fragment = document.createDocumentFragment();
          updatedElements.forEach(el => fragment.appendChild(el));

          // Clear and append in one operation to avoid flickering
          const scrollPos = userList.scrollTop; // Save scroll position
          userList.innerHTML = '';
          userList.appendChild(fragment);
          userList.scrollTop = scrollPos; // Restore scroll position
        }
      }

      // Handle user selection from the list
      userList.addEventListener('click', function (e) {
        const userItem = e.target.closest('[data-user]');
        if (userItem) {
          selectedUser = userItem.getAttribute('data-user');
          // Ensure selectedUser is a string
          selectedUser = selectedUser.toString();
          const user = chatData.users.find(u => u.id.toString() === selectedUser);
          if (!user.gender) {
            getGender(user.name).then(gender => {
              user.gender = gender;
              updateChatTitle(user);
              loadChatHistory(selectedUser);
              showChatInterface(true);
            });
          } else {
            updateChatTitle(user);
            loadChatHistory(selectedUser);
            showChatInterface(true);
          }

          // Update the active state in the UI
          userList.querySelectorAll('[data-user]').forEach(item => {
            item.classList.remove('active');
          });
          userItem.classList.add('active');
        }
      });

      // Modified updateChatTitle without user icon
      function updateChatTitle(user) {
        chatTitle.innerHTML = `
          <div class="flex items-center" id="chat-user-info">
            <h3 class="text-lg font-semibold user-name ml-2">${user.name}</h3>
          </div>
        `;
      }

      // Improved loadChatHistory function
      function loadChatHistory(userId, forceRebuild = false) {
        const messages = chatData.chats[userId] || [];
        const currentMessages = Array.from(chatContainer.querySelectorAll('.message-container'));

        // Check if we need to update at all
        if (forceRebuild || currentMessages.length !== messages.length) {
          // Save scroll position
          const isScrolledToBottom = chatContainer.scrollHeight - chatContainer.scrollTop <= chatContainer.clientHeight + 50;

          // Create a fragment to batch DOM updates
          const fragment = document.createDocumentFragment();

          // Clear container
          chatContainer.innerHTML = '';

          // Addall messages to fragment
          messages.forEach((msg, index) => {
            const messageElement = createMessageElement(msg.sender, msg.message, msg.sender !== 'Agent', index);
            fragment.appendChild(messageElement);
            // Store original messages for translation
            originalMessages[index] = msg.message;
          });

          // Append all messages at once
          chatContainer.appendChild(fragment);

          // Restore scroll position
          if (isScrolledToBottom) {
            chatContainer.scrollTop = chatContainer.scrollHeight;
          }
        }
      }

      // Helper function to create message elements
      function createMessageElement(sender, text, isCustomer, messageId) {
        let avatarHtml = '';
        if (isCustomer) {
          // Find the selected user's data from chatData
          let user = chatData.users.find(u => u.id.toString() === selectedUser);
          const firstName = user?.name?.split(' ')[0] || '';
          // Use the cached gender if available
          const gender = genderCache[firstName] || 'unknown';
          let avatarUrl = (gender.toLowerCase() === 'female')
            ? 'https://ui.shadcn.com/avatars/01.png'
            : 'https://ui.shadcn.com/avatars/02.png';
          avatarHtml = `<img src="${avatarUrl}" alt="${sender}" class="w-10 h-10 rounded-full mr-2 ">`;
        } else {
          // For Agent messages, use the restaurant's logo
          avatarHtml = `<img src="static/images/centered-gg-logo.png" alt="Restaurant Logo" class="w-10 h-10 rounded-full mr-2 user-icon bg-white">`;
        }

        const messageElement = document.createElement('div');
        messageElement.className = `message-container ${isCustomer ? 'justify-start' : 'justify-end'} flex items-end mb-4`;

        messageElement.innerHTML = `
    <div class="${isCustomer ? 'mr-3' : 'order-2 ml-3'}">
      ${avatarHtml}
    </div>
    <div class="message-bubble p-3 rounded-lg max-w-[80%]" data-message-id="${messageId}">
      <p class="text-sm">${text}</p>
    </div>
  `;

        return messageElement;
      }

      // Optimized display message function
      function displayMessage(sender, text, isCustomer, messageId) {
        const messageElement = createMessageElement(sender, text, isCustomer, messageId);

        // Store the original message text
        originalMessages[messageId] = text;

        // Append to container
        chatContainer.appendChild(messageElement);

        // Scroll to the bottom of the chat
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }
      function sendMessage() {
        if (!selectedUser) {
          alert("Please select a user to chat with first.");
          return;
        }
        const message = messageInput.value.trim();
        if (message) {
          const now = new Date();
          const timestamp = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          const originalTimestamp = now.getTime();
          const messageId = `msg-${originalTimestamp}`;

          // Optimistic UI update
          displayMessage('Agent', message, false, `agent-${messageId}`);
          messageInput.value = '';
          // Reset textarea height after sending
          messageInput.style.height = '40px';
          messageInput.style.overflowY = 'hidden';

          // Add the message to the chat data with original timestamp
          if (!chatData.chats[selectedUser]) {
            chatData.chats[selectedUser] = [];
          }

          chatData.chats[selectedUser].push({
            id: messageId,
            sender: 'Agent',
            message: message,
            timestamp: timestamp,
            originalTimestamp: originalTimestamp,
            customer: false
          });

          // Save to local chat system
          fetch('/send_message', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              userId: selectedUser,
              message: message,
              timestamp: timestamp,
              sender: 'Agent',
              customer: false,
              id: messageId
            }),
          })
            .then(response => response.json())
          // Handle local chat response...

          // Also save to Supabase
          fetch('/supabase_chats', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              user_id: parseInt(selectedUser),
              message: message,
              timestamp: now.toISOString(),
              sender: 'Agent',
              customer: false
            }),
          })
            .then(response => response.json())
            .then(data => {
              console.log('Message saved to Supabase:', data);
            })
            .catch(error => {
              console.error('Error saving to Supabase:', error);
            });
        }
      }

      // Handle sending messages via Enter key
      messageInput.addEventListener('keydown', function (e) {
        if (e.key === 'Enter' && !e.shiftKey) {
          // Only send if Enter is pressed WITHOUT Shift
          e.preventDefault(); // Prevent default newline in this case
          sendMessage();
        }
      });

      // Handle sending messages via Send button click
      document.getElementById('send-button').addEventListener('click', function (e) {
        e.preventDefault();
        sendMessage();
      });


      // Handle language selection from the dropdown
      languageDropdown.addEventListener('click', function (e) {
        if (e.target.tagName === 'A') {
          e.preventDefault();
          const selectedLanguage = e.target.getAttribute('data-lang');
          if (selectedLanguage !== currentLanguage) {
            currentLanguage = selectedLanguage;
            translateChat(selectedLanguage);
            updateVisualSelection(selectedLanguage);
          }
        }
      });

      // Update visual selection of the active language
      function updateVisualSelection(selectedLang) {
        const languageOptions = languageDropdown.querySelectorAll('a[data-lang]');
        languageOptions.forEach(option => {
          if (option.getAttribute('data-lang') === selectedLang) {
            option.classList.add('active-language');
          } else {
            option.classList.remove('active-language');
          }
        });
      }

      // Translate chat messages to the selected language
      async function translateChat(language) {
        const messageElements = chatContainer.querySelectorAll('.message-bubble p');

        for (let element of messageElements) {
          const messageBubble = element.parentElement;
          const messageId = messageBubble.getAttribute('data-message-id');
          const originalText = originalMessages[messageId] || element.textContent;

          if (language === 'original') {
            element.textContent = originalText;
          } else {
            if (translationCache[`${originalText}-${language}`]) {
              element.textContent = translationCache[`${originalText}-${language}`];
            } else {
              try {
                const translatedText = await translateMessage(originalText, language);
                element.textContent = translatedText;
                addToCache(originalText, language, translatedText);
              } catch (error) {
                console.error('Translation error:', error);
              }
            }
          }
        }
      }

      // Translate a single message using Google Translate API
      async function translateMessage(text, targetLanguage) {
        const url = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=auto&tl=${targetLanguage}&dt=t&q=${encodeURIComponent(text)}`;
        try {
          const response = await fetch(url);
          if (!response.ok) {
            throw new Error(`Translation API error: ${response.statusText}`);
          }
          const data = await response.json();
          const translatedText = data[0][0][0];
          return translatedText;
        } catch (error) {
          console.error('Translation error:', error);
          return text; // Return original text if translation fails
        }
      }

      // Add translation to cache
      function addToCache(originalText, targetLang, translatedText) {
        if (Object.keys(translationCache).length >= MAX_CACHE_ENTRIES) {
          // Remove the first key (FIFO) to make space
          const firstKey = Object.keys(translationCache)[0];
          delete translationCache[firstKey];
        }
        translationCache[`${originalText}-${targetLang}`] = translatedText;
        localStorage.setItem('translationCache', JSON.stringify(translationCache));
      }

      // Initialize visual selection based on default language
      updateVisualSelection(currentLanguage);

      console.log("FETCHING CHATS from /livechat_chats")
      fetchChatsFromSupabase(); // Fetch chats from Supabase

      // WebSocket connection variables
      let socket = null;
      let isConnected = false;
      let reconnectAttempts = 0;
      const maxReconnectAttempts = 5;
      let reconnectTimeout = null;

      // Server-Sent Events (SSE) connection
      let eventSource = null;

      function setupRealtimeSubscription() {
        console.log("Setting up WebSocket connection for real-time updates...");
        const statusElement = document.getElementById('realtime-status');

        // Close existing socket if any
        if (socket) {
          socket.close();
        }

        // Create WebSocket connection to Supabase Realtime
        // The WebSocket URL is based on the Supabase project ID
        const wsUrl = "wss://nuqxdjuaoccswunhqixz.supabase.co/realtime/v1/websocket?apikey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im51cXhkanVhb2Njc3d1bmhxaXh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTcxMjI2OTcsImV4cCI6MjAzMjY5ODY5N30.sHkkzEb5oCTlLB3MQ0420XtJpURXW1DIHuHm4M9kDPI";

        try {
          socket = new WebSocket(wsUrl);

          // Connection opened
          socket.addEventListener('open', (event) => {
            console.log('WebSocket connection established');
            statusElement.textContent = 'Realtime: Connected';
            statusElement.className = 'fixed bottom-2 right-2 px-2 py-1 text-xs rounded bg-green-200 text-green-700';
            isConnected = true;
            reconnectAttempts = 0; // Reset reconnect attempts on successful connection

            // Subscribe to the Supabase Realtime channel for the chats table
            // This follows the Supabase Realtime protocol
            const channelId = Math.random().toString(36).substring(2, 15);
            const joinMessage = {
              topic: "realtime:public:chats",
              event: "phx_join",
              payload: {},
              ref: channelId
            };

            // Send the join message
            socket.send(JSON.stringify(joinMessage));

            // Subscribe to all changes (INSERT, UPDATE, DELETE) on the chats table
            const subscribeMessage = {
              topic: "realtime:public:chats",
              event: "postgres_changes",
              payload: {
                config: {
                  event: "*",
                  schema: "public",
                  table: "chats"
                }
              },
              ref: channelId
            };

            // Send the subscribe message
            socket.send(JSON.stringify(subscribeMessage));

            console.log('Sent subscription request to Supabase Realtime');
          });

          // Listen for messages from Supabase Realtime
          socket.addEventListener('message', (event) => {
            try {
              const data = JSON.parse(event.data);
              console.log('WebSocket message received:', data);

              // Supabase Realtime messages have a specific format
              // We need to handle the different message types

              // Handle JOIN message (connection established)
              if (data.type === 'phx_reply' && data.event === 'phx_join') {
                console.log('Successfully joined Supabase Realtime channel');
              }

              // Handle database changes
              if (data.event === 'postgres_changes' && data.payload) {
                const payload = data.payload;

                // Handle the message based on its type
                if (payload.eventType === 'INSERT') {
                  handleNewMessage(payload.new);
                } else if (payload.eventType === 'UPDATE') {
                  handleUpdatedMessage(payload.old, payload.new);
                } else if (payload.eventType === 'DELETE') {
                  handleDeletedMessage(payload.old);
                }
              }
            } catch (error) {
              console.error('Error processing WebSocket message:', error);
            }
          });

          // Connection closed
          socket.addEventListener('close', (event) => {
            console.log('WebSocket connection closed', event);
            statusElement.textContent = 'Realtime: Disconnected';
            statusElement.className = 'fixed bottom-2 right-2 px-2 py-1 text-xs rounded bg-yellow-200 text-yellow-700';
            isConnected = false;

            // Attempt to reconnect if not closing intentionally
            if (reconnectAttempts < maxReconnectAttempts) {
              reconnectAttempts++;
              const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // Exponential backoff
              console.log(`Attempting to reconnect in ${delay / 1000} seconds...`);

              reconnectTimeout = setTimeout(() => {
                setupRealtimeSubscription();
              }, delay);
            } else {
              statusElement.textContent = 'Realtime: Failed';
              statusElement.className = 'fixed bottom-2 right-2 px-2 py-1 text-xs rounded bg-red-200 text-red-700';
              console.log('Maximum reconnection attempts reached. Falling back to polling.');

              // Fall back to polling
              setInterval(fetchChatsFromSupabase, 30000);
            }
          });

          // Connection error
          socket.addEventListener('error', (error) => {
            console.error('WebSocket error:', error);
            statusElement.textContent = 'Realtime: Error';
            statusElement.className = 'fixed bottom-2 right-2 px-2 py-1 text-xs rounded bg-red-200 text-red-700';
          });

        } catch (error) {
          console.error('Error creating WebSocket connection:', error);
          statusElement.textContent = 'Realtime: Failed';
          statusElement.className = 'fixed bottom-2 right-2 px-2 py-1 text-xs rounded bg-red-200 text-red-700';

          // Fall back to polling
          setInterval(fetchChatsFromSupabase, 30000);
        }

        // Clean up WebSocket connection when window is closed
        window.addEventListener('beforeunload', () => {
          console.log("Cleaning up WebSocket and SSE connections");
          if (socket) {
            socket.close();
          }
          if (reconnectTimeout) {
            clearTimeout(reconnectTimeout);
          }
          if (eventSource) {
            eventSource.close();
          }
        });
      }

      function handleRealtimeChange(payload) {
        console.log("Realtime change received:", payload);

        try {
          // Extract the event type and records from the payload
          // Handle different payload formats
          let eventType, newRecord, oldRecord;

          // Handle payload from SSE (from app.py)
          if (payload.data && payload.data.table === 'chats') {
            // This is the format from our SSE endpoint
            eventType = payload.data.type;
            newRecord = payload.data.record;
            oldRecord = payload.data.old_record;
            console.log("Processing SSE payload:", eventType, newRecord);
          } else {
            // Standard format from handleRealtimeChange
            eventType = payload.eventType;
            newRecord = payload.new;
            oldRecord = payload.old;
          }

          // Validate the payload
          if (!eventType || (eventType === 'INSERT' && !newRecord)) {
            console.error("Invalid realtime payload:", payload);
            return;
          }

          // Handle different types of changes
          switch (eventType) {
            case 'INSERT':
              // A new chat message was added
              handleNewMessage(newRecord);

              // Play a sound notification (optional)
              // const audio = new Audio('/static/sounds/notification.mp3');
              // audio.play();
              break;

            case 'UPDATE':
              // A chat message was updated
              handleUpdatedMessage(oldRecord, newRecord);
              break;

            case 'DELETE':
              // A chat message was deleted
              handleDeletedMessage(oldRecord);
              break;

            default:
              console.log("Unknown event type:", eventType);
          }

          // After processing the change, refresh the chat data
          if (selectedUser) {
            // Refresh the current chat if needed
            loadChatHistory(selectedUser, true);
          }

          // Update the user list to reflect any changes
          updateUserList();

        } catch (error) {
          console.error("Error handling realtime change:", error);
        }
      }

      function handleNewMessage(message) {
        // If the message is from 'Agent', assume it was already displayed optimistically
        // by the sendMessage function. Skip adding it again via realtime update.
        if (message.sender === 'Agent') {
          console.log("Ignoring realtime update for agent message (already displayed optimistically):", message.id);
          // We might still want to update the user list timestamp even for agent messages
          const userId = message.user_id.toString();
          const isSelectedUser = selectedUser === userId;
          // Update user list timestamp if necessary
          if (chatData.chats[userId]) {
            const lastMsgIndex = chatData.chats[userId].length - 1;
            // Check if this is indeed the latest message before updating list
            if (chatData.chats[userId][lastMsgIndex]?.id === message.id) {
              requestAnimationFrame(updateUserList);
            }
          }
          return; // Do not process/display agent messages received via realtime
        }

        console.log("Processing new message:", message);
        const userId = message.user_id.toString();

        // Store the original timestamp for sorting
        const originalTimestamp = new Date(message.timestamp).getTime();

        // Create a formatted message object
        const formattedMessage = {
          id: message.id, // Add this line
          sender: message.sender,
          message: message.message,
          timestamp: new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          originalTimestamp: originalTimestamp, // Store original timestamp for sorting
          customer: message.customer
        };

        // Track if UI needs immediate update
        const isSelectedUser = selectedUser === userId;

        // If this is a new user we haven't seen before
        if (!chatData.users.some(u => u.id.toString() === userId)) {
          if (message.customer) {
            // Create a new user object
            const roomNumber = 101 + parseInt(userId);
            const newUser = {
              id: userId,
              name: message.sender,
              status: "Online",
              platform: "WhatsApp",
              phone_number: message.phone_number || message.sender,
              room_number: roomNumber
            };

            chatData.users.push(newUser);
            console.log("Added new user from realtime update:", newUser);
          }
        }

        // Initialize the user's chat array if needed
        if (!chatData.chats[userId]) {
          chatData.chats[userId] = [];
        }

        // Add the message to the user's chat history
        chatData.chats[userId].push(formattedMessage);

        // CRITICAL CHANGE: Force immediate DOM update for selected user
        if (isSelectedUser) {
          // Force a redraw by directly manipulating the DOM
          const messageId = `rt-${Date.now()}`;

          // Create element
          const messageElement = createMessageElement(
            formattedMessage.sender,
            formattedMessage.message,
            formattedMessage.customer,
            messageId
          );

          // Add custom class to highlight
          messageElement.classList.add('realtime-new');

          // Store original message for translation
          originalMessages[messageId] = formattedMessage.message;

          // Directly append to DOM - no async/setTimeout
          chatContainer.appendChild(messageElement);

          // Force scroll without timeout
          chatContainer.scrollTop = chatContainer.scrollHeight;

          // Bypass animation delay by immediately adding highlight class
          requestAnimationFrame(() => {
            messageElement.classList.add('message-highlight');
            // Remove it after animation completes
            setTimeout(() => messageElement.classList.remove('message-highlight'), 1000);
          });
        }

        // Update user list (less critical, can be done after message display)
        requestAnimationFrame(() => {
          updateUserList();

          // Add notification for non-selected users
          if (!isSelectedUser) {
            const userItem = document.querySelector(`li[data-user="${userId}"]`);
            if (userItem) {
              userItem.classList.add('new-message');
              setTimeout(() => userItem.classList.remove('new-message'), 5000);
            }
          }
        });
      }
      function handleUpdatedMessage(oldMessage, newMessage) {
        console.log("Message updated - old:", oldMessage, "new:", newMessage);

        if (!newMessage || !newMessage.id) {
          console.error("Invalid updated message data:", newMessage);
          return;
        }

        const userId = newMessage.user_id.toString();
        const messageId = newMessage.id.toString();

        // 1. Update the message in chatData if it exists
        if (chatData.chats[userId]) {
          // Improved message lookup by checking ID and timestamp
          const msgIndex = chatData.chats[userId].findIndex(msg => {
            // First try ID match (most accurate)
            if (msg.id && msg.id.toString() === messageId) {
              return true;
            }

            // Fallback to approximate matching by timestamp and sender
            const msgTime = new Date(msg.timestamp).getTime();
            const updateTime = new Date(newMessage.timestamp).getTime();
            const timeDiff = Math.abs(msgTime - updateTime);

            // If timestamps are within 2 seconds and senders match, consider it a match
            return timeDiff < 2000 && msg.sender === newMessage.sender;
          });

          if (msgIndex !== -1) {
            // Update the message data
            chatData.chats[userId][msgIndex].message = newMessage.message;

            // 2. If this user is currently visible, update the DOM
            if (selectedUser === userId) {
              // Find the message element in the DOM
              const messageElements = chatContainer.querySelectorAll('.message-bubble');
              let messageElement = null;

              // First try to find by message ID
              for (let el of messageElements) {
                const elId = el.getAttribute('data-message-id');
                if (elId === messageId || elId === `msg-${messageId}`) {
                  messageElement = el;
                  break;
                }
              }

              // If message element not found by ID, try by position in chat
              if (!messageElement && messageElements[msgIndex]) {
                messageElement = messageElements[msgIndex];
              }

              // If found, update the content
              if (messageElement) {
                const textElement = messageElement.querySelector('p');
                if (textElement) {
                  // Update the text content
                  textElement.textContent = newMessage.message;

                  // Store updated text for translation
                  const elementId = messageElement.getAttribute('data-message-id');
                  originalMessages[elementId] = newMessage.message;

                  // Add visual indication that message was updated
                  messageElement.classList.add('message-updated');

                  // Add "edited" indicator if it doesn't exist
                  if (!messageElement.classList.contains('has-edited-mark')) {
                    messageElement.classList.add('has-edited-mark');
                    // Add small text to indicate message was edited
                    const editedMark = document.createElement('span');
                    editedMark.className = 'edited-mark text-xs text-gray-400 ml-1';
                    editedMark.textContent = '(edited)';
                    textElement.appendChild(editedMark);
                  }

                  // Remove highlight after animation
                  setTimeout(() => {
                    messageElement.classList.remove('message-updated');
                  }, 1000);
                }
              } else {
                console.warn("Could not find DOM element for updated message:", messageId);
                // If we can't find the element, refresh the entire chat as fallback
                loadChatHistory(userId, true);
              }
            }
          } else {
            console.warn("Updated message not found in local data:", messageId);

            // Add the message as new if we couldn't find it
            chatData.chats[userId].push({
              id: newMessage.id,
              sender: newMessage.sender,
              message: newMessage.message,
              timestamp: new Date(newMessage.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
              customer: newMessage.customer
            });

            // Refresh the UI if this user is selected
            if (selectedUser === userId) {
              loadChatHistory(userId, true);
            }
          }
        } else {
          // If we don't have any chat data for this user yet, initialize it
          chatData.chats[userId] = [{
            id: newMessage.id,
            sender: newMessage.sender,
            message: newMessage.message,
            timestamp: new Date(newMessage.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            customer: newMessage.customer
          }];

          // Create a new user if necessary
          if (!chatData.users.some(u => u.id.toString() === userId)) {
            const roomNumber = 101 + parseInt(userId);
            chatData.users.push({
              id: userId,
              name: newMessage.sender,
              status: "Online",
              platform: "WhatsApp",
              phone_number: newMessage.phone_number || newMessage.sender,
              room_number: roomNumber
            });
          }

          updateUserList();
        }
      }
      function handleDeletedMessage(deletedMessage) {
        // Implementation for handling deleted messages if needed
        console.log("Message deleted:", deletedMessage);
      }

      // Function to refresh chat data from the server
      function refreshChatData() {
        console.log("Refreshing chat data from server...");

        // Fetch the latest data from the server
        fetch('/livechat_chats')
          .then(response => response.json())
          .then(data => {
            console.log(`Received ${data.length} chats from server`);

            // Process the data to update the UI
            processChatsData(data);

            // If a user is selected, refresh their chat
            if (selectedUser) {
              loadChatHistory(selectedUser, true);
            }
          })
          .catch(error => {
            console.error("Error refreshing chat data:", error);
          });
      }

      // Function to set up Server-Sent Events (SSE) connection
      function setupSSEConnection() {
        console.log("Setting up SSE connection for real-time updates...");
        const statusElement = document.getElementById('realtime-status');

        // Close existing connection if any
        if (eventSource) {
          eventSource.close();
        }

        try {
          // Create new EventSource connection
          eventSource = new EventSource('/livechat_events');

          // Connection opened
          eventSource.addEventListener('open', function () {
            console.log('SSE connection established');
            statusElement.textContent = 'Realtime: Connected (SSE)';
            statusElement.className = 'fixed bottom-2 right-2 px-2 py-1 text-xs rounded bg-green-200 text-green-700';

            // Refresh data when connection is established
            refreshChatData();
          });

          // Listen for messages
          eventSource.addEventListener('message', function (event) {
            try {
              const data = JSON.parse(event.data);
              console.log('SSE message received:', data);

              // Handle different event types
              if (data.event === 'connected') {
                console.log('SSE connection confirmed:', data.data);
              } else if (data.event === 'ping') {
                // Just a keep-alive ping, no action needed
              } else if (data.event === 'db_change') {
                // Process database change
                handleRealtimeChange(data.data);

                // Refresh the chat data to ensure we have the latest
                refreshChatData();
              }
            } catch (error) {
              console.error('Error processing SSE message:', error);
            }
          });

          // Handle errors
          eventSource.addEventListener('error', function (error) {
            console.error('SSE connection error:', error);
            statusElement.textContent = 'Realtime: Error (SSE)';
            statusElement.className = 'fixed bottom-2 right-2 px-2 py-1 text-xs rounded bg-red-200 text-red-700';

            // Close the connection on error
            eventSource.close();

            // Try to reconnect after a delay
            setTimeout(setupSSEConnection, 5000);
          });

        } catch (error) {
          console.error('Error creating SSE connection:', error);
          statusElement.textContent = 'Realtime: Failed (SSE)';
          statusElement.className = 'fixed bottom-2 right-2 px-2 py-1 text-xs rounded bg-red-200 text-red-700';
        }
      }

      // Initialize both Supabase realtime subscription and SSE connection
      setupRealtimeSubscription();
      setupSSEConnection();
    });
  </script>

  <!-- Add this HTML just before the closing </body> tag -->

  <!-- User Profile Panel -->
  <div id="user-profile-panel" class="user-profile-panel">
    <div class="panel-content">
      <button id="close-profile-panel" class="close-button">&times;</button>
      <div id="profile-details">
        <!-- Profile details will be populated dynamically -->
        <h2 id="profile-name">John Doe</h2>
        <p id="profile-email">Email: <EMAIL></p>
        <p id="profile-phone">Phone: +****************</p>
        <p id="profile-room">Room: 101</p>
        <p id="profile-platform">Platform: WhatsApp</p>
        <!-- Add more profile fields as needed -->
        <!-- Temporary Debugging Content -->
        <div style="margin-top: 20px; color: red;">
          <p>This is a test message to confirm visibility.</p>
        </div>
      </div>
    </div>
  </div>
  <style>
    /* templates/livechat.html */

    /* User Profile Panel Styles */
    .relative-container {
      position: relative;
      /* Establish positioning context */
    }

    .flex-row {
      display: flex;
      width: 100%;
      height: 100%;
    }

    #chat-interface {
      flex-grow: 1;
      transition: width 0.3s ease-in-out;
    }

    .user-profile-panel {
      position: fixed;
      /* Changed from relative to fixed */
      top: 0;
      right: 0;
      width: 0;
      /* Hidden by default */
      max-width: 400px;
      /* Maximum width when open */
      height: 100%;
      background-color: #ffffff;
      /* White background for visibility */
      box-shadow: -2px 0 8px rgba(0, 0, 0, 0.2);
      transition: width 0.3s ease-in-out;
      z-index: 1001;
      /* Above the overlay */
      overflow-y: auto;
      padding: 20px;
      /* Add some padding */
      display: none;
      /* Hidden by default */
    }

    .user-profile-panel.open {
      width: 400px;
      /* Slide in */
      display: block;
    }

    .panel-content {
      position: relative;
      /* For positioning the close button */
    }

    .close-button {
      position: absolute;
      top: 10px;
      right: 15px;
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: #333333;
    }

    /* Overlay to dim the background when panel is open */
    .panel-overlay {
      position: fixed;
      /* Changed from absolute to fixed */
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: none;
      z-index: 1000;
      /* Below the panel */
    }

    .panel-overlay.active {
      display: block;
    }

    /* Sample styles for profile details */
    #profile-details h2 {
      margin-top: 40px;
      /* Ensure space below close button */
      margin-bottom: 20px;
      color: #333333;
    }

    #profile-details p {
      margin-bottom: 10px;
      color: #555555;
    }
  </style>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      console.log("DOM fully loaded and parsed");

      const chatTitle = document.getElementById('chat-title');
      const userProfilePanel = document.getElementById('user-profile-panel');
      const panelOverlay = document.querySelector('.panel-overlay');
      const closeProfileButton = document.getElementById('close-profile-panel');

      // Function to open the profile panel
      function openUserProfile(user) {
        document.getElementById('profile-name').textContent = user.name;
        document.getElementById('profile-email').textContent = `Email: ${user.email}`;
        document.getElementById('profile-phone').textContent = `Phone: ${user.phone_number}`;
        document.getElementById('profile-room').textContent = `Room: ${user.room_number}`;
        document.getElementById('profile-platform').textContent = `Platform: ${user.platform}`;

        userProfilePanel.classList.add('open');
        panelOverlay.classList.add('active');
      }

      // Function to close the profile panel
      function closeUserProfile() {
        userProfilePanel.classList.remove('open');
        panelOverlay.classList.remove('active');
      }

      // Prevent overlay from closing when clicking inside the profile panel
      userProfilePanel.addEventListener('click', function (event) {
        event.stopPropagation();
      });

      // Event listener for closing the profile panel when clicking the overlay
      panelOverlay.addEventListener('click', closeUserProfile);
    });
  </script>
  <script>
    // Add event listeners to the Assign Chat buttons when the document is loaded
    document.addEventListener('DOMContentLoaded', function () {
      // Get the staff assignment modal
      const staffModal = document.getElementById('staff-assignment-modal');
      const closeStaffModalBtn = document.getElementById('close-staff-modal');

      // Set up close button
      if (closeStaffModalBtn) {
        closeStaffModalBtn.addEventListener('click', function () {
          staffModal.classList.add('hidden');
          document.body.classList.remove('overflow-hidden');
        });
      }

      // Close if clicking outside the modal content
      staffModal.addEventListener('click', function (e) {
        if (e.target === staffModal || e.target === staffModal.querySelector('.absolute')) {
          staffModal.classList.add('hidden');
          document.body.classList.remove('overflow-hidden');
        }
      });

      // Add click handlers to all Assign Chat buttons
      const assignButtons = document.querySelectorAll('#staff-container button');
      assignButtons.forEach(button => {
        button.addEventListener('click', function () {
          // Get the staff name from the parent element
          const staffElement = button.closest('.flex.items-center.space-x-4');
          const staffName = staffElement.querySelector('.font-medium.leading-none').textContent;

          console.log(`Assigning chat to ${staffName}`);

          // Visual feedback that the button was clicked
          const originalHTML = button.innerHTML;
          button.innerHTML = '<span style="font-size: 14px; white-space: nowrap;">Assigned!</span>';
          button.classList.add("bg-green-600");
          button.classList.add("text-white");

          // Reset after a short delay
          setTimeout(() => {
            button.innerHTML = originalHTML;
            button.classList.remove("bg-green-600");
            button.classList.remove("text-white");

            // Close the modal after assigning
            staffModal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
          }, 1000);
        });
      });

      // Find the "Assign Staff" link in dropdowns and attach event listener
      document.querySelectorAll('a.uk-drop-close').forEach(link => {
        if (link.textContent.trim().includes('Assign Staff')) {
          link.addEventListener('click', function (e) {
            e.preventDefault();
            staffModal.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
          });
        }
      });

      // IMPORTANT: Override the staff_assignment.js functionality to prevent dynamic rendering
      // This ensures our hardcoded buttons are not replaced
      if (window.renderStaffMembers) {
        window.renderStaffMembers = function () {
          console.log("Staff rendering disabled - using hardcoded staff members");
          return; // Do nothing - keep our hardcoded staff members
        };
      }

      if (window.openStaffModal) {
        // const originalOpenStaffModal = window.openStaffModal; // Not strictly needed if we fully redefine
        window.openStaffModal = function () {
          const currentStaffModal = document.getElementById('staff-assignment-modal');
          if (!currentStaffModal) return;

          currentStaffModal.classList.remove('modal-closing'); // Ensure closing animation class is removed
          currentStaffModal.classList.remove('hidden'); // Show modal, triggering opening animation via CSS
          document.body.classList.add('overflow-hidden');
        };
      } else {
        // Define if staff_assignment.js might not have defined it
        window.openStaffModal = function () {
          const currentStaffModal = document.getElementById('staff-assignment-modal');
          if (!currentStaffModal) return;
          currentStaffModal.classList.remove('modal-closing');
          currentStaffModal.classList.remove('hidden');
          document.body.classList.add('overflow-hidden');
        };
        console.warn("window.openStaffModal was not previously defined by staff_assignment.js. Defined now in livechat.html.");
      }
    });
  </script>
  <!-- Staff Assignment Modal -->
  <div id="staff-assignment-modal" class="fixed inset-0 z-50 hidden" style="z-index: 2000;">
    <!-- Backdrop with blur effect -->
    <div class="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>

    <!-- Modal container -->
    <div class="relative flex items-center justify-center min-h-screen p-4">
      <!-- Using exact same markup as isolated.html -->
      <div class="universal-background-color max-w-2xl w-full rounded-lg shadow-lg modal-card-animated">
        <div id="staff-members" class="uk-card card">
          <div class="uk-card-header pb-0">
            <div class="flex items-center justify-between">
              <h3 class="font-semibold leading-none tracking-tight">
                Assign Chat to Staff Member
              </h3>
              <button id="close-staff-modal" class="text-muted-foreground hover:text-foreground"
                style="background-color: transparent;">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M18 6 6 18"></path>
                  <path d="m6 6 12 12"></path>
                </svg>
              </button>
            </div>
            <p class="text-sm">
              Assign a staff member to manage this guest chat.
            </p>
          </div>
          <div class="uk-card-body pt-0">
            <hr class="card my-4">
            <div class="space-y-4" id="staff-container">
              <h4 class="text-sm font-medium">Available staff members: </h4>
              <!-- Hardcoded staff members with Assign Chat buttons -->
              <div class="flex items-center space-x-4">
                <span class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                  <img class="aspect-square h-full w-full" src="https://ui.shadcn.com/avatars/01.png">
                </span>
                <div class="flex-1">
                  <p class="text-sm font-medium leading-none">Dixith Mediga</p>
                  <p class="text-sm"><EMAIL></p>
                </div>
                <div class="flex justify-end w-full">
                  <button class="uk-button uk-button-default flex items-center justify-center card universal-hover"
                    style="background-color: transparent; padding: 0 20px; height: 36px; line-height: 30px; width: 160px;">
                    <!-- Using a standard SVG for user assignment -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-user-plus mr-2">
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <line x1="19" x2="19" y1="8" y2="14" />
                      <line x1="22" x2="16" y1="11" y2="11" />
                    </svg>
                    <span style="font-size: 14px; white-space: nowrap;">Assign Chat</span>
                  </button>
                </div>
              </div>

              <div class="flex items-center space-x-4">
                <span class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                  <img class="aspect-square h-full w-full" src="https://ui.shadcn.com/avatars/04.png">
                </span>
                <div class="flex-1">
                  <p class="text-sm font-medium leading-none">Harsh Jadhav</p>
                  <p class="text-sm"><EMAIL></p>
                </div>
                <div class="flex justify-end w-full">
                  <button class="uk-button uk-button-default flex items-center justify-center card universal-hover"
                    style="background-color: transparent; padding: 0 20px; height: 36px; line-height: 30px; width: 160px;">
                    <!-- Using a standard SVG for user assignment -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-user-plus mr-2">
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <line x1="19" x2="19" y1="8" y2="14" />
                      <line x1="22" x2="16" y1="11" y2="11" />
                    </svg>
                    <span style="font-size: 14px; white-space: nowrap;">Assign Chat</span>
                  </button>
                </div>
              </div>

              <div class="flex items-center space-x-4">
                <span class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                  <img class="aspect-square h-full w-full" src="https://ui.shadcn.com/avatars/02.png">
                </span>
                <div class="flex-1">
                  <p class="text-sm font-medium leading-none">George Garriga</p>
                  <p class="text-sm"><EMAIL></p>
                </div>
                <div class="flex justify-end w-full">
                  <button class="uk-button uk-button-default flex items-center justify-center card universal-hover"
                    style="background-color: transparent; padding: 0 20px; height: 36px; line-height: 30px; width: 160px;">
                    <!-- Using a standard SVG for user assignment -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-user-plus mr-2">
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <line x1="19" x2="19" y1="8" y2="14" />
                      <line x1="22" x2="16" y1="11" y2="11" />
                    </svg>
                    <span style="font-size: 14px; white-space: nowrap;">Assign Chat</span>
                  </button>
                </div>
              </div>

              <div class="flex items-center space-x-4">
                <span class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                  <img class="aspect-square h-full w-full" src="https://ui.shadcn.com/avatars/05.png">
                </span>
                <div class="flex-1">
                  <p class="text-sm font-medium leading-none">Jackon Lee</p>
                  <p class="text-sm"><EMAIL></p>
                </div>
                <div class="flex justify-end w-full">
                  <button class="uk-button uk-button-default flex items-center justify-center card universal-hover"
                    style="background-color: transparent; padding: 0 20px; height: 36px; line-height: 30px; width: 160px;">
                    <!-- Using a standard SVG for user assignment -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-user-plus mr-2">
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <line x1="19" x2="19" y1="8" y2="14" />
                      <line x1="22" x2="16" y1="11" y2="11" />
                    </svg>
                    <span style="font-size: 14px; white-space: nowrap;">Assign Chat</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    /* Modal styles */
    #staff-assignment-modal {
      transition: opacity 0.3s ease;
    }

    #staff-assignment-modal.hidden {
      opacity: 0;
      pointer-events: none;
    }

    #staff-assignment-modal:not(.hidden) {
      opacity: 1;
      pointer-events: auto;
    }

    /* Backdrop blur effect */
    .backdrop-blur-sm {
      backdrop-filter: blur(4px);
      -webkit-backdrop-filter: blur(4px);
    }

    /* Modal appearance animation */
    #staff-members {
      background-color: var(--card-background);
      transition: transform 0.3s ease, opacity 0.3s ease;
      transform: translateY(0);
      opacity: 1;
    }

    #staff-assignment-modal.hidden #staff-members {
      transform: translateY(20px);
      opacity: 0;
    }

    .light .themer-input {
      background-color: transparent !important;
      color: black !important;
      border: transparent !important;
      outline: none !important;
      /* Removes the focus border */
      box-shadow: none !important;
    }

    .pure-black .themer-input {
      background-color: transparent !important;
      color: white !important;
      border: transparent !important;
      border: transparent !important;
      outline: none !important;
      /* Removes the focus border */
      box-shadow: none !important;
    }

    .uk-label {
      color: var(--dropdown-text);
      background-color: var(--background);
    }

    .uk-drop.uk-dropdown {
      color: var(--dropdown-text);
    }

    .light .uk-table-divider>tr:not(:first-child),
    .light .uk-table-divider>:not(:first-child)>tr,
    .light .uk-table-divider>:first-child>tr:not(:first-child) {
      border-color: #e5e7eb;
    }

    .pure-black .uk-table-divider>tr:not(:first-child),
    .pure-black .uk-table-divider>:not(:first-child)>tr,
    .pure-black .uk-table-divider>:first-child>tr:not(:first-child) {
      border-color: #27272a;
    }

    .light .uk-label {
      background-color: white;
    }

    .pure-black .uk-label {
      background-color: #09090b;
    }
  </style>
  <!-- Food Carousel Modal -->
  <div id="food-carousel-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
    <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm" id="food-modal-backdrop"></div>
    <div
      class="card universal-background-color rounded-lg shadow-lg w-full max-w-2xl z-10 relative border border-gray-300">
      <!-- Header with divider -->
      <div class="flex justify-between items-center p-4 border-b card">
        <h3 class="text-lg font-medium">Food Menu</h3>
        <button id="close-food-modal"
          style="background-color: transparent; height: 34px; border: none; cursor: pointer;"
          class="flex items-center justify-center card" aria-label="Close Modal">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Food Carousel (copied from productsss.html) -->
      <div class="p-4">
        <div class="carousel-container" data-carousel="food">
          <div class="carousel">
            <div class="carousel-inner">
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/foodmenu/burrata.png" alt="Burrata">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Burrata</h3>
                  <p class="mt-1 text-lg font-medium">$25</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/foodmenu/chicken.png" alt="Chicken">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Chicken</h3>
                  <p class="mt-1 text-lg font-medium">$32</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/foodmenu/filletsteak.png" alt="Fillet Steak">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Fillet Steak</h3>
                  <p class="mt-1 text-lg font-medium">$45</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/foodmenu/grilledsalmon.png" alt="Grilled Salmon">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Grilled Salmon</h3>
                  <p class="mt-1 text-lg font-medium">$38</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/foodmenu/lobsterroll.png" alt="Lobster Roll">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Lobster Roll</h3>
                  <p class="mt-1 text-lg font-medium">$42</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/foodmenu/pumpkinwithcoconut.png" alt="Pumpkin with Coconut">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Pumpkin with Coconut</h3>
                  <p class="mt-1 text-lg font-medium">$28</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/foodmenu/steakandlobster.png" alt="Steak and Lobster">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Steak and Lobster</h3>
                  <p class="mt-1 text-lg font-medium">$65</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/foodmenu/tbone.png" alt="T-Bone Steak">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">T-Bone Steak</h3>
                  <p class="mt-1 text-lg font-medium">$55</p>
                </div>
              </div>
            </div>
          </div>

          <div class="slide-indicator">Slide 1 of 8</div>


          <button class="carousel-control carousel-control-prev card">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-arrow-left">
              <path d="m12 19-7-7 7-7" />
              <path d="M19 12H5" />
            </svg>
          </button>

          <button class="carousel-control carousel-control-next card">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-arrow-right">
              <path d="M5 12h14" />
              <path d="m12 5 7 7-7 7" />
            </svg>
          </button>
        </div>

        <!-- Footer with buttons to use selected item -->
        <div class="flex justify-end mt-4">
          <button id="cancel-food-selection" class="uk-button uk-button-default mr-2 card universal-hover"
            style="border-radius: 8px; height: 36px; line-height: 30px;">Cancel</button>
          <button id="use-selected-food" class="uk-button uk-button-default mr-2 card universal-hover"
            style="border-radius: 8px; height: 36px; line-height: 30px;">Send</button>
        </div>
      </div>
    </div>
  </div>
  <!-- Room Bookings Carousel Modal -->
  <div id="room-carousel-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
    <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm" id="room-modal-backdrop"></div>
    <div
      class="card universal-background-color rounded-lg shadow-lg w-full max-w-2xl z-10 relative border border-gray-300">
      <!-- Header with divider -->
      <div class="flex justify-between items-center p-4 border-b card">
        <h3 class="text-lg font-medium">Room Bookings</h3>
        <button id="close-room-modal"
          style="background-color: transparent; height: 34px; border: none; cursor: pointer;"
          class="flex items-center justify-center card" aria-label="Close Modal">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Room Carousel -->
      <div class="p-4">
        <div class="carousel-container" data-carousel="room">
          <div class="carousel">
            <div class="carousel-inner">
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/roombooking/executivesuite.jpeg" alt="Executive Suite">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Executive Suite</h3>
                  <p class="mt-1 text-lg font-medium">$150</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/roombooking/juniorsuite.jpeg" alt="Junior Suite">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Junior Suite</h3>
                  <p class="mt-1 text-lg font-medium">$55</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/roombooking/juniorsuitedeluxe.jpeg" alt="Junior Suite Deluxe">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Junior Suite Deluxe</h3>
                  <p class="mt-1 text-lg font-medium">$65</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/roombooking/raid.jpeg" alt="Royal Suite">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Royal Suite</h3>
                  <p class="mt-1 text-lg font-medium">$75</p>
                </div>
              </div>
            </div>
          </div>

          <div class="slide-indicator">Slide 1 of 4</div>


          <button class="carousel-control carousel-control-prev card">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-arrow-left">
              <path d="m12 19-7-7 7-7" />
              <path d="M19 12H5" />
            </svg>
          </button>

          <button class="carousel-control carousel-control-next card">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-arrow-right">
              <path d="M5 12h14" />
              <path d="m12 5 7 7-7 7" />
            </svg>
          </button>
        </div>

        <!-- Footer with buttons to use selected item -->
        <div class="flex justify-end mt-4">
          <button id="cancel-room-selection" class="uk-button uk-button-default mr-2 card universal-hover"
            style="border-radius: 8px; height: 36px; line-height: 30px;">Cancel</button>
          <button id="use-selected-room" class="uk-button uk-button-primary"
            style="border-radius: 8px; height: 36px; line-height: 30px;">Use Selected Room</button>
        </div>
      </div>
    </div>
  </div>
  <!-- Spa Carousel Modal -->
  <div id="spa-carousel-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
    <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm" id="spa-modal-backdrop"></div>
    <div
      class="card universal-background-color rounded-lg shadow-lg w-full max-w-2xl z-10 relative border border-gray-300">
      <!-- Header with divider -->
      <div class="flex justify-between items-center p-4 border-b card">
        <h3 class="text-lg font-medium">Spa Services</h3>
        <button id="close-spa-modal" style="background-color: transparent; height: 34px; border: none; cursor: pointer;"
          class="flex items-center justify-center card" aria-label="Close Modal">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Spa Carousel -->
      <div class="p-4">
        <div class="carousel-container" data-carousel="spa">
          <div class="carousel">
            <div class="carousel-inner">
              <div class="carousel-item">
                <img
                  src="https://secretoasisspa.co.uk/wp-content/uploads/2023/10/secret-oasis-spa-aromatherapy-massage_safety.jpg"
                  alt="Aromatherapy Massage">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Aromatherapy Massage</h3>
                  <p class="mt-1 text-lg font-medium">$120</p>
                </div>
              </div>
              <div class="carousel-item">
                <img
                  src="https://secretoasisspa.co.uk/wp-content/uploads/2023/10/secret-oasis-spa-aromatherapy-massage_safety.jpg"
                  alt="Hot Stone Massage">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Hot Stone Massage</h3>
                  <p class="mt-1 text-lg font-medium">$140</p>
                </div>
              </div>
              <div class="carousel-item">
                <img
                  src="https://secretoasisspa.co.uk/wp-content/uploads/2023/10/secret-oasis-spa-aromatherapy-massage_safety.jpg"
                  alt="Luxury Facial">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Luxury Facial</h3>
                  <p class="mt-1 text-lg font-medium">$95</p>
                </div>
              </div>
              <div class="carousel-item">
                <img
                  src="https://secretoasisspa.co.uk/wp-content/uploads/2023/10/secret-oasis-spa-aromatherapy-massage_safety.jpg"
                  alt="Body Scrub Treatment">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Body Scrub Treatment</h3>
                  <p class="mt-1 text-lg font-medium">$85</p>
                </div>
              </div>
              <div class="carousel-item">
                <img
                  src="https://secretoasisspa.co.uk/wp-content/uploads/2023/10/secret-oasis-spa-aromatherapy-massage_safety.jpg"
                  alt="Couples Massage">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Couples Massage</h3>
                  <p class="mt-1 text-lg font-medium">$220</p>
                </div>
              </div>
            </div>
          </div>

          <div class="slide-indicator">Slide 1 of 5</div>


          <button class="carousel-control carousel-control-prev card">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-arrow-left">
              <path d="m12 19-7-7 7-7" />
              <path d="M19 12H5" />
            </svg>
          </button>

          <button class="carousel-control carousel-control-next card">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-arrow-right">
              <path d="M5 12h14" />
              <path d="m12 5 7 7-7 7" />
            </svg>
          </button>
        </div>

        <!-- Footer with buttons to use selected item -->
        <div class="flex justify-end mt-4">
          <button id="cancel-spa-selection" class="uk-button uk-button-default mr-2 card universal-hover"
            style="border-radius: 8px; height: 36px; line-height: 30px;">Cancel</button>
          <button id="use-selected-spa" class="uk-button uk-button-primary"
            style="border-radius: 8px; height: 36px; line-height: 30px;">Use Selected Service</button>
        </div>
      </div>
    </div>
  </div>
  <!-- Massage Carousel Modal -->
  <div id="massage-carousel-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
    <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm" id="massage-modal-backdrop"></div>
    <div
      class="card universal-background-color rounded-lg shadow-lg w-full max-w-2xl z-10 relative border border-gray-300">
      <!-- Header with divider -->
      <div class="flex justify-between items-center p-4 border-b card">
        <h3 class="text-lg font-medium">Massage Services</h3>
        <button id="close-massage-modal"
          style="background-color: transparent; height: 34px; border: none; cursor: pointer;"
          class="flex items-center justify-center card" aria-label="Close Modal">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Massage Carousel -->
      <div class="p-4">
        <div class="carousel-container" data-carousel="massage">
          <div class="carousel">
            <div class="carousel-inner">
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/starterimages/massage.png" alt="Swedish Massage">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Swedish Massage</h3>
                  <p class="mt-1 text-lg font-medium">$90</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/starterimages/massage.png" alt="Deep Tissue Massage">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Deep Tissue Massage</h3>
                  <p class="mt-1 text-lg font-medium">$110</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/starterimages/massage.png" alt="Thai Massage">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Thai Massage</h3>
                  <p class="mt-1 text-lg font-medium">$100</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/starterimages/massage.png" alt="Sports Massage">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Sports Massage</h3>
                  <p class="mt-1 text-lg font-medium">$120</p>
                </div>
              </div>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/starterimages/massage.png" alt="Reflexology">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Reflexology</h3>
                  <p class="mt-1 text-lg font-medium">$80</p>
                </div>
              </div>
            </div>
          </div>

          <div class="slide-indicator">Slide 1 of 5</div>


          <button class="carousel-control carousel-control-prev card">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-arrow-left">
              <path d="m12 19-7-7 7-7" />
              <path d="M19 12H5" />
            </svg>
          </button>

          <button class="carousel-control carousel-control-next card">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-arrow-right">
              <path d="M5 12h14" />
              <path d="m12 5 7 7-7 7" />
            </svg>
          </button>
        </div>

        <!-- Footer with buttons to use selected item -->
        <div class="flex justify-end mt-4">
          <button id="cancel-massage-selection" class="uk-button uk-button-default mr-2 card universal-hover"
            style="border-radius: 8px; height: 36px; line-height: 30px;">Cancel</button>
          <button id="use-selected-massage" class="uk-button uk-button-primary"
            style="border-radius: 8px; height: 36px; line-height: 30px;">Use Selected Service</button>
        </div>
      </div>
    </div>
  </div>
  <style>
    /* Carousel Styles from productsss.html */
    .carousel-container {
      position: relative;
      width: 100%;
      max-width: 700px;
      height: 400px;
      margin: 0 auto 40px;
    }

    .carousel {
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: relative;
      border-radius: 8px;
    }

    .carousel-inner {
      display: flex;
      width: 100%;
      height: 100%;
      transition: transform 0.5s ease;
    }

    .carousel-item {
      min-width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .carousel-item img {
      width: 80%; /* Reduced width */
      margin-left: auto; /* Center the image */
      margin-right: auto; /* Center the image */
      height: 85%;
      object-fit: cover;
      border-radius: 8px 8px 0 0;
    }

    .carousel-item .product-info {
      padding: 15px;
      width: 100%;
      text-align: center;
      color: var(--dropdown-text);
      border-radius: 0 0 8px 8px;
    }

    .carousel-control {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      z-index: 10;
      transition: background-color 0.1s ease;
border: 1px solid var(--border-color);
    }

    .carousel-control-prev {
      left: 10px;
    }

    .carousel-control-next {
      right: 10px;
    }

    .slide-indicator {
      position: absolute;
      bottom: -24px;
      left: 50%;
      transform: translateX(-50%);
.slide-indicator {
      display: none; /* Hide the slide indicator text */
    }
      font-size: 12px;
      color: var(--text-color);
    }

    /* Food modal animation */
    #food-carousel-modal {
      transition: opacity 0.3s ease;
    }

    #food-carousel-modal.hidden {
      opacity: 0;
      pointer-events: none;
    }

    #food-carousel-modal:not(.hidden) {
      opacity: 1;
      pointer-events: auto;
    }

    #food-carousel-modal .card {
      transform: translateY(20px);
      opacity: 0;
      transition: transform 0.3s ease, opacity 0.3s ease;
    }

    #food-carousel-modal:not(.hidden) .card {
      transform: translateY(0);
      opacity: 1;
    }
  </style>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Room carousel modal functionality
      const roomButton = document.getElementById('room-template-button');
      const roomModal = document.getElementById('room-carousel-modal');
      const closeRoomBtn = document.getElementById('close-room-modal');
      const cancelRoomBtn = document.getElementById('cancel-room-selection');
      const useSelectedRoomBtn = document.getElementById('use-selected-room');
      const roomModalBackdrop = document.getElementById('room-modal-backdrop');

      // Variables for room carousel
      let roomCurrentIndex = 0;
      let roomTotalItems = 0;
      let isRoomCarouselSliding = false;

      // Open modal when room button is clicked
      if (roomButton) {
        roomButton.addEventListener('click', function (e) {
          e.stopPropagation(); // Prevent event bubbling
          roomModal.classList.remove('hidden');

          // Initialize carousel
          initRoomCarousel();
        });
      }

      // Close modal functions
      function closeRoomModal() {
        roomModal.classList.add('hidden');
      }

      // Close buttons
      if (closeRoomBtn) closeRoomBtn.addEventListener('click', closeRoomModal);
      if (cancelRoomBtn) cancelRoomBtn.addEventListener('click', closeRoomModal);
      if (roomModalBackdrop) roomModalBackdrop.addEventListener('click', closeRoomModal);

      // Use selected room item
      if (useSelectedRoomBtn) {
        useSelectedRoomBtn.addEventListener('click', function () {
          // Get currently visible carousel item
          const visibleItem = document.querySelector('#room-carousel-modal .carousel-item:nth-child(' + (roomCurrentIndex + 1) + ')');
          const roomName = visibleItem.querySelector('h3').textContent;
          const roomPrice = visibleItem.querySelector('p').textContent;

          // Get the message input field
          const messageInput = document.getElementById('message-input');
          if (messageInput) {
            // Add room information to the message
            messageInput.value = `I'd like to book the ${roomName} (${roomPrice} per night).`;

            // Trigger auto-resize if that function exists
            if (typeof autoResizeTextarea === 'function') {
              autoResizeTextarea();
            }
          }

          // Close the modal
          closeRoomModal();
        });
      }

      // Room carousel functionality
      function initRoomCarousel() {
        const carouselContainer = document.querySelector('.carousel-container[data-carousel="room"]');
        if (!carouselContainer) return;

        const carouselInner = carouselContainer.querySelector('.carousel-inner');
        const items = carouselContainer.querySelectorAll('.carousel-item');
        const prevBtn = carouselContainer.querySelector('.carousel-control-prev');
        const nextBtn = carouselContainer.querySelector('.carousel-control-next');
        const slideIndicator = carouselContainer.querySelector('.slide-indicator');

        // Reset carousel position
        roomCurrentIndex = 0;
        roomTotalItems = items.length;
        isRoomCarouselSliding = false; // Reset sliding state

        // Update carousel position
        function updateRoomCarousel() {
          carouselInner.style.transform = `translateX(-${roomCurrentIndex * 100}%)`;
          slideIndicator.textContent = `Slide ${roomCurrentIndex + 1} of ${roomTotalItems}`;
        }

        // Update slide indicator text initially
        updateRoomCarousel();

        // Listen for transition end to unlock sliding
        carouselInner.addEventListener('transitionend', () => {
          isRoomCarouselSliding = false;
        });

        // Event listeners
        prevBtn.addEventListener('click', function () {
          if (isRoomCarouselSliding) return;
          isRoomCarouselSliding = true;
          roomCurrentIndex = (roomCurrentIndex > 0) ? roomCurrentIndex - 1 : roomTotalItems - 1;
          updateRoomCarousel();
        });

        nextBtn.addEventListener('click', function () {
          if (isRoomCarouselSliding) return;
          isRoomCarouselSliding = true;
          roomCurrentIndex = (roomCurrentIndex < roomTotalItems - 1) ? roomCurrentIndex + 1 : 0;
          updateRoomCarousel();
        });
      }

      // Keyboard navigation
      document.addEventListener('keydown', function (e) {
        if (!roomModal || roomModal.classList.contains('hidden')) return;

        if (e.key === 'Escape') {
          closeRoomModal();
        } else if (e.key === 'ArrowRight') {
          document.querySelector('#room-carousel-modal .carousel-control-next').click();
        } else if (e.key === 'ArrowLeft') {
          document.querySelector('#room-carousel-modal .carousel-control-prev').click();
        } else if (e.key === 'Enter') {
          useSelectedRoomBtn.click();
        }
      });
    });
  </script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Food carousel modal functionality
      const foodButton = document.getElementById('food-template-button');
      const foodModal = document.getElementById('food-carousel-modal');
      const closeBtn = document.getElementById('close-food-modal');
      const cancelBtn = document.getElementById('cancel-food-selection');
      const useSelectedBtn = document.getElementById('use-selected-food');
      const modalBackdrop = document.getElementById('food-modal-backdrop');

      // Open modal when food button is clicked
      if (foodButton) {
        foodButton.addEventListener('click', function (e) {
          e.stopPropagation(); // Prevent event bubbling
          foodModal.classList.remove('hidden');

          // Initialize carousel
          initFoodCarousel();
        });
      }

      // Close modal functions
      function closeModal() {
        foodModal.classList.add('hidden');
      }

      // Close buttons
      if (closeBtn) closeBtn.addEventListener('click', closeModal);
      if (cancelBtn) cancelBtn.addEventListener('click', closeModal);
      if (modalBackdrop) modalBackdrop.addEventListener('click', closeModal);

      // Use selected food item
      if (useSelectedBtn) {
        useSelectedBtn.addEventListener('click', function () {
          // Get currently visible carousel item
          const visibleItem = document.querySelector('.carousel-item:nth-child(' + (currentIndex + 1) + ')');
          const foodName = visibleItem.querySelector('h3').textContent;
          const foodPrice = visibleItem.querySelector('p').textContent;

          // Get the message input field
          const messageInput = document.getElementById('message-input');
          if (messageInput) {
            // Add food information to the message
            messageInput.value = `I'd like to order the ${foodName} (${foodPrice}).`;

            // Trigger auto-resize if that function exists
            if (typeof autoResizeTextarea === 'function') {
              autoResizeTextarea();
            }
          }

          // Close the modal
          closeModal();
        });
      }

      // Carousel functionality
      let currentIndex = 0;
      let totalItems = 0;

      function initFoodCarousel() {
        const carouselContainer = document.querySelector('.carousel-container[data-carousel="food"]');
        if (!carouselContainer) return;

        const carouselInner = carouselContainer.querySelector('.carousel-inner');
        const items = carouselContainer.querySelectorAll('.carousel-item');
        const prevBtn = carouselContainer.querySelector('.carousel-control-prev');
        const nextBtn = carouselContainer.querySelector('.carousel-control-next');
        const slideIndicator = carouselContainer.querySelector('.slide-indicator');

        // Reset carousel position
        currentIndex = 0;
        totalItems = items.length;

        // Update carousel position
        function updateCarousel() {
          carouselInner.style.transform = `translateX(-${currentIndex * 100}%)`;
          slideIndicator.textContent = `Slide ${currentIndex + 1} of ${totalItems}`;
        }

        // Update slide indicator text initially
        updateCarousel();

        // Event listeners
        prevBtn.addEventListener('click', function () {
          currentIndex = (currentIndex > 0) ? currentIndex - 1 : totalItems - 1;
          updateCarousel();
        });

        nextBtn.addEventListener('click', function () {
          currentIndex = (currentIndex < totalItems - 1) ? currentIndex + 1 : 0;
          updateCarousel();
        });
      }

      // Keyboard navigation
      document.addEventListener('keydown', function (e) {
        if (foodModal.classList.contains('hidden')) return;

        if (e.key === 'Escape') {
          closeModal();
        } else if (e.key === 'ArrowRight') {
          document.querySelector('.carousel-control-next').click();
        } else if (e.key === 'ArrowLeft') {
          document.querySelector('.carousel-control-prev').click();
        } else if (e.key === 'Enter') {
          useSelectedBtn.click();
        }
      });
    });
  </script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Spa carousel modal functionality
      const spaButton = document.getElementById('spa-template-button');
      const spaModal = document.getElementById('spa-carousel-modal');
      const closeSpaBtn = document.getElementById('close-spa-modal');
      const cancelSpaBtn = document.getElementById('cancel-spa-selection');
      const useSelectedSpaBtn = document.getElementById('use-selected-spa');
      const spaModalBackdrop = document.getElementById('spa-modal-backdrop');

      // Variables for spa carousel
      let spaCurrentIndex = 0;
      let spaTotalItems = 0;

      // Open modal when spa button is clicked
      if (spaButton) {
        spaButton.addEventListener('click', function (e) {
          e.stopPropagation(); // Prevent event bubbling
          spaModal.classList.remove('hidden');

          // Initialize carousel
          initSpaCarousel();
        });
      }

      // Close modal functions
      function closeSpaModal() {
        spaModal.classList.add('hidden');
      }

      // Close buttons
      if (closeSpaBtn) closeSpaBtn.addEventListener('click', closeSpaModal);
      if (cancelSpaBtn) cancelSpaBtn.addEventListener('click', closeSpaModal);
      if (spaModalBackdrop) spaModalBackdrop.addEventListener('click', closeSpaModal);

      // Use selected spa service
      if (useSelectedSpaBtn) {
        useSelectedSpaBtn.addEventListener('click', function () {
          // Get currently visible carousel item
          const visibleItem = document.querySelector('#spa-carousel-modal .carousel-item:nth-child(' + (spaCurrentIndex + 1) + ')');
          const spaName = visibleItem.querySelector('h3').textContent;
          const spaPrice = visibleItem.querySelector('p').textContent;

          // Get the message input field
          const messageInput = document.getElementById('message-input');
          if (messageInput) {
            // Add spa information to the message
            messageInput.value = `I'd like to book the ${spaName} treatment (${spaPrice}).`;

            // Trigger auto-resize if that function exists
            if (typeof autoResizeTextarea === 'function') {
              autoResizeTextarea();
            }
          }

          // Close the modal
          closeSpaModal();
        });
      }

      // Spa carousel functionality
      function initSpaCarousel() {
        const carouselContainer = document.querySelector('.carousel-container[data-carousel="spa"]');
        if (!carouselContainer) return;

        const carouselInner = carouselContainer.querySelector('.carousel-inner');
        const items = carouselContainer.querySelectorAll('.carousel-item');
        const prevBtn = carouselContainer.querySelector('.carousel-control-prev');
        const nextBtn = carouselContainer.querySelector('.carousel-control-next');
        const slideIndicator = carouselContainer.querySelector('.slide-indicator');

        // Reset carousel position
        spaCurrentIndex = 0;
        spaTotalItems = items.length;

        // Update carousel position
        function updateSpaCarousel() {
          carouselInner.style.transform = `translateX(-${spaCurrentIndex * 100}%)`;
          slideIndicator.textContent = `Slide ${spaCurrentIndex + 1} of ${spaTotalItems}`;
        }

        // Update slide indicator text initially
        updateSpaCarousel();

        // Event listeners
        prevBtn.addEventListener('click', function () {
          spaCurrentIndex = (spaCurrentIndex > 0) ? spaCurrentIndex - 1 : spaTotalItems - 1;
          updateSpaCarousel();
        });

        nextBtn.addEventListener('click', function () {
          spaCurrentIndex = (spaCurrentIndex < spaTotalItems - 1) ? spaCurrentIndex + 1 : 0;
          updateSpaCarousel();
        });
      }

      // Keyboard navigation
      document.addEventListener('keydown', function (e) {
        if (spaModal.classList.contains('hidden')) return;

        if (e.key === 'Escape') {
          closeSpaModal();
        } else if (e.key === 'ArrowRight') {
          document.querySelector('#spa-carousel-modal .carousel-control-next').click();
        } else if (e.key === 'ArrowLeft') {
          document.querySelector('#spa-carousel-modal .carousel-control-prev').click();
        } else if (e.key === 'Enter') {
          useSelectedSpaBtn.click();
        }
      });
    });
  </script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Massage carousel modal functionality
      const massageButton = document.getElementById('massage-template-button');
      const massageModal = document.getElementById('massage-carousel-modal');
      const closeMassageBtn = document.getElementById('close-massage-modal');
      const cancelMassageBtn = document.getElementById('cancel-massage-selection');
      const useSelectedMassageBtn = document.getElementById('use-selected-massage');
      const massageModalBackdrop = document.getElementById('massage-modal-backdrop');

      // Variables for massage carousel
      let massageCurrentIndex = 0;
      let massageTotalItems = 0;

      // Open modal when massage button is clicked
      if (massageButton) {
        massageButton.addEventListener('click', function (e) {
          e.stopPropagation(); // Prevent event bubbling
          massageModal.classList.remove('hidden');

          // Initialize carousel
          initMassageCarousel();
        });
      }

      // Close modal functions
      function closeMassageModal() {
        massageModal.classList.add('hidden');
      }

      // Close buttons
      if (closeMassageBtn) closeMassageBtn.addEventListener('click', closeMassageModal);
      if (cancelMassageBtn) cancelMassageBtn.addEventListener('click', closeMassageModal);
      if (massageModalBackdrop) massageModalBackdrop.addEventListener('click', closeMassageModal);

      // Use selected massage service
      if (useSelectedMassageBtn) {
        useSelectedMassageBtn.addEventListener('click', function () {
          // Get currently visible carousel item
          const visibleItem = document.querySelector('#massage-carousel-modal .carousel-item:nth-child(' + (massageCurrentIndex + 1) + ')');
          const massageName = visibleItem.querySelector('h3').textContent;
          const massagePrice = visibleItem.querySelector('p').textContent;

          // Get the message input field
          const messageInput = document.getElementById('message-input');
          if (messageInput) {
            // Add massage information to the message
            messageInput.value = `I'd like to book the ${massageName} (${massagePrice}).`;

            // Trigger auto-resize if that function exists
            if (typeof autoResizeTextarea === 'function') {
              autoResizeTextarea();
            }
          }

          // Close the modal
          closeMassageModal();
        });
      }

      // Massage carousel functionality
      function initMassageCarousel() {
        const carouselContainer = document.querySelector('.carousel-container[data-carousel="massage"]');
        if (!carouselContainer) return;

        const carouselInner = carouselContainer.querySelector('.carousel-inner');
        const items = carouselContainer.querySelectorAll('.carousel-item');
        const prevBtn = carouselContainer.querySelector('.carousel-control-prev');
        const nextBtn = carouselContainer.querySelector('.carousel-control-next');
        const slideIndicator = carouselContainer.querySelector('.slide-indicator');

        // Reset carousel position
        massageCurrentIndex = 0;
        massageTotalItems = items.length;

        // Update carousel position
        function updateMassageCarousel() {
          carouselInner.style.transform = `translateX(-${massageCurrentIndex * 100}%)`;
          slideIndicator.textContent = `Slide ${massageCurrentIndex + 1} of ${massageTotalItems}`;
        }

        // Update slide indicator text initially
        updateMassageCarousel();

        // Event listeners
        prevBtn.addEventListener('click', function () {
          massageCurrentIndex = (massageCurrentIndex > 0) ? massageCurrentIndex - 1 : massageTotalItems - 1;
          updateMassageCarousel();
        });

        nextBtn.addEventListener('click', function () {
          massageCurrentIndex = (massageCurrentIndex < massageTotalItems - 1) ? massageCurrentIndex + 1 : 0;
          updateMassageCarousel();
        });
      }

      // Keyboard navigation
      document.addEventListener('keydown', function (e) {
        if (massageModal.classList.contains('hidden')) return;

        if (e.key === 'Escape') {
          closeMassageModal();
        } else if (e.key === 'ArrowRight') {
          document.querySelector('#massage-carousel-modal .carousel-control-next').click();
        } else if (e.key === 'ArrowLeft') {
          document.querySelector('#massage-carousel-modal .carousel-control-prev').click();
        } else if (e.key === 'Enter') {
          useSelectedMassageBtn.click();
        }
      });
    });
  </script>
</body>

</html>
