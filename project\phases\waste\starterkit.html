<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Dashboard</title>
      <!-- Add Geist font -->
      <link href="https://api.fontshare.com/v2/css?f[]=geist@400&display=swap" rel="stylesheet">
      <style>
         html, body, input, button, select, option, textarea {
         font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif !important;
         }
      </style>
      <script src="https://cdn.tailwindcss.com"></script>
      <link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/slate.min.css"/>
      <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit.min.js"></script>
      <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit-icons.min.js"></script>
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css" integrity="sha512-xh6O/CkQoPOWDdYTDqeRzpZIEgenhKcnPmTRcOCbasIUlC/XNK5kE0942Pbe7RM0e/uUR9RCJr/j/qJ8EowPMTA==" crossorigin="anonymous" referrerpolicy="no-referrer"/>
      <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
      <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation"></script>
      <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
      <link rel="stylesheet" href="../static/styles/custom.css">
      <script src="../static/js/languagetranslator.js"></script>
      <script src="../static/js/themes.js"></script>
      <link rel="stylesheet" href="../static/styles/loadinganimations.css">
      <link rel="stylesheet" href="../static/styles/scrollbar.css">
      <script src="../static/js/loading.js"></script>
   </head>
   <body class="light">
      <style>
         .sidebar {
         position: absolute;
         top: 0;
         left: 0;
         bottom: 0;
         width: 280px;
         overflow-y: auto;
         height: 100%;
         }
         /* Task list styles */
         #task-list {
         max-height: 225px;
         overflow-y: auto;
         scrollbar-width: thin;
         scrollbar-color: #A0AEC0 #EDF2F7;
         }
         #task-list::-webkit-scrollbar {
         width: 6px;
         }
         #task-list::-webkit-scrollbar-track {
         background: #EDF2F7;
         }
         #task-list::-webkit-scrollbar-thumb {
         background-color: #A0AEC0;
         border-radius: 3px;
         }
         .task {
         padding-bottom: 12px;
         margin-bottom: 12px;
         border-bottom: 1px solid #E5E7EB;
         height: 60px;
         }
         .task:last-child {
         border-bottom: none;
         padding-bottom: 0;
         margin-bottom: 0;
         }
         .done {
         opacity: 0.5;
         }
         .task.done label {
         color: #000;
         text-decoration: line-through;
         }
         .custom-checkbox {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            width: 16px;
            height: 16px;
            border: 1px solid #A0AEC0;
            border-radius: 3px;
            background-color: transparent;
            cursor: pointer;
            position: relative;
         }
         .custom-checkbox:checked::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 10px;
            height: 10px;
            background-color: #3182CE;
            border-radius: 2px;
         }
      </style>
      </head>
      <body class="light">
         <div id="loading-overlay" class="loading-overlay">
            <div class="loader"></div>
         </div>
         <div class="grid min-h-screen w-full overflow-hidden lg:grid-cols-[280px_1fr]">
         {% include 'sidebar.html' %}
         <div class="flex flex-col">
         <header class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-6">
            <div class="flex items-center gap-4">
               <a class="lg:hidden text-gray-600 hover:text-gray-800" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                     <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                     <path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1L21 9"></path>
                     <path d="M12 3v6"></path>
                  </svg>
                  <span class="sr-only">Home</span>
               </a>
               <h1 class="font-semibold text-lg">Google analytics</h1>
            </div>
            {% include 'topright.html' %}
         </header>
         <!-- Inter Font (used by shadcn/ui) -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Remix Icon CDN -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- ApexCharts CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/apexcharts@latest/dist/apexcharts.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50 p-4 md:p-6">
    <div class="container max-w-7xl mt-7 ml-7">
        

        <!-- KPI Card Section -->
        <div class="sm:max-w-2xl mb-6 bg-white p-6 rounded-lg border border-gray-120 shadow-sm">
            <!-- Header Section -->
            <div class="mb-4">
                <h3 class="text-sm text-gray-500">Total sales</h3>
                <p class="text-3xl font-semibold text-gray-900">$292,400</p>
            </div>

            <!-- Distribution Section -->
            <div class="mt-5">
                <h4 class="text-sm text-gray-500 mb-3">Sales channel distribution</h4>
                <!-- Progress Bar -->
                <div class="mt-3 flex h-1.5 w-full rounded-full bg-gray-100 overflow-hidden">
                    <div class="w-[34.4%] bg-blue-500"></div>
                    <div class="w-[30.6%] bg-orange-500"></div>
                    <div class="w-[20.9%] bg-sky-500"></div>
                    <div class="w-[14.1%] bg-purple-500"></div>
                </div>
            </div>

            <!-- Channel Cards Grid -->
            <dl class="mt-6 grid grid-cols-1 gap-3 sm:grid-cols-2">
                <!-- Direct Sales Card -->
                <div class="group relative bg-white p-4 rounded-md border border-gray-100 hover:border-gray-200 transition-all">
                    <div class="flex items-center space-x-2">
                        <span class="w-2.5 h-2.5 rounded-sm bg-blue-500"></span>
                        <dt class="text-sm text-gray-900">
                            <a href="#" class="focus:outline-none">
                                <span class="absolute inset-0" aria-hidden="true"></span>
                                Direct sales
                            </a>
                        </dt>
                    </div>
                    <dd class="mt-1 text-sm text-gray-700">
                        <span class="font-semibold">34.4%</span> · $100.5K
                    </dd>
                    <i class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-gray-600 transition-colors"></i>
                </div>

                <!-- Retail Stores Card -->
                <div class="group relative bg-white p-3 rounded-md border border-gray-100 hover:border-gray-200 transition-all">
                    <div class="flex items-center space-x-2">
                        <span class="w-2.5 h-2.5 rounded-sm bg-orange-500"></span>
                        <dt class="text-sm text-gray-900">
                            <a href="#" class="focus:outline-none">
                                <span class="absolute inset-0" aria-hidden="true"></span>
                                Retail stores
                            </a>
                        </dt>
                    </div>
                    <dd class="mt-1 text-sm text-gray-700">
                        <span class="font-semibold">30.6%</span> · $89.5K
                    </dd>
                    <i class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-gray-600 transition-colors"></i>
                </div>

                <!-- E-commerce Card -->
                <div class="group relative bg-white p-3 rounded-md border border-gray-100 hover:border-gray-200 transition-all">
                    <div class="flex items-center space-x-2">
                        <span class="w-2.5 h-2.5 rounded-sm bg-sky-500"></span>
                        <dt class="text-sm text-gray-900">
                            <a href="#" class="focus:outline-none">
                                <span class="absolute inset-0" aria-hidden="true"></span>
                                E-commerce
                            </a>
                        </dt>
                    </div>
                    <dd class="mt-1 text-sm text-gray-700">
                        <span class="font-semibold">20.9%</span> · $61.2K
                    </dd>
                    <i class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-gray-600 transition-colors"></i>
                </div>

                <!-- Wholesale Card -->
                <div class="group relative bg-white p-3 rounded-md border border-gray-100 hover:border-gray-200 transition-all">
                    <div class="flex items-center space-x-2">
                        <span class="w-2.5 h-2.5 rounded-sm bg-purple-500"></span>
                        <dt class="text-sm text-gray-900">
                            <a href="#" class="focus:outline-none">
                                <span class="absolute inset-0" aria-hidden="true"></span>
                                Wholesale
                            </a>
                        </dt>
                    </div>
                    <dd class="mt-1 text-sm text-gray-700">
                        <span class="font-semibold">14.1%</span> · $41.2K
                    </dd>
                    <i class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-gray-600 transition-colors"></i>
                </div>
            </dl>
        </div> 
</body>
</html>