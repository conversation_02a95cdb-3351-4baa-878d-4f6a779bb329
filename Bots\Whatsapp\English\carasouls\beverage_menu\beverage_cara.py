import requests
import os
import json

# Authentication credentials
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')

# Payload data
payload = {
    "friendly_name": "beverage_menu_cara",
    "language": "en",
    "types": {
        "twilio/carousel": {
            "body": "Explore Our Exquisite Beverage Selections!",
            "cards": [
                {
                    "title": "Beers",
                    "body": "Coronita, Estrella Galicia, Estrella Galicia 0.0% (Non-Alcoholic), Peroni",
                    "media": "https://images-new.vercel.app/beveragemenu/beers.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "View Beers",
                            "id": "view_beers"
                        }
                    ]
                },
                {
                    "title": "Cocktails",
                    "body": "Beatnik Spiritz, Floral Fusion, <PERSON>, Duck Pond, Ciudad Dei Sol, and more",
                    "media": "https://images-new.vercel.app/beveragemenu/cocktail.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "View Cocktails",
                            "id": "view_cocktails"
                        }
                    ]
                },
                {
                    "title": "Mocktails",
                    "body": "Mr Ginger, Berries Mojito, Summer Cooler",
                    "media": "https://images-new.vercel.app/beveragemenu/drinks.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "View Mocktails",
                            "id": "view_mocktails"
                        }
                    ]
                },
                {
                    "title": "Soda",
                    "body": "Creative Tonic Water, Zero Azucar Tonic, Exotic Yuzu Drink, Fever Tree Tonic",
                    "media": "https://images-new.vercel.app/beveragemenu/soda.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "View Soda",
                            "id": "view_soda"
                        }
                    ]
                },
                {
                    "title": "Water",
                    "body": "Premium water selections",
                    "media": "https://images-new.vercel.app/beveragemenu/water.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "View Water",
                            "id": "view_water"
                        }
                    ]
                },
                {
                    "title": "Sweet Wine",
                    "body": "Morentia Cream, Castano Tinto Dulce, Jose Pariente",
                    "media": "https://images-new.vercel.app/beveragemenu/wineeee22.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "View Sweet Wine",
                            "id": "view_sweet_wine"
                        }
                    ]
                },
                {
                    "title": "Red Wine",
                    "body": "Puro Red, Dos Marias, Gomez Cruzado, Viña Sastre Roble",
                    "media": "https://images-new.vercel.app/beveragemenu/wineeee22.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "View Red Wine",
                            "id": "view_red_wine"
                        }
                    ]
                },
                {
                    "title": "Rosé & White Wine",
                    "body": "Puro Rosé, Puro White, Le Bijou Sophie, Barbuntin, Jose Pariente",
                    "media": "https://images-new.vercel.app/beveragemenu/wineeee22.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "View Rosé & White Wine",
                            "id": "view_rose_white_wine"
                        }
                    ]
                },
                {
                    "title": "Sparkling Wine",
                    "body": "Moët & Chandon, Moët & Chandon Ice, Veuve Clicquot",
                    "media": "https://images-new.vercel.app/beveragemenu/wineeee22.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "View Sparkling Wine",
                            "id": "view_sparkling_wine"
                        }
                    ]
                }
            ]
        }
    }
}

# Make the POST request
response = requests.post(
    'https://content.twilio.com/v1/Content',
    auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN),
    json=payload
)

# Enhanced response handling
print("\n=== Response Details ===")
print(f"Status Code: {response.status_code}")
print("\n=== Headers ===")
for header, value in response.headers.items():
    print(f"{header}: {value}")

print("\n=== Response Content ===")
try:
    # Try to print formatted JSON
    print(json.dumps(response.json(), indent=2))
except json.JSONDecodeError:
    # If not JSON, print raw text
    print(response.text)

print("\n=== Request Details ===")
print(f"Request URL: {response.request.url}")
print(f"Request Method: {response.request.method}")
print("Request Headers:")
for header, value in response.request.headers.items():
    print(f"{header}: {value}")

print("\n=== Timing ===")
print(f"Elapsed Time: {response.elapsed.total_seconds()} seconds")

if response.status_code != 200:
    print("\n=== Error Details ===")
    print(f"Error Status Code: {response.status_code}")
    print("Error Response:", response.text)
