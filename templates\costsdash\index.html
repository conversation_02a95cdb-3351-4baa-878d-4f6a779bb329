<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-[#fbf9f9] group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
      <div class="layout-container flex h-full grow flex-col">
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f1e9ea] px-10 py-3">
          <div class="flex items-center gap-4 text-[#191010]">
            <div class="size-4">
              <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M13.8261 17.4264C16.7203 18.1174 20.2244 18.5217 24 18.5217C27.7756 18.5217 31.2797 18.1174 34.1739 17.4264C36.9144 16.7722 39.9967 15.2331 41.3563 14.1648L24.8486 40.6391C24.4571 41.267 23.5429 41.267 23.1514 40.6391L6.64374 14.1648C8.00331 15.2331 11.0856 16.7722 13.8261 17.4264Z"
                  fill="currentColor"
                ></path>
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M39.998 12.236C39.9944 12.2537 39.9875 12.2845 39.9748 12.3294C39.9436 12.4399 39.8949 12.5741 39.8346 12.7175C39.8168 12.7597 39.7989 12.8007 39.7813 12.8398C38.5103 13.7113 35.9788 14.9393 33.7095 15.4811C30.9875 16.131 27.6413 16.5217 24 16.5217C20.3587 16.5217 17.0125 16.131 14.2905 15.4811C12.0012 14.9346 9.44505 13.6897 8.18538 12.8168C8.17384 12.7925 8.16216 12.767 8.15052 12.7408C8.09919 12.6249 8.05721 12.5114 8.02977 12.411C8.00356 12.3152 8.00039 12.2667 8.00004 12.2612C8.00004 12.261 8 12.2607 8.00004 12.2612C8.00004 12.2359 8.0104 11.9233 8.68485 11.3686C9.34546 10.8254 10.4222 10.2469 11.9291 9.72276C14.9242 8.68098 19.1919 8 24 8C28.8081 8 33.0758 8.68098 36.0709 9.72276C37.5778 10.2469 38.6545 10.8254 39.3151 11.3686C39.9006 11.8501 39.9857 12.1489 39.998 12.236ZM4.95178 15.2312L21.4543 41.6973C22.6288 43.5809 25.3712 43.5809 26.5457 41.6973L43.0534 15.223C43.0709 15.1948 43.0878 15.1662 43.104 15.1371L41.3563 14.1648C43.104 15.1371 43.1038 15.1374 43.104 15.1371L43.1051 15.135L43.1065 15.1325L43.1101 15.1261L43.1199 15.1082C43.1276 15.094 43.1377 15.0754 43.1497 15.0527C43.1738 15.0075 43.2062 14.9455 43.244 14.8701C43.319 14.7208 43.4196 14.511 43.5217 14.2683C43.6901 13.8679 44 13.0689 44 12.2609C44 10.5573 43.003 9.22254 41.8558 8.2791C40.6947 7.32427 39.1354 6.55361 37.385 5.94477C33.8654 4.72057 29.133 4 24 4C18.867 4 14.1346 4.72057 10.615 5.94478C8.86463 6.55361 7.30529 7.32428 6.14419 8.27911C4.99695 9.22255 3.99999 10.5573 3.99999 12.2609C3.99999 13.1275 4.29264 13.9078 4.49321 14.3607C4.60375 14.6102 4.71348 14.8196 4.79687 14.9689C4.83898 15.0444 4.87547 15.1065 4.9035 15.1529C4.91754 15.1762 4.92954 15.1957 4.93916 15.2111L4.94662 15.223L4.95178 15.2312ZM35.9868 18.996L24 38.22L12.0131 18.996C12.4661 19.1391 12.9179 19.2658 13.3617 19.3718C16.4281 20.1039 20.0901 20.5217 24 20.5217C27.9099 20.5217 31.5719 20.1039 34.6383 19.3718C35.082 19.2658 35.5339 19.1391 35.9868 18.996Z"
                  fill="currentColor"
                ></path>
              </svg>
            </div>
            <h2 class="text-[#191010] text-lg font-bold leading-tight tracking-[-0.015em]">ServiceHub</h2>
          </div>
          <div class="flex flex-1 justify-end gap-8">
            <div class="flex items-center gap-9">
              <a class="text-[#191010] text-sm font-medium leading-normal" href="#">Dashboard</a>
              <a class="text-[#191010] text-sm font-medium leading-normal" href="#">Bookings</a>
              <a class="text-[#191010] text-sm font-medium leading-normal" href="#">Guests</a>
              <a class="text-[#191010] text-sm font-medium leading-normal" href="#">Staff</a>
              <a class="text-[#191010] text-sm font-medium leading-normal" href="#">Reports</a>
            </div>
            <button
              class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 bg-[#f1e9ea] text-[#191010] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
            >
              <div class="text-[#191010]" data-icon="Bell" data-size="20px" data-weight="regular">
                <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                  <path
                    d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z"
                  ></path>
                </svg>
              </div>
            </button>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBOFCncJV6dQkDHdZGm0XHx_bJ--pJWavwtdUZ7lhTA9So8Se8TNIBCf7ZHaKqY91qkdoUDgq1yyvBP9W7oP1ghc--qL3mu39Ha9zk9Uia9YMVpYdbQfaQeqFOpb1pTVafi5bBgjvzT8Fe9hR8xY73UJvIQaxPRStN1XFVqemf_bhuUmUhrY-nbbtmnspwV3491MfkYMkZhk2yENKJAyGrn5euNKuDw29ZlqHG4kOhsjQ2GbgDJSTy3n2pnH6qNsU0G8oem-UbUz2Tv");'
            ></div>
          </div>
        </header>
        <div class="px-40 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div class="flex flex-wrap justify-between gap-3 p-4"><p class="text-[#191010] tracking-light text-[32px] font-bold leading-tight min-w-72">Cost Overview</p></div>
            <h2 class="text-[#191010] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Infrastructure / Server Costs</h2>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#e3d4d4] bg-[#fbf9f9]">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-[#fbf9f9]">
                      <th class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-120 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">Service</th>
                      <th class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-240 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">
                        Monthly Cost
                      </th>
                      <th class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-360 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">
                        Trend (3 Months)
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">EC2</td>
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$1,200</td>
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">Up 5%</td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">S3</td>
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$300</td>
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">Stable</td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">RDS</td>
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$800</td>
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">Down 2%</td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">Lambda</td>
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$150</td>
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">Up 10%</td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">
                        CloudFront
                      </td>
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$250</td>
                      <td class="table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">Stable</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-120{display: none;}}
                @container(max-width:240px){.table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-240{display: none;}}
                @container(max-width:360px){.table-1d30de72-ed28-4749-be5e-7c5daaf9b96d-column-360{display: none;}}
              </style>
            </div>
            <div class="flex flex-wrap gap-4 p-4">
              <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#f1e9ea]">
                <p class="text-[#191010] text-base font-medium leading-normal">Total Infrastructure Cost</p>
                <p class="text-[#191010] tracking-light text-2xl font-bold leading-tight">$2,700</p>
              </div>
            </div>
            <h2 class="text-[#191010] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Communication Channels</h2>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#e3d4d4] bg-[#fbf9f9]">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-[#fbf9f9]">
                      <th class="table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-120 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">
                        Provider
                      </th>
                      <th class="table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-240 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">Channel</th>
                      <th class="table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-360 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">
                        Cost per Engagement
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">Twilio</td>
                      <td class="table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-240 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">SMS</td>
                      <td class="table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">
                        $0.05 per message
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">Twilio</td>
                      <td class="table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-240 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">Voice</td>
                      <td class="table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">
                        $0.10 per minute
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">Twilio</td>
                      <td class="table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-240 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">WhatsApp</td>
                      <td class="table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">
                        $0.08 per session
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-120{display: none;}}
                @container(max-width:240px){.table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-240{display: none;}}
                @container(max-width:360px){.table-1415b01d-458c-4be7-b709-d23f2d90f26b-column-360{display: none;}}
              </style>
            </div>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#e3d4d4] bg-[#fbf9f9]">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-[#fbf9f9]">
                      <th class="table-aeadeee3-87b7-4eb6-bb50-d96bca45f1ff-column-120 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">Channel</th>
                      <th class="table-aeadeee3-87b7-4eb6-bb50-d96bca45f1ff-column-240 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">
                        Tool Cost
                      </th>
                      <th class="table-aeadeee3-87b7-4eb6-bb50-d96bca45f1ff-column-360 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">
                        Sessions per Month
                      </th>
                      <th class="table-aeadeee3-87b7-4eb6-bb50-d96bca45f1ff-column-480 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">
                        Cost per Session
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-aeadeee3-87b7-4eb6-bb50-d96bca45f1ff-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">Webchat</td>
                      <td class="table-aeadeee3-87b7-4eb6-bb50-d96bca45f1ff-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$500</td>
                      <td class="table-aeadeee3-87b7-4eb6-bb50-d96bca45f1ff-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">5,000</td>
                      <td class="table-aeadeee3-87b7-4eb6-bb50-d96bca45f1ff-column-480 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$0.10</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-aeadeee3-87b7-4eb6-bb50-d96bca45f1ff-column-120{display: none;}}
                @container(max-width:240px){.table-aeadeee3-87b7-4eb6-bb50-d96bca45f1ff-column-240{display: none;}}
                @container(max-width:360px){.table-aeadeee3-87b7-4eb6-bb50-d96bca45f1ff-column-360{display: none;}}
                @container(max-width:480px){.table-aeadeee3-87b7-4eb6-bb50-d96bca45f1ff-column-480{display: none;}}
              </style>
            </div>
            <div class="flex flex-wrap gap-4 p-4">
              <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#f1e9ea]">
                <p class="text-[#191010] text-base font-medium leading-normal">Cost per Engagement by Channel</p>
                <p class="text-[#191010] tracking-light text-2xl font-bold leading-tight">SMS: $0.05, Voice: $0.10, WhatsApp: $0.08, Webchat: $0.10</p>
              </div>
            </div>
            <h2 class="text-[#191010] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">AI &amp; External APIs</h2>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#e3d4d4] bg-[#fbf9f9]">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-[#fbf9f9]">
                      <th class="table-b0194013-36c3-43a2-9333-2207bd183865-column-120 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">API</th>
                      <th class="table-b0194013-36c3-43a2-9333-2207bd183865-column-240 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">
                        Function
                      </th>
                      <th class="table-b0194013-36c3-43a2-9333-2207bd183865-column-360 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">Cost</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-b0194013-36c3-43a2-9333-2207bd183865-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">OpenAI</td>
                      <td class="table-b0194013-36c3-43a2-9333-2207bd183865-column-240 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">Chat</td>
                      <td class="table-b0194013-36c3-43a2-9333-2207bd183865-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">
                        $0.002 per 1K tokens
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-b0194013-36c3-43a2-9333-2207bd183865-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">OpenAI</td>
                      <td class="table-b0194013-36c3-43a2-9333-2207bd183865-column-240 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">
                        Recommendations
                      </td>
                      <td class="table-b0194013-36c3-43a2-9333-2207bd183865-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">
                        $0.0015 per 1K tokens
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-b0194013-36c3-43a2-9333-2207bd183865-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">
                        Google AI
                      </td>
                      <td class="table-b0194013-36c3-43a2-9333-2207bd183865-column-240 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">Chat</td>
                      <td class="table-b0194013-36c3-43a2-9333-2207bd183865-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">
                        $0.0025 per 1K tokens
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-b0194013-36c3-43a2-9333-2207bd183865-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">OTA APIs</td>
                      <td class="table-b0194013-36c3-43a2-9333-2207bd183865-column-240 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">Booking</td>
                      <td class="table-b0194013-36c3-43a2-9333-2207bd183865-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">
                        $0.50 per transaction
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-b0194013-36c3-43a2-9333-2207bd183865-column-120{display: none;}}
                @container(max-width:240px){.table-b0194013-36c3-43a2-9333-2207bd183865-column-240{display: none;}}
                @container(max-width:360px){.table-b0194013-36c3-43a2-9333-2207bd183865-column-360{display: none;}}
              </style>
            </div>
            <div class="flex flex-wrap gap-4 p-4">
              <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#f1e9ea]">
                <p class="text-[#191010] text-base font-medium leading-normal">AI API Cost</p>
                <p class="text-[#191010] tracking-light text-2xl font-bold leading-tight">$1,500</p>
              </div>
              <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#f1e9ea]">
                <p class="text-[#191010] text-base font-medium leading-normal">OTA API Cost</p>
                <p class="text-[#191010] tracking-light text-2xl font-bold leading-tight">$2,000</p>
              </div>
            </div>
            <h2 class="text-[#191010] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">SaaS &amp; Third-party Tools</h2>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#e3d4d4] bg-[#fbf9f9]">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-[#fbf9f9]">
                      <th class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-120 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">Tool</th>
                      <th class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-240 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">
                        Subscription
                      </th>
                      <th class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-360 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">Cost</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">CRM</td>
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">Monthly</td>
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$200</td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">
                        Marketing Tools
                      </td>
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">Monthly</td>
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$150</td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">
                        Project Tools
                      </td>
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">Monthly</td>
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$100</td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">
                        Analytics
                      </td>
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">Monthly</td>
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$50</td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">Support</td>
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">Monthly</td>
                      <td class="table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$75</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-120{display: none;}}
                @container(max-width:240px){.table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-240{display: none;}}
                @container(max-width:360px){.table-6e809192-cc5a-48e4-bb54-c122b3f66220-column-360{display: none;}}
              </style>
            </div>
            <div class="flex flex-wrap gap-4 p-4">
              <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#f1e9ea]">
                <p class="text-[#191010] text-base font-medium leading-normal">Total SaaS &amp; Third-party Tools Cost</p>
                <p class="text-[#191010] tracking-light text-2xl font-bold leading-tight">$575</p>
              </div>
            </div>
            <h2 class="text-[#191010] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Operational Cost Metrics</h2>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#e3d4d4] bg-[#fbf9f9]">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-[#fbf9f9]">
                      <th class="table-535be9a0-0efe-47e3-b957-ac0b7876ad87-column-120 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">Metric</th>
                      <th class="table-535be9a0-0efe-47e3-b957-ac0b7876ad87-column-240 px-4 py-3 text-left text-[#191010] w-[400px] text-sm font-medium leading-normal">Cost</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-535be9a0-0efe-47e3-b957-ac0b7876ad87-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">
                        Per Guest Interaction
                      </td>
                      <td class="table-535be9a0-0efe-47e3-b957-ac0b7876ad87-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$0.25</td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-535be9a0-0efe-47e3-b957-ac0b7876ad87-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">
                        Per Booking
                      </td>
                      <td class="table-535be9a0-0efe-47e3-b957-ac0b7876ad87-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$1.50</td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-535be9a0-0efe-47e3-b957-ac0b7876ad87-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">
                        Staff Dashboard
                      </td>
                      <td class="table-535be9a0-0efe-47e3-b957-ac0b7876ad87-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$500</td>
                    </tr>
                    <tr class="border-t border-t-[#e3d4d4]">
                      <td class="table-535be9a0-0efe-47e3-b957-ac0b7876ad87-column-120 h-[72px] px-4 py-2 w-[400px] text-[#191010] text-sm font-normal leading-normal">
                        Platform Upkeep
                      </td>
                      <td class="table-535be9a0-0efe-47e3-b957-ac0b7876ad87-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8b5b5c] text-sm font-normal leading-normal">$1,000</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-535be9a0-0efe-47e3-b957-ac0b7876ad87-column-120{display: none;}}
                @container(max-width:240px){.table-535be9a0-0efe-47e3-b957-ac0b7876ad87-column-240{display: none;}}
              </style>
            </div>
            <div class="flex flex-wrap gap-4 p-4">
              <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#f1e9ea]">
                <p class="text-[#191010] text-base font-medium leading-normal">Total Operational Costs</p>
                <p class="text-[#191010] tracking-light text-2xl font-bold leading-tight">$1,750</p>
              </div>
            </div>
            <h2 class="text-[#191010] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Alerts &amp; Trends</h2>
            <p class="text-[#191010] text-base font-normal leading-normal pb-3 pt-1 px-4">
              High-cost areas: Infrastructure (EC2), AI APIs (Chat), OTA APIs. Monthly spikes observed in server costs due to increased traffic. Suggestions: Optimize EC2
              instances, explore alternative AI API providers, negotiate OTA API fees.
            </p>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
