<script src="https://cdn.tailwindcss.com"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
<link rel="stylesheet" href="../static/styles/custom.css">
<link rel="stylesheet" href="../static/styles/loadinganimations.css">
<script src="../static/js/languagetranslator.js" defer></script>
<script src="../static/js/loading.js" defer></script>
<script src="../static/js/themes.js" defer></script>
<link rel="stylesheet" href="../static/styles/scrollbar.css">

<link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/slate.min.css"/>
<script src="https://unpkg.com/@phosphor-icons/web"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
<script src="https://cdn.amcharts.com/lib/4/core.js"></script>
<script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
<script src="https://kit.fontawesome.com/c6c71387b9.js" crossorigin="anonymous"></script>
<script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
<link rel="preconnect" href="https://rsms.me/" />
<link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
<link rel="stylesheet" href="../static/styles/custom.css">
<script src="../static/js/languagetranslator.js" defer></script>
<script src="https://cdn.jsdelivr.net/npm/uikit@latest/dist/js/uikit.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/uikit@latest/dist/js/uikit-icons.min.js"></script>
<link rel="stylesheet" href="../static/styles/loadinganimations.css">
<link rel="stylesheet" href="../static/styles/chartdropdown.css">
<script src="../static/js/loading.js" defer></script>
<script src="../static/js/chartsdropsdown.js" defer></script>
<script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
<script src="https://cdn.amcharts.com/lib/4/themes/dark.js"></script>
<link rel="stylesheet" href="../static/styles/scrollbar.css">
<link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/slate.min.css"/>
<body class="light">
  <div id="loading-overlay" class="loading-overlay">
    <div class="typing-indicator">
      <div class="typing-circle"></div>
      <div class="typing-circle"></div>
      <div class="typing-circle"></div>
      <div class="typing-shadow"></div>
      <div class="typing-shadow"></div>
      <div class="typing-shadow"></div>
    </div>
  </div>
  <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] overflow-hidden">
    {% include 'sidebar.html' %}
    <div class="flex flex-col">
      <header class="flex h-14 lg:h-[60px] items-center gap-4 border-b  px-6 card justify-between">
        <a class="lg:hidden" href="#">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                <path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9"></path>
                <path d="M12 3v6"></path>
            </svg>
            <span class="sr-only">Home</span>

        </a>
        <h1 class="font-semibold text-lg dark:">AI analytics and configuration</h1>    
        {% include 'topright.html' %}            
    </header>
        <main class="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6">
          <div class="grid gap-4 md:grid-cols-1 lg:grid-cols-1">
            <div class="rounded-lg border  shadow-sm card" data-v0-t="card">
              <div class="flex flex-col space-y-1.5 p-6">
                <h3 class="whitespace-nowrap text-2xl font-semibold leading-none tracking-tight">Chatbot configuration</h3>
                <p class="text-sm text-muted-foreground">
                  Please change the settings for the AI chatbot as per your needs.
                </p>
              </div>
              <div class="p-6">
                <form class="grid gap-4">
                  <div class="grid grid-cols-2 gap-4">
                    <div class="grid gap-2">
                      <label
                        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        for="hotel-name"
                      >
                      Name
                      </label>
                      <input
                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        id="hotel-name"
                        placeholder="Enter AI name....."
                      />
                    </div>
                    <div class="grid gap-2">
                      <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="urgency">
                          Model
                      </label>
                      <div class="relative w-full">
                        <div id="urgency-selector" class="theme-responsive-button theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer">
                          <span id="selected-option">Llama 3</span>
                          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                          </svg>
                        </div>
                        <ul id="urgency-options" class="absolute z-50 hidden w-full py-1 mt-1 overflow-auto text-sm bg-background rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none">
                          <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="Low">GPT-4o</li>
                          <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="Medium">Claude 3.5 Sonnet</li>
                          <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="High">Llama 3 (recommended)</li>
                        </ul>
                      </div>

                      <script>
                        document.addEventListener('DOMContentLoaded', function() {
                          const selector = document.getElementById('urgency-selector');
                          const options = document.getElementById('urgency-options');
                          const selectedOption = document.getElementById('selected-option');
                        
                          selector.addEventListener('click', function(event) {
                            event.stopPropagation();
                            options.classList.toggle('hidden');
                          });
                        
                          options.querySelectorAll('li').forEach(option => {
                            option.addEventListener('click', function(event) {
                              event.stopPropagation();
                              selectedOption.textContent = this.textContent;
                              options.classList.add('hidden');
                            });
                          });
                        
                          // Close the dropdown when clicking outside
                          document.addEventListener('click', function() {
                            options.classList.add('hidden');
                          });
                        });
                      </script>
                      <style>
                        /* Add this style block to your CSS */
                        #temperature {
                          margin-top: 20px; /* Add spacing at the top */
                          margin-bottom: 20px; /* Add spacing at the bottom */
                          -webkit-appearance: none;
                          appearance: none;
                          width: 100%;
                          height: 2px;
                          background: #e0e0e0;
                          outline: none;
                          opacity: 0.7;
                          transition: opacity .2s;
                        }
                      
                        #temperature::-webkit-slider-thumb {
                          -webkit-appearance: none;
                          appearance: none;
                          width: 20px;
                          height: 20px;
                          background: black; /* Change thumb color to black */
                          cursor: pointer;
                          border-radius: 50%;
                        }
                      
                        #temperature::-moz-range-thumb {
                          width: 20px;
                          height: 20px;
                          background: black; /* Change thumb color to black */
                          cursor: pointer;
                          border-radius: 50%;
                        }
                      </style>
                  </div>                
                  
                  </div>
                  <div class="grid gap-2">
                    <label
                      class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      for="description"
                    >
                      Prompt
                    </label>
                    <textarea
                      class="flex min-h-[120px] max-h-[200px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      id="description"
                      placeholder="Enter a detailed prompt for the AI chatbot"
                    ></textarea>
                  </div>

                  <!-- Replace the file upload section with this new layout -->
                  <div class="grid grid-cols-2 gap-4">
                    <!-- Temperature Slider Section -->
                    <div>
                    <div class="flex items-center gap-2">
                      <h3 class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                        Temperature : &nbsp;<span id="temperatureValue">0.5</span>
                      </h3>
                      <div class="group">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div class="absolute translate-y-1 bg-black text-white text-xs rounded py-1 px-2 hidden group-hover:block w-48 text-center">
                          Adjust the temperature to control randomness in AI responses.
                        </div>
                      </div>
                    </div>
                    <div class="p-4 rounded-md h-24 flex items-center mt-2 border card">
                      <input
                        type="range"
                        id="temperature"
                        name="temperature"
                        min="0.1"
                        max="0.9"
                        step="0.1"
                        value="0.5"
                        class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        oninput="updateTemperatureValue(this.value)"
                      >
                    </div>

                    <!-- Added "Know more about it" Text -->
                    <p class="text-sm text-muted-foreground mt-2">
                      Know more about temperature<a href="https://community.openai.com/t/cheat-sheet-mastering-temperature-and-top-p-in-chatgpt-api/172683" class="text-blue-500 hover:underline ml-1">here</a>.
                    </p>
                    </div>

                    <!-- Add this script to enable real-time updates -->
                    <script>
                    function updateTemperatureValue(value) {
                      document.getElementById('temperatureValue').textContent = value;
                    }

                    // Initialize the temperature display on page load
                    document.addEventListener('DOMContentLoaded', function() {
                      const temperatureSlider = document.getElementById('temperature');
                      const temperatureDisplay = document.getElementById('temperatureValue');
                      
                      // Set initial value
                      temperatureDisplay.textContent = temperatureSlider.value;
                      
                      // Update display on input
                      temperatureSlider.addEventListener('input', function() {
                        temperatureDisplay.textContent = this.value;
                      });
                    });
                    </script>
                    
                    <div class="grid gap-2">
                      <div class="flex items-center">
                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                          Knowledge Base
                        </label>
                        <div class="relative ml-2 group">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 text-xs rounded py-1 px-2 hidden group-hover:block w-48 text-center">
                            Upload your knowledge base PDF here for the AI to learn from.
                          </div>
                        </div>
                      </div>
                      <div class="rounded-md border card border-input p-4 relative">
                        <!-- Clear Button -->
                        <button
                          type="button" 
                          id="clear-file-1"
                          class="absolute top-2 right-8  hidden" style="background-color: transparent"
                          onclick="clearFile('file-upload-1', 'file-name-1', 'icon-1', 'clear-file-1')"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                    
                        <!-- Edit Button -->
                        <button
                          type="button" 
                          id="edit-file-1"
                          class="absolute top-2 right-2 hidden"  style="background-color: transparent"
                          onclick="openEditOverlay()"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5M18.5 2.5a2.121 2.121 0 113 3L12 15l-4 1 1-4 9.5-9.5z" />
                          </svg>
                          <span class="sr-only">Edit File</span>
                        </button>
                    
                        <!-- File Upload Label -->
                        <label for="file-upload-1" class="flex flex-col items-center justify-center cursor-pointer">
                          <svg id="icon-1" xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                          </svg>
                          <span id="file-name-1" class="text-sm font-medium">Upload Text File</span>
                          <input
                            id="file-upload-1"
                            type="file"
                            class="hidden"
                            accept=".txt"
                            onchange="updateFileName(this, 'file-name-1', 'icon-1', 'clear-file-1'); showEditButton()"
                          />
                        </label>
                      </div>
                      <p class="text-sm text-muted-foreground mt-0">
                        Know more about knowledge base
                        <a href="https://community.openai.com/t/how-knowledge-base-files-are-handled-assistants-api/601721" class="text-blue-500 hover:underline ml-1">here</a>.
                      </p>
                    </div>
                    
                    <!-- Edit Overlay Modal -->
                    <div id="edit-overlay" class="fixed inset-0 bg-opacity-50 flex items-center justify-center hidden">
                      <div class=" overlay-card rounded-lg shadow-lg w-11/12 md:w-2/3 lg:w-1/2">
                        <div class="flex justify-between items-center p-4 border-b card">
                          <h2 class="text-lg font-semibold">Edit Knowledge Base</h2>
                          <button type="button" onclick="closeEditOverlay()" class="text-gray-500 hover:text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </div>
                        <div class="p-4">
                          <textarea id="edit-textarea" class="w-full h-64 p-2 border rounded" placeholder="Loading..."></textarea>
                        </div>
                        <div class="flex justify-end p-4 border-t card">
                          <button type="button" onclick="saveEditedText()" class="mr-2 px-4 py-2 special-button">Save</button>
                          <button type="button" onclick="closeEditOverlay()" class="px-4 py-2 special-button2">Cancel</button>
                        </div>
                      </div>
                    </div>

                    <style>
                      /* Edit Overlay Styles */
                    #edit-overlay {
                      z-index: 50; /* Ensure it's above other elements */
                    }

                    #edit-overlay {
                      max-height: 90vh;
                      overflow-y: auto;
                    }
                                        </style>
                    <script>
                      let currentFileContent = '';

                      function showEditButton() {
                        document.getElementById('edit-file-1').classList.remove('hidden');
                      }

                      function openEditOverlay() {
                        const fileInput = document.getElementById('file-upload-1');
                        const file = fileInput.files[0];
                        if (!file) {
                          alert('No file uploaded.');
                          return;
                        }

                        const reader = new FileReader();
                        reader.onload = function(e) {
                          currentFileContent = e.target.result;
                          document.getElementById('edit-textarea').value = currentFileContent;
                          document.getElementById('edit-overlay').classList.remove('hidden');
                        };
                        reader.readAsText(file);
                      }

                      function closeEditOverlay() {
                        document.getElementById('edit-overlay').classList.add('hidden');
                      }

                      function saveEditedText() {
                        const editedContent = document.getElementById('edit-textarea').value;
                        currentFileContent = editedContent;
                        const blob = new Blob([editedContent], { type: 'text/plain' });
                        const fileInput = document.getElementById('file-upload-1');
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(new File([blob], fileInput.files[0]?.name || 'edited-file.txt', { type: 'text/plain' }));
                        fileInput.files = dataTransfer.files;

                        // Update the file name display if needed
                        // document.getElementById('file-name-1').textContent = fileInput.files[0].name;

                        closeEditOverlay();
                        alert('File updated successfully. Don\'t forget to submit the changes!');
                      }
                    </script>
                  </div>
                  
                  <script>
                  function updateFileName(input, spanId, iconId, clearBtnId) {
                    const fileName = input.files[0]?.name;
                    const fileNameSpan = document.getElementById(spanId);
                    const iconSvg = document.getElementById(iconId);
                    const clearBtn = document.getElementById(clearBtnId);
                    
                    if (fileName) {
                      fileNameSpan.textContent = fileName;
                      // Change the icon to a file icon
                      iconSvg.innerHTML = `
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      `;
                      clearBtn.classList.remove('hidden');
                    } else {
                      fileNameSpan.textContent = 'Upload Text File';
                      // Revert to the original upload icon
                      iconSvg.innerHTML = `
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      `;
                      clearBtn.classList.add('hidden');
                    }
                  }
                  
                  function clearFile(inputId, spanId, iconId, clearBtnId) {
                    const fileInput = document.getElementById(inputId);
                    fileInput.value = ''; // Clear the file input
                    updateFileName(fileInput, spanId, iconId, clearBtnId);
                  }
                  </script>
              
                  <button
                    class="inline-flex items-center justify-center whitespace-nowrap rounded-md bg-black px-4 py-2 text-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-gray-800"
                    type="submit"
                  >
                    Submit changes
                  </button>
                </form>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  </div>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
        const toggleButton = document.getElementById('urgency-button');
        const dropdownMenu = document.getElementById('urgency-options');

        toggleButton.addEventListener('click', function () {
            const isExpanded = toggleButton.getAttribute('aria-expanded') === 'true';
            toggleButton.setAttribute('aria-expanded', !isExpanded);
            dropdownMenu.style.display = isExpanded ? 'none' : 'block';
        });

        dropdownMenu.addEventListener('click', function (event) {
            if (event.target.tagName.toLowerCase() === 'li') {
                const selectedOption = event.target.textContent;
                toggleButton.querySelector('span').textContent = selectedOption;
                toggleButton.setAttribute('aria-expanded', 'false');
                dropdownMenu.style.display = 'none';
            }
        });

        document.addEventListener('click', function (event) {
            if (!toggleButton.contains(event.target) && !dropdownMenu.contains(event.target)) {
                toggleButton.setAttribute('aria-expanded', 'false');
                dropdownMenu.style.display = 'none';
            }
        });
    });

    // Theme switching functionality
    document.addEventListener('DOMContentLoaded', function() {
        const themeSelector = document.querySelector('.theme-selector');
        const themeOptions = document.querySelectorAll('.theme-option');
        const themeSelectorBtn = themeSelector.querySelector('.theme-selector-btn');

        // Function to apply theme
        function applyTheme(themeName) {
            document.body.className = themeName;
            localStorage.setItem('selectedTheme', themeName);
            themeSelectorBtn.innerHTML = '<i class="fas fa-palette"></i> ' + themeName.charAt(0).toUpperCase() + themeName.slice(1).replace('-', ' ');
        }

        // Check if there's a saved theme in localStorage
        const savedTheme = localStorage.getItem('selectedTheme');

        // Apply saved theme or default to 'light'
        if (savedTheme) {
            applyTheme(savedTheme);
        } else {
            applyTheme('light');
        }

        themeOptions.forEach(option => {
            option.addEventListener('click', function() {
                const selectedTheme = this.getAttribute('data-theme');
                applyTheme(selectedTheme);
            });
        });
    });
  </script>
</body>
</html>