{"users": [{"id": 0, "name": "Dixith", "status": "Online", "avatar": "https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult", "phone_number": "+919398760681", "room_number": "101", "platform": "WhatsApp"}, {"id": 1, "name": "<PERSON>", "status": "Offline", "avatar": "https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult", "phone_number": "+919398760682", "room_number": "204", "platform": "WhatsApp"}, {"id": 2, "name": "<PERSON>", "status": "Online", "avatar": "https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult", "phone_number": "+919398760683", "room_number": "305", "platform": "WhatsApp"}, {"id": 3, "name": "<PERSON>", "status": "Online", "avatar": "https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult", "phone_number": "+919398760684", "room_number": "402", "platform": "WhatsApp"}, {"id": 4, "name": "<PERSON>", "status": "Online", "avatar": "https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult", "phone_number": "+919398760685", "room_number": "503", "platform": "WhatsApp"}, {"id": 5, "name": "<PERSON>", "status": "Offline", "avatar": "https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult", "phone_number": "+919398760686", "room_number": "218", "platform": "WhatsApp"}, {"id": 6, "name": "<PERSON>", "status": "Online", "avatar": "https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult", "phone_number": "+919398760687", "room_number": "124", "platform": "WhatsApp"}, {"id": 7, "name": "<PERSON>", "status": "Online", "avatar": "https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult", "phone_number": "+919398760688", "room_number": "315", "platform": "WhatsApp"}, {"id": 8, "name": "<PERSON>", "status": "Offline", "avatar": "https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult", "phone_number": "+919398760689", "room_number": "420", "platform": "WhatsApp"}, {"id": 9, "name": "<PERSON>", "status": "Online", "avatar": "https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult", "phone_number": "+919398760690", "room_number": "512", "platform": "WhatsApp"}, {"id": 10, "name": "<PERSON>", "status": "Online", "avatar": "https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult", "phone_number": "+919398760691", "room_number": "230", "platform": "WhatsApp"}, {"id": 11, "name": "<PERSON>", "status": "Offline", "avatar": "https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult", "phone_number": "+919398760692", "room_number": "333", "platform": "WhatsApp"}, {"id": 12, "name": "<PERSON><PERSON>", "status": "Online", "avatar": "https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult", "phone_number": "+919398760693", "room_number": "415", "platform": "WhatsApp"}], "chats": {"0": [{"sender": "Dixith", "message": "Hey can I please get some extra pillows ?", "timestamp": "23:56", "customer": true}, {"sender": "Agent", "message": "Sure we will send you extra pillows", "timestamp": "00:00", "customer": false}, {"sender": "Dixith", "message": "Also, I need some towels", "timestamp": "19:53", "customer": true}, {"sender": "Agent", "message": "Noted. We will send towels to your room shortly.", "timestamp": "19:55", "customer": false}, {"sender": "Dixith", "message": "Thank you", "timestamp": "19:56", "customer": true}, {"sender": "Agent", "message": "You're welcome! Let us know if you need anything else.", "timestamp": "19:57", "customer": false}, {"sender": "Dixith", "message": "Can I get an extra blanket too?", "timestamp": "20:10", "customer": true}, {"sender": "Agent", "message": "Of course! We will deliver an extra blanket shortly.", "timestamp": "20:12", "customer": false}, {"sender": "Dixith", "message": "Perfect. Thanks again!", "timestamp": "20:15", "customer": true}, {"sender": "Agent", "message": "Can I get an extra blanket too?\n\nCan I get an extra blanket too?\n\nCan I get an extra blanket too?\n\nCan I get an extra blanket too?\n\nCan I get an extra blanket too?\n\nCan I get an extra blanket too?\n\nCan I get an extra blanket too?", "timestamp": "04:40", "customer": false}, {"sender": "Agent", "message": "Thank you for staying with us! Your check-out is scheduled for today. Please let us know if you need a late check-out or any assistance with luggage.", "timestamp": "05:32", "customer": false}, {"sender": "Agent", "message": "Welcome to our hotel! Your room is ready for check-in. Please let us know if you need any assistance during your stay.", "timestamp": "05:34", "customer": false}, {"sender": "Agent", "message": "Thank you for staying with us! Your check-out is scheduled for today. Please let us know if you need a late check-out or any assistance with luggage.", "timestamp": "21:26", "customer": false}, {"sender": "Agent", "message": "Our complimentary WiFi network is 'Hotel-Guest'. The password is your room number followed by your last name (e.g., 101Smith). Let us know if you have any connectivity issues.", "timestamp": "21:29", "customer": false}], "1": [{"sender": "<PERSON>", "message": "Hi, what time does breakfast service end?", "timestamp": "07:30", "customer": true}, {"sender": "Agent", "message": "Good morning! Breakfast is served until 10:30 AM.", "timestamp": "07:31", "customer": false}, {"sender": "<PERSON>", "message": "Great, and is the gym open 24/7?", "timestamp": "07:32", "customer": true}, {"sender": "Agent", "message": "Yes, our fitness center is accessible 24/7 with your room key.", "timestamp": "07:33", "customer": false}, {"sender": "<PERSON>", "message": "Perfect! One more thing - can I get a late checkout tomorrow?", "timestamp": "07:35", "customer": true}, {"sender": "Agent", "message": "Let me check availability for you... Yes, we can extend your checkout until 2 PM.", "timestamp": "07:37", "customer": false}, {"sender": "<PERSON>", "message": "That's exactly what I needed, thank you!", "timestamp": "07:38", "customer": true}, {"sender": "Agent", "message": "Our complimentary WiFi network is 'Hotel-Guest'. The password is your room number followed by your last name (e.g., 101Smith). Let us know if you have any connectivity issues", "timestamp": "08:29", "customer": false}], "2": [{"sender": "<PERSON>", "message": "The AC in my room isn't working properly", "timestamp": "15:20", "customer": true}, {"sender": "Agent", "message": "I apologize for the inconvenience. I'll send maintenance right away to check it.", "timestamp": "15:22", "customer": false}, {"sender": "<PERSON>", "message": "How long will it take?", "timestamp": "15:23", "customer": true}, {"sender": "Agent", "message": "Our maintenance team will be there within 15 minutes.", "timestamp": "15:24", "customer": false}, {"sender": "<PERSON>", "message": "OK, thanks", "timestamp": "15:25", "customer": true}, {"sender": "Agent", "message": "Has the maintenance team arrived?", "timestamp": "15:40", "customer": false}, {"sender": "<PERSON>", "message": "Yes, they're fixing it now", "timestamp": "15:41", "customer": true}, {"sender": "Agent", "message": "Excellent! Please let us know if you need anything else.", "timestamp": "15:42", "customer": false}], "3": [{"sender": "<PERSON>", "message": "Hello, do you offer room service?", "timestamp": "18:45", "customer": true}, {"sender": "Agent", "message": "Yes, we do! Would you like me to send you our menu?", "timestamp": "18:46", "customer": false}, {"sender": "<PERSON>", "message": "Yes please!", "timestamp": "18:47", "customer": true}, {"sender": "Agent", "message": "I've sent the menu to your room. You can order by dialing 0 or through this chat.", "timestamp": "18:48", "customer": false}, {"sender": "<PERSON>", "message": "I'd like to order the grilled salmon and a glass of white wine", "timestamp": "19:00", "customer": true}, {"sender": "Agent", "message": "Excellent choice! Your order will be delivered in approximately 30 minutes.", "timestamp": "19:02", "customer": false}, {"sender": "<PERSON>", "message": "Wonderful, thank you", "timestamp": "19:03", "customer": true}, {"sender": "Agent", "message": "Your dinner is on the way to your room now.", "timestamp": "19:28", "customer": false}, {"sender": "<PERSON>", "message": "Got it, thanks! 😊", "timestamp": "19:30", "customer": true}, {"sender": "Agent", "message": "sup", "timestamp": "15:31", "customer": false}, {"sender": "Agent", "message": "Our complimentary WiFi network is 'Hotel-Guest'. The password is your room number followed by your last name (e.g., 101Smith). Let us know if you have any connectivity issues.", "timestamp": "22:21", "customer": false}], "4": [{"sender": "<PERSON>", "message": "The shower in my bathroom is leaking", "timestamp": "08:15", "customer": true}, {"sender": "Agent", "message": "I'm sorry to hear that. I'll send someone from maintenance to look at it immediately.", "timestamp": "08:17", "customer": false}, {"sender": "<PERSON>", "message": "Thank you. Also, is there any way to get more coffee pods for the machine?", "timestamp": "08:20", "customer": true}, {"sender": "Agent", "message": "Absolutely! What type of coffee would you prefer? We have regular, decaf, and a variety of flavors.", "timestamp": "08:22", "customer": false}, {"sender": "<PERSON>", "message": "Regular is fine, and maybe a couple of decaf too", "timestamp": "08:25", "customer": true}, {"sender": "Agent", "message": "Perfect. Housekeeping will bring those along with the maintenance staff.", "timestamp": "08:27", "customer": false}, {"sender": "<PERSON>", "message": "Great, thank you for your help", "timestamp": "08:30", "customer": true}, {"sender": "Agent", "message": "You're very welcome! Is there anything else you need assistance with today?", "timestamp": "08:32", "customer": false}, {"sender": "<PERSON>", "message": "No, that's all for now", "timestamp": "08:35", "customer": true}], "5": [{"sender": "<PERSON>", "message": "Hi there, I think I left my charger :(", "timestamp": "09:10", "customer": true}, {"sender": "Agent", "message": "I'll check with our lost and found department right away. Can you describe the charger?", "timestamp": "09:12", "customer": false}, {"sender": "<PERSON>", "message": "It's a white iPhone charger with a blue tag on the cable", "timestamp": "09:15", "customer": true}, {"sender": "Agent", "message": "Thank you for the description. Let me check for you.", "timestamp": "09:17", "customer": false}, {"sender": "Agent", "message": "Good news! We found your charger. You can pick it up from the front desk at your convenience.", "timestamp": "09:25", "customer": false}, {"sender": "<PERSON>", "message": "That's wonderful! I'll come down in about 30 minutes", "timestamp": "09:27", "customer": true}, {"sender": "Agent", "message": "Perfect! We'll have it ready for you at the front desk.", "timestamp": "09:29", "customer": false}], "6": [{"sender": "<PERSON>", "message": "What are the pool hours today?", "timestamp": "10:45", "customer": true}, {"sender": "Agent", "message": "Our pool is open from 7:00 AM to 10:00 PM today.", "timestamp": "10:47", "customer": false}, {"sender": "<PERSON>", "message": "Do you provide pool towels or should I bring from my room?", "timestamp": "10:50", "customer": true}, {"sender": "Agent", "message": "We provide fresh pool towels at the pool area. No need to bring them from your room.", "timestamp": "10:52", "customer": false}, {"sender": "<PERSON>", "message": "Perfect! Is there a lifeguard on duty?", "timestamp": "10:55", "customer": true}, {"sender": "Agent", "message": "Yes, a lifeguard is on duty from 8:00 AM to 8:00 PM. Outside those hours, swimming is at your own risk.", "timestamp": "10:57", "customer": false}, {"sender": "<PERSON>", "message": "Thanks for the information!", "timestamp": "11:00", "customer": true}, {"sender": "Agent", "message": "You're welcome! Enjoy your swim.", "timestamp": "11:02", "customer": false}, {"sender": "Agent", "message": "how was your swim", "timestamp": "21:52", "customer": false}, {"sender": "Agent", "message": "Thank you for staying with us! Your check-out is scheduled for today. Please let us know if you need a late check-out or any assistance with luggage.", "timestamp": "22:24", "customer": false}], "7": [{"sender": "<PERSON>", "message": "The toilet in my bathroom is clogged", "timestamp": "12:30", "customer": true}, {"sender": "Agent", "message": "I'm sorry for the inconvenience. I'll send maintenance to your room immediately. Are you in the room now?", "timestamp": "12:32", "customer": false}, {"sender": "<PERSON>", "message": "Yes, I'm in the room", "timestamp": "12:34", "customer": true}, {"sender": "Agent", "message": "Thank you. Maintenance will be there within 15 minutes.", "timestamp": "12:36", "customer": false}, {"sender": "<PERSON>", "message": "Actually, I need to step out for lunch. Can they come after 2pm?", "timestamp": "12:38", "customer": true}, {"sender": "Agent", "message": "No problem. I'll reschedule the maintenance visit for 2:15 PM. Will that work for you?", "timestamp": "12:40", "customer": false}, {"sender": "<PERSON>", "message": "That's perfect, thank you", "timestamp": "12:42", "customer": true}, {"sender": "Agent", "message": "You're welcome. Enjoy your lunch, and we'll take care of the issue when you return.", "timestamp": "12:44", "customer": false}], "8": [{"sender": "<PERSON>", "message": "I'd like to book a spa appointment for tomorrow", "timestamp": "14:00", "customer": true}, {"sender": "Agent", "message": "I'd be happy to help with that. What type of treatment are you interested in?", "timestamp": "14:02", "customer": false}, {"sender": "<PERSON>", "message": "A 60-minute deep tissue massage", "timestamp": "14:05", "customer": true}, {"sender": "Agent", "message": "Great choice. Let me check the availability for tomorrow. What time would you prefer?", "timestamp": "14:07", "customer": false}, {"sender": "<PERSON>", "message": "Sometime in the morning, if possible", "timestamp": "14:10", "customer": true}, {"sender": "Agent", "message": "We have availability at 9:30 AM or 11:00 AM tomorrow. Which would you prefer?", "timestamp": "14:12", "customer": false}, {"sender": "<PERSON>", "message": "11:00 AM would be perfect", "timestamp": "14:15", "customer": true}, {"sender": "Agent", "message": "Excellent. I've booked your 60-minute deep tissue massage for 11:00 AM tomorrow. Please arrive 15 minutes early to complete paperwork.", "timestamp": "14:17", "customer": false}, {"sender": "<PERSON>", "message": "Will do. Is there a cancellation policy I should know about?", "timestamp": "14:20", "customer": true}, {"sender": "Agent", "message": "Yes, cancellations must be made at least 4 hours in advance to avoid a 50% charge. Would you like me to email you the full spa policies?", "timestamp": "14:22", "customer": false}, {"sender": "<PERSON>", "message": "Yes, please send them to my email", "timestamp": "14:25", "customer": true}, {"sender": "Agent", "message": "I've sent the spa policies to your email on file. Looking forward to your visit tomorrow!", "timestamp": "14:27", "customer": false}, {"sender": "<PERSON>", "message": "Great! Also, can I get a newspaper delivered to my room tomorrow morning?", "timestamp": "14:30", "customer": true}, {"sender": "Agent", "message": "Certainly! What newspaper would you prefer?", "timestamp": "14:31", "customer": false}, {"sender": "<PERSON>", "message": "The New York Times, please.", "timestamp": "14:32", "customer": true}, {"sender": "Agent", "message": "Okay, The New York Times will be delivered to your room tomorrow morning. Is there a specific time you'd like it delivered?", "timestamp": "14:33", "customer": false}, {"sender": "<PERSON>", "message": "Around 7:00 AM would be great.", "timestamp": "14:34", "customer": true}, {"sender": "Agent", "message": "Perfect. The New York Times will be delivered to your room at 7:00 AM tomorrow. Anything else I can assist you with?", "timestamp": "14:35", "customer": false}, {"sender": "<PERSON>", "message": "No, that's all for now. Thanks!", "timestamp": "14:36", "customer": true}, {"sender": "Agent", "message": "You're welcome! Have a great day!", "timestamp": "14:37", "customer": false}], "9": [{"sender": "<PERSON>", "message": "The TV in my room isn't working", "timestamp": "16:05", "customer": true}, {"sender": "Agent", "message": "I'm sorry to hear that. Can you tell me what happens when you try to turn it on?", "timestamp": "16:07", "customer": false}, {"sender": "<PERSON>", "message": "It turns on but shows 'No Signal' on all channels", "timestamp": "16:10", "customer": true}, {"sender": "Agent", "message": "Thank you for that information. Let me send a technician to check the connection. Will you be in your room for the next 30 minutes?", "timestamp": "16:12", "customer": false}, {"sender": "<PERSON>", "message": "Yes, I'll be here", "timestamp": "16:15", "customer": true}, {"sender": "Agent", "message": "Perfect. Our technician will be there shortly. In the meantime, you can access our in-room entertainment through the hotel app on your mobile device.", "timestamp": "16:17", "customer": false}, {"sender": "<PERSON>", "message": "Oh, I didn't know about the app. I'll try that while I wait", "timestamp": "16:20", "customer": true}, {"sender": "Agent", "message": "It's available in both app stores - just search for 'Hotel Connect'. Let me know if you need help setting it up.", "timestamp": "16:22", "customer": false}, {"sender": "<PERSON>", "message": "Found it! I'll download it now", "timestamp": "16:25", "customer": true}], "10": [{"sender": "<PERSON>", "message": "Do you have halal food options at your restaurant?", "timestamp": "17:30", "customer": true}, {"sender": "Agent", "message": "Yes, we do offer halal options in our restaurant. Our dinner menu has several halal-certified dishes.", "timestamp": "17:32", "customer": false}, {"sender": "<PERSON>", "message": "That's great to hear. Do I need to make a reservation?", "timestamp": "17:35", "customer": true}, {"sender": "Agent", "message": "Reservations are recommended but not required. Would you like me to book a table for you?", "timestamp": "17:37", "customer": false}, {"sender": "<PERSON>", "message": "Yes please, for tonight at 8:00 PM for 2 people", "timestamp": "17:40", "customer": true}, {"sender": "Agent", "message": "I've booked your table for 2 at 8:00 PM tonight. Any special requests or dietary restrictions I should note?", "timestamp": "17:42", "customer": false}, {"sender": "<PERSON>", "message": "No other restrictions, just the halal options", "timestamp": "17:45", "customer": true}, {"sender": "Agent", "message": "Perfect. I've noted your preference for halal options. Your reservation is confirmed. We look forward to serving you tonight.", "timestamp": "17:47", "customer": false}, {"sender": "<PERSON>", "message": "Thank you very much", "timestamp": "17:50", "customer": true}], "11": [{"sender": "<PERSON>", "message": "I think I misplaced my room key. Can I get a new one?", "timestamp": "13:10", "customer": true}, {"sender": "Agent", "message": "I'm sorry about your key. We can certainly issue you a new one. To verify your identity, could you please provide your name and room number?", "timestamp": "13:12", "customer": false}, {"sender": "<PERSON>", "message": "<PERSON>, room 333", "timestamp": "13:15", "customer": true}, {"sender": "Agent", "message": "Thank you, Ms. <PERSON>. For security purposes, could you also confirm the email address on file for your reservation?", "timestamp": "13:17", "customer": false}, {"sender": "<PERSON>", "message": "It's sofia.<PERSON><PERSON><PERSON><PERSON>@email.com", "timestamp": "13:20", "customer": true}, {"sender": "Agent", "message": "Thank you for confirming. Your new key will be available for pickup at the front desk. Please bring a photo ID with you.", "timestamp": "13:22", "customer": false}, {"sender": "<PERSON>", "message": "Will do. I'll come down shortly", "timestamp": "13:25", "customer": true}, {"sender": "Agent", "message": "Perfect. We've deactivated your previous key for security. Is there anything else you need assistance with?", "timestamp": "13:27", "customer": false}, {"sender": "<PERSON>", "message": "No, that's all. Thank you for your help", "timestamp": "13:30", "customer": true}, {"sender": "Agent", "message": "You're very welcome. We're here to help anytime.", "timestamp": "13:32", "customer": false}], "12": [{"sender": "<PERSON><PERSON>", "message": "The Wi-Fi signal in my room is very weak.", "timestamp": "20:05", "customer": true}, {"sender": "Agent", "message": "I apologize for the inconvenience. Let me check if we can improve the signal. Are you able to connect at all?", "timestamp": "20:07", "customer": false}, {"sender": "<PERSON><PERSON>", "message": "Yes, but it keeps disconnecting and is very slow", "timestamp": "20:10", "customer": true}, {"sender": "Agent", "message": "We can offer a portable Wi-Fi booster for your room. Would you like us to bring one up?", "timestamp": "20:12", "customer": false}, {"sender": "<PERSON><PERSON>", "message": "Yes, that would be helpful. Thank you", "timestamp": "20:15", "customer": true}, {"sender": "Agent", "message": "Our IT technician will bring the booster to your room within 30 minutes. They'll also check your current connection to see if there are any other issues.", "timestamp": "20:17", "customer": false}, {"sender": "<PERSON><PERSON>", "message": "Great, I appreciate the quick response", "timestamp": "20:20", "customer": true}, {"sender": "Agent", "message": "You're welcome! If you need faster internet for work purposes, we also have a business center on the lobby level with high-speed connections.", "timestamp": "20:22", "customer": false}, {"sender": "<PERSON><PERSON>", "message": "That's good to know. What are the business center hours?", "timestamp": "20:25", "customer": true}, {"sender": "Agent", "message": "The business center is open 24/7 and accessible with your room key. It has computers, printers, and a dedicated fiber connection.", "timestamp": "20:27", "customer": false}, {"sender": "<PERSON><PERSON>", "message": "Perfect! Thank you for all the information", "timestamp": "20:30", "customer": true}, {"sender": "Agent", "message": "You're very welcome! The technician is on the way with your Wi-Fi booster now.", "timestamp": "20:32", "customer": false}], "None": []}}