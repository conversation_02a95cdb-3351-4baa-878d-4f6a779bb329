document.addEventListener('DOMContentLoaded', () => {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex'; // Immediately show overlay
        requestAnimationFrame(() => {
            loadingOverlay.style.opacity = '1';
        });

        // Ensure loading overlay is shown for at least minLoadingTime
        // But not longer than maxLoadingTime
        setTimeout(() => requestAnimationFrame(hideLoadingOverlay), maxLoadingTime);
    }
    
    // Record the time when DOM content loaded
    window.domContentLoadedTime = Date.now();
});

window.addEventListener('load', () => {
    // Calculate how much time has passed since DOM content loaded
    const elapsedTime = Date.now() - (window.domContentLoadedTime || 0);
    const remainingTime = Math.max(0, minLoadingTime - elapsedTime);
    
    // Hide overlay after ensuring minimum display time
    if (elapsedTime < maxLoadingTime) {
        setTimeout(() => requestAnimationFrame(hideLoadingOverlay), remainingTime);
    }
});

const maxLoadingTime = 10000; // 10 seconds max
const minLoadingTime = 2000; // 2 seconds minimum loading time

function hideLoadingOverlay() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.transition = 'opacity 0.4s ease'; // Smoother and quicker transition
        loadingOverlay.style.opacity = '0';
        setTimeout(() => {
            loadingOverlay.style.display = 'none'; // Ensure it's hidden after the fade-out
        }, 400); // Match transition duration
    }
}

// Optimize pulseLoader to ensure it starts smoothly
function pulseLoader() {
    const loader = document.querySelector('.loader');
    if (loader) {
        let scale = 1;
        let growing = true;

        function animate() {
            scale = growing ? scale + 0.002 : scale - 0.002;
            if (scale >= 1.07) growing = false;
            if (scale <= 1) growing = true;

            loader.style.transform = `scale(${scale}) rotate(15deg)`;
            requestAnimationFrame(animate);
        }

        requestAnimationFrame(animate);
    }
}

// Call the pulseLoader function when the page loads
window.addEventListener('load', pulseLoader);