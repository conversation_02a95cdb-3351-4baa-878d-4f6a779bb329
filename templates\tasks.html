<!-- TESTING -->
<!--Dix testing-->
<!--(The UI is done and perfect, Backend is done and perfect.)-->
<!--Just test out if there are any bugs and make sure its perfect-->
<!--ESTIMATED TIME : 5-10 min-->

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Tasks</title>
    {% include 'imports.html' %}
    <script src="https://unpkg.com/@supabase/supabase-js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Fetch Supabase configuration from the server
            fetch('/api/supabase-config')
                .then(response => response.json())
                .then(config => {
                    // Initialize Supabase client with values from the server
                    try {
                        window.supabase = supabase.createClient(config.SUPABASE_URL, config.SUPABASE_ANON_KEY);
                        console.log("✅ Supabase client initialized successfully");
                    } catch (error) {
                        console.error("❌ Failed to initialize Supabase client:", error);
                    }
                })
                .catch(error => {
                    console.error("❌ Failed to fetch Supabase configuration:", error);
                });
        });
    </script>
</head>
<style>
    body {
        visibility: hidden;
    }

    .light .themer-input {
        background-color: transparent !important;
        color: black !important;
        border: transparent !important;
        outline: none !important;
        /* Removes the focus border */
        box-shadow: none !important;
    }

    .pure-black .themer-input {
        background-color: transparent !important;
        color: white !important;
        border: transparent !important;
        border: transparent !important;
        outline: none !important;
        /* Removes the focus border */
        box-shadow: none !important;
    }

    .uk-label {
        color: var(--dropdown-text);
        background-color: var(--background);
    }

    .uk-drop.uk-dropdown {
        color: var(--dropdown-text);
    }

    .light .uk-table-divider>tr:not(:first-child),
    .uk-table-divider>:not(:first-child)>tr,
    .uk-table-divider>:first-child>tr:not(:first-child) {
        border-color: #e5e7eb;
    }

    .pure-black .uk-table-divider>tr:not(:first-child),
    .pure-black .uk-table-divider>:not(:first-child)>tr,
    .pure-black .uk-table-divider>:first-child>tr:not(:first-child) {
        border-color: #27272a;
    }

    .light .uk-label {
        background-color: white;
    }

    .pure-black .uk-label {
        background-color: #09090b;
    }

    /* Alternative solution using pseudo-element */
    .order-table-container thead::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 1px;
        background-color: var(--border-color, #e5e7eb);
    }

    .light .order-table-container thead::after {
        background-color: #e5e7eb;
    }

    .pure-black .order-table-container thead::after {
        background-color: #27272a;
    }

    /* Style UIkit success notifications to look like cards */
    .uk-notification-message.uk-notification-message-success {
        background-color: var(--card, var(--background));
        /* Use card background variable or fallback */
        color: var(--card-foreground, var(--foreground));
        /* Use card text color variable or fallback */
        border: 1px solid var(--border-color);
        /* Use the theme's border color */
        border-radius: 8px;
        /* Match card border-radius if applicable, adjust if needed */
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
        /* Optional: Add a subtle shadow */
        /* Override default UIkit success background/color */
        background-image: none;
        /* Remove default gradient if any */
        padding: 12px 16px;
        /* Adjust padding if needed */
        /* Ensure text color is readable */
    }

    /* Hide notification close button for task completion notifications */
    .uk-notification-message.uk-notification-message-success .uk-notification-close {
        display: none !important;
    }

    /* Keep close button visible for other notifications */
    .uk-notification-message:not(.uk-notification-message-success) .uk-notification-close {
        opacity: 0.6 !important;
    }

    /* Undo button styling within notifications */
    .uk-notification-message .undo-button {
        font-family: inherit;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 24px;
        min-width: 48px;
    }

    /* Theme-specific undo button colors with higher specificity */
    .light .uk-notification-message.uk-notification-message-success .undo-button {
        background: #171717 !important;
        border: 1px solid #171717 !important;
        color: #ffffff !important;
    }

    .light .uk-notification-message.uk-notification-message-success .undo-button:hover {
        background: #2a2a2a !important;
        border: 1px solid #2a2a2a !important;
    }

    .pure-black .uk-notification-message.uk-notification-message-success .undo-button {
        background: #fcfcfc !important;
        border: 1px solid #fcfcfc !important;
        color: #000000 !important;
    }

    .pure-black .uk-notification-message.uk-notification-message-success .undo-button:hover {
        background: #e5e5e5 !important;
        border: 1px solid #e5e5e5 !important;
    }

    /* Ensure notification message content has proper flex layout */
    .uk-notification-message.uk-notification-message-success > div {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    /* Minimalistic Scrollbar for Order Items */
    #task-detail-items {
        /* Firefox */
        scrollbar-width: thin;
        scrollbar-color: var(--scrollbar-thumb-color) transparent;
        /* thumb track */
    }

    /* Webkit browsers (Chrome, Safari, Edge) */
    #task-detail-items::-webkit-scrollbar {
        width: 5px;
        /* Width of the vertical scrollbar */
        height: 5px;
        /* Height of the horizontal scrollbar */
    }

    #task-detail-items::-webkit-scrollbar-track {
        background: transparent;
        /* Make track invisible */
        margin: 2px;
        /* Optional margin around the track */
    }

    #task-detail-items::-webkit-scrollbar-thumb {
        background-color: var(--scrollbar-thumb-color);
        /* Use CSS variable for color */
        border-radius: 10px;
        /* Rounded corners for the thumb */
        border: 1px solid transparent;
        /* Optional: adds a tiny border */
    }

    /* Define scrollbar thumb colors for light and dark themes */
    .light {
        --scrollbar-thumb-color: rgba(0, 0, 0, 0.25);
    }

    .pure-black {
        --scrollbar-thumb-color: rgba(255, 255, 255, 0.25);
    }
</style>

<body class="bg-background text-foreground">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
    </head>


    {% include 'components/loading.html' %}

    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] ">
        {% include 'sidebar.html' %}

        <div class="flex flex-col">
            <header
                class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
                <div style="margin-left: 8px;" class="flex items-center gap-2 px-4 pl-0">
                    <button id="toggle-btn" class="opacity-100 transition-opacity duration-300 focus:outline-none"
                        style="background-color: transparent !important;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-panel-left">
                            <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                            <path d="M9 3v18"></path>
                        </svg>
                    </button>
                    <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-2 h-4"
                        style="background-color: var(--border-color);"></div>
                    <nav aria-label="breadcrumb">
                        <ol
                            class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
                            <div class="menubar" role="menubar">
                                <div class="menubar-indicator"></div>
                                <a href="/tasks" role="menuitem" class="active">Tasks</a>
                                <a href="/issue" role="menuitem">Issues</a>
                            </div>
                        </ol>
                    </nav>
                </div>
                {% include 'topright.html' %}
            </header>
            <main class="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6 card"
                style="min-height: 0; height: calc(100vh - 60px); overflow-y: auto;">

                <div class="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-9 gap-x-4 gap-y-4">
                    <div class="col-span-1 md:col-span-2 lg:col-span-3 card rounded-lg border relative overflow-hidden animated-on-load animate-conversation-analytics" data-v0-t="card"
                        style="height: 150px;">
                        <div class="p-4 pb-1 flex flex-row items-center justify-between space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-large">Total Tasks</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-messages-square">
                                <path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2z" />
                                <path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1" />
                            </svg>
                        </div>
                        <div class="px-4 pb-0 relative z-10">
                            <div class="flex items-baseline">
                                <div id="total-tips" style="margin-top: 0px" class="text-2xl font-bold">763</div>
                            </div>
                        </div>

                        <!-- Static Progress Bar - No Animation -->
                        <div class="px-4 absolute bottom-3 left-0 right-0 w-full">
                            <div class="overflow-hidden w-full">
                                <!-- Static Progress Bar Container -->
                                <div class="flex w-full rounded-full bg-gray-100 h-[10px] overflow-hidden">
                                    <!-- Pre-rendered Progress Bars with static widths -->
                                    <div class="h-full bg-green-500" style="width: 50%;"></div>
                                    <div class="h-full bg-rose-500" style="width: 30%;"></div>
                                    <div class="h-full bg-blue-500" style="width: 20%;"></div>
                                </div>
                                <!-- Static Legend -->
                                <div class="mt-2 flex flex-wrap justify-center text-[12px]">
                                    <div class="flex items-center mr-3 mb-1">
                                        <span class="inline-block w-2.5 h-2.5 rounded-sm bg-green-500 mr-1"></span>
                                        <span>Completed Tasks: 220</span>
                                    </div>
                                    <div class="flex items-center mr-3 mb-1">
                                        <span class="inline-block w-2.5 h-2.5 rounded-sm bg-rose-500 mr-1"></span>
                                        <span>Pending Tasks: 35</span>
                                    </div>
                                    <div class="flex items-center mr-3 mb-1">
                                        <span class="inline-block w-2.5 h-2.5 rounded-sm bg-blue-500 mr-1"></span>
                                        <span>Invalid Tasks: 15</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Card 2: Total Invalid Tasks -->
                    <div class="col-span-1 md:col-span-2 lg:col-span-2 card rounded-lg border shadow-sm relative">
                        <div class="px-6 pt-[21px] flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Invalid Tasks</h3>
                            <!-- Using an X icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-x">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </div>
                        <div class="p-6 relative">
                            <div class="text-2xl font-bold">15</div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Total invalid tasks</p>
                            <!-- Loss indicator (using similar SVG styling) -->
                            <div
                                class="absolute bottom-7 right-4 flex items-center text-xs text-red-600 border border-red-500  px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-arrow-down-narrow-wide mr-0.5">
                                    <path d="m3 16 4 4 4-4" />
                                    <path d="M7 20V4" />
                                    <path d="M11 4h4" />
                                    <path d="M11 8h7" />
                                    <path d="M11 12h10" />
                                </svg>
                                5.2%
                            </div>
                        </div>
                    </div>
                    <!-- Card 3: Total Completed Tasks -->
                    <div class="col-span-1 md:col-span-2 lg:col-span-2 card rounded-lg border shadow-sm relative">
                        <div class="px-6 pt-[21px] flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Completed Tasks</h3>
                            <!-- Using a check icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-check">
                                <polyline points="20 6 9 17 4 12"></polyline>
                            </svg>
                        </div>
                        <div class="p-6 relative">
                            <div class="text-2xl font-bold">220</div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Total completed tasks</p>
                            <!-- Profit indicator -->
                            <div
                                class="absolute bottom-7 right-4 flex items-center text-xs text-green-600 border border-green-500 bg-transparent px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-arrow-up-narrow-wide mr-0.5">
                                    <path d="m3 8 4-4 4 4" />
                                    <path d="M7 4v16" />
                                    <path d="M11 12h4" />
                                    <path d="M11 16h7" />
                                    <path d="M11 20h10" />
                                </svg>
                                15.5%
                            </div>
                        </div>
                    </div>
                    <!-- Card 4: Tasks In Progress -->
                    <div class="col-span-1 md:col-span-2 lg:col-span-2 card rounded-lg border shadow-sm relative">
                        <div class="px-6 pt-[21px] flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">In Progress</h3>
                            <!-- Using a clock icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-clock">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                        </div>
                        <div class="p-6 relative">
                            <div class="text-2xl font-bold">35</div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Tasks in progress</p>
                            <!-- Profit indicator -->
                            <div
                                class="absolute bottom-7 right-4 flex items-center text-xs text-green-600 border border-green-500 bg-transparent px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-arrow-up-narrow-wide mr-0.5">
                                    <path d="m3 8 4-4 4 4" />
                                    <path d="M7 4v16" />
                                    <path d="M11 12h4" />
                                    <path d="M11 16h7" />
                                    <path d="M11 20h10" />
                                </svg>
                                8.5%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col flex-1">
                    <div class="flex items-center justify-between">
                        <div class="flex flex-1 gap-4">
                            <div class="w-[250px] flex items-center relative">
                                <uk-icon class="absolute left-2 opacity-50" icon="search"></uk-icon>
                                <input class="card uk-input rounded-md pl-8" type="text" placeholder="Filter Tasks...">
                            </div>
                            <div class="flex items-center gap-2 h-[36px]">
                                <button
                                    class="card inline-flex items-center justify-center rounded-md border border-dashed border-input px-3 text-xs font-medium shadow-sm hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring h-full universal-hover"
                                    aria-haspopup="true" style="background: transparent;">
                                    <span class="mr-2 size-4">
                                        <uk-icon icon="circle-plus">
                                        </uk-icon>
                                    </span>
                                    Category
                                </button>
                                <!-- Container for selected Category items - initially hidden -->
                                <div id="selected-status-container"
                                    class="class hidden h-[36px] min-w-[10px] flex items-center border border-dashed border-input rounded-md px-1 card">
                                    <!-- Category pills will be inserted here by JavaScript -->
                                </div>
                            </div>

                            <div class="uk-drop uk-dropdown" id="tasks-category-dropdown"
                                uk-drop="mode: click; pos: bottom-left">
                                <div class="m-1 flex items-center px-2">
                                    <uk-icon class="opacity-50" icon="search">
                                    </uk-icon>
                                    <input
                                        class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                        placeholder="Category" type="text">
                                </div>
                                <ul class="uk-dropdown-nav">
                                    <li class="uk-nav-divider"></li>
                                    <li>
                                        <a class="uk-drop-close" href="#demo" role="button">
                                            <div class="flex flex-1 items-center justify-between">
                                                <div class="flex flex-1 items-center gap-x-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;"
                                                        width="16" height="16" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="lucide lucide-hand-platter-icon lucide-hand-platter">
                                                        <path d="M12 3V2" />
                                                        <path
                                                            d="m15.4 17.4 3.2-2.8a2 2 0 1 1 2.8 2.9l-3.6 3.3c-.7.8-1.7 1.2-2.8 1.2h-4c-1.1 0-2.1-.4-2.8-1.2l-1.302-1.464A1 1 0 0 0 6.151 19H5" />
                                                        <path d="M2 14h12a2 2 0 0 1 0 4h-2" />
                                                        <path d="M4 10h16" />
                                                        <path d="M5 10a7 7 0 0 1 14 0" />
                                                        <path d="M5 14v6a1 1 0 0 1-1 1H2" />
                                                    </svg>
                                                    <span>Food</span>
                                                </div>
                                                <span class="font-geist-mono text-xs">21</span>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a class="uk-drop-close" href="#demo" role="button">
                                            <div class="flex flex-1 items-center justify-between">
                                                <div class="flex flex-1 items-center gap-x-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;"
                                                        width="16" height="16" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="lucide lucide-martini-icon lucide-martini">
                                                        <path d="M8 22h8" />
                                                        <path d="M12 11v11" />
                                                        <path d="m19 3-7 8-7-8Z" />
                                                    </svg>
                                                    <span>Beverage</span>
                                                </div>
                                                <span class="font-geist-mono text-xs">21</span>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a class="uk-drop-close" href="#demo" role="button">
                                            <div class="flex flex-1 items-center justify-between">
                                                <div class="flex flex-1 items-center gap-x-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;"
                                                        width="16" height="16" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="lucide lucide-user-icon lucide-user">
                                                        <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                                                        <circle cx="12" cy="7" r="4" />
                                                    </svg>
                                                    <span>Massage</span>
                                                </div>
                                                <span class="font-geist-mono text-xs">20</span>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a class="uk-drop-close" href="#demo" role="button">
                                            <div class="flex flex-1 items-center justify-between">
                                                <div class="flex flex-1 items-center gap-x-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;"
                                                        width="16" height="16" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="lucide lucide-sparkles-icon lucide-sparkles">
                                                        <path
                                                            d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                                                        <path d="M20 3v4" />
                                                        <path d="M22 5h-4" />
                                                        <path d="M4 17v2" />
                                                        <path d="M5 18H3" />
                                                    </svg>
                                                    <span>Spa</span>
                                                </div>
                                                <span class="font-geist-mono text-xs">19</span>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a class="uk-drop-close" href="#demo" role="button">
                                            <div class="flex flex-1 items-center justify-between">
                                                <div class="flex flex-1 items-center gap-x-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;"
                                                        width="16" height="16" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        class="lucide lucide-bed-double-icon lucide-bed-double">
                                                        <path d="M2 20v-8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v8" />
                                                        <path d="M4 10V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v4" />
                                                        <path d="M12 4v6" />
                                                        <path d="M2 18h20" />
                                                    </svg>
                                                    <span>Room Booking</span>
                                                </div>
                                                <span class="font-geist-mono text-xs">19</span>
                                            </div>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <!-- Fix the priority container by ensuring it has proper closing tag and all required classes -->
                            <div class="flex items-center gap-2 h-[36px]">

                                <!-- Container for selected priority items - fixed with proper closing tag -->
                                <div id="selected-priority-container"
                                    class="hidden h-[36px] min-w-[10px] flex items-center border border-dashed border-input rounded-md px-1 card">
                                    <!-- Priority pills will be inserted here by JavaScript -->
                                </div>
                            </div>
                            <div class="uk-drop uk-dropdown price-filter-dropdown"
                                uk-drop="mode: click; pos: bottom-left">
                                <ul class="uk-dropdown-nav">
                                    <li>
                                        <a class="uk-drop-close" href="#demo" role="button">
                                            <div class="flex flex-1 items-center justify-between">
                                                <div class="flex flex-1 items-center gap-x-2">
                                                    <svg width="15" height="15" viewBox="0 0 15 15" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        class="mr-2 h-4 w-4 text-muted-foreground">
                                                        <path
                                                            d="M7.5 2C7.77614 2 8 2.22386 8 2.5L8 11.2929L11.1464 8.14645C11.3417 7.95118 11.6583 7.95118 11.8536 8.14645C12.0488 8.34171 12.0488 8.65829 11.8536 8.85355L7.85355 12.8536C7.75979 12.9473 7.63261 13 7.5 13C7.36739 13 7.24021 12.9473 7.14645 12.8536L3.14645 8.85355C2.95118 8.65829 2.95118 8.34171 3.14645 8.14645C3.34171 7.95118 3.65829 7.95118 3.85355 8.14645L7 11.2929L7 2.5C7 2.22386 7.22386 2 7.5 2Z"
                                                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                        </path>
                                                    </svg>
                                                    <span>€10</span>
                                                </div>
                                                <span class="font-geist-mono text-xs">36</span>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a class="uk-drop-close" href="#demo" role="button">
                                            <div class="flex flex-1 items-center justify-between">
                                                <div class="flex flex-1 items-center gap-x-2">
                                                    <svg width="15" height="15" viewBox="0 0 15 15" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        class="mr-2 h-4 w-4 text-muted-foreground">
                                                        <path
                                                            d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z"
                                                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                        </path>
                                                    </svg>
                                                    <span>€25</span>
                                                </div>
                                                <span class="font-geist-mono text-xs">33</span>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a class="uk-drop-close" href="#demo" role="button">
                                            <div class="flex flex-1 items-center justify-between">
                                                <div class="flex flex-1 items-center gap-x-2">
                                                    <svg width="15" height="15" viewBox="0 0 15 15" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        class="mr-2 h-4 w-4 text-muted-foreground">
                                                        <path
                                                            d="M7.14645 2.14645C7.34171 1.95118 7.65829 1.95118 7.85355 2.14645L11.8536 6.14645C12.0488 6.34171 12.0488 6.65829 11.8536 6.85355C11.6583 7.04882 11.3417 7.04882 11.1464 6.85355L8 3.70711L8 12.5C8 12.7761 7.77614 13 7.5 13C7.22386 13 7 12.7761 7 12.5L7 3.70711L3.85355 6.85355C3.65829 7.04882 3.34171 7.04882 3.14645 6.85355C2.95118 6.65829 2.95118 6.34171 3.14645 6.14645L7.14645 2.14645Z"
                                                            fill="currentColor" fill-rule="evenodd" clip-rule="evenodd">
                                                        </path>
                                                    </svg>
                                                    <span>€150</span>
                                                </div>
                                                <span class="font-geist-mono text-xs">31</span>
                                            </div>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <script>
                            document.addEventListener('DOMContentLoaded', function () {
                                // Define status and priority containers
                                const statusContainer = document.getElementById('selected-status-container');
                                const priorityContainer = document.getElementById('selected-priority-container');

                                // Define status and priority sets to track selections
                                const selectedStatuses = new Set();
                                const selectedPriorities = new Set();

                                // Define valid status and priority values
                                const validStatuses = ['Backlog', 'Todo', 'In Progress', 'Done', 'Cancelled'];
                                const validPriorities = ['Low', 'Medium', 'High'];

                                // Function to create filter pills (used for both status and priority)
                                function createFilterPill(name, type) {
                                    const pill = document.createElement('div');
                                    pill.className = 'inline-flex items-center rounded-sm bg-accent/50 px-1.5 py-0.5 text-xs font-medium  mx-0.5';

                                    // Create the pill content with text and remove button
                                    pill.innerHTML = `
                                            ${name}
                                            <button class="ml-1 size-3 rounded-sm opacity-60 ring-offset-background hover:opacity-100" style="background-color: transparent;">
                                                <uk-icon icon="x" ratio="0.6"></uk-icon>
                                                <span class="sr-only">Remove</span>
                                            </button>
                                        `;

                                    // Add click handler to the remove button
                                    const container = type === 'status' ? statusContainer : priorityContainer;
                                    const selectedSet = type === 'status' ? selectedStatuses : selectedPriorities;

                                    pill.querySelector('button').addEventListener('click', function (e) {
                                        e.preventDefault();
                                        e.stopPropagation();

                                        // Remove from selected set
                                        selectedSet.delete(name);

                                        // Remove the pill
                                        pill.remove();

                                        // Hide container if empty
                                        if (selectedSet.size === 0) {
                                            container.classList.add('hidden');
                                        }
                                    });

                                    return pill;
                                }

                                // Find all dropdown items
                                document.querySelectorAll('.uk-dropdown-nav li a').forEach(function (item) {
                                    item.addEventListener('click', function (e) {
                                        // Find the span that contains the status/priority name
                                        const spans = item.querySelectorAll('.flex-1.items-center span');

                                        if (spans.length === 0) return;

                                        // Extract the text (status or priority name)
                                        let itemName = '';
                                        spans.forEach(span => {
                                            const text = span.textContent.trim();
                                            if (text && !text.match(/^\d+$/)) { // Skip if it's just a number
                                                itemName = text;
                                            }
                                        });

                                        if (!itemName) return;

                                        // Check if this is a status item
                                        if (validStatuses.includes(itemName)) {
                                            // Only add if not already selected
                                            if (!selectedStatuses.has(itemName)) {
                                                // Add to our set
                                                selectedStatuses.add(itemName);

                                                // Show the container if it was hidden
                                                statusContainer.classList.remove('hidden');

                                                // Add the new pill
                                                statusContainer.appendChild(createFilterPill(itemName, 'status'));
                                            }
                                        }
                                        // Check if this is a priority item
                                        else if (validPriorities.includes(itemName)) {
                                            // Only add if not already selected
                                            if (!selectedPriorities.has(itemName)) {
                                                // Add to our set
                                                selectedPriorities.add(itemName);

                                                // Show the container if it was hidden
                                                priorityContainer.classList.remove('hidden');

                                                // Add the new pill
                                                priorityContainer.appendChild(createFilterPill(itemName, 'priority'));
                                            }
                                        }
                                    });
                                });
                            });
                        </script>
                        <div class="">
                            <button class="uk-button uk-button-default universal-hover theme-text card"
                                aria-haspopup="true" style="background-color: transparent; height: 36px;">
                                <span class="mr-2 size-4">
                                    <uk-icon icon="settings-2"></uk-icon>
                                </span>
                                View
                            </button>
                            <div class="uk-drop uk-dropdown dropdown-content" id="tasks-category-dropdown"
                                uk-drop="mode: click; pos: bottom-left">
                                <div class="m-1 flex items-center px-2">
                                    <uk-icon class="opacity-50" icon="search">
                                    </uk-icon>
                                    <input
                                        class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                        placeholder="Category" type="text">
                                </div>
                                <ul class="uk-dropdown-nav">

                                    <li class="uk-nav-divider"></li>
                                    <li>
                                        <a class="uk-drop-close" href="#demo" role="button">
                                            <uk-icon class="mr-2" icon="check"></uk-icon>
                                            Pending Tasks
                                        </a>
                                    </li>
                                    <li>
                                        <a class="uk-drop-close" href="#demo" role="button">
                                            <uk-icon class="mr-2" icon="check"></uk-icon>
                                            Completed Tasks
                                        </a>
                                    </li>
                                    <li>
                                        <a class="uk-drop-close" href="#demo" role="button">
                                            <uk-icon class="mr-2" icon="check"></uk-icon>
                                            Archived Tasks
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            // Toggle check icon for view dropdown items using opacity
                            const viewItems = document.querySelectorAll('#view-dropdown li a');
                            viewItems.forEach(function (item) {
                                // Only apply to items whose text content is "Title", "Status", or "Priority"
                                const text = item.textContent.trim();
                                if (['Title', 'Status', 'Priority'].includes(text)) {
                                    item.addEventListener('click', function (e) {
                                        e.preventDefault();
                                        let icon = item.querySelector('uk-icon');
                                        if (icon) {
                                            // Toggle opacity between 1 and 0
                                            icon.style.opacity = (icon.style.opacity === '0' ? '1' : '0');
                                        } else {
                                            // Create a new icon if it doesn't exist
                                            const newIcon = document.createElement('uk-icon');
                                            newIcon.className = 'mr-2';
                                            newIcon.setAttribute('icon', 'check');
                                            newIcon.style.opacity = '1';
                                            item.prepend(newIcon);
                                        }
                                    });
                                }
                            });
                        });
                    </script>
                    <style>
                        /* Add fixed height container styles */
                        .order-table-container {
                            flex: 1;
                            overflow-y: auto;
                            scrollbar-width: none;
                            /* For Firefox */
                        }

                        .order-table-container::-webkit-scrollbar {
                            display: none;
                            /* For Chrome, Safari and Opera */
                        }

                        /* Style for icon images */
                        .platform-icon,
                        .language-icon {
                            width: 20px;
                            height: 20px;
                            object-fit: contain;
                        }

                        /* Category label styles */
                        .category-label {
                            display: inline-flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 0.25rem;
                            font-size: 0.75rem;
                            font-weight: 500;
                            padding: 0.25rem 0.5rem;
                            background-color: var(--accent);
                            color: var(--dropdown-text);
                        }

                        /* Enhanced fixed header solution */
                        .order-table-container {
                            flex: 1;
                            max-height: 80vh;
                            overflow-y: auto;
                            position: relative;
                        }

                        /* Make header fully opaque without shadow */
                        .order-table-container thead {
                            position: sticky;
                            top: 0;
                            z-index: 1;
                        }

                        .order-table-container thead tr {
                            background-color: var(--background);
                        }

                        /* Ensure each header cell has proper background */
                        .order-table-container thead th {
                            background-color: var(--background);
                            position: relative;
                            z-index: 11;
                        }

                        /* Specific theme colors */
                        .light .order-table-container thead tr,
                        .light .order-table-container thead th {
                            background-color: white;
                        }

                        .pure-black .order-table-container thead tr,
                        .pure-black .order-table-container thead th {
                            background-color: #09090b;
                        }

                        /* New styles for icons and room number */
                        .language-cell {
                            margin-left: 4px;
                        }

                        .platform-cell {
                            margin-left: 4px;
                        }

                        .room-number {
                            transform: translateX(8px);
                        }

                        .language-icon-container {
                            transform: translateX(8px);
                        }

                        .platform-icon-container {
                            transform: translateX(8px);
                        }

                        /* Task row hover effect */
                        .uk-table-hover tbody tr {
                            transition: background-color 0.2s ease;
                        }

                        .light .uk-table-hover tbody tr:hover {
                            background-color: rgba(0, 0, 0, 0.04);
                            cursor: pointer;
                        }

                        .pure-black .uk-table-hover tbody tr:hover {
                            background-color: rgba(255, 255, 255, 0.05);
                            cursor: pointer;
                        }

                        .order-table-container {
                            flex: 1;
                            max-height: calc(100vh - 350px);
                            /* Adjusted to account for header, cards, and filters */
                            overflow-y: auto;
                            position: relative;
                            scrollbar-width: none;
                            /* For Firefox */
                            -ms-overflow-style: none;
                            /* For IE and Edge */
                        }
                    </style>

                    <div class="uk-overflow-auto mt-4 rounded-md border border-border card order-table-container">
                        <table class="uk-table uk-table-middle uk-table-divider uk-table-hover uk-table-small card">
                            <thead class="card sticky top-0 z-1">
                                <tr class="card">
                                    <th class="uk-table-shrink p-2">
                                        <div class="checkbox-wrapper-46">
                                            <input type="checkbox" id="select_all" class="inp-cbx">
                                            <label for="select_all" class="cbx">
                                                <span>
                                                    <svg viewBox="0 0 12 10">
                                                        <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                                    </svg>
                                                </span>
                                            </label>
                                        </div>
                                    </th>
                                    <th class="p-2">Order #</th>
                                    <th class="p-2">Guest Name</th>
                                    <th class="p-2">Guest Phone/ID</th>
                                    <th class="p-2">Room</th>
                                    <th class="uk-table-expand p-2">Item</th>
                                    <th class="p-2">Language</th>
                                    <th class="p-2">Platform</th>
                                    <th class="p-2">Price</th>
                                    <th class="uk-table-shrink p-2"></th>
                                </tr>
                            </thead>
                            <tbody id="order-table-body">
                                <!-- Table rows will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <script>
                    // Simple realtime implementation for Task_list
                    document.addEventListener('DOMContentLoaded', function () {

                    });

                    function updateTaskStatusCounts(data) {
                        try {
                            // Total tasks (first card)
                            document.querySelector('.grid.grid-cols-4 .card:nth-child(1) .text-2xl').textContent = data.length;

                            // Invalid tasks (second card)
                            const invalidCount = data.filter(task =>
                                task.Status === 'Invalid' || task.Status === 'Cancelled').length;
                            document.querySelector('.grid.grid-cols-4 .card:nth-child(2) .text-2xl').textContent = invalidCount;

                            // Completed tasks (third card)
                            const completedCount = data.filter(task =>
                                task.Status === 'Completed' || task.Status === 'Done').length;
                            document.querySelector('.grid.grid-cols-4 .card:nth-child(3) .text-2xl').textContent = completedCount;

                            // In progress tasks (fourth card)
                            const inProgressCount = data.filter(task =>
                                task.Status === 'In Progress').length;
                            document.querySelector('.grid.grid-cols-4 .card:nth-child(4) .text-2xl').textContent = inProgressCount;
                        } catch (error) {
                            console.error("Error updating task status counts:", error);
                        }
                    }

                    document.addEventListener('DOMContentLoaded', function () {
                        // Fetch data from the /tasklist endpoint
                        // Wait for Supabase to be initialized
                        setTimeout(() => {
                            if (!window.supabase) {
                                console.error("Supabase client not available");
                                return;
                            }

                            console.log("Setting up simple realtime subscription");

                            // Create simple channel
                            const channel = supabase.channel('table-updates');

                            // Subscribe to all Task_list changes
                            channel
                                .on('postgres_changes', {
                                    event: '*',
                                    schema: 'public',
                                    table: 'Task_list'
                                }, (payload) => {
                                    console.log("Change detected:", payload);

                                    // Simply refresh data when any change happens
                                    fetch('/tasklist')
                                        .then(response => response.json())
                                        .then(data => {
                                            // Update the table with fresh data
                                            window.allTaskData = data;
                                            data.sort((a, b) => a["Task ID"].localeCompare(b["Task ID"]));
                                            populateTable(data);
                                            updateTaskStatusCounts(data);

                                            // Show simple notification
                                            const notification = document.createElement('div');
                                            notification.textContent = `Task ${payload.eventType.toLowerCase()}d!`;
                                            notification.style = 'position:fixed; top:20px; right:20px; background:#4CAF50; color:white; padding:10px; border-radius:4px; z-index:9999;';
                                            document.body.appendChild(notification);
                                            setTimeout(() => notification.remove(), 3000);
                                        });
                                })
                                .subscribe();
                        }, 500); // Short delay to ensure Supabase is loaded


                        fetch('/tasklist')
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error('Network response was not ok');
                                }
                                return response.json();
                            })
                            .then(data => {
                                window.allTaskData = data; // store globally for filtering
                                data.sort((a, b) => a["Task ID"].localeCompare(b["Task ID"]));
                                populateTable(data);
                                updateCategoryFilters(data);
                                updatePriceFilters(data);
                            })
                            .catch(error => {
                                console.error('Error fetching task list:', error);
                                document.getElementById('order-table-body').innerHTML =
                                    '<tr><td colspan="10" class="text-center p-4">Failed to load data. Please try again later.</td></tr>';
                            });
                        fetchTaskList();

                        // Function to fetch data from /tasklist
                        function fetchTaskList() {
                            fetch('/tasklist')
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error('Network response was not ok');
                                    }
                                    return response.json();
                                })
                                .then(data => {
                                    // Sort data by Task ID
                                    data.sort((a, b) => {
                                        return a["Task ID"].localeCompare(b["Task ID"]);
                                    });

                                    populateTable(data);
                                    updateCategoryFilters(data);
                                    updatePriceFilters(data);
                                })
                                .catch(error => {
                                    console.error('Error fetching task list:', error);
                                    document.getElementById('order-table-body').innerHTML =
                                        '<tr><td colspan="10" class="text-center p-4">Failed to load data. Please try again later.</td></tr>';
                                });
                        }

                        // Function to populate the table with data
                        function populateTable(data) {
                            const tableBody = document.getElementById('order-table-body');
                            tableBody.innerHTML = '';

                            data.forEach((order, index) => {
                                // Parse the pricing and quantity JSON strings if they exist
                                let pricing = {};
                                let quantities = {};

                                try {
                                    if (order.Pricing) {
                                        pricing = JSON.parse(order.Pricing);
                                    }
                                    if (order.Quantity) {
                                        quantities = JSON.parse(order.Quantity);
                                    }
                                } catch (e) {
                                    console.error('Error parsing JSON:', e);
                                }

                                // Create the items display with quantities
                                const items = order.Items ? order.Items.split(', ') : [];
                                const itemsWithQuantity = items.map(item => {
                                    const qty = quantities[item] ? ` (${quantities[item]})` : '';
                                    return `${item}${qty}`;
                                }).join(', ');

                                // Format phone number
                                const phone = order["Phone Number"] ? formatPhoneNumber(order["Phone Number"].toString()) : '';

                                // Get language image or default text
                                const languageDisplay = order.Language === 'English' ?
                                    `<img src="https://cdn-icons-png.flaticon.com/128/197/197374.png" alt="English" class="language-icon">` :
                                    `<span class="uk-label capitalize card">${order.Language?.toLowerCase() || ''}</span>`;

                                // Get platform image
                                const platformDisplay = order.Platform === 'Whatsapp' ?
                                    `<img src="/static/icons/whatsapp2.png" alt="WhatsApp" class="platform-icon">` :
                                    `<span>${order.Platform || ''}</span>`;

                                const row = document.createElement('tr');
                                row.setAttribute('data-task-id', order["Task ID"] || '');

                                row.innerHTML = `
                                            <td class="uk-table-shrink p-2">
                                                <div class="checkbox-wrapper-46">
                                                    <input type="checkbox" id="task_${index}" class="inp-cbx">
                                                    <label for="task_${index}" class="cbx">
                                                        <span>
                                                            <svg viewBox="0 0 12 10">
                                                                <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                                            </svg>
                                                        </span>
                                                    </label>
                                                </div>
                                            </td>
                                            <td class="p-2">${order["Task ID"] || ''}</td>
                                            <td class="p-2">
                                                <span class="font-medium">${order["Guest Name"] || ''}</span>
                                            </td>
                                            <td class="p-2">
                                                <span class="text-muted-foreground">${phone}</span>
                                            </td>
                                            <td class="p-2">
                                                <span class="uk-label uk-label-primary" style="margin-right: 12px">${order["Room Number"] || ''}</span>
                                            </td>
                                            <td class="uk-table-expand max-w-[300px] p-2">
                                                <div class="truncate">${itemsWithQuantity}</div>
                                            </td>
                                            <td class="p-2">
                                                <div class="language-icon-container">${languageDisplay}
                                            </div>
                                            </td>
                                            <td class="p-2">
                                                <div class="flex flex-1 items-center gap-x-2 platform-icon-container">
                                                    ${platformDisplay}
                                                </div>
                                            </td>
                                            <td class="uk-text-nowrap p-2">
                                                <div class="flex flex-1 items-center gap-x-2">
                                                    <span class="font-medium">€${order["Total Price"] || '0'}</span>
                                                </div>
                                            </td>
                                            <td class="p-2">
                                                <button class="uk-icon-button uk-icon-button-xsmall" style="background: transparent;" aria-haspopup="true">
                                                    <span class="size-4">
                                                        <uk-icon icon="ellipsis"></uk-icon>
                                                    </span>
                                                </button>
                                                <div class="uk-drop uk-dropdown" uk-dropdown="mode: click; pos: bottom-right">
                                                    <ul class="uk-dropdown-nav uk-nav">
                                                        <li><a class="uk-drop-close justify-between" href="#demo" role="button">Assign to Staff</a></li>
                                                        <li><a class="uk-drop-close justify-between" href="#demo" role="button">Chat with User</a></li>
                                                        <li class="uk-nav-divider"></li>
                                                        <li>
                                                            <a class="uk-drop-close justify-between" href="#demo" role="button">
                                                                Mark as invalid
                                                                <span class="ml-auto text-xs tracking-widest opacity-60">⌘⌫</span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        `;
                                tableBody.appendChild(row);
                            });
                        }

                        // Function to format phone number
                        function formatPhoneNumber(phoneNumber) {
                            // Format: +91 XXXX XXX XXX (assuming Indian number format)
                            if (phoneNumber.length >= 12) {
                                return `+${phoneNumber.substring(0, 2)} ${phoneNumber.substring(2, 6)} ${phoneNumber.substring(6, 9)} ${phoneNumber.substring(9)}`;
                            }
                            return phoneNumber;
                        }

                        // Function to update category filters based on data
                        function updateCategoryFilters(data) {
                            // Get unique categories
                            const categories = [...new Set(data.map(item => item.Category))].sort();
                            const categoryDropdown = document.querySelector('#tasks-category-dropdown .uk-dropdown-nav');

                            if (categoryDropdown) {
                                // Clear existing category items (keep the divider)
                                const divider = categoryDropdown.querySelector('.uk-nav-divider');
                                categoryDropdown.innerHTML = '';
                                if (divider) categoryDropdown.appendChild(divider.cloneNode(true));

                                // Map of category icons
                                const categoryIcons = {
                                    'Food': `<svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-hand-platter-icon lucide-hand-platter"><path d="M12 3V2" /><path d="m15.4 17.4 3.2-2.8a2 2 0 1 1 2.8 2.9l-3.6 3.3c-.7.8-1.7 1.2-2.8 1.2h-4c-1.1 0-2.1-.4-2.8-1.2l-1.302-1.464A1 1 0 0 0 6.151 19H5" /><path d="M2 14h12a2 2 0 0 1 0 4h-2" /><path d="M4 10h16" /><path d="M5 10a7 7 0 0 1 14 0" /><path d="M5 14v6a1 1 0 0 1-1 1H2" /></svg>`,
                                    'Beverage': `<svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-martini-icon lucide-martini"><path d="M8 22h8" /><path d="M12 11v11" /><path d="m19 3-7 8-7-8Z" /></svg>`,
                                    'Food & Beverage': `<svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-hand-platter-icon lucide-hand-platter"><path d="M12 3V2" /><path d="m15.4 17.4 3.2-2.8a2 2 0 1 1 2.8 2.9l-3.6 3.3c-.7.8-1.7 1.2-2.8 1.2h-4c-1.1 0-2.1-.4-2.8-1.2l-1.302-1.464A1 1 0 0 0 6.151 19H5" /><path d="M2 14h12a2 2 0 0 1 0 4h-2" /><path d="M4 10h16" /><path d="M5 10a7 7 0 0 1 14 0" /><path d="M5 14v6a1 1 0 0 1-1 1H2" /></svg>`,
                                    'Spa': `<svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles-icon lucide-sparkles"><path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" /><path d="M20 3v4" /><path d="M22 5h-4" /><path d="M4 17v2" /><path d="M5 18H3" /></svg>`,
                                    'Massage': `<svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-icon lucide-user"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" /><circle cx="12" cy="7" r="4" /></svg>`,
                                    'Spa & Massage': `<svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles-icon lucide-sparkles"><path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" /><path d="M20 3v4" /><path d="M22 5h-4" /><path d="M4 17v2" /><path d="M5 18H3" /></svg>`,
                                    'Room Booking': `<svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bed-double-icon lucide-bed-double"><path d="M2 20v-8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v8" /><path d="M4 10V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v4" /><path d="M12 4v6" /><path d="M2 18h20" /></svg>`,
                                    'Room Bookings': `<svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bed-double-icon lucide-bed-double"><path d="M2 20v-8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v8" /><path d="M4 10V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v4" /><path d="M12 4v6" /><path d="M2 18h20" /></svg>`
                                };

                                // Add new category items with their icons
                                categories.forEach(category => {
                                    const count = data.filter(item => item.Category === category).length;
                                    // Get the icon for this category or use an empty string if not found
                                    const icon = categoryIcons[category] || '';

                                    const li = document.createElement('li');
                                    li.innerHTML = `
                                    <a class="uk-drop-close" href="#demo" role="button">
                                        <div class="flex flex-1 items-center justify-between">
                                            <div class="flex flex-1 items-center gap-x-2">
                                                ${icon}
                                                <span>${category}</span>
                                            </div>
                                            <span class="font-geist-mono text-xs">${count}</span>
                                        </div>
                                    </a>
                                `;
                                    categoryDropdown.appendChild(li);
                                });
                            }
                            // Add event listeners to category dropdown items for filtering
                            setTimeout(() => {
                                const categoryDropdown = document.querySelector('#tasks-category-dropdown .uk-dropdown-nav');
                                if (!categoryDropdown) return;

                                categoryDropdown.querySelectorAll('li a').forEach(link => {
                                    link.addEventListener('click', function (e) {
                                        e.preventDefault();
                                        const categoryText = this.innerText.trim().split('\n')[0];

                                        // Map dropdown option to matching categories
                                        const categoryMap = {
                                            'Food': ['Food', 'Food & Beverage'],
                                            'Beverage': ['Beverage', 'Food & Beverage'],
                                            'Spa': ['Spa & Massage'],
                                            'Massage': ['Spa & Massage'],
                                            'Room Booking': ['Room Bookings']
                                        };

                                        const matchCategories = categoryMap[categoryText] || [];

                                        const filteredData = window.allTaskData.filter(item => matchCategories.includes(item.Category));

                                        populateTable(filteredData);

                                        // Update the selected status container
                                        const statusContainer = document.getElementById('selected-status-container');
                                        statusContainer.innerHTML = '';
                                        statusContainer.classList.remove('hidden');

                                        const pill = document.createElement('div');
                                        pill.className = 'inline-flex items-center rounded-sm bg-accent/50 px-1.5 py-0.5 text-xs font-medium mx-0.5';
                                        pill.textContent = categoryText;

                                        const removeBtn = document.createElement('button');
                                        removeBtn.className = 'ml-1 size-3 rounded-sm opacity-60 ring-offset-background hover:opacity-100';
                                        removeBtn.style.backgroundColor = 'transparent';
                                        removeBtn.innerHTML = '<uk-icon icon="x" ratio="0.6"></uk-icon><span class="sr-only">Remove</span>';

                                        removeBtn.addEventListener('click', function () {
                                            statusContainer.classList.add('hidden');
                                            populateTable(window.allTaskData);
                                        });

                                        pill.appendChild(removeBtn);
                                        statusContainer.appendChild(pill);
                                    });
                                });
                            }, 500);


                        }

                        // Function to update price filters based on data
                        function updatePriceFilters(data) {
                            // Create price ranges based on the data
                            const prices = data.map(item => Number(item["Total Price"] || 0));
                            const maxPrice = Math.max(...prices);

                            let priceRanges = [
                                { label: '€0-€50', filter: price => price <= 50 },
                                { label: '€51-€100', filter: price => price > 50 && price <= 100 },
                                { label: '€101-€150', filter: price => price > 100 && price <= 150 }
                            ];

                            // Add higher range if there are items with higher prices
                            if (maxPrice > 150) {
                                priceRanges.push({ label: '€151+', filter: price => price > 150 });
                            }

                            const priceDropdown = document.querySelector('.price-filter-dropdown .uk-dropdown-nav');

                            if (priceDropdown) {
                                priceDropdown.innerHTML = '';

                                priceRanges.forEach(range => {
                                    const count = data.filter(item => range.filter(Number(item["Total Price"] || 0))).length;

                                    const li = document.createElement('li');
                                    li.innerHTML = `
                                                <a class="uk-drop-close" href="#demo" role="button">
                                                    <div class="flex flex-1 items-center justify-between">
                                                        <div class="flex flex-1 items-center gap-x-2">
                                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none"
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                class="mr-2 h-4 w-4 text-muted-foreground">
                                                                <path
                                                                    d="M7.5 2C7.77614 2 8 2.22386 8 2.5L8 11.2929L11.1464 8.14645C11.3417 7.95118 11.6583 7.95118 11.8536 8.14645C12.0488 8.34171 12.0488 8.65829 11.8536 8.85355L7.85355 12.8536C7.75979 12.9473 7.63261 13 7.5 13C7.36739 13 7.24021 12.9473 7.14645 12.8536L3.14645 8.85355C2.95118 8.65829 2.95118 8.34171 3.14645 8.14645C3.34171 7.95118 3.65829 7.95118 3.85355 8.14645L7 11.2929L7 2.5C7 2.22386 7.22386 2 7.5 2Z"
                                                                    fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
                                                            </svg>
                                                            <span>${range.label}</span>
                                                        </div>
                                                        <span class="font-geist-mono text-xs">${count}</span>
                                                    </div>
                                                </a>
                                            `;
                                    priceDropdown.appendChild(li);
                                });
                            }
                        }

                        // Handle "Select All" checkbox
                        document.getElementById('select_all').addEventListener('change', function () {
                            const isChecked = this.checked;
                            document.querySelectorAll('#order-table-body input[type="checkbox"]').forEach(checkbox => {
                                checkbox.checked = isChecked;
                            });
                        });
                    });
                </script>
                <script>
                    // Add real-time filtering functionality
                    document.addEventListener('DOMContentLoaded', function () {
                        const filterInput = document.querySelector('input[placeholder="Filter Tasks..."]');
                        const tableBody = document.getElementById('order-table-body');

                        filterInput.addEventListener('input', function () {
                            const searchText = this.value.toLowerCase();
                            const rows = tableBody.querySelectorAll('tr');

                            rows.forEach(row => {
                                const cells = row.querySelectorAll('td');
                                let shouldShow = false;

                                // Check each cell except the first (checkbox) and last (actions)
                                for (let i = 1; i < cells.length - 1; i++) {
                                    const cellText = cells[i].textContent.toLowerCase();
                                    if (cellText.includes(searchText)) {
                                        shouldShow = true;
                                        break;
                                    }
                                }

                                row.style.display = shouldShow ? '' : 'none';
                            });
                        });
                    });
                </script>
            </main>
            <div id="task-detail-modal" uk-modal
                style="backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px); background: rgba(0, 0, 0, 0.5); z-index: 1050;">
                <div class="uk-modal-dialog uk-modal-body card bg-background text-foreground"
                    style="backdrop-filter: none !important; -webkit-backdrop-filter: none !important; opacity: 1 !important; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); width: 400px; height: auto; padding: 24px 24px 0 24px; overflow: hidden;">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
                                <line x1="8" y1="2" x2="8" y2="6" />
                                <line x1="16" y1="2" x2="16" y2="6" />
                                <line x1="3" y1="10" x2="21" y2="10" />
                            </svg>
                            <h2 class="text-base font-medium m-0 ml-2" id="task-detail-title">#<span
                                    id="task-detail-id"></span></h2>
                        </div>
                        <div class="flex items-center">
                            <!-- Status badge moved next to close button -->
                            <span class="badge pending mr-3">Pending</span>
                            <button class="uk-modal-close no-hover-effect" type="button"
                                style="position: static; background: none; border: none; padding: 0; margin: 0;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round">
                                    <path d="M18 6 6 18" />
                                    <path d="m6 6 12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <hr class="uk-hr card mt-4 mb-4 divider" />

                    <!-- Guest Information Section -->
                    <div class="mb-3">
                        <div class="grid grid-cols-2 gap-x-6 gap-y-4">
                            <!-- Left Column -->
                            <div class="space-y-4">
                                <div>
                                    <p class="text-sm text-muted-foreground font-light mb-1">Guest</p>
                                    <p class="font-medium text-base" id="task-detail-guest"></p>
                                </div>
                                <div>
                                    <p class="text-sm text-muted-foreground font-light mb-1">Room</p>
                                    <p class="font-medium text-base" id="task-detail-room"></p>
                                </div>
                                <div>
                                    <p class="text-sm text-muted-foreground font-light mb-1">Platform</p>
                                    <p class="font-medium text-base" id="task-detail-platform">WhatsApp</p>
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="space-y-4">
                                <div>
                                    <p class="text-sm text-muted-foreground font-light mb-1">Phone</p>
                                    <p class="font-medium text-base" id="task-detail-phone"></p>
                                </div>
                                <div>
                                    <p class="text-sm text-muted-foreground font-light mb-1">Language</p>
                                    <div class="flex items-center gap-2" id="task-detail-language-container">
                                        <img id="task-detail-language-flag"
                                            src="https://cdn-icons-png.flaticon.com/128/197/197374.png"
                                            alt="Language Flag" class="w-5 h-5 rounded-sm">
                                        <span id="task-detail-language" class="font-medium text-base">English</span>
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm text-muted-foreground font-light mb-1">Category</p>
                                    <p class="font-medium text-base" id="task-detail-category">Food & Beverages</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End of Guest Information Section -->

                    <hr class="uk-hr card mt-4 mb-4 divider" />

                    <!-- Order Items Section -->
                    <div class="mt-4 mb-2">
                        <h3 class="text-sm font-light mb-2 flex items-center">Order Items : <span
                                id="task-detail-items-count"
                                class="text-xs px-2 py-0.5 rounded-full bg-accent/50 font-medium">0</span></h3>
                        <div id="task-detail-items" class="space-y-1">

                        </div>
                    </div>

                    <!-- Price Information -->
                    <div class="flex justify-between my-4 total-price">
                        <span class="font-medium">Total</span>
                        <span class="font-bold text-green-600" id="task-detail-price">€125.00</span>
                    </div>

                    <hr class="uk-hr card mt-4 mb-4" style="margin-top: 24px; margin-bottom: 24px;" />

                    <!-- Action Buttons -->
                    <div class="flex justify-around action-buttons">
                        <button class="uk-button uk-button-default assign-button card"
                            style="background-color: transparent;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="mr-2">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                                <circle cx="9" cy="7" r="4" />
                                <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                                <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                            </svg>
                            Assign to a Staff
                        </button>
                        <button class="uk-button uk-button-primary opp-color-button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="mr-2">
                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                                <polyline points="22 4 12 14.01 9 11.01" />
                            </svg>
                            Mark as Complete
                        </button>
                    </div>
                    <!-- Spacer div removed to avoid double spacing -->
                </div>
            </div>

            <style>
                /* Task detail modal styling */
                #task-detail-modal .uk-modal-dialog {
                    max-width: 400px;
                    height: auto;
                    border-radius: 12px;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;
                    position: relative;
                }

                #task-detail-modal .uk-modal-dialog>.action-buttons {
                    margin-bottom: 24px !important;
                }

                #task-detail-modal .uk-modal-dialog>*:last-child {
                    margin-bottom: 24px;
                    padding-bottom: 0 !important;
                }

                /* Status badge styling */
                .badge {
                    display: inline-flex;
                    align-items: center;
                    padding: 4px 12px;
                    border-radius: 16px;
                    font-size: 12px;
                    font-weight: 500;
                }

                .badge.pending {
                    background-color: rgba(251, 191, 36, 0.1);
                    color: #f59e0b;
                }

                /* Task ID styling in header */
                #task-detail-title {
                    display: flex;
                    align-items: center;
                }

                #task-detail-id {
                    display: inline !important;
                }



                .quantity-button {
                    color: black;
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    background-color: #f3f4f6;
                    border: none;
                    border-radius: 6px;
                    padding: 4fpx;
                    padding-left: 12px;
                    padding-right: 12px;
                    cursor: pointer;
                }

                /* Action buttons styling */
                .action-buttons {
                    position: relative;
                    width: 100%;
                    margin-bottom: 24px;
                }


                .uk-button {
                    border-radius: 8px;
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 8px 12px;
                    height: 42px;
                }

                .assign-button {
                    border: 1px solid #e5e7eb;
                    background-color: white;
                    color: #374151;
                }

                .complete-button {
                    background-color: #18181b;
                    color: white;
                    border: none;
                }

                /* Total price styling */
                .total-price {
                    margin-top: 16px;
                    margin-bottom: 0;
                }

                #task-detail-price {
                    color: #10b981;
                }

                /* Make the order items section scrollable */
                #task-detail-items {
                    max-height: 120px;
                    overflow-y: auto;
                    border-radius: 8px;
                    padding: 0 2px;
                    margin-bottom: 16px;
                }

                /* Divider styling */
                .uk-hr.card.divider {
                    margin: 12px 0;

                }

                /* Animation for modal entrance */
                #task-detail-modal .uk-modal-dialog {
                    animation: modalFadeIn 0.2s ease-out;
                }

                @keyframes modalFadeIn {
                    from {
                        opacity: 0;
                        transform: translateY(10px) scale(0.98);
                    }

                    to {
                        opacity: 1;
                        transform: translateY(0) scale(1);
                    }
                }

                /* Remove extra margin from the modal's last child to ensure only 24px gap below the action buttons */
                #task-detail-modal .uk-modal-dialog>div:last-child {

                    padding-bottom: 0 !important;
                }
            </style>

            <script>
                // Enhanced robust click handler for task rows
                document.addEventListener('DOMContentLoaded', function () {
                    console.log("Setting up task detail click handlers");

                    // Wait a short time to ensure table is populated
                    setTimeout(() => {
                        // Add event listener to the table body
                        const tableBody = document.getElementById('order-table-body');
                        if (!tableBody) {
                            console.error("Could not find order-table-body element");
                            return;
                        }

                        // Use delegation for better reliability
                        document.addEventListener('click', function (e) {
                            // Find if we clicked on or inside a table row
                            const row = e.target.closest('#order-table-body tr');
                            if (!row) return;

                            // Skip if clicking on checkbox or action buttons
                            if (e.target.closest('input[type="checkbox"]') ||
                                e.target.closest('button') ||
                                e.target.closest('a')) {
                                return;
                            }

                            console.log("Task row clicked:", row);

                            // Get task ID from the row
                            const taskId = row.getAttribute('data-task-id');
                            if (!taskId) {
                                console.error("No task ID found on row");
                                return;
                            }

                            console.log("Opening details for task:", taskId);

                            // Find the task in the global allTaskData array
                            if (!window.allTaskData) {
                                console.error("Task data not loaded yet");
                                return;
                            }

                            const task = window.allTaskData.find(t => t["Task ID"] === taskId);
                            if (!task) {
                                console.error("Task not found in data");
                                return;
                            }

                            // Populate the modal with task details
                            document.getElementById('task-detail-id').textContent = task["Task ID"] || '';
                            document.getElementById('task-detail-guest').textContent = task["Guest Name"] || 'N/A';
                            document.getElementById('task-detail-phone').textContent = formatPhoneNumber(task["Phone Number"] || '');
                            document.getElementById('task-detail-room').textContent = task["Room Number"] || 'N/A';
                            document.getElementById('task-detail-category').textContent = task["Category"] || 'Food & Beverages';
                            document.getElementById('task-detail-platform').textContent = task["Platform"] || 'WhatsApp';

                            // Set language with flag
                            const languageText = document.getElementById('task-detail-language');
                            const languageFlag = document.getElementById('task-detail-language-flag');

                            languageText.textContent = task.Language || 'English';

                            // Choose flag based on language
                            if (task.Language === 'English') {
                                languageFlag.src = 'https://cdn-icons-png.flaticon.com/128/197/197374.png';
                            } else {
                                languageFlag.src = 'https://cdn-icons-png.flaticon.com/128/197/197374.png'; // Default to UK flag
                            }

                            // Clear and populate items
                            const itemsContainer = document.getElementById('task-detail-items');
                            itemsContainer.innerHTML = '';

                            // Parse the items from task data
                            let items = task.Items ? task.Items.split(', ') : [];
                            let quantities = {};
                            let pricing = {};

                            // Try to parse quantities and pricing
                            try {
                                if (task.Quantity) quantities = JSON.parse(task.Quantity);
                                if (task.Pricing) pricing = JSON.parse(task.Pricing);
                            } catch (e) {
                                console.error('Error parsing JSON:', e);
                            }

                            // Calculate total number of items
                            let totalItemCount = 0;
                            items.forEach(item => {
                                let quantityStr = quantities[item] || "x1";
                                let quantity = 1;
                                if (typeof quantityStr === "string" && quantityStr.startsWith("x")) {
                                    quantity = parseInt(quantityStr.substring(1)) || 1;
                                } else if (!isNaN(quantityStr)) {
                                    quantity = Number(quantityStr);
                                }
                                totalItemCount += quantity;
                            });

                            // Update the items count display
                            document.getElementById('task-detail-items-count').textContent = totalItemCount;

                            // Add each item to the container
                            let totalPrice = 0;
                            items.forEach(item => {
                                // Parse quantity: "x1" → 1
                                let quantityStr = quantities[item] || "x1";
                                let quantity = 1;
                                if (typeof quantityStr === "string" && quantityStr.startsWith("x")) {
                                    quantity = parseInt(quantityStr.substring(1)) || 1;
                                } else if (!isNaN(quantityStr)) {
                                    quantity = Number(quantityStr);
                                }

                                // Parse price: "45" → 45
                                let priceStr = pricing[item] || "0";
                                let price = parseFloat(priceStr) || 0;

                                let itemTotal = price * quantity;
                                totalPrice += itemTotal;

                                // Truncate item name if longer than 25 characters
                                const displayItemName = item.length > 25 ? item.substring(0, 25) + '...' : item;
                                const fullItemName = item; // Store the full name for the tooltip

                                const itemElement = document.createElement('div');
                                itemElement.className = 'flex card justify-between items-center py-1.5 px-2 border-b border-border/30';
                                itemElement.innerHTML = `
                                <div class="flex items-center gap-1">
                                    <span class="text-sm" title="${fullItemName}">${displayItemName}</span> <!-- Added title attribute -->
                                    <span class="text-xs text-muted-foreground ml-1 quantity-button">x${quantity}</span>
                                </div>
                                <span class="text-sm">€${itemTotal.toFixed(2)}</span>
                            `;
                                itemsContainer.appendChild(itemElement);
                            });

                            // Set total price
                            document.getElementById('task-detail-price').textContent = `€${totalPrice.toFixed(2)}`;

                            // Show the modal using UIkit
                            UIkit.modal('#task-detail-modal').show();
                        });

                        // Helper function to format phone number
                        function formatPhoneNumber(phoneNumber) {
                            if (phoneNumber && phoneNumber.length >= 10) {
                                // Format: +91 98 48 99 5046 (based on your image)
                                const parts = [];
                                parts.push('+' + phoneNumber.substring(0, 2)); // Country code
                                parts.push(phoneNumber.substring(2, 4));
                                parts.push(phoneNumber.substring(4, 6));
                                parts.push(phoneNumber.substring(6, 8));
                                parts.push(phoneNumber.substring(8));
                                return parts.join(' ');
                            }
                            return phoneNumber;
                        }

                        console.log("Task detail click handlers configured");
                    }, 1000);

                    // Add listener for the "Mark as Complete" button
                    const completeButton = document.querySelector('#task-detail-modal .opp-color-button');
                    if (completeButton) {
                        completeButton.addEventListener('click', function () {
                            const taskId = document.getElementById('task-detail-id').textContent;
                            console.log(`Marking task ${taskId} as complete (UI only)`);

                            // Hide the modal first
                            UIkit.modal('#task-detail-modal').hide();

                            // Show success notification with GIF
                            const notification = UIkit.notification({
                                message: `
                                    <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                                        <span>Task #${taskId} completed.</span>
                                        <button id="undo-task-${taskId}" class="undo-button" style="
                                            padding: 4px 8px;
                                            border-radius: 4px;
                                            font-size: 12px;
                                            cursor: pointer;
                                            margin-left: 12px;
                                            white-space: nowrap;
                                            transition: all 0.2s ease;
                                        ">
                                            Undo
                                        </button>
                                    </div>
                                `,
                                status: 'success', // Use 'success' status for green styling
                                pos: 'bottom-right', // Position the notification
                                timeout: 4000 // Auto-close after 4 seconds
                            });

                            // Add click handler for undo button
                            setTimeout(() => {
                                const undoButton = document.getElementById(`undo-task-${taskId}`);
                                if (undoButton) {
                                    undoButton.addEventListener('click', function(e) {
                                        e.stopPropagation();
                                        console.log(`Undoing completion for task ${taskId}`);
                                        
                                        // Close the notification
                                        notification.close();
                                        
                                        // Show undo confirmation
                                        UIkit.notification({
                                            message: `Task #${taskId} completion undone.`,
                                            status: 'success',
                                            pos: 'bottom-right',
                                            timeout: 2000
                                        });
                                        
                                        // Here you would typically make an API call to revert the task status
                                        // For now, we're just showing the UI feedback
                                    });
                                }
                            }, 100);
                        });
                    } else {
                        console.error("Could not find the 'Mark as Complete' button.");
                    }
                });
            </script>


</body>

</html>