<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Agents</title>
    {% include 'imports.html' %}
    <script src="https://unpkg.com/@supabase/supabase-js"></script>
</head>
<style>
    body {
        visibility: hidden;
    }

    .light .themer-input {
        background-color: transparent !important;
        color: black !important;
        border: transparent !important;
        outline: none !important;
        /* Removes the focus border */
        box-shadow: none !important;
    }

    .pure-black .themer-input {
        background-color: transparent !important;
        color: white !important;
        border: transparent !important;
        border: transparent !important;
        outline: none !important;
        /* Removes the focus border */
        box-shadow: none !important;
    }

    .uk-label {
        color: var(--dropdown-text);
        background-color: var(--background);
    }

    .uk-drop.uk-dropdown {
        color: var(--dropdown-text);
    }

    .light .uk-table-divider>tr:not(:first-child),
    .uk-table-divider>:not(:first-child)>tr,
    .uk-table-divider>:first-child>tr:not(:first-child) {
        border-color: #e5e7eb;
    }

    .pure-black .uk-table-divider>tr:not(:first-child),
    .pure-black .uk-table-divider>:not(:first-child)>tr,
    .pure-black .uk-table-divider>:first-child>tr:not(:first-child) {
        border-color: #27272a;
    }

    .light .uk-label {
        background-color: white;
    }

    .pure-black .uk-label {
        background-color: #09090b;
    }

    /* Alternative solution using pseudo-element */
    .order-table-container thead::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 1px;
        background-color: var(--border-color, #e5e7eb);
    }

    .light .order-table-container thead::after {
        background-color: #e5e7eb;
    }

    .pure-black .order-table-container thead::after {
        background-color: #27272a;
    }

    /* #call-list>button {
        height: 120px;  Removed as we are making it dynamic and taller */
    /* } */
    .clamp-3-lines { /* Ensure this class is defined for summary truncation */
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.4;
        max-height: calc(1.4em * 3);
        margin-bottom: 0 !important; /* Remove spacing since footer was removed */
    }
    /* Matching call list button styling with livechat */
    #call-list button {
        /* margin-bottom: 10px; */ /* Replaced by space-y-3 on #call-list parent */
        transition: background-color 0.15s ease-in-out, transform 0.15s ease-in-out; /* Updated transition */
        background-color: var(--card, transparent); /* Use card background for consistency */
        /* border: 1px solid var(--border-color); */ /* Removed border for borderless design */
        position: relative;
        z-index: 1;
        height: 100px; /* Fixed height instead of min-height */
        padding: 0.5rem; /* Further reduced padding */
        animation: messageIn 0.3s ease-out;
        display: flex;
        flex-direction: column;
        justify-content: flex-start; /* Changed from space-between to flex-start to reduce empty space */
        border-radius: 0.375rem; /* Added for modern look (rounded-md) */
    }

    #call-list button:hover {
        background-color: var(--muted) !important; /* Modern hover color */
        cursor: pointer;
        transform: translateY(-2px); /* Subtle lift effect */
        /* No border */
    }

    #call-list button.active {
        background-color: var(--card, transparent) !important; /* No visible selection indicator */
        /* border-color: var(--border-color); */ /* Removed border */
        transform: translateY(0px); /* Reset or adjust transform for active state */
        /* No border */
        border-left: 3px solid #dc2626 !important; /* Blue left border strip for selected item */
        padding-left: calc(0.5rem - 3px) !important; /* Adjust padding to account for border */
    }

    /* Dark mode support */
    .pure-black #call-list button:hover {
        background-color: #3f3f46 !important; /* Dark mode hover (e.g., Tailwind zinc-700) */
        /* transform is inherited from the general hover rule */
        /* No border */
    }

    .pure-black #call-list button.active {
        background-color: var(--card, transparent) !important; /* No visible selection indicator for dark mode */
        /* border-color: var(--border-color); */ /* Removed border */
        /* transform is inherited from the general active rule */
        /* No border */
        border-left: 3px solid #dc2626 !important; /* Blue left border strip for selected item in dark mode */
        padding-left: calc(0.5rem - 3px) !important; /* Adjust padding to account for border */
    }

    /* Animation for call items appearing */
    @keyframes messageIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Flash animation for selected calls */
    @keyframes flashNew {

        0%,
        100% {
            background-color: transparent;
        }

        50% {
            background-color: rgba(59, 130, 246, 0.15);
        }
    }

    .call-highlight {
        animation: flashNew 0.8s ease-out;
    }

    /* Remove fixed height constraint - already handled by min-height */
    /* #call-list>button {
        height: auto !important;
    } */

    /* Consistent spacing */
    #call-list {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
    }

    /* Call details content panels styling */
    .call-details-content {
        display: none;
        /* animation: fadeIn 0.3s ease-out; */ /* Replaced by fadeInContent */
    }

    .call-details-content.active {
        display: block;
        animation: fadeInContent 0.4s ease-out forwards; /* Use enhanced animation */
    }

    /* @keyframes fadeIn { // Replaced by fadeInContent
        from {
            opacity: 0;
            transform: translateY(10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    } */


    /* Style the call details menubar to match the main menubar (Enhanced version) */
    .call-details-menubar {
        display: flex;
        position: relative;
        background-color: var(--background);
        border-radius: 0.5rem;
        padding: 0.25rem;
        gap: 0.5rem;
        margin-bottom: 1rem;
        border: 1px solid var(--border-color);
    }

    .call-details-menubar a {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        border-radius: 0.25rem;
        color: var(--muted-foreground);
        user-select: none;
        white-space: nowrap;
        text-decoration: none;
        z-index: 1;
        transition: color 0.25s cubic-bezier(0.65, 0, 0.35, 1);
    }

    .call-details-menubar a:hover {
        color: var(--foreground);
    }

    .call-details-menubar-indicator {
        position: absolute;
        height: calc(100% - 0.5rem);
        top: 0.25rem;
        left: 0.25rem;
        transition: all 0.25s cubic-bezier(0.65, 0, 0.35, 1);
        border-radius: 0.25rem;
        background-color: var(--muted);
        z-index: 0;
    }

    .call-details-menubar a.active {
        color: var(--foreground);
        position: relative;
        overflow: hidden;
        border: 1px solid var(--border-color);
        height: 100px;
    }

    .analytics-value { /* This is part of .analytics-metric, kept from enhanced */
        font-size: 1.25rem; /* Reduced from 1.75rem */
        font-weight: 600;
        line-height: 1.1;
        margin-top: auto;
        text-align: left;
    }

    .analytics-label { /* This is part of .analytics-metric, kept from enhanced */
        font-size: 0.875rem;
        color: var(--muted-foreground);
        margin-bottom: 0.5rem;
        font-weight: 500;
        text-align: left;
    }

    /* Enhanced sentiment bar styling */
    .sentiment-bar {
        height: 8px;
        background: linear-gradient(to right, #ef4444, #eab308, #22c55e);
        border-radius: 4px;
        position: relative;
        /* margin-top: 0.5rem; */ /* Removed as enhanced version might have different spacing context */
        /* margin-bottom: 1rem; */ /* Removed as enhanced version might have different spacing context */
    }

    .sentiment-marker {
        position: absolute;
        width: 16px; /* Enhanced */
        height: 16px; /* Enhanced */
        background-color: white;
        border: 2px solid var(--foreground);
        border-radius: 50%;
        transform: translateY(-50%) translateX(-50%); /* Enhanced for better centering */
        top: 50%; /* Enhanced for better centering */
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* Enhanced */
    }

    /* Enhanced call timeline styling */
    .call-timeline {
        position: relative;
        padding-left: 28px; /* Enhanced */
        margin-top: 1.5rem; /* Enhanced */
    }

    .call-timeline::before {
        content: '';
        position: absolute;
        left: 7px; /* Enhanced */
        top: 8px;
        bottom: 8px;
        width: 2px;
        background-color: var(--border-color);
    }

    .call-timeline-item {
        position: relative;
        padding-bottom: 1.5rem; /* Enhanced */
        animation: fadeIn 0.5s ease-out; /* Note: fadeIn keyframes might be different or duplicated, check below */
        animation-fill-mode: both;
    }

    .call-timeline-item:nth-child(1) {
        animation-delay: 0.1s;
    }

    .call-timeline-item:nth-child(2) {
        animation-delay: 0.3s;
    }

    .call-timeline-item:nth-child(3) {
        animation-delay: 0.5s;
    }

    .call-timeline-item::before {
        content: '';
        position: absolute;
        left: -28px; /* Enhanced to match padding-left */
        top: 6px;
        width: 14px; /* Enhanced */
        height: 14px; /* Enhanced */
        border-radius: 50%;
        border: 2px solid var(--background); /* Enhanced */
        background-color: var(--muted-foreground);
        z-index: 1; /* Enhanced */
    }

    .call-timeline-item:first-child::before {
        background-color: #22c55e;
    }

    .call-timeline-item:last-child::before {
        background-color: #ef4444;
    }

    /* Tab content fade in animation */
    /* .call-details-content.active is already defined above with this animation */

    @keyframes fadeInContent { /* This is the keyframe for tab content */
        from {
            opacity: 0;
            transform: translateY(8px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<body class="bg-background text-foreground">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
    <!-- Removed duplicate </head> tag -->

    {% include 'components/loading.html' %}

    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] ">
        {% include 'sidebar.html' %}

        <div class="flex flex-col">
            <header
                class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
                <div style="margin-left: 8px;" class="flex items-center gap-2 px-4 pl-0">
                    <button id="toggle-btn" class="opacity-100 transition-opacity duration-300 focus:outline-none"
                        style="background-color: transparent !important;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-panel-left">
                            <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                            <path d="M9 3v18"></path>
                        </svg>
                    </button>
                    <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-2 h-4"
                        style="background-color: var(--border-color);"></div>
                    <nav aria-label="breadcrumb">
                        <ol
                            class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
                            <div class="menubar" role="menubar">
                                <div class="menubar-indicator"></div>
                                <a href="/livechat" role="menuitem">Omni Channel</a>
                                <a href="/aichat" role="menuitem">Guest Genius</a>
                                <a href="/voicebot" role="menuitem" class="active">Voice Agents</a>
                            </div>
                        </ol>
                    </nav>
                </div>
                {% include 'topright.html' %}
            </header>
            <div class="flex flex-1 h-[calc(100vh-60px)] gap-0 px-0">
                <!-- Left: Call List -->
                <div class="card flex flex-col w-1/3 max-w-xs min-w-[220px] bg-background px-0 h-full">
                    <!-- Replace the current header with this -->
                    <div
                        class="flex h-14 flex-none items-center border-b border-border px-l-4 py-2 card justify-between">
                        <div class="p-4 pr-0 w-full">
                            <div class="uk-inline w-full">
                                <span class="uk-form-icon text-muted-foreground">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-search">
                                        <circle cx="11" cy="11" r="8"></circle>
                                        <path d="m21 21-4.3-4.3"></path>
                                    </svg>
                                </span>
                                <input id="call-search-input"
                                    class="uk-input card w-full rounded-lg p-2 text-sm" type="text"
                                    placeholder="Search by number">
                            </div>
                        </div>
                        <div class="pt-4 pb-4 pl-1 pr-3">
                        </div>
                    </div>
                    <!-- Replace your current call list buttons with these -->
                    <div id="call-list" class="flex-1 overflow-y-auto px-4 space-y-2 max-h-[calc(100vh-120px)]">
                        <!-- Call items are now rendered dynamically by JavaScript -->
                    </div>
                </div>
                <div class="card border-l border-border h-full mx-0"></div>
                <!-- Right: Call Details -->
                <div class="card flex-1 flex flex-col bg-background py-0 px-0">
                    <div class="flex h-14 flex-none items-center border-b border-border p-2 card">
                        <div class="flex w-full justify-between items-center">
                            <h3 class="text-lg font-semibold flex-grow ps-1 ml-1">Call Details</h3>
                            <div class="flex gap-x-2 divide-x divide-border">
                                <ul class="uk-iconnav flex items-center space-x-3">
                                    <ul class="uk-iconnav pl-2 b-l border-left">
                                        <li>
                                            <a href="#demo" aria-haspopup="true"
                                                class="inline-flex items-center justify-center h-8 w-8 rounded chat-dropdown-icon-hover transition-colors duration-150">
                                                <span class="sr-only">Menu</span>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-ellipsis-vertical">
                                                    <circle cx="12" cy="12" r="1"></circle>
                                                    <circle cx="12" cy="5" r="1"></circle>
                                                    <circle cx="12" cy="19" r="1"></circle>
                                                </svg>
                                            </a>
                                            <div class="uk-dropdown uk-drop dropdown-content"
                                                uk-dropdown="pos: bottom-right; mode: click">
                                                <ul class="uk-dropdown-nav uk-nav">
                                                    <li><a class="uk-drop-close" href="#" role="button">Download
                                                            Recording</a></li>
                                                    <li><a class="uk-drop-close" href="#" role="button">Export
                                                            Transcript</a></li>
                                                    <li><a class="uk-drop-close" href="#" role="button">Delete Call</a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </li>
                                    </ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div id="call-details" class="flex-1 p-4">
                        <!-- Replace the current call details menubar with this -->
                        <!-- Replace the current call details menubar with this -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="menubar mr-2" role="menubar">
                                <div class="menubar-indicator"></div>
                                <a href="#" role="menuitem" data-tab="details" class="active">Call Details</a>
                                <a href="#" role="menuitem" data-tab="history">Call History</a>
                            </div>

                            <!-- Settings Icon Dropdown -->
                            <div class="relative">
                                <a href="#tab-settings" aria-haspopup="true"
                                    class="inline-flex items-center justify-center h-8 w-8 rounded chat-dropdown-icon-hover transition-colors duration-150">
                                    <span class="sr-only">Settings</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-settings">
                                        <path
                                            d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z">
                                        </path>
                                        <circle cx="12" cy="12" r="3"></circle>
                                    </svg>
                                </a>
                                <div class="uk-dropdown uk-drop dropdown-content"
                                    uk-dropdown="pos: bottom-right; mode: click">
                                    <ul class="uk-dropdown-nav uk-nav">
                                        <li><a class="uk-drop-close" href="#" role="button">Call Preferences</a></li>
                                        <li><a class="uk-drop-close" href="#" role="button">Notification Settings</a>
                                        </li>
                                        <li><a class="uk-drop-close" href="#" role="button">Display Options</a></li>
                                        <li class="uk-nav-divider"></li>
                                        <li><a class="uk-drop-close" href="#" role="button">Reset View</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div id="details-tab" class="call-details-content active">
                            <div class="flex flex-col items-center justify-center h-[60vh] text-center px-4">
                                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-muted/50 mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="text-muted-foreground">
                                        <path
                                            d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                                        </path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-medium mb-1">No Call Selected</h3>
                                <p class="text-muted-foreground ">Select a call from the list to view detailed
                                    information.</p>
                            </div>
                        </div>

                        <!-- Analytics tab removed as requested -->

                        <div id="history-tab" class="call-details-content">
                            <div class="call-detail-item mb-3">
                                <span class="font-semibold">Previous Calls:</span> <span
                                    class="call-previous-count">--</span>
                            </div>

                            <div class="call-timeline">
                                <div class="call-timeline-item">
                                    <div class="text-sm font-medium">--</div>
                                    <div class="text-xs text-muted-foreground">--</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <script>
                let callsData = [];

                // Add ripple effect to buttons when clicked (optional, can be removed if not used)
                function addRippleEffect(button) {
                    // Ensure 'button-clicked' class and its CSS are defined if this is used.
                    // button.classList.add('button-clicked');
                    // setTimeout(() => {
                    //     button.classList.remove('button-clicked');
                    // }, 1000);
                }

                function renderCallListItems(calls) {
                    const callListContainer = document.getElementById('call-list');
                    if (!callListContainer) {
                        console.error("Call list container (#call-list) not found.");
                        return;
                    }
                    callListContainer.innerHTML = ''; // Clear any previous items

                    if (!calls || calls.length === 0) {
                        // The "No Call Data" message is handled in the details panel by DOMContentLoaded logic
                        return;
                    }

                    calls.forEach((call, index) => {
                        const button = document.createElement('button');
                        // The class 'card' is applied for general card styling, other classes for layout and interaction
                        button.className = 'card w-full text-left p-3 border border-border rounded-lg transition focus:outline-none mb-2';
                        button.onclick = () => showCallDetails(index);
                        // addRippleEffect(button); // Uncomment if ripple effect is desired and styled

                        let formattedTime = call.call_start_time || "N/A";
                        let formattedDate = "";
                        try {
                            // If call_start_time is just time, Date() will use today's date.
                            const callDateTime = new Date(call.call_start_time);
                            if (!isNaN(callDateTime)) {
                                formattedTime = callDateTime.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                                formattedDate = callDateTime.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                            }
                        } catch(e) {
                            console.warn("Could not parse call_start_time for date formatting:", call.call_start_time);
                        }


                        const summaryText = (call.call_summary || 'No summary available');
                        // clamp-2-lines CSS class will handle truncation visually
                        // The title attribute will show the full summary on hover.

                        // Create DOM elements instead of using innerHTML to prevent XSS
                        const container = document.createElement('div');
                        container.className = 'flex flex-col h-full';

                        const contentDiv = document.createElement('div');
                        contentDiv.className = 'flex-grow';

                        const headerDiv = document.createElement('div');
                        headerDiv.className = 'flex justify-between items-start';

                        const phoneDiv = document.createElement('div');
                        phoneDiv.className = 'font-semibold text-sm truncate text-foreground dark:text-foreground-dark';
                        const phoneNumber = call.guest_phone || 'Unknown Number';
                        const formattedPhone = phoneNumber !== 'Unknown Number' ? `+${phoneNumber}` : phoneNumber;
                        const phoneText = document.createTextNode(formattedPhone);
                        phoneDiv.appendChild(phoneText);
                        phoneDiv.setAttribute('title', formattedPhone);

                        const timeSpanHeader = document.createElement('span');
                        timeSpanHeader.className = 'text-xs font-medium px-2 py-0.5 rounded border border-border card bg-card';
                        const timeTextHeader = document.createTextNode(formattedTime);
                        timeSpanHeader.appendChild(timeTextHeader);
                        timeSpanHeader.setAttribute('title', formattedTime);

                        headerDiv.appendChild(phoneDiv);
                        headerDiv.appendChild(timeSpanHeader);

                        const summaryP = document.createElement('p');
                        summaryP.className = 'text-xs text-muted-foreground dark:text-muted-foreground-dark leading-snug clamp-3-lines mt-3';
                        const summaryContent = document.createTextNode(summaryText);
                        summaryP.appendChild(summaryContent);
                        summaryP.setAttribute('title', summaryText);

                        contentDiv.appendChild(headerDiv);
                        contentDiv.appendChild(summaryP);

                        container.appendChild(contentDiv);
                        button.appendChild(container);
                        callListContainer.appendChild(button);
                    });
                }


                document.addEventListener('DOMContentLoaded', async function () {
                    const controller = new AbortController();
                    window.addEventListener('beforeunload', () => controller.abort());
                    try {
                        const response = await fetch('/voicebot_data', { signal: controller.signal });
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        const fetchedData = await response.json();
                        if (Array.isArray(fetchedData)) {
                            callsData = fetchedData;
                        } else {
                            console.error("Fetched data from /voicebot_data is not an array:", fetchedData);
                            callsData = [];
                        }
                    } catch (e) {
                        console.error("Error fetching voice_calls_data from /voicebot_data:", e);
                        callsData = []; // Fallback to empty array on error
                        const detailsTab = document.querySelector('#details-tab');
                        if (detailsTab) {
                                // Using a simple text error message to avoid sourcing new SVGs
                                detailsTab.innerHTML = `
                                <div class="flex flex-col items-center justify-center h-[60vh] text-center px-4">
                                    <div class="w-16 h-16 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-700/30 mb-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-500">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                        </svg>
                                    </div>
                                    <h3 class="text-lg font-medium mb-1 text-red-700 dark:text-red-300">Error Loading Call Data</h3>
                                    <p class="text-muted-foreground">Could not fetch call data from the server. Please try again later.<br/>Details: ${e.message}</p>
                                </div>`;
                            }
                        }

                    // Sort by id descending before rendering
                    callsData.sort((a, b) => (b.id ?? 0) - (a.id ?? 0));
                    renderCallListItems(callsData);

                    if (callsData.length > 0) {
                        showCallDetails(0); // Automatically select and show details for the first call

                        // Ensure 'Details' tab is visually active and its content displayed, and indicator is positioned.
                        const detailsTabLink = document.querySelector('.call-details-menubar a[data-tab="details"]');
                        const detailsTabContent = document.getElementById('details-tab');
                        const menubarLinks = document.querySelectorAll('.call-details-menubar a');
                        const menubarContents = document.querySelectorAll('.call-details-content');
                        const menubarIndicator = document.querySelector('.call-details-menubar-indicator');

                        menubarLinks.forEach(l => l.classList.remove('active'));
                        menubarContents.forEach(c => c.classList.remove('active'));

                        if (detailsTabLink) detailsTabLink.classList.add('active');
                        if (detailsTabContent) detailsTabContent.classList.add('active'); // Show the details tab content

                        // Position the indicator under the 'Details' tab
                        if (menubarIndicator && detailsTabLink && detailsTabLink.offsetParent) { // Check offsetParent to ensure it's visible and has dimensions
                            const menubarElement = detailsTabLink.closest('.call-details-menubar');
                            if (menubarElement) {
                                const menubarPaddingLeft = parseFloat(window.getComputedStyle(menubarElement).paddingLeft) || 0;
                                menubarIndicator.style.width = `${detailsTabLink.offsetWidth}px`;
                                // Calculate offsetLeft relative to the menubar's content box
                                menubarIndicator.style.transform = `translateX(${detailsTabLink.offsetLeft - menubarPaddingLeft}px)`;
                            }
                        }

                    } else {
                        // This handles the case where callsData is empty or an error occurred.
                        const detailsTab = document.querySelector('#details-tab');
                        // Only update if it's not already showing an error message from fetch
                        if (detailsTab && !detailsTab.innerHTML.includes("Error Loading Call Data")) {
                             detailsTab.innerHTML =
                            '<div class="flex flex-col items-center justify-center h-[60vh] text-center px-4">' +
                                '<div class="w-16 h-16 flex items-center justify-center rounded-full bg-muted/50 mb-4">' +
                                    '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" ' +
                                        'fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" ' +
                                        'stroke-linejoin="round" class="text-muted-foreground">' +
                                        '<path ' +
                                            'd="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">' +
                                        '</path>' +
                                    '</svg>' +
                                '</div>' +
                                '<h3 class="text-lg font-medium mb-1">No Call Data Available</h3>' +
                                '<p class="text-muted-foreground">No calls found or data could not be loaded.</p>' +
                            '</div>';
                        }
                    }
                });
            </script>
            <!-- Add this to your script section -->
            <script>
                // Initialize search functionality
                function initializeSearch() {
                    const searchInput = document.getElementById('call-search-input');
                    if (!searchInput || searchInput.dataset.listenerAttached) {
                        // If no input or listener already attached, do nothing.
                        // console.error('Search input not found or listener already attached'); // Optional: for debugging
                        return;
                    }
                    searchInput.dataset.listenerAttached = 'true'; // Mark as listener attached

                    function performSearch() {
                        const searchQuery = searchInput.value.trim().toLowerCase();

                        if (!Array.isArray(callsData)) {
                            console.error('callsData is not an array:', callsData);
                            return;
                        }

                        // Filter calls based on phone number
                        const filteredCalls = searchQuery === '' ? callsData : callsData.filter(call => {
                            if (!call) return false;

                            // Check all possible phone number fields
                            const phoneFields = [
                                call.guest_phone,
                                call.number,
                                call.phone
                            ].filter(Boolean); // Remove null/undefined values

                            return phoneFields.some(phone =>
                                String(phone).toLowerCase().includes(searchQuery)
                            );
                        });

                        console.log(`Search: "${searchQuery}" found ${filteredCalls.length} results`);
                        renderCallListItems(filteredCalls);
                    }

                    // Debounce the search to improve performance
                    let searchTimeout;
                    searchInput.addEventListener('input', function(e) {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(performSearch, 100); // Small delay for better performance
                    });

                    // Initial render
                    performSearch();
                }

                document.addEventListener('DOMContentLoaded', initializeSearch);

                // Tab switching functionality for call details
                document.addEventListener('DOMContentLoaded', function () {
                    // Find the menubar links that control tabs
                    const tabLinks = document.querySelectorAll('[data-tab]');
                    const tabContents = document.querySelectorAll('.call-details-content');

                    // Handle tab switching
                    tabLinks.forEach(link => {
                        link.addEventListener('click', function (e) {
                            e.preventDefault();

                            // Update active tab class
                            tabLinks.forEach(l => l.classList.remove('active'));
                            this.classList.add('active');

                            // Show corresponding content
                            const tabId = this.getAttribute('data-tab');
                            tabContents.forEach(content => {
                                content.classList.remove('active');
                            });
                            // Check if the tab exists before trying to add the active class
                            const tabElement = document.getElementById(`${tabId}-tab`);
                            if (tabElement) {
                                tabElement.classList.add('active');
                            }
                        });

                        // Handle tab switching
                    });
                });

                // Function to display call details when a call is selected
                // Helper functions for constructing call detail components safely
                function createCallMetadataSection(call) {
                    const container = document.createElement('div');
                    container.className = 'grid grid-cols-2 md:grid-cols-5 gap-3 mb-4';

                    // Create metrics for different call properties
                    const createMetric = (label, value, extraContent = null) => {
                        const metric = document.createElement('div');
                        metric.className = 'analytics-metric flex flex-col justify-start';

                        const labelEl = document.createElement('div');
                        labelEl.className = 'analytics-label';
                        labelEl.textContent = label;
                        metric.appendChild(labelEl);

                        const valueEl = document.createElement('div');
                        valueEl.className = 'analytics-value';
                        if (extraContent) {
                            valueEl.className += ' flex items-center';
                            valueEl.appendChild(extraContent);
                        }
                        valueEl.appendChild(document.createTextNode(value || 'N/A'));
                        metric.appendChild(valueEl);

                        return metric;
                    };

                    // Phone Number
                    container.appendChild(createMetric('Phone Number', call.guest_phone || 'N/A'));

                    // Date & Time - display just time like in the left list
                    let formattedDateTime = "N/A";
                    if (call.call_start_time) {
                        try {
                            const callDateTime = new Date(call.call_start_time);
                            if (!isNaN(callDateTime)) {
                                formattedDateTime = callDateTime.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                            } else {
                                formattedDateTime = call.call_start_time; // Fallback to original value
                            }
                        } catch(e) {
                            formattedDateTime = call.call_start_time; // Fallback to original value
                        }
                    }
                    container.appendChild(createMetric('Time', formattedDateTime));

                    // Speaker Ratio
                    container.appendChild(createMetric('Speaker Ratio', call.call_ratio || 'N/A'));

                    // Duration
                    container.appendChild(createMetric('Duration', call.call_time || 'N/A'));

                    // Status with indicator
                    const statusIndicator = document.createElement('span');
                    statusIndicator.className = 'inline-block w-2 h-2 rounded-full mr-2';
                    if (call.call_status === 'Completed') {
                        statusIndicator.classList.add('bg-green-500');
                    } else if (call.call_status === 'Missed') {
                        statusIndicator.classList.add('bg-red-500');
                    } else {
                        statusIndicator.classList.add('bg-amber-500');
                    }
                    container.appendChild(createMetric('Status', call.call_status || 'N/A', statusIndicator));

                    return container;
                }

                function createSentimentAnalysisSection(call) {
                    const container = document.createElement('div');
                    container.className = 'mb-4';

                    // Header with icon
                    const header = document.createElement('h4');
                    header.className = 'text-base font-medium mb-3 flex items-center';

                    const icon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                    icon.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                    icon.setAttribute('width', '16');
                    icon.setAttribute('height', '16');
                    icon.setAttribute('viewBox', '0 0 24 24');
                    icon.setAttribute('fill', 'none');
                    icon.setAttribute('stroke', 'currentColor');
                    icon.setAttribute('stroke-width', '2');
                    icon.setAttribute('stroke-linecap', 'round');
                    icon.setAttribute('stroke-linejoin', 'round');
                    icon.setAttribute('class', 'mr-2');

                    // Add the SVG paths for the emoji icon
                    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    circle.setAttribute('cx', '12');
                    circle.setAttribute('cy', '12');
                    circle.setAttribute('r', '10');
                    icon.appendChild(circle);

                    const smile = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                    smile.setAttribute('d', 'M8 14s1.5 2 4 2 4-2 4-2');
                    icon.appendChild(smile);

                    const leftEye = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    leftEye.setAttribute('x1', '9');
                    leftEye.setAttribute('x2', '9.01');
                    leftEye.setAttribute('y1', '9');
                    leftEye.setAttribute('y2', '9');
                    icon.appendChild(leftEye);

                    const rightEye = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    rightEye.setAttribute('x1', '15');
                    rightEye.setAttribute('x2', '15.01');
                    rightEye.setAttribute('y1', '9');
                    rightEye.setAttribute('y2', '9');
                    icon.appendChild(rightEye);

                    header.appendChild(icon);
                    header.appendChild(document.createTextNode('Sentiment Analysis'));
                    container.appendChild(header);

                    // Sentiment bar
                    const sentimentContainer = document.createElement('div');
                    sentimentContainer.className = 'p-4 bg-muted/50 rounded-lg';

                    const sentimentBar = document.createElement('div');
                    sentimentBar.className = 'sentiment-bar';

                    // Calculate sentiment position
                    let sentimentPercent = 50; // Default to neutral
                    if (call.sentiment_analysis) {
                        const parts = String(call.sentiment_analysis).split('/');
                        if (parts.length === 2) {
                            const score = parseInt(parts[0]);
                            const maxScore = parseInt(parts[1]);
                            if (!isNaN(score) && !isNaN(maxScore) && maxScore > 0) {
                                sentimentPercent = (score / maxScore) * 100;
                            }
                        }
                    }

                    const marker = document.createElement('div');
                    marker.className = 'sentiment-marker';
                    marker.style.left = sentimentPercent + '%';
                    sentimentBar.appendChild(marker);
                    sentimentContainer.appendChild(sentimentBar);

                    // Sentiment legend
                    const legend = document.createElement('div');
                    legend.className = 'flex justify-between text-xs text-muted-foreground mt-2';

                    const sentimentLabels = [
                        { color: 'bg-red-600', text: 'Very Negative' },
                        { color: 'bg-red-500', text: 'Negative' },
                        { color: 'bg-red-300', text: 'Slightly Negative' },
                        { color: 'bg-amber-400', text: 'Neutral' },
                        { color: 'bg-green-300', text: 'Slightly Positive' },
                        { color: 'bg-green-500', text: 'Positive' },
                        { color: 'bg-green-600', text: 'Very Positive' }
                    ];

                    sentimentLabels.forEach(item => {
                        const label = document.createElement('span');
                        label.className = 'flex items-center';

                        const dot = document.createElement('span');
                        dot.className = `inline-block w-2 h-2 rounded-full mr-1 ${item.color}`;
                        label.appendChild(dot);

                        label.appendChild(document.createTextNode(item.text));
                        legend.appendChild(label);
                    });

                    sentimentContainer.appendChild(legend);
                    container.appendChild(sentimentContainer);

                    return container;
                }

                function createCallSummarySection(call) {
                    const container = document.createElement('div');
                    container.className = 'mb-4';

                    // Header with icon
                    const header = document.createElement('h4');
                    header.className = 'text-base font-medium mb-3 flex items-center';

                    const icon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                    icon.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                    icon.setAttribute('width', '16');
                    icon.setAttribute('height', '16');
                    icon.setAttribute('viewBox', '0 0 24 24');
                    icon.setAttribute('fill', 'none');
                    icon.setAttribute('stroke', 'currentColor');
                    icon.setAttribute('stroke-width', '2');
                    icon.setAttribute('stroke-linecap', 'round');
                    icon.setAttribute('stroke-linejoin', 'round');
                    icon.setAttribute('class', 'mr-2');

                    // Add the SVG path for the message icon
                    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                    path.setAttribute('d', 'M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z');
                    icon.appendChild(path);

                    header.appendChild(icon);
                    header.appendChild(document.createTextNode('Call Summary'));
                    container.appendChild(header);

                    // Summary content
                    const content = document.createElement('div');
                    content.className = 'p-3 bg-muted/50 rounded-lg text-sm leading-relaxed';
                    content.textContent = call.call_summary || 'No summary available.';
                    container.appendChild(content);

                    return container;
                }

                function createCallTranscriptSection(call) {
                    const container = document.createElement('div');

                    // Header with icon
                    const header = document.createElement('h4');
                    header.className = 'text-base font-medium mb-2 flex items-center';

                    const icon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                    icon.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                    icon.setAttribute('width', '16');
                    icon.setAttribute('height', '16');
                    icon.setAttribute('viewBox', '0 0 24 24');
                    icon.setAttribute('fill', 'none');
                    icon.setAttribute('stroke', 'currentColor');
                    icon.setAttribute('stroke-width', '2');
                    icon.setAttribute('stroke-linecap', 'round');
                    icon.setAttribute('stroke-linejoin', 'round');
                    icon.setAttribute('class', 'mr-2');

                    // Add the SVG path for the transcript icon
                    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                    path.setAttribute('d', 'm3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z');
                    icon.appendChild(path);

                    header.appendChild(icon);
                    header.appendChild(document.createTextNode('Call Transcript'));
                    container.appendChild(header);

                    // Transcript content
                    const content = document.createElement('div');
                    content.className = 'p-4 bg-muted rounded-lg text-sm leading-relaxed';

                    if (call.call_transcript) {
                        // Split transcript by newlines and create paragraphs
                        const lines = call.call_transcript.split('\\n');
                        lines.forEach(line => {
                            if (line.trim()) {
                                const paragraph = document.createElement('p');
                                paragraph.className = 'mb-1.5';
                                paragraph.textContent = line;
                                content.appendChild(paragraph);
                            }
                        });
                    } else {
                        content.textContent = 'No transcript available.';
                    }

                    container.appendChild(content);
                    return container;
                }

                function createDivider() {
                    const divider = document.createElement('div');
                    divider.className = 'border-t card border-border my-4';
                    return divider;
                }

                function createLoadingView() {
                    const container = document.createElement('div');
                    container.className = 'flex flex-col items-center justify-center h-[60vh] text-center px-4';

                    const iconContainer = document.createElement('div');
                    iconContainer.className = 'w-16 h-16 flex items-center justify-center rounded-full bg-muted/50 mb-4';

                    const icon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                    icon.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                    icon.setAttribute('width', '24');
                    icon.setAttribute('height', '24');
                    icon.setAttribute('viewBox', '0 0 24 24');
                    icon.setAttribute('fill', 'none');
                    icon.setAttribute('stroke', 'currentColor');
                    icon.setAttribute('stroke-width', '2');
                    icon.setAttribute('stroke-linecap', 'round');
                    icon.setAttribute('stroke-linejoin', 'round');

                    const path1 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                    path1.setAttribute('d', 'M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-3.66 0L0.598 9.084a1 1 0 0 0-.02 1.838l8.57 3.908a2 2 0 0 0 1.838 0l8.592-3.908z');
                    icon.appendChild(path1);

                    const path2 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                    path2.setAttribute('d', 'M12 22v-8.5');
                    icon.appendChild(path2);

                    const path3 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                    path3.setAttribute('d', 'm3.29 10.71-.055 4.046a2 2 0 0 0 .34 1.206L12 22l8.426-6.038a2 2 0 0 0 .34-1.206l-.055-4.046');
                    icon.appendChild(path3);

                    iconContainer.appendChild(icon);
                    container.appendChild(iconContainer);

                    const title = document.createElement('h3');
                    title.className = 'text-lg font-medium mb-1';
                    title.textContent = 'Loading Data...';
                    container.appendChild(title);

                    const message = document.createElement('p');
                    message.className = 'text-muted-foreground';
                    message.textContent = 'Please wait or select a call if data has loaded.';
                    container.appendChild(message);

                    return container;
                }

                function showCallDetails(index) {
                    if (!callsData || callsData.length === 0) {
                        console.warn("Call data not loaded yet or is empty. Cannot show details for index:", index);
                        const detailsTab = document.querySelector('#details-tab');
                        if (detailsTab && !detailsTab.querySelector('.text-red-500')) { // Avoid overwriting error message
                            detailsTab.innerHTML = '';
                            detailsTab.appendChild(createLoadingView());
                        }
                        return;
                    }

                    // Check if index is valid for the fetched data
                    if (index < 0 || index >= callsData.length) {
                        console.warn("Invalid call index for fetched data:", index);
                        return;
                    }

                    // Get the call data from fetched data
                    const call = callsData[index];

                    // Highlight the selected call in the list
                    const callButtons = document.querySelectorAll('#call-list button');
                    callButtons.forEach((btn, i) => {
                        if (i === index) {
                            btn.classList.add('active');
                            btn.classList.add('call-highlight');
                            setTimeout(() => btn.classList.remove('call-highlight'), 800);
                        } else {
                            btn.classList.remove('active');
                        }
                    });

                    // Clear and update call details using our component functions
                    const detailsTab = document.querySelector('#details-tab');
                    detailsTab.innerHTML = '';

                    const container = document.createElement('div');
                    container.className = 'card border border-border rounded-lg p-4 space-y-4';

                    // Add all components to the container
                    container.appendChild(createCallMetadataSection(call));
                    container.appendChild(createDivider());
                    container.appendChild(createSentimentAnalysisSection(call));
                    container.appendChild(createDivider());
                    container.appendChild(createCallSummarySection(call));
                    container.appendChild(createDivider());
                    container.appendChild(createCallTranscriptSection(call));

                    detailsTab.appendChild(container);

                   // Analytics tab removed as requested

                   // Update call history tab with enhanced styling - refactored to use DOM manipulation
                   function createCallHistorySection(call) {
                   const container = document.createElement('div');
                   container.className = 'card border border-border rounded-lg p-4 space-y-4';

                   // Header section
                   const headerDiv = document.createElement('div');
                   headerDiv.className = 'flex items-center justify-between';

                   const header = document.createElement('h4');
                   header.className = 'text-base font-medium flex items-center';

                        // Create clock icon
                   const icon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                   icon.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                   icon.setAttribute('width', '16');
                   icon.setAttribute('height', '16');
                   icon.setAttribute('viewBox', '0 0 24 24');
                   icon.setAttribute('fill', 'none');
                   icon.setAttribute('stroke', 'currentColor');
                   icon.setAttribute('stroke-width', '2');
                   icon.setAttribute('stroke-linecap', 'round');
                   icon.setAttribute('stroke-linejoin', 'round');
                   icon.setAttribute('class', 'mr-2');

                   const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                   path.setAttribute('d', 'M12 8v4l3 3');
                   icon.appendChild(path);

                        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                        circle.setAttribute('cx', '12');
                        circle.setAttribute('cy', '12');
                        circle.setAttribute('r', '10');
                        icon.appendChild(circle);

                        header.appendChild(icon);
                        header.appendChild(document.createTextNode('Call History'));
                        headerDiv.appendChild(header);

                        // Call count badge
                        const countBadge = document.createElement('span');
                        countBadge.className = 'bg-muted px-2 py-1 rounded text-xs font-medium';
                        countBadge.textContent = Math.floor(Math.random() * 5) + ' Previous Calls';
                        headerDiv.appendChild(countBadge);

                        container.appendChild(headerDiv);

                        // Timeline section
                        const timeline = document.createElement('div');
                        timeline.className = 'call-timeline';

                        // Create timeline items
                        const createTimelineItem = (title, time) => {
                            const item = document.createElement('div');
                            item.className = 'call-timeline-item';

                            const titleDiv = document.createElement('div');
                            titleDiv.className = 'text-sm font-medium';
                            titleDiv.textContent = title;
                            item.appendChild(titleDiv);

                            const timeDiv = document.createElement('div');
                            timeDiv.className = 'text-xs text-muted-foreground';
                            timeDiv.textContent = time;
                            item.appendChild(timeDiv);

                            return item;
                        };

                        // Add timeline items with proper data formatting
                        let callStartTime = "N/A";
                        if (call.call_start_time) {
                            try {
                                const callDateTime = new Date(call.call_start_time);
                                if (!isNaN(callDateTime)) {
                                    callStartTime = callDateTime.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                                } else {
                                    callStartTime = call.call_start_time;
                                }
                            } catch(e) {
                                callStartTime = call.call_start_time;
                            }
                        }

                        timeline.appendChild(createTimelineItem('Call Started', callStartTime));
                        timeline.appendChild(createTimelineItem('Agent Connected', '0:05 into call'));
                        timeline.appendChild(createTimelineItem('Call Ended', 'Duration: ' + (call.call_time || 'N/A')));

                        container.appendChild(timeline);
                        return container;
                    }

                    // Update history tab
                    const historyTab = document.querySelector('#history-tab');
                    historyTab.innerHTML = '';
                    historyTab.appendChild(createCallHistorySection(call));
                }</script>
</body>

</html>