<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width" />
    <title>Franken UI</title>
    <link rel="preconnect" href="https://rsms.me/" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />

    <style>
      :root {
        font-family: Inter, sans-serif;
        font-feature-settings: "liga" 1, "calt" 1; /* fix for Chrome */
      }
      @supports (font-variation-settings: normal) {
        :root {
          font-family: InterVariable, sans-serif;
        }
      }
    </style>

    <!-- For stability in production, it's recommended that you hardcode the latest version in the CDN link. -->

    <link
      rel="stylesheet"
      href="https://unpkg.com/franken-ui@internal/dist/css/core.min.css"
    />
    <link
      rel="stylesheet"
      href="https://unpkg.com/franken-ui@internal/dist/css/utilities.min.css"
    />

    <script>
      const htmlElement = document.documentElement;

      const __FRANKEN__ = JSON.parse(
        localStorage.getItem("__FRANKEN__") || "{}"
      );

      if (
        __FRANKEN__.mode === "dark" ||
        (!__FRANKEN__.mode &&
          window.matchMedia("(prefers-color-scheme: dark)").matches)
      ) {
        htmlElement.classList.add("dark");
      } else {
        htmlElement.classList.remove("dark");
      }

      htmlElement.classList.add(__FRANKEN__.theme || "uk-theme-zinc");
      htmlElement.classList.add(__FRANKEN__.radii || "uk-radii-md");
      htmlElement.classList.add(__FRANKEN__.shadows || "uk-shadows-sm");
    </script>

    <script
      type="module"
      src="https://unpkg.com/franken-ui@internal/dist/js/core.iife.js"
    ></script>
    <script
      type="module"
      src="https://unpkg.com/franken-ui@internal/dist/js/icon.iife.js"
    ></script>
  </head>
  <body class="bg-background text-foreground">
    <!-- START CODING HERE -->
    <div class="uk-container uk-container-xl mt-40 grid gap-y-8 md:grid-cols-2">
      <div class="space-y-4">
        <h1 class="uk-hero-sm font-bold">Franken UI: Dos</h1>
        <p class="text-lg font-light">New coat, old boat.</p>
        <div class="flex gap-x-1">
          <a
            class="uk-btn uk-btn-primary"
            href="https://next.franken-ui.dev/docs/introduction"
          >
            Learn
          </a>
          <a
            class="uk-btn uk-btn-default"
            href="https://github.com/sponsors/sveltecult"
            target="_blank"
          >
            <uk-icon icon="heart" cls-custom="mr-2 text-pink-500"></uk-icon>
            Support
          </a>
          <div class="uk-inline">
            <button
              class="uk-btn uk-btn-default uk-btn-icon"
              aria-label="Customize"
            >
              <uk-icon icon="palette"></uk-icon>
            </button>
            <div
              class="uk-card uk-card-body uk-drop w-96"
              data-uk-drop="mode: click; offset: 8"
            >
              <div class="uk-h3">Customize</div>
              <p class="mb-4 mt-1 text-sm text-muted-foreground">
                Customize your Franken UI experience.
              </p>
              <uk-theme-switcher id="theme-switcher">
                <select hidden="">
                  <optgroup label="Theme">
                    <option data-hex="#52525b" value="uk-theme-zinc">
                      Zinc
                    </option>
                    <option data-hex="#64748b" value="uk-theme-slate">
                      Slate
                    </option>
                    <option data-hex="#78716c" value="uk-theme-stone">
                      Stone
                    </option>
                    <option data-hex="#6b7280" value="uk-theme-gray">
                      Gray
                    </option>
                    <option data-hex="#737373" value="uk-theme-neutral">
                      Neutral
                    </option>
                    <option data-hex="#dc2626" value="uk-theme-red">Red</option>
                    <option data-hex="#e11d48" value="uk-theme-rose">
                      Rose
                    </option>
                    <option data-hex="#f97316" value="uk-theme-orange">
                      Orange
                    </option>
                    <option data-hex="#16a34a" value="uk-theme-green">
                      Green
                    </option>
                    <option data-hex="#2563eb" value="uk-theme-blue">
                      Blue
                    </option>
                    <option data-hex="#facc15" value="uk-theme-yellow">
                      Yellow
                    </option>
                    <option data-hex="#7c3aed" value="uk-theme-violet">
                      Violet
                    </option>
                  </optgroup>
                  <optgroup label="Radii">
                    <option value="uk-radii-none">None</option>
                    <option value="uk-radii-sm">Small</option>
                    <option value="uk-radii-md">Medium</option>
                    <option value="uk-radii-lg">Large</option>
                  </optgroup>
                  <optgroup label="Shadows">
                    <option value="uk-shadows-none">None</option>
                    <option value="uk-shadows-sm">Small</option>
                    <option value="uk-shadows-md">Medium</option>
                    <option value="uk-shadows-lg">Large</option>
                  </optgroup>
                  <optgroup label="Mode">
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                  </optgroup>
                </select>
              </uk-theme-switcher>
            </div>
          </div>
        </div>
      </div>
      <div class="">
        <h2 class="uk-h3">Follow</h2>
        <ul class="uk-list uk-list-hyphen mt-4">
          <li>
            <a
              class="uk-link"
              href="https://bsky.app/profile/sveltecult.bsky.social"
              target="_blank"
            >
              https://bsky.app/profile/sveltecult.bsky.social
            </a>
          </li>
          <li>
            <a
              class="uk-link"
              href="https://github.com/sveltecult"
              target="_blank"
            >
              https://github.com/sveltecult
            </a>
          </li>
          <li>
            <a
              class="uk-link"
              href="https://github.com/franken-ui"
              target="_blank"
            >
              https://github.com/franken-ui
            </a>
          </li>
          <li>
            <a class="uk-link" href="https://x.com/sveltecult" target="_blank">
              https://x.com/sveltecult
            </a>
          </li>
        </ul>
      </div>
    </div>
    <!-- END -->
  </body>
</html>