SPA BOOKING -------------------------------------------------------------------------------------------------------
────────────────────────────────────────────────────────────────────────────────────────────────────
{
    "OriginalRepliedMessageSender": "whatsapp:+***********",
    "SmsMessageSid": "SM77e665c0c9fa903d4a71fbb06f4a413c",
    "NumMedia": "0",
    "ProfileName": "Dixith",
    "MessageType": "button",
    "SmsSid": "SM77e665c0c9fa903d4a71fbb06f4a413c",
    "WaId": "************",
    "SmsStatus": "received",
    "Body": "Book Spa",
    "ButtonText": "Book Spa",
    "To": "whatsapp:+***********",
    "ButtonPayload": "book_spa_option",
    "NumSegments": "1",
    "ReferralNumMedia": "0",
    "MessageSid": "SM77e665c0c9fa903d4a71fbb06f4a413c",
    "AccountSid": "AC29e4f657f73d2a87580fe0a2b314e59a",
    "OriginalRepliedMessageSid": "MM7d4062a1220b6be7f9f89d702c40a1fc",
    "From": "whatsapp:+************",
    "ApiVersion": "2010-04-01"
}
────────────────────────────────────────────────────────────────────────────────────────────────────


127.0.0.1 - - [05/May/2025 04:03:56] "POST / HTTP/1.1" 200 -

────────────────────────────────────────────────────────────────────────────────────────────────────
{
    "ChannelPrefix": "whatsapp",
    "ApiVersion": "2010-04-01",
    "MessageStatus": "sent",
    "SmsSid": "MM838fc4d6cbc79697011a30f150b9eafe",
    "SmsStatus": "sent",
    "ChannelInstallSid": "XE8349bb0a6ab52afd4f83e6f00b80b627",
    "To": "whatsapp:+************",
    "From": "whatsapp:+***********",
    "MessageSid": "MM838fc4d6cbc79697011a30f150b9eafe",
    "StructuredMessage": "false",
    "AccountSid": "AC29e4f657f73d2a87580fe0a2b314e59a",
    "ChannelToAddress": "+91939876XXXX"
}
────────────────────────────────────────────────────────────────────────────────────────────────────


127.0.0.1 - - [05/May/2025 04:03:58] "POST / HTTP/1.1" 200 -

────────────────────────────────────────────────────────────────────────────────────────────────────
{
    "ChannelPrefix": "whatsapp",
    "ApiVersion": "2010-04-01",
    "MessageStatus": "delivered",
    "SmsSid": "MM838fc4d6cbc79697011a30f150b9eafe",
    "SmsStatus": "delivered",
    "ChannelInstallSid": "XE8349bb0a6ab52afd4f83e6f00b80b627",
    "To": "whatsapp:+************",
    "From": "whatsapp:+***********",
    "MessageSid": "MM838fc4d6cbc79697011a30f150b9eafe",
    "AccountSid": "AC29e4f657f73d2a87580fe0a2b314e59a",
    "ChannelToAddress": "+91939876XXXX"
}
────────────────────────────────────────────────────────────────────────────────────────────────────


127.0.0.1 - - [05/May/2025 04:03:58] "POST / HTTP/1.1" 200 -

────────────────────────────────────────────────────────────────────────────────────────────────────
{
    "ListId": "spa_date_7",
    "OriginalRepliedMessageSender": "whatsapp:+***********",
    "SmsMessageSid": "SM45797c863b0e98e1d9f3eff75d63d4a0",
    "NumMedia": "0",
    "ProfileName": "Dixith",
    "MessageType": "interactive",
    "SmsSid": "SM45797c863b0e98e1d9f3eff75d63d4a0",
    "WaId": "************",
    "SmsStatus": "received",
    "Body": "spa_date_7",
    "To": "whatsapp:+***********",
    "ListTitle": "12-05-2025",
    "NumSegments": "1",
    "ReferralNumMedia": "0",
    "MessageSid": "SM45797c863b0e98e1d9f3eff75d63d4a0",
    "AccountSid": "AC29e4f657f73d2a87580fe0a2b314e59a",
    "OriginalRepliedMessageSid": "MM838fc4d6cbc79697011a30f150b9eafe",
    "From": "whatsapp:+************",
    "ApiVersion": "2010-04-01"
}
────────────────────────────────────────────────────────────────────────────────────────────────────


127.0.0.1 - - [05/May/2025 04:04:02] "POST / HTTP/1.1" 200 -

────────────────────────────────────────────────────────────────────────────────────────────────────
{
    "ChannelPrefix": "whatsapp",
    "ApiVersion": "2010-04-01",
    "MessageStatus": "delivered",
    "SmsSid": "SM80f620fca27e2cb49a636e13bf56896d",
    "SmsStatus": "delivered",
    "To": "whatsapp:+************",
    "From": "whatsapp:+***********",
    "MessageSid": "SM80f620fca27e2cb49a636e13bf56896d",
    "AccountSid": "AC29e4f657f73d2a87580fe0a2b314e59a",
    "ChannelToAddress": "+91939876XXXX"
}
────────────────────────────────────────────────────────────────────────────────────────────────────


127.0.0.1 - - [05/May/2025 04:04:05] "POST / HTTP/1.1" 200 -

────────────────────────────────────────────────────────────────────────────────────────────────────
{
    "ChannelPrefix": "whatsapp",
    "ApiVersion": "2010-04-01",
    "MessageStatus": "sent",
    "SmsSid": "SM80f620fca27e2cb49a636e13bf56896d",
    "SmsStatus": "sent",
    "ChannelInstallSid": "XE8349bb0a6ab52afd4f83e6f00b80b627",
    "To": "whatsapp:+************",
    "From": "whatsapp:+***********",
    "MessageSid": "SM80f620fca27e2cb49a636e13bf56896d",
    "StructuredMessage": "false",
    "AccountSid": "AC29e4f657f73d2a87580fe0a2b314e59a",
    "ChannelToAddress": "+91939876XXXX"
}
────────────────────────────────────────────────────────────────────────────────────────────────────


127.0.0.1 - - [05/May/2025 04:04:06] "POST / HTTP/1.1" 200 -

------------------------------- SPA BOOKING ENDSSS -----------------------------------------------------------------


TASK SAMPLE DATA -===============================================================================================-
[
    {
        "Task ID": "TASK-3958",
        "Items": "Drinks",
        "Quantity": "{\"Drinks\": \"x1\"}",
        "Category": "Beverage",
        "Room Number": 335,
        "Phone Number": ************,
        "Guest Name": "Sean Carson",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Drinks\": \"89\"}",
        "Total Price": 89
    },
    {
        "Task ID": "TASK-4381",
        "Items": "Fillet Steak, Red Wine",
        "Quantity": "{\"Fillet Steak\": \"x1\", \"Red Wine\": \"x1\"}",
        "Category": "Food & Beverage",
        "Room Number": 271,
        "Phone Number": 919956560743,
        "Guest Name": "Christopher Frazier",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Fillet Steak\": \"45\", \"Red Wine\": \"40\"}",
        "Total Price": 85
    },
    {
        "Task ID": "TASK-4502",
        "Items": "Spa Services",
        "Quantity": "{\"Spa Services\": \"x1\"}",
        "Category": "Spa & Massage",
        "Room Number": 411,
        "Phone Number": 916962009731,
        "Guest Name": "Teresa Hernandez",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Spa Services\": \"120\"}",
        "Total Price": 120
    },
    {
        "Task ID": "TASK-4875",
        "Items": "Burrata, Drinks",
        "Quantity": "{\"Burrata\": \"x1\", \"Drinks\": \"x1\"}",
        "Category": "Food & Beverage",
        "Room Number": 713,
        "Phone Number": 918749148210,
        "Guest Name": "Beth Adkins",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Burrata\": \"25\", \"Drinks\": \"89\"}",
        "Total Price": 114
    },
    {
        "Task ID": "TASK-5832",
        "Items": "Steak and Lobster",
        "Quantity": "{\"Steak and Lobster\": \"x1\"}",
        "Category": "Food",
        "Room Number": 763,
        "Phone Number": 916100555190,
        "Guest Name": "Robert Rice",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Steak and Lobster\": \"65\"}",
        "Total Price": 65
    },
    {
        "Task ID": "TASK-5910",
        "Items": "Fillet Steak, Red Wine",
        "Quantity": "{\"Fillet Steak\": \"x1\", \"Red Wine\": \"x1\"}",
        "Category": "Food & Beverage",
        "Room Number": 171,
        "Phone Number": 917294557462,
        "Guest Name": "Jacob Welch",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Fillet Steak\": \"45\", \"Red Wine\": \"40\"}",
        "Total Price": 85
    },
    {
        "Task ID": "TASK-5983",
        "Items": "Pumpkin with Coconut, Beers",
        "Quantity": "{\"Pumpkin with Coconut\": \"x1\", \"Beers\": \"x1\"}",
        "Category": "Food & Beverage",
        "Room Number": 572,
        "Phone Number": 917322379295,
        "Guest Name": "Jose Lucas",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Pumpkin with Coconut\": \"28\", \"Beers\": \"10\"}",
        "Total Price": 38
    },
    {
        "Task ID": "TASK-6120",
        "Items": "Water, Beers",
        "Quantity": "{\"Water\": \"x2\", \"Beers\": \"x2\"}",
        "Category": "Beverage",
        "Room Number": 922,
        "Phone Number": 917901275096,
        "Guest Name": "Rachel Goodwin",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Water\": \"10\", \"Beers\": \"20\"}",
        "Total Price": 30
    },
    {
        "Task ID": "TASK-6849",
        "Items": "Chicken, Cocktail",
        "Quantity": "{\"Chicken\": \"x2\", \"Cocktail\": \"x2\"}",
        "Category": "Food & Beverage",
        "Room Number": 460,
        "Phone Number": 917978351769,
        "Guest Name": "Sarah Reilly",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Chicken\": \"64\", \"Cocktail\": \"96\"}",
        "Total Price": 160
    },
    {
        "Task ID": "TASK-7294",
        "Items": "Grilled Salmon, Water",
        "Quantity": "{\"Grilled Salmon\": \"x2\", \"Water\": \"x2\"}",
        "Category": "Food & Beverage",
        "Room Number": 644,
        "Phone Number": 919273288363,
        "Guest Name": "Dana Hayes",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Grilled Salmon\": \"76\", \"Water\": \"10\"}",
        "Total Price": 86
    },
    {
        "Task ID": "TASK-7326",
        "Items": "Massage",
        "Quantity": "{\"Massage\": \"x1\"}",
        "Category": "Spa & Massage",
        "Room Number": 212,
        "Phone Number": 918748976360,
        "Guest Name": "Margaret Hogan",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Massage\": \"2500\"}",
        "Total Price": 2500
    },
    {
        "Task ID": "TASK-8620",
        "Items": "Lobster Roll, Red Wine",
        "Quantity": "{\"Lobster Roll\": \"x1\", \"Red Wine\": \"x1\"}",
        "Category": "Food & Beverage",
        "Room Number": 569,
        "Phone Number": 919272117670,
        "Guest Name": "Lisa Ferguson",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Lobster Roll\": \"42\", \"Red Wine\": \"40\"}",
        "Total Price": 82
    },
    {
        "Task ID": "TASK-8792",
        "Items": "Steak and Lobster",
        "Quantity": "{\"Steak and Lobster\": \"x1\"}",
        "Category": "Food",
        "Room Number": 285,
        "Phone Number": 918317296029,
        "Guest Name": "Mr. Patrick Mclean",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Steak and Lobster\": \"65\"}",
        "Total Price": 65
    },
    {
        "Task ID": "TASK-9417",
        "Items": "Burrata, Beers",
        "Quantity": "{\"Burrata\": \"x1\", \"Beers\": \"x2\"}",
        "Category": "Food & Beverage",
        "Room Number": 317,
        "Phone Number": 916113584134,
        "Guest Name": "Maria Frye",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Burrata\": \"25\", \"Beers\": \"20\"}",
        "Total Price": 45
    },
    {
        "Task ID": "TASK-2164",
        "Items": "Royal Suite",
        "Quantity": "{\"Royal Suite\": \"x1\"}",
        "Category": "Room Bookings",
        "Room Number": 709,
        "Phone Number": 919984743740,
        "Guest Name": "Erika Klein",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Royal Suite\": \"75\"}",
        "Total Price": 75
    },
    {
        "Task ID": "TASK-2301",
        "Items": "Executive Suite",
        "Quantity": "{\"Executive Suite\": \"x1\"}",
        "Category": "Room Bookings",
        "Room Number": 458,
        "Phone Number": 919015219548,
        "Guest Name": "Jennifer Mayer",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Executive Suite\": \"150\"}",
        "Total Price": 150
    },
    {
        "Task ID": "TASK-3057",
        "Items": "Cocktail, Soda",
        "Quantity": "{\"Cocktail\": \"x1\", \"Soda\": \"x1\"}",
        "Category": "Beverage",
        "Room Number": 841,
        "Phone Number": 919312588564,
        "Guest Name": "Linda Grimes",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Cocktail\": \"48\", \"Soda\": \"3\"}",
        "Total Price": 51
    },
    {
        "Task ID": "TASK-3745",
        "Items": "T-Bone Steak, Soda",
        "Quantity": "{\"T-Bone Steak\": \"x1\", \"Soda\": \"x1\"}",
        "Category": "Food & Beverage",
        "Room Number": 700,
        "Phone Number": 916518861690,
        "Guest Name": "Nicholas Huffman",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"T-Bone Steak\": \"55\", \"Soda\": \"3\"}",
        "Total Price": 58
    },
    {
        "Task ID": "TASK-1173",
        "Items": "Junior Suite Deluxe",
        "Quantity": "{\"Junior Suite Deluxe\": \"x1\"}",
        "Category": "Room Bookings",
        "Room Number": 522,
        "Phone Number": 919984743740,
        "Guest Name": "Steven Casey Phd",
        "Platform": "Whatsapp",
        "Language": "English",
        "Pricing": "{\"Junior Suite Deluxe\": \"65\"}",
        "Total Price": 65
    }
]
==================================================================================================================