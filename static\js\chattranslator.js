(function() {
    // Map to store original messages for restoration
    const originalMessages = new Map();
    // Current selected language for chat
    let currentChatLanguage = localStorage.getItem('chatLanguage') || 'en';

    // Initialize the chat translator
    function initializeChatTranslator() {
        setupLanguageSelectors();
        applyChatLanguage(currentChatLanguage);
        observeNewMessages();
    }

    // Set up event listeners for language selection from the existing dropdown
    function setupLanguageSelectors() {
        const languageDropdown = document.getElementById('language-dropdown');
        if (!languageDropdown) {
            console.error('Language dropdown (#language-dropdown) not found!');
            return;
        }

        languageDropdown.querySelectorAll('a[data-lang]').forEach(option => {
            option.addEventListener('click', function(event) {
                event.preventDefault();
                const selectedLang = this.getAttribute('data-lang');
                applyChatLanguage(selectedLang);
            });
        });
    }

    // Apply the selected language to the chat messages
    function applyChatLanguage(langCode) {
        currentChatLanguage = langCode;
        localStorage.setItem('chatLanguage', langCode);
        updateVisualSelection(langCode);
        
        if (langCode === 'en') {
            restoreOriginalMessages();
        } else {
            translateChatSection(langCode);
        }
    }

    // Update visual indication for selected language in the dropdown
    function updateVisualSelection(selectedLang) {
        const languageDropdown = document.getElementById('language-dropdown');
        if (!languageDropdown) return;

        languageDropdown.querySelectorAll('a[data-lang]').forEach(option => {
            if (option.getAttribute('data-lang') === selectedLang) {
                option.classList.add('active-language'); // Add a class for styling selected language
            } else {
                option.classList.remove('active-language');
            }
        });
    }

    // Restore original messages when English is selected
    function restoreOriginalMessages() {
        originalMessages.forEach((originalText, msgElement) => {
            msgElement.textContent = originalText;
        });
    }

    // Translate all existing chat messages to the selected language
    async function translateChatSection(targetLang) {
        const chatContainer = document.getElementById('chat-container');
        if (!chatContainer) {
            console.error('Chat container (#chat-container) not found!');
            return;
        }

        const messageElements = chatContainer.querySelectorAll('.message-bubble p');
        for (let msgElement of messageElements) {
            const originalText = originalMessages.get(msgElement) || msgElement.textContent;
            if (!originalMessages.has(msgElement)) {
                originalMessages.set(msgElement, msgElement.textContent);
            }

            try {
                const translatedText = await translateText(originalText, targetLang);
                msgElement.textContent = translatedText;
            } catch (error) {
                console.error('Translation error:', error);
            }
        }
    }

    // Translate individual text using a translation API
    function translateText(text, targetLanguage) {
        const url = 'https://libretranslate.de/translate'; // Using LibreTranslate as an example
        const body = {
            q: text,
            source: 'auto',
            target: targetLanguage,
            format: 'text'
        };

        return fetch(url, {
            method: 'POST',
            body: JSON.stringify(body),
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Translation API error: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.translatedText) {
                return data.translatedText;
            }
            throw new Error('Invalid translation response');
        })
        .catch(error => {
            console.error('Error translating text:', error);
            return text; // Fallback to original text on error
        });
    }

    // Observe new messages added to the chat and translate them if needed
    function observeNewMessages() {
        const chatContainer = document.getElementById('chat-container');
        if (!chatContainer) {
            console.error('Chat container (#chat-container) not found for MutationObserver!');
            return;
        }

        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const msgElement = node.querySelector('.message-bubble p');
                            if (msgElement && currentChatLanguage !== 'en') {
                                const originalText = msgElement.textContent;
                                if (!originalMessages.has(msgElement)) {
                                    originalMessages.set(msgElement, originalText);
                                }
                                translateText(originalText, currentChatLanguage)
                                    .then(translatedText => {
                                        msgElement.textContent = translatedText;
                                    })
                                    .catch(error => {
                                        console.error('Error translating new message:', error);
                                    });
                            }
                        }
                    });
                }
            });
        });

        observer.observe(chatContainer, { childList: true, subtree: true });
    }

    // Initialize the chat translator when the DOM is fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeChatTranslator);
    } else {
        initializeChatTranslator();
    }

})();