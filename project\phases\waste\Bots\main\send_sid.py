import os
from flask import Flask, request
from twilio.rest import Client
from dotenv import load_dotenv

# Load environment variables from the .env file
load_dotenv()

# Twilio account credentials
TWILIO_ACCOUNT_SID="**********************************"
TWILIO_AUTH_TOKEN="005d910f85546392a91f58a3878c437c"
FROM_WHATSAPP_NUMBER="whatsapp:+***********"
TO_WHATSAPP_NUMBER="whatsapp:+************"


# Initialize Twilio Client
client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

# Create Flask app
app = Flask(__name__)

@app.route("/", methods=['POST'])
def handle_incoming_message():
    incoming_data = request.get_json() if request.is_json else request.form.to_dict()
    message_body = incoming_data.get('Body', '').strip()
    sender = TO_WHATSAPP_NUMBER
    if message_body.upper() == "XX":
        try:
            message = client.messages.create(
                from_=FROM_WHATSAPP_NUMBER,
                to=sender,
                content_sid='HX3726915d3db0b903d3b6fb7a415f75f8'
            )
            print(f"Starter SID sent to {sender}: {message.sid}")
        except Exception as e:
            print(f"Error sending starter SID: {e}")
    
    return '', 204

if __name__ == '__main__':
    app.run(debug=False, port=5000)