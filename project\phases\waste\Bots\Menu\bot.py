from flask import Flask, request
from twilio.twiml.messaging_response import MessagingResponse
from twilio.rest import Client

app = Flask(__name__)

account_sid = '**********************************'
auth_token = '005d910f85546392a91f58a3878c437c'
client = Client(account_sid, auth_token)

@app.route("/webhook", methods=['POST'])
def webhook():
    incoming_msg = request.values.get('Body', '').lower()
    resp = MessagingResponse()
    msg = resp.message()
    msg.body(f"You said: {incoming_msg}")
    return str(resp)

if __name__ == "__main__":
    app.run(debug=True)