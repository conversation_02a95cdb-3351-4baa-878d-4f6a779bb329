from flask import Flask, render_template, jsonify, request, session, redirect, url_for, Response, stream_with_context
import requests
import json
import os
import logging
import queue
import threading
from datetime import datetime, timedelta
from dotenv import load_dotenv
from auth import auth, login_required
from functools import wraps
import time
from twilio.rest import Client
from twilio.twiml.messaging_response import MessagingResponse
import gender_guesser.detector as gender
from settings_api import settings_bp
import asyncio
from typing import Optional
from realtime import AsyncRealtimeClient, RealtimeSubscribeStates

# ==================== TERMINAL COLOR HELPERS ====================
class TermColors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    DIVIDER = f"{'-'*50}"

def print_colored(msg, color=TermColors.ENDC, spacing=False, divider=False):
    if spacing:
        print()
    if divider:
        print(TermColors.CYAN + TermColors.DIVIDER + TermColors.ENDC)
    print(color + str(msg) + TermColors.ENDC)
    if divider:
        print(TermColors.CYAN + TermColors.DIVIDER + TermColors.ENDC)
    if spacing:
        print()

# ==================== INITIAL SETUP & CONFIGURATION ====================

# Global variables for Supabase Realtime Chats
realtime_socket = None
realtime_channel = None
realtime_subscription = None
chats_data = []  # Store the latest chats data

# Create a list to track connected SSE clients
sse_clients = []  # Each client has its own queue

# ----- >  Flask Configuration < ----- #

app = Flask(__name__)
app.register_blueprint(settings_bp)
app.secret_key = os.urandom(24)
app.register_blueprint(auth)

# Load environment variables from .env file
load_dotenv()

# Get the values from environment variables
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY')
TWILIO_ACCOUNT_SID = os.getenv('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.getenv('TWILIO_AUTH_TOKEN')
TWILIO_PHONE_NUMBER = os.getenv('FROM_WHATSAPP_NUMBER', '').replace('whatsapp:+', '')  # Extract just the number

# Log the environment variables (with color and divider)
def _mask(val, keep=4):
    return (val[:keep] + "…") if val else "None"
print_colored(f"SUPABASE_URL: {_mask(SUPABASE_URL)}", TermColors.BLUE, divider=True)
print_colored(f"TWILIO_ACCOUNT_SID: {_mask(TWILIO_ACCOUNT_SID)}", TermColors.BLUE)
print_colored(f"TWILIO_PHONE_NUMBER: {_mask(TWILIO_PHONE_NUMBER)}", TermColors.BLUE, divider=True)

app.permanent_session_lifetime = timedelta(minutes=3000)

# Variables for request control
REQUEST_TIMEOUT = 40
rate_limit = False # Set to True to enable rate limiting

# Configure logging
logger = logging.getLogger(__name__)

# Initialize Twilio client
twilio_client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

# Supabase Headers
headers = {
    "apikey": SUPABASE_ANON_KEY,
    "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=minimal"
}

# === Constants for Food Catalogue (from Bots/Whatsapp/English/app.py) ===
FOOD_PRODUCTS = [
    # Room Booking (these are included in the original FOOD_PRODUCTS list, keeping for consistency if needed elsewhere, though only "Food Menu" is used for the catalogue)
    {"id": "98o9bhjshv", "section_title": "Room Booking"},
    {"id": "6izhhyylks", "section_title": "Room Booking"},
    {"id": "c0qd6ldgkt", "section_title": "Room Booking"},
    {"id": "6wz8tf0n7y", "section_title": "Room Booking"},
    # Food Menu
    {"id": "cckhxov5k2", "section_title": "Food Menu"}, # Example: Pizza
    {"id": "zwuw3m67mn", "section_title": "Food Menu"}, # Example: Burger
    {"id": "42uaridjbf", "section_title": "Food Menu"}, # Example: Pasta
    {"id": "jflfigxy78", "section_title": "Food Menu"}, # Example: Salad
    {"id": "eyb1hi65xl", "section_title": "Food Menu"}, # Example: Steak
    {"id": "n86o421iyw", "section_title": "Food Menu"}, # Example: Chicken Dish
    {"id": "0j2d89sy2t", "section_title": "Food Menu"}, # Example: Seafood
    {"id": "rz84jsys6a", "section_title": "Food Menu"}  # Example: Dessert
]

FOOD_CATALOG_ID = '8855764864434585'
FOOD_CATALOGUE_TEMPLATE = 'HXcd49a2926d65bf604099c6045792ce1c' # Main catalogue template SID
ORDER_FOOD_TEMPLATE = "HX74a4f19dc28d264a2f110d1259544995"    # Fallback/Original food order SID, also used as the trigger from livechat previously

# Gender Guesser Setup
d = gender.Detector()

@app.route('/logout')
def logout():
    # Clear the session data
    session.clear()
    # Redirect to the login page (or homepage)
    return redirect(url_for('auth.login')) # Assuming 'login' is in the 'auth' blueprint

# Decorator to check if requests are allowed (Rate Limiting)
def check_request_allowed(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not rate_limit:
            return f(*args, **kwargs)

        current_time = time.time()
        start_time = session.get('start_time', current_time)

        if current_time - start_time > REQUEST_TIMEOUT:
            return jsonify({'error': 'Session expired. Please refresh the page.'}), 403

        session['last_request_time'] = current_time
        return f(*args, **kwargs)
    return decorated_function

# ==================== PAGE RENDERING ROUTES ====================
@app.route('/')
@login_required
def index():
    session['start_time'] = time.time()
    session['last_request_time'] = time.time()
    return render_template('dashboard.html')

@app.route('/dev')
@login_required
def dev():
    return render_template('dev.html')

@app.route('/status')
@login_required
def status():
    return render_template('status.html')

@app.route('/sales')
@login_required
def open_sales():
    return render_template('sales.html')

@app.route('/analytics')
@login_required
def open_analytics():
    return render_template('analytics.html')

@app.route('/products')
@login_required
def open_products():
    return render_template('productconfig.html')

@app.route('/pmsmanagement')
@login_required
def open_pmsmanagement():
    return render_template('pmsmanagement.html')

@app.route('/proana') # Product Analytics?
@login_required
def open_productana():
    return render_template('productana.html') # Consider renaming template if possible

@app.route('/settings')
@login_required
def settings():
    roles = [
        {"name": "Owner", "description": "Admin-level to all resources."},
        {"name": "Management", "description": "Management access to hotel operations."},
        {"name": "Development", "description": "Technical development access."},
        {"name": "Housekeeping", "description": "Housekeeping staff access."},
        {"name": "Receptionist", "description": "Front desk reception access."}
    ]
    return render_template('settexp.html', roles=roles) # Consider renaming template if possible

@app.route('/trial-settings')
def trial_settings():
    return render_template('new_versionpages/settings.html')

@app.route('/users')
@login_required
def open_users():
    return render_template('users.html')

@app.route('/aichat')
@login_required
def open_aichat():
    return render_template('aichat.html')

@app.route('/issue')
@login_required
def open_issuepage():
    return render_template('issues.html')

@app.route('/livechat')
@login_required
def open_livechat(): # Renamed function from open_taksks
    return render_template('livechat.html')

@app.route('/tasks')
@login_required
def open_tasks():
    return render_template('tasks.html')

@app.route('/googleana') # Google Analytics?
@login_required
def open_googleana():
    return render_template('googleana.html')

@app.route('/pmsanalytics')
@login_required
def open_pmsanalytics():
    return render_template('pmsanalytics.html')

@app.route('/smanalytics')
@login_required
def open_smanalytics():
    return render_template('smanalytics.html')

@app.route('/aiconfig')
@login_required
def open_aiconfig():
    return render_template('config.html') # Consider renaming template if possible

@app.route('/pmsconfig')
@login_required
def open_pmsconfig():
    return render_template('pmsconfig.html')

@app.route('/config') # General Configuration?
@login_required
def open_config():
    return render_template('configuration.html')

# ==================== VOICEBOT CONFIGURATION ====================

@app.route('/voicebot')
@login_required
def open_voicebot():
    voice_calls_data = []
    try:
        voicebot_data_url = f"{SUPABASE_URL}/rest/v1/voicebot_data?select=*"
        print_colored(f"Requesting: {voicebot_data_url} for /voicebot route", TermColors.BLUE, divider=False, spacing=False)
        r = requests.get(voicebot_data_url, headers=headers)

        print_colored(f"Voicebot data response status for /voicebot route: {r.status_code}", TermColors.CYAN, spacing=False)
        if r.status_code == 200:
            voice_calls_data = r.json()
        else:
            print_colored(f"ERROR RESPONSE for /voicebot route: {r.text}", TermColors.RED, divider=True, spacing=False)
            logger.error(f"Failed to fetch voicebot_data for /voicebot route: {r.text}")
            # Optionally, you could pass an error message to the template
            # return render_template('voicebot.html', error_message="Failed to load data.")

    except Exception as e:
        import traceback
        print_colored(f"Exception in /voicebot route while fetching data: {str(e)}", TermColors.RED, divider=True, spacing=False)
        print_colored(traceback.format_exc(), TermColors.RED, spacing=False)
        logger.error(f"An unexpected error occurred in /voicebot route: {e}")
        # Optionally, pass an error message to the template
        # return render_template('voicebot.html', error_message="Server error occurred.")

    return render_template('voicebot.html', voice_calls_data=voice_calls_data)

@app.route('/voicebot_data', methods=['GET'])
@login_required # Assuming this route also needs login
def get_voicebot_data():
    try:
        voicebot_data_url = f"{SUPABASE_URL}/rest/v1/voicebot_data?select=*"
        print_colored(f"Requesting: {voicebot_data_url}", TermColors.BLUE, divider=False, spacing=False)
        r = requests.get(voicebot_data_url, headers=headers)

        print_colored(f"Voicebot data response status: {r.status_code}", TermColors.CYAN, spacing=False)
        if r.status_code != 200:
            print_colored(f"ERROR RESPONSE: {r.text}", TermColors.RED, divider=True, spacing=False)
            return jsonify({"error": f"Failed to fetch voicebot_data: {r.text}"}), 500
        
        return jsonify(r.json()), r.status_code

    except Exception as e:
        import traceback
        print_colored(f"Exception in get_voicebot_data: {str(e)}", TermColors.RED, divider=True, spacing=False)
        print_colored(traceback.format_exc(), TermColors.RED, spacing=False)
        return jsonify({"error": f"Server error: {str(e)}"}), 500
# ==================== SUPABASE DATA FETCHING & UPDATING ROUTES ====================

@app.route('/supabase_chats', methods=['GET'])
def get_supabase_chats():
    print_colored("In the GET_SUPABASE_CHATS FUNCTION", TermColors.GREEN, divider=True, spacing=False)

    try:
        # Print the exact URL and headers being used (with API key partially masked)
        masked_key = SUPABASE_ANON_KEY[:5] + "..." if SUPABASE_ANON_KEY else "None"
        print_colored(f"Supabase URL: {SUPABASE_URL}", TermColors.BLUE, spacing=False)
        print_colored(f"Using API key starting with: {masked_key}", TermColors.BLUE, spacing=False)

        # Fetch all chats - note the lowercase 'chats' table name
        chats_url = f"{SUPABASE_URL}/rest/v1/chats?select=*"
        print_colored(f"Requesting: {chats_url}", TermColors.BLUE, divider=True, spacing=False)
        r = requests.get(chats_url, headers=headers)

        print_colored(f"Chats response status: {r.status_code}", TermColors.CYAN, spacing=False)
        if r.status_code != 200:
            print_colored(f"ERROR RESPONSE: {r.text}", TermColors.RED, divider=True, spacing=False)
            return jsonify({"error": f"Failed to fetch chats: {r.text}"}), 500

        # We don't need the users table anymore
        return jsonify(r.json()), r.status_code

    except Exception as e:
        import traceback
        print_colored(f"Exception in get_supabase_chats: {str(e)}", TermColors.RED, divider=True, spacing=False)
        print_colored(traceback.format_exc(), TermColors.RED, spacing=False)
        return jsonify({"error": f"Server error: {str(e)}"}), 500


# POST a new chat message
@app.route('/supabase_chats', methods=['POST'])
def post_supabase_chat():
    logger.info("In the POST_SUPABASE_CHATS FUNCTION")
    try:
        chat = request.get_json()

        # Log what we're sending to Supabase
        logger.info(f"Sending to Supabase: {chat}")

        # Ensure required fields are present
        required_fields = ['user_id', 'message', 'timestamp', 'sender', 'customer']
        for field in required_fields:
            if field not in chat:
                logger.warning(f"Missing required field in Supabase chat: {field}")
                return jsonify({"error": f"Missing required field: {field}"}), 400

        # Ensure customer is a string 'true' or 'false', not a boolean
        if isinstance(chat['customer'], bool):
            chat['customer'] = str(chat['customer']).lower()

        # Add phone_number to the chat data if it's not already there
        if 'phone_number' not in chat or not chat['phone_number']:
            # Only try to find the phone number in Supabase
            try:
                query_url = f"{SUPABASE_URL}/rest/v1/chats"
                params = {
                    "user_id": f"eq.{chat['user_id']}",
                    "select": "phone_number",
                    "limit": 1
                }

                response = requests.get(
                    query_url,
                    headers=headers,
                    params=params
                )

                if response.status_code == 200 and response.json():
                    supabase_data = response.json()
                    if supabase_data and supabase_data[0].get('phone_number'):
                        chat['phone_number'] = supabase_data[0]['phone_number']
                        logger.info(f"Added phone_number {chat['phone_number']} from Supabase for user {chat['user_id']}")
            except Exception as e2:
                logger.warning(f"Could not add phone_number from Supabase: {str(e2)}")

        # POST the chat message to Supabase
        # Use the already sanitized SUPABASE_URL
        post_url = f"{SUPABASE_URL}/rest/v1/chats"
        logger.info(f"POSTing to: {post_url}")

        r = requests.post(
            post_url,
            json=chat,
            headers=headers
        )

        logger.info(f"Response status: {r.status_code}")
        if r.status_code not in (200, 201, 204):
            logger.error(f"Error response from Supabase: {r.text}")
            return jsonify({"error": f"Failed to save chat: {r.text}"}), 500

        # Handle the response properly - some Supabase endpoints return empty responses
        if r.text.strip():
            try:
                response_data = r.json()
                return jsonify({"success": True, "id": response_data.get("id", "unknown")}), 201
            except json.JSONDecodeError:
                logger.warning("Empty or non-JSON response from Supabase, but status code indicates success")
                return jsonify({"success": True}), 201
        else:
            # Empty response but successful status code
            return jsonify({"success": True}), 201

    except Exception as e:
        import traceback
        logger.error(f"Exception in post_supabase_chat: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": f"Server error: {str(e)}"}), 500
    
# ========================== SUPABASE CHATS CONFIGURATION ====================

@app.route('/api/supabase-config')
def get_supabase_config():
    """Provide Supabase configuration to the client."""
    return jsonify({
        "SUPABASE_URL": SUPABASE_URL,
        "SUPABASE_ANON_KEY": SUPABASE_ANON_KEY
    })

@app.route('/update_stats', methods=['POST'])
@check_request_allowed
def update_stats():
    current_day = datetime.now().strftime('%a').lower()

    response = requests.get(f"{SUPABASE_URL}/rest/v1/Total_users_chart?select=*", headers=headers)
    db_data = response.json()

    if db_data:
        row = db_data[0]
        new_count = int(row[current_day]) + 1
        update_data = {current_day: str(new_count)}
        update_response = requests.patch(
            f"{SUPABASE_URL}/rest/v1/Total_users_chart?id=eq.{row['id']}",
            headers=headers,
            json=update_data
        )
        if update_response.status_code == 204:
            return jsonify({"message": f"Updated {current_day} count to {new_count}", "status": "success"}), 200
        else:
            logger.error(f"Error updating Total_users_chart: {update_response.text}")
            return jsonify({"message": "Error updating data", "status": "error"}), 500
    else:
        logger.error("No data found in Total_users_chart table")
        return jsonify({"message": "No data found in the Total_users_chart table", "status": "error"}), 404

@app.route('/firstrowana')
@check_request_allowed
def fetch_firstrowana():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/firstrowana', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching firstrowana: {response.text}")
        return jsonify([]), response.status_code
    return jsonify(response.json())

def get_tasks():
    endpoint = f"{SUPABASE_URL}/rest/v1/Task_list"
    try:
        response = requests.get(
            endpoint,
            headers=headers
        )
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching tasks: {e}")
        return None

@app.route('/tasklist')
def tasklist():
    tasks = get_tasks()
    if tasks:
        return jsonify(tasks)
    return jsonify({"error": "Failed to fetch tasks"}), 500

@app.route('/tasksana')
@check_request_allowed
def fetch_tasks_analytics():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/tasksana', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching tasks analytics: {response.text}")
        return jsonify([]), response.status_code
    return jsonify(response.json())

@app.route('/fetch-tasks')
@check_request_allowed
def fetch_tasks():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/Tasks', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching tasks: {response.text}")
        return jsonify([]), response.status_code
    return jsonify(response.json())

@app.route('/getuserlist') # Fetches from Supabase 'Users' table, distinct from chat users
@check_request_allowed
def fetch_userlist():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/Users', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching user list from Supabase: {response.text}")
        return jsonify([]), response.status_code
    return jsonify(response.json())

@app.route('/saleschart')
@check_request_allowed
def fetch_data_for_chart():
    tips_response = requests.get(f'{SUPABASE_URL}/rest/v1/salescharttips', headers=headers)
    sales_response = requests.get(f'{SUPABASE_URL}/rest/v1/sales_chartts', headers=headers)

    if sales_response.status_code != 200:
        logger.error(f"Error fetching sales chart data: {sales_response.text}")
        return jsonify([]), sales_response.status_code
    if tips_response.status_code != 200:
        logger.error(f"Error fetching sales tips data: {tips_response.text}")
        # Decide if you want to return partial data or fail completely
        return jsonify([]), tips_response.status_code # Or return sales data only

    return jsonify({"sales_chart": sales_response.json(), "sales_tips": tips_response.json()})

@app.route('/aivsmanual')
def fetch_data_for_aivsmanual():
    ai_response = requests.get(f'{SUPABASE_URL}/rest/v1/ai_interactions', headers=headers)
    man_response = requests.get(f'{SUPABASE_URL}/rest/v1/manual_interactions', headers=headers)

    if man_response.status_code != 200:
        logger.error(f"Error fetching manual interactions: {man_response.text}")
        return jsonify([]), man_response.status_code
    if ai_response.status_code != 200:
        logger.error(f"Error fetching AI interactions: {ai_response.text}")
        return jsonify([]), ai_response.status_code

    return jsonify({"ai_inter": ai_response.json(), "manual_inter": man_response.json()})

@app.route('/firstrowsales')
@check_request_allowed
def fetch_firstrowsales():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/firstrowsales', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching first row sales: {response.text}")
        return jsonify([]), response.status_code
    return jsonify(response.json())

@app.route('/weekly_reviews')
@check_request_allowed
def fetch_weekly_reviews():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/weekly_reviews', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching weekly reviews: {response.text}")
        return jsonify([]), response.status_code
    return jsonify(response.json())

@app.route('/fetch-completed-tasks')
@check_request_allowed
def fetch_completed_tasks():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/completed_tasks', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching completed tasks: {response.text}")
        return jsonify([]), response.status_code
    return jsonify(response.json())

@app.route('/fetch-users')
@check_request_allowed
def fetch_users():
    # First try to get users from the Supabase Users table
    response = requests.get(f'{SUPABASE_URL}/rest/v1/Users', headers=headers)
    users_from_db = []

    if response.status_code == 200:
        users_from_db = response.json()
        logger.info(f"Retrieved {len(users_from_db)} users from Supabase Users table")
    else:
        logger.error(f"Error fetching users from Supabase: {response.text}")

    # Then get any additional users from chat data
    try:
        # Get chat data
        chats_response = requests.get(f"{SUPABASE_URL}/rest/v1/chats?select=*", headers=headers)

        if chats_response.status_code == 200:
            chats = chats_response.json()

            # Extract unique users from chats
            user_map = {}
            for chat in chats:
                if chat.get('customer') and chat.get('user_id'):
                    user_id = str(chat.get('user_id'))

                    if user_id not in user_map:
                        # Check if this user_id exists in users_from_db
                        existing_user = next((u for u in users_from_db if str(u.get('id')) == user_id), None)

                        if not existing_user:
                            # Create a new user object
                            room_number = 101 + int(user_id) if user_id.isdigit() else "Unknown"
                            user_map[user_id] = {
                                'id': int(user_id) if user_id.isdigit() else user_id,
                                'Name': chat.get('sender', 'Guest'),
                                'phone': chat.get('sender') if '+' in str(chat.get('sender')) else '',
                                'room_no': room_number,
                                'platform': 'WhatsApp'
                            }

            # Add users from chats to the result
            users_from_chats = list(user_map.values())
            combined_users = users_from_db + users_from_chats
            logger.info(f"Added {len(users_from_chats)} additional users from chat data")

            return jsonify(combined_users)
    except Exception as e:
        logger.error(f"Error processing chat data for users: {str(e)}")

    # If we couldn't get chat data, just return the database users
    return jsonify(users_from_db)

@app.route('/updateduserslist')
@check_request_allowed
def fetch_updated_users_list():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/Users_list', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching updated users list: {response.text}")
        return jsonify([]), response.status_code
    return jsonify(response.json())


@app.route('/fetch-platforms')
@check_request_allowed
def fetch_platforms():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/platforms', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching platforms data: {response.text}")
        return jsonify([]), response.status_code
    return jsonify(response.json())

@app.route('/fetch-data') # Fetches from 'firstrowdash' table
@check_request_allowed
def fetch_data():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/firstrowdash', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching first row dash data: {response.text}")
        return jsonify([]), response.status_code
    rows = response.json()
    print("Fetched data (firstrowdash):", rows) # Added context to print
    return jsonify(rows)

@app.route('/customers') # Fetches from 'customers' table
@check_request_allowed
def fetch_customers():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/customers', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching customers data: {response.text}")
        return jsonify([]), response.status_code
    rows = response.json()
    print("Fetched data (customers):", rows) # Added context to print
    return jsonify(rows)

@app.route('/fetch-tpp') # Fetches from 'tppd' table
@check_request_allowed
def fetch_tpp():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/tppd', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching tpp data: {response.text}")
        return jsonify([]), response.status_code
    return jsonify(response.json())
@app.route('/fetch-sales-list')
@check_request_allowed
def fetch_sales_list():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/Sales_list', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching Sales_list data: {response.text}")
        return jsonify([]), response.status_code
    return jsonify(response.json())

@app.route('/product_list')
@check_request_allowed
def fetch_product_list():
    response = requests.get(f'{SUPABASE_URL}/rest/v1/product_list?select=*', headers=headers)
    if response.status_code != 200:
        logger.error(f"Error fetching product_list data: {response.text}")
        return jsonify([]), response.status_code
    return jsonify(response.json())


@app.route('/send_message', methods=['POST'])
def send_message():
    data = request.json
    logger.info(f"Received message data from dashboard: {data}")

    user_id_str = str(data.get('userId'))
    message = data.get('message')
    timestamp = data.get('timestamp')
    sender = data.get('sender')

    if not all([user_id_str, message, timestamp, sender]):
        logger.warning(f"Send message request missing fields: {data}")
        return jsonify(success=False, error="Missing required fields"), 400

    # Send message directly using Supabase for user lookup
    try:
        # Get the phone number from Supabase for this user
        query_url = f"{SUPABASE_URL}/rest/v1/chats"
        params = {
            "user_id": f"eq.{user_id_str}",
            "select": "phone_number",
            "limit": 1
        }

        response = requests.get(
            query_url,
            headers=headers,
            params=params
        )

        if response.status_code == 200 and response.json():
            supabase_data = response.json()
            if supabase_data and supabase_data[0].get('phone_number'):
                phone_number_raw = supabase_data[0]['phone_number']
                logger.info(f"Found phone number in Supabase: {phone_number_raw}")

                # Format phone number for Twilio
                recipient_number_twilio = f'whatsapp:{phone_number_raw.lstrip("whatsapp:+")}'
                if not recipient_number_twilio.startswith('whatsapp:+'):
                     recipient_number_twilio = f'whatsapp:+{recipient_number_twilio.lstrip("whatsapp:")}'


                from_number_twilio = os.getenv('FROM_WHATSAPP_NUMBER')
                if not from_number_twilio:
                    logger.error("FROM_WHATSAPP_NUMBER environment variable is not set for sending message")
                    return jsonify({"success": False, "error": "Sender WhatsApp number not configured"}), 500

                # Check if the message indicates sending the food catalogue
                try:
                    message_data = json.loads(message) # 'message' is the string from the livechat payload
                    if isinstance(message_data, dict) and message_data.get('type') == 'send_food_catalogue':
                        logger.info(f"Request to send full food catalogue to {recipient_number_twilio}")

                        # Get all food menu products (filter to only include Food Menu items)
                        food_menu_products_filtered = [p for p in FOOD_PRODUCTS if p["section_title"] == "Food Menu"]

                        if not food_menu_products_filtered:
                            logger.error("No food menu products found in FOOD_PRODUCTS to build catalogue.")
                            # Fallback to simple food order template if no products
                            try:
                                twilio_fallback_message = twilio_client.messages.create(
                                    content_sid=ORDER_FOOD_TEMPLATE,
                                    to=recipient_number_twilio,
                                    from_=from_number_twilio
                                )
                                logger.info(f"Sent fallback food template (ORDER_FOOD_TEMPLATE) due to no products. SID: {twilio_fallback_message.sid}")
                                return jsonify(success=True, whatsapp_response={"success": True, "message_sid": twilio_fallback_message.sid, "note": "No food products for catalogue, sent fallback."})
                            except Exception as e_fallback:
                                logger.error(f"Error sending fallback food template: {e_fallback}")
                                return jsonify(success=False, error=f"Failed to send fallback food template: {str(e_fallback)}"), 500
                        
                        items_payload = [{"id": p["id"]} for p in food_menu_products_filtered]
                        thumbnail_id = food_menu_products_filtered[0]["id"] if food_menu_products_filtered else ""

                        content_vars = {
                            '1': FOOD_CATALOG_ID,
                            '2': "Food Menu",  # Title
                            '3': "Explore our full selection of delicious dishes!",  # Body
                            '4': "Available for room service or at our restaurant.",  # Subtitle
                            '5': thumbnail_id,
                            'products': json.dumps(items_payload) # Ensure products is a JSON string
                        }
                        
                        try:
                            logger.info(f"Attempting to send food catalogue template (SID: {FOOD_CATALOGUE_TEMPLATE}) with vars: {content_vars}")
                            twilio_catalogue_message = twilio_client.messages.create(
                                content_sid=FOOD_CATALOGUE_TEMPLATE,
                                content_variables=json.dumps(content_vars), # content_variables must be a JSON string
                                to=recipient_number_twilio,
                                from_=from_number_twilio
                            )
                            logger.info(f"Food menu catalogue sent. SID: {twilio_catalogue_message.sid}")
                            return jsonify(success=True, whatsapp_response={"success": True, "message_sid": twilio_catalogue_message.sid})
                        except Exception as e_catalogue:
                            logger.error(f"Error sending food catalogue (SID: {FOOD_CATALOGUE_TEMPLATE}): {e_catalogue}")
                            # Fallback to original/simpler food order template
                            try:
                                twilio_fallback_message = twilio_client.messages.create(
                                    content_sid=ORDER_FOOD_TEMPLATE,
                                    to=recipient_number_twilio,
                                    from_=from_number_twilio
                                )
                                logger.info(f"Sent fallback food template (ORDER_FOOD_TEMPLATE) due to catalogue error. SID: {twilio_fallback_message.sid}")
                                return jsonify(success=True, whatsapp_response={"success": True, "message_sid": twilio_fallback_message.sid, "note": "Catalogue send failed, sent fallback."})
                            except Exception as e_fallback:
                                logger.error(f"Error sending fallback food template: {e_fallback}")
                                return jsonify(success=False, error=f"Failed to send catalogue and fallback food template: {str(e_fallback)}"), 500
                    else:
                        # Not a food catalogue request, send as plain text
                        logger.info("Message is not a food_catalogue request, sending as plain text.")
                        result = send_whatsapp_message(phone_number_raw, message) # send_whatsapp_message handles formatting
                        logger.info(f"WhatsApp API response (plain text message): {result}")
                        if result.get("success"):
                            return jsonify(success=True, whatsapp_response=result)
                        else:
                            return jsonify(success=False, error=result.get("error", "Unknown WhatsApp error sending plain text"), whatsapp_response=result), 500
                except json.JSONDecodeError:
                    # Message is not JSON, treat as plain text
                    logger.info("Message is not JSON, sending as plain text.")
                    result = send_whatsapp_message(phone_number_raw, message) # send_whatsapp_message handles formatting
                    logger.info(f"WhatsApp API response (plain text message after JSONDecodeError): {result}")
                    if result.get("success"):
                        return jsonify(success=True, whatsapp_response=result)
                    else:
                        return jsonify(success=False, error=result.get("error", "Unknown WhatsApp error sending plain text"), whatsapp_response=result), 500
                except Exception as e_parse:
                    logger.warning(f"Error trying to parse message or handle food_catalogue type: {e_parse}")
                    # Fallback to sending as plain text if any other parsing issue
                    result = send_whatsapp_message(phone_number_raw, message)
                    logger.info(f"WhatsApp API response (plain text message after general parsing error): {result}")
                    if result.get("success"):
                        return jsonify(success=True, whatsapp_response=result)
                    else:
                        return jsonify(success=False, error=result.get("error", "Unknown WhatsApp error sending plain text"), whatsapp_response=result), 500
        
        error_message = f"User not found or phone number missing for user ID: {user_id_str} when attempting to send message."
        logger.warning(error_message)
        return jsonify(success=False, error=error_message), 404

    except ValueError as e:
        logger.error(f"Value error in send_message: {str(e)}")
        return jsonify(success=False, error=str(e)), 400
    except Exception as e:
        error_message = f"An unexpected error occurred in send_message: {str(e)}"
        logger.exception(error_message)
        return jsonify(success=False, error=error_message), 500

# ==================== TWILIO WEBHOOK & BOT ROUTES ====================

# Function to send WhatsApp messages using Twilio
def send_whatsapp_message(phone_number, message):
    # Basic validation for phone number format
    if not phone_number:
        logger.error("Empty phone number provided for WhatsApp message")
        return {"success": False, "error": "Empty phone number provided"}

    # Remove any 'whatsapp:' prefix if it exists
    if phone_number.startswith('whatsapp:'):
        phone_number = phone_number[9:]

    # Ensure it has a + prefix for Twilio
    if not phone_number.startswith('+'):
        phone_number = '+' + phone_number

    logger.info(f"Sending WhatsApp message to {phone_number}")

    try:
        # Ensure the phone number is properly formatted for Twilio
        to_number = f'whatsapp:{phone_number}'
        from_number = os.getenv('FROM_WHATSAPP_NUMBER')  # Use correct env var, should be 'whatsapp:+<number>'

        if not from_number:
            logger.error("FROM_WHATSAPP_NUMBER environment variable is not set")
            return {"success": False, "error": "Sender WhatsApp number not configured"}

        # Prevent sending to self
        if to_number == from_number:
            logger.error("Cannot send WhatsApp message to the same number as sender")
            return {"success": False, "error": "Cannot send message to the same number as sender"}

        logger.info(f"Twilio sending from {from_number} to {to_number}")
        logger.info(f"Using TWILIO_ACCOUNT_SID: {TWILIO_ACCOUNT_SID}")
        logger.info(f"Using FROM_WHATSAPP_NUMBER: {from_number}")

        # Ensure Twilio client is initialized
        if not twilio_client:
            logger.error("Twilio client is not initialized")
            return {"success": False, "error": "Twilio client is not initialized"}

        message_instance = twilio_client.messages.create(
            body=message,
            from_=from_number,
            to=to_number
        )
        logger.info(f"Successfully sent WhatsApp message. SID: {message_instance.sid}")
        return {"success": True, "message_sid": message_instance.sid}
    except Exception as e:
        error_message = f"Error sending WhatsApp message to {phone_number}: {str(e)}"
        logger.exception(error_message)
        return {"success": False, "error": error_message}


# ==================== MESSAGE TEMPLATE MANAGEMENT ROUTES ====================

@app.route('/data/templates.json')
def get_templates():
    try:
        with open('data/templates.json', 'r', encoding='utf-8') as f:
            templates_data = json.load(f)

        # Ensure the templates key exists
        if 'templates' not in templates_data:
            templates_data['templates'] = []

        # Log the number of templates being returned
        template_count = len(templates_data['templates'])
        logger.info(f"Returning {template_count} templates from JSON file")

        return jsonify(templates_data)
    except FileNotFoundError:
        # If the file doesn't exist, create it with an empty templates array
        logger.warning("templates.json not found, creating new file with empty templates array")
        templates_data = {"templates": []}

        # Create the data directory if it doesn't exist
        os.makedirs('data', exist_ok=True)

        # Write the empty templates file
        with open('data/templates.json', 'w', encoding='utf-8') as f:
            json.dump(templates_data, f, indent=2, ensure_ascii=False)

        return jsonify(templates_data)
    except json.JSONDecodeError:
        # If the file exists but is not valid JSON, return an empty templates array
        logger.error("Invalid JSON in templates.json, returning empty templates array")
        return jsonify({"templates": []})
    except Exception as e:
        logger.error(f"Error reading templates.json: {str(e)}")
        return jsonify({"error": "Failed to load templates", "templates": []}), 500
    
# ==================== MESSAGE TEMPLATE MANAGEMENT ROUTES ====================

@app.route('/save_templates', methods=['POST'])
def save_templates():
    try:
        templates_data = request.json

        if not templates_data or 'templates' not in templates_data:
            return jsonify({"error": "Invalid template data format"}), 400

        # Ensure templates is a list
        if not isinstance(templates_data['templates'], list):
            return jsonify({"error": "Templates must be a list"}), 400

        # Log the number of templates being saved
        template_count = len(templates_data['templates'])
        logger.info(f"Saving {template_count} templates to JSON file")

        with open('data/templates.json', 'w', encoding='utf-8') as f:
            json.dump(templates_data, f, indent=2, ensure_ascii=False)

        return jsonify({
            "success": True,
            "message": "Templates saved successfully",
            "count": template_count
        })
    except Exception as e:
        logger.error(f"Error saving templates: {str(e)}")
        return jsonify({"error": f"Failed to save templates: {str(e)}"}), 500

# ==================== UTILITY ROUTES ====================

@app.route('/proxy') # Simple proxy, use with caution
def proxy():
    url = request.args.get('url')
    if not url:
        return "No URL provided", 400

    try:
        # Add headers to mimic a browser request if needed
        proxy_headers = {'User-Agent': 'Mozilla/5.0'}
        response = requests.get(url, headers=proxy_headers, stream=True, timeout=10) # Added stream and timeout
        # Check for successful response
        response.raise_for_status()

        # Get content type, default if not found
        content_type = response.headers.get('content-type', 'application/octet-stream')

        # Stream the response back
        return Response(response.iter_content(chunk_size=1024), content_type=content_type)

    except requests.exceptions.RequestException as e:
        logger.error(f"Proxy request failed for {url}: {str(e)}")
        return f"Error fetching URL: {str(e)}", 500
    except Exception as e:
        logger.error(f"Unexpected proxy error for {url}: {str(e)}")
        return f"An unexpected error occurred: {str(e)}", 500

@app.route('/guess_gender', methods=['GET'])
def guess_gender():
    name = request.args.get('name', '')
    if not name:
        return jsonify({'gender': 'unknown'}), 400

    first_name = name.split()[0]
    try:
        guessed = d.get_gender(first_name)
        if guessed in ['male', 'mostly_male']:
            gender_result = 'male'
        elif guessed in ['female', 'mostly_female']:
            gender_result = 'female'
        else:
            gender_result = 'unknown'
    except Exception as e:
        logger.error(f"Error guessing gender for '{first_name}': {str(e)}")
        gender_result = 'unknown'

    return jsonify({'gender': gender_result})


# ==================== SUPABASE REALTIME INTEGRATION ====================


# Function to broadcast a message to all connected SSE clients
def broadcast_to_clients(message):
    # Convert message to JSON string if it's not already a string
    if not isinstance(message, str):
        message = json.dumps(message)

    # Add message to each client's queue
    for client_queue in sse_clients:
        client_queue.put(message)

    print_colored(f"Broadcasted message to {len(sse_clients)} clients", TermColors.GREEN, divider=True, spacing=False)

def handle_db_changes(payload):
    print_colored(f"Change detected in Supabase chats table: {payload}", TermColors.BLUE, divider=True, spacing=False)
    # Update the global chats_data when changes are detected
    global chats_data
    # Fetch the latest data from Supabase
    response = requests.get(f"{SUPABASE_URL}/rest/v1/chats?select=*", headers=headers)
    if response.status_code == 200:
        chats_data = response.json()
        print_colored(f"Updated chats_data with {len(chats_data)} records", TermColors.BLUE, spacing=False)

        # Extract the payload data directly if it's in the expected format
        if isinstance(payload, dict) and 'data' in payload:
            # This is likely the format from the Supabase Realtime client
            # Just pass it through as is
            message = {
                "event": "db_change",
                "data": payload
            }
            broadcast_to_clients(message)
            return

        # If we get here, we need to extract the data manually
        event_type = None
        record = None
        old_record = None

        # Handle different payload formats from Supabase Realtime
        if hasattr(payload, 'get'):
            # If payload is a dict-like object
            event_type = payload.get('type', '')
            record = payload.get('record', {})
            old_record = payload.get('old_record', {})
        elif hasattr(payload, 'eventType'):
            # If payload is from AsyncRealtimeClient
            event_type = payload.eventType
            record = getattr(payload, 'new', {})
            old_record = getattr(payload, 'old', {})

        # Create a standardized payload for the client
        client_payload = {
            'data': {
                'type': event_type,
                'record': record,
                'old_record': old_record,
                'table': 'chats'
            }
        }

        # Broadcast the change to all connected clients
        message = {
            "event": "db_change",
            "data": client_payload
        }
        broadcast_to_clients(message)

# Route to display all chats from Supabase with real-time updates
@app.route('/livechat_chats')
def livechat_chats():
    global chats_data

    # If chats_data is empty, fetch initial data
    if not chats_data:
        response = requests.get(f"{SUPABASE_URL}/rest/v1/chats?select=*", headers=headers)
        if response.status_code == 200:
            chats_data = response.json()

    # Return the chats data as JSON
    return jsonify(chats_data)

# Server-Sent Events (SSE) endpoint for real-time updates
@app.route('/livechat_events')
def livechat_events():
    def generate():
        # Send initial message to confirm connection
        yield 'data: {"event": "connected", "data": "SSE connection established"}\n\n'

        # Create a client-specific queue
        client_queue = queue.Queue()
        sse_clients.append(client_queue)

        try:
            while True:
                # Check for messages in the client queue
                try:
                    message = client_queue.get(timeout=1)
                    yield f'data: {message}\n\n'
                except queue.Empty:
                    # Send a keep-alive message every 30 seconds
                    yield 'data: {"event": "ping"}\n\n'

        except GeneratorExit:
            # Client disconnected, remove from clients list
            if client_queue in sse_clients:
                sse_clients.remove(client_queue)

    return Response(stream_with_context(generate()),
                   mimetype='text/event-stream',
                   headers={'Cache-Control': 'no-cache',
                            'Connection': 'keep-alive',
                            'X-Accel-Buffering': 'no'})

async def realtime_listener():
    global realtime_socket, realtime_channel, realtime_subscription, chats_data

    # Fetch initial chats data
    try:
        # Use the already sanitized SUPABASE_URL
        response = requests.get(f"{SUPABASE_URL}/rest/v1/chats?select=*", headers=headers)
        if response.status_code == 200:
            chats_data = response.json()
            print(f"Initial load: {len(chats_data)} chat records")
        else:
            print(f"Failed to load initial chats data: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"Error fetching initial chats data: {str(e)}")

    # Use the same Supabase URL and key for realtime
    # Extract the domain part from the URL
    if SUPABASE_URL:
        REALTIME_URL = f"wss://{SUPABASE_URL.replace('https://', '')}/realtime/v1"
    else:
        logger.error("SUPABASE_URL environment variable is not set")
        REALTIME_URL = ""

    API_KEY = SUPABASE_ANON_KEY

    if REALTIME_URL and API_KEY:
        realtime_socket = AsyncRealtimeClient(REALTIME_URL, API_KEY)
        realtime_channel = realtime_socket.channel("chats")
    else:
        logger.error("Cannot initialize AsyncRealtimeClient: missing URL or API key")
        realtime_socket = None
        realtime_channel = None

    def on_subscribe(status: RealtimeSubscribeStates, err: Optional[Exception]):
        if status == RealtimeSubscribeStates.SUBSCRIBED:
            print_colored("Connected to Supabase Realtime!", TermColors.GREEN, divider=True, spacing=False)
        elif err:
            print_colored(f"Supabase Realtime connection error: {err}", TermColors.RED, divider=True, spacing=False)

    if realtime_channel:
        realtime_subscription = realtime_channel.on_postgres_changes(
            event='*',
            schema='public',
            table='chats',
            callback=handle_db_changes
        )

        await realtime_channel.subscribe(on_subscribe)
    else:
        logger.error("Cannot subscribe to Supabase Realtime: channel not initialized")

    try:
        while True:
            await asyncio.sleep(1)
    except asyncio.CancelledError:
        if realtime_socket:
            await realtime_socket.close()
    except Exception as e:
        logger.error(f"Error in realtime listener: {str(e)}")
        if realtime_socket:
            await realtime_socket.close()

def start_realtime_listener():
    """Start the Supabase Realtime listener in a separate thread"""
    def run_async_loop():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(realtime_listener())

    # Start the listener in a daemon thread so it doesn't block app shutdown
    realtime_thread = threading.Thread(target=run_async_loop, daemon=True)
    realtime_thread.start()
    logger.info("Started Supabase Realtime listener in background thread")



# SEND SID'S FOR LIVECHAT INTERACTIVE TEMPLATES.

# --- Template SIDs for LiveChat ---
LIVECHAT_TEMPLATE_SIDS = {
    "Food": "HX74a4f19dc28d264a2f110d1259544995",
    "Beverage": "HXa95d31210871c5d9f80465bd7177d3bc",
    "Spa": "HX0ea1611c7a662becc730c2b9dc788554",
    "Massage": "HX37984b929b741f01a1d97c120da1cefb",
    "Room Booking": "HX90086324ff4757c28a22446152eab6e6",
    "Experiences": "HX50460e90855bb0fd7417eccb26e4e63a"
}

@app.route('/send_livechat_template', methods=['POST'])
@login_required # Ensure only logged-in users can trigger this
def send_livechat_template():
    """
    Receives a request from the frontend to send a specific template
    to a given phone number.
    """
    data = request.json
    recipient_number = data.get('recipient_number')
    template_type = data.get('template_type')

    if not recipient_number or not template_type:
        return jsonify({"success": False, "error": "Missing recipient_number or template_type"}), 400

    template_sid = LIVECHAT_TEMPLATE_SIDS.get(template_type)

    if not template_sid:
        return jsonify({"success": False, "error": f"Unknown template type: {template_type}"}), 400

    # Ensure recipient number is in the correct format for Twilio
    if not recipient_number.startswith('whatsapp:+'):
         recipient_number = f'whatsapp:+{recipient_number.lstrip("+")}'

    from_number = os.getenv('FROM_WHATSAPP_NUMBER') # Use the configured sender number

    if not from_number:
        logger.error("FROM_WHATSAPP_NUMBER environment variable is not set")
        return jsonify({"success": False, "error": "Sender WhatsApp number not configured"}), 500

    logger.info(f"Attempting to send '{template_type}' template (SID: {template_sid}) to {recipient_number}")

    try:
        message = twilio_client.messages.create(
            from_=from_number,
            to=recipient_number,
            content_sid=template_sid
        )
        logger.info(f"Successfully sent template message. SID: {message.sid}")
        return jsonify({"success": True, "message_sid": message.sid})
    except Exception as e:
        logger.error(f"Error sending template message: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


# ==================== APPLICATION RUN ====================

if __name__ == '__main__':
    logger.info("Starting Flask application...")
    app.config['JSONIFY_PRETTYPRINT_REGULAR'] = False

    start_realtime_listener()

    app.run(debug=True, port=1212)
