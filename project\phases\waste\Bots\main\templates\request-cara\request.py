import requests
import json

# Twilio account details (replace with actual Account SID and Auth Token)
TWILIO_ACCOUNT_SID = '**********************************'
TWILIO_AUTH_TOKEN = '005d910f85546392a91f58a3878c437c'

# Twilio API endpoint (as per the given URL)
url = 'https://content.twilio.com/v1/Content/HX61df7be61ad716233665a3ff4559a825/ApprovalRequests/whatsapp'

# Data payload to be sent
payload = {
    "name": "v4_food_or_bev",  # Update this as needed
    "category": "MARKETING"
}

# Making the POST request
response = requests.post(
    url,
    auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN),
    headers={'Content-Type': 'application/json'},
    data=json.dumps(payload)
)

# Check the response status
if response.status_code == 200:
    print("Request successful!")
    print(response.json())
else:
    print(f"Request failed with status: {response.status_code}")
    print(response.text)
    print(response.json())