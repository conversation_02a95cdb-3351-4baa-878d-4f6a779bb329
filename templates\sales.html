<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales</title>
    {% include 'imports.html' %}
    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://preline.co/assets/js/hs-apexcharts-helpers.js"></script>
    <link rel="stylesheet" href="../static/styles/totalsales.css">
    <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>

    <style>
        /* Glow effects for category/platform cards */
        .main-custom-hover-effect:hover {
            box-shadow: 0 0 15px 5px;
        }

        /* Food */
        .main-custom-hover-effect:hover [class*="bg-emerald-500"]~dd,
        .main-custom-hover-effect:hover .bg-emerald-500 {
            box-shadow: 0 0 15px 5px rgba(16, 185, 129, 0.5);
        }

        /* Beverages */
        .main-custom-hover-effect:hover [class*="bg-amber-500"]~dd,
        .main-custom-hover-effect:hover .bg-amber-500 {
            box-shadow: 0 0 15px 5px rgba(245, 158, 11, 0.5);
        }

        /* Spa */
        .main-custom-hover-effect:hover [class*="bg-indigo-500"]~dd,
        .main-custom-hover-effect:hover .bg-indigo-500 {
            box-shadow: 0 0 15px 5px rgba(99, 102, 241, 0.5);
        }

        /* Massage */
        .main-custom-hover-effect:hover [class*="bg-rose-500"]~dd,
        .main-custom-hover-effect:hover .bg-rose-500 {
            box-shadow: 0 0 15px 5px rgba(244, 63, 94, 0.5);
        }

        /* Room Bookings */
        .main-custom-hover-effect:hover [class*="bg-cyan-500"]~dd,
        .main-custom-hover-effect:hover .bg-cyan-500 {
            box-shadow: 0 0 15px 5px rgba(6, 182, 212, 0.5);
        }

        /* Others */
        .main-custom-hover-effect:hover [class*="bg-gray-500"]~dd,
        .main-custom-hover-effect:hover .bg-gray-500 {
            box-shadow: 0 0 15px 5px rgba(107, 114, 128, 0.5);
        }

        /* Whatsapp */
        .main-custom-hover-effect:hover [class*="bg-green-600"]~dd,
        .main-custom-hover-effect:hover .bg-green-600 {
            box-shadow: 0 0 15px 5px rgba(22, 163, 74, 0.5);
        }

        /* Messenger */
        .main-custom-hover-effect:hover [class*="bg-blue-500"]~dd,
        .main-custom-hover-effect:hover .bg-blue-500 {
            box-shadow: 0 0 15px 5px rgba(59, 130, 246, 0.5);
        }

        /* Instagram */
        .main-custom-hover-effect:hover [class*="bg-pink-500"]~dd,
        .main-custom-hover-effect:hover .bg-pink-500 {
            box-shadow: 0 0 15px 5px rgba(236, 72, 153, 0.5);
        }

        /* Voice Bot */
        .main-custom-hover-effect:hover [class*="bg-purple-500"]~dd,
        .main-custom-hover-effect:hover .bg-purple-500 {
            box-shadow: 0 0 15px 5px rgba(168, 85, 247, 0.5);
        }

        /* SMS */
        .main-custom-hover-effect:hover [class*="bg-yellow-500"]~dd,
        .main-custom-hover-effect:hover .bg-yellow-500 {
            box-shadow: 0 0 15px 5px rgba(234, 179, 8, 0.5);
        }

        /* PMS and Others */
        .main-custom-hover-effect:hover [class*="bg-slate-500"]~dd,
        .main-custom-hover-effect:hover .bg-slate-500 {
            box-shadow: 0 0 15px 5px rgba(100, 116, 139, 0.5);
        }

        body {
            visibility: hidden;
        }

        #chartdiv {
            width: 100%;
            height: 550px;
        }

        svg {
            display: block;
        }

        .chart-container {
            position: relative;
            margin-top: 20px;
            height: 400px;
            /* Increased height */
            width: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            /* Aligns content to bottom */
        }

        .table-container {
            max-height: 640px;
            overflow-y: auto;
        }

        .table-container thead {
            position: sticky;
            top: 0;
            z-index: 1;
        }

        .icon {
            margin-top: 1px;
        }

        .platform-icon {
            margin-top: 1px;
        }

        .table-container {
            max-height: 640px;
            overflow-y: auto;
            scrollbar-width: none;
            /* For Firefox */
            -ms-overflow-style: none;
            /* For Internet Explorer and Edge */
        }

        .table-container::-webkit-scrollbar {
            display: none;
            /* For Chrome, Safari and Opera */
        }

        .table-container thead {
            position: sticky;
            top: 0;
            z-index: 1;
        }

        /* Remove scrollbars */
        .table-container {
            max-height: 640px;
            overflow-y: hidden;
            /* Disable vertical scrolling */
        }

        .table-container::-webkit-scrollbar {
            display: none;
            /* Hide scrollbar for Chrome, Safari, and Opera */
        }

        .table-container {
            scrollbar-width: none;
            /* Hide scrollbar for Firefox */
            -ms-overflow-style: none;
            /* Hide scrollbar for IE and Edge */
        }
    </style>
    <style>
        .icon,
        .platform-icon {
            margin-top: 1px;
            border: 2px solid black;
            /* Increase border width */
            box-shadow: none;
            /* Remove any shadow that might blur the border */
        }
    </style>
</head>

<body class="light">

    <!-- Franken UI -->
    {% include 'components/loading.html' %}
    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] ">
        {% include 'sidebar.html' %}
        <div class="flex flex-col">
            <header
                class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
                <div style="margin-left: 8px;" class="flex items-center gap-2 px-4 pl-0">
                    <button id="toggle-btn" class="opacity-100 transition-opacity duration-300 focus:outline-none"
                        style="background-color: transparent !important;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-panel-left">
                            <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                            <path d="M9 3v18"></path>
                        </svg>
                    </button>
                    <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-3 h-4"
                        style="background-color: var(--border-color);"></div>
                    <nav aria-label="breadcrumb">
                        <ol
                            class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
                            <!-- Sales Report (active) -->
                            <div class="menubar" role="menubar">
                                <div class="menubar-indicator"></div>
                                <a href="/sales" role="menuitem" class="active">Sales Report</a>
                                <a href="/pmsanalytics" role="menuitem">PMS Analytics</a>
                                <a href="/googleana" role="menuitem">Google Analytics</a>
                                <a href="/status" role="menuitem">Status Report</a>
                            </div>
                        </ol>
                    </nav>
                </div>
                {% include 'topright.html' %}
            </header>
            <main class="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6">
                <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <div class="card relative overflow-hidden rounded-lg border ground shadow-sm" data-v0-t="card"
                        data-metric="revenue">
                        <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Total revenue</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-badge-euro">
                                <path
                                    d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z" />
                                <path d="M7 12h5" />
                                <path d="M15 9.4a4 4 0 1 0 0 5.2" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div id="total-sales" class="text-2xl font-bold">€0.00</div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Total Lifetime revenue</p>
                            <div
                                class="absolute bottom-7 right-4 flex items-center text-xs text-green-600 border border-green-500 bg-transparent px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-arrow-up-narrow-wide mr-0.5">
                                    <path d="m3 8 4-4 4 4" />
                                    <path d="M7 4v16" />
                                    <path d="M11 12h4" />
                                    <path d="M11 16h7" />
                                    <path d="M11 20h10" />
                                </svg>
                                8.5%
                            </div>
                        </div>
                        <div class="absolute bottom-6 right-7 w-28 h-20">

                        </div>
                        <script>
                            // Store historical data for each metric
                            const metricHistory = {
                                revenue: [],
                                tips: [],
                                reviews: [],
                                sales: []
                            };

                            // Maximum number of historical data points to store
                            const MAX_HISTORY = 5;

                            // Function to generate SVG path from data array
                            function generateSVGPath(dataArray) {
                                if (!dataArray || dataArray.length === 0) {
                                    // Default path if no data
                                    return "M0 100 L20 70 L40 50 L60 40 L80 30 L100 20";
                                }

                                // Normalize the data to fit within the SVG viewBox (0-100)
                                const normalizedData = normalizeData(dataArray);

                                // Build the path data string
                                let pathData = "M0 " + (100 - normalizedData[0]);

                                // Calculate points evenly distributed across the x-axis
                                const xStep = 100 / (normalizedData.length - 1);
                                for (let i = 1; i < normalizedData.length; i++) {
                                    const x = xStep * i;
                                    const y = 100 - normalizedData[i]; // Invert y since SVG y=0 is at the top
                                    pathData += " L" + x + " " + y;
                                }

                                return pathData;
                            }

                            // Function to normalize data values to fit within 0-100 range
                            function normalizeData(dataArray) {
                                // Find min and max values
                                const min = Math.min(...dataArray);
                                const max = Math.max(...dataArray);

                                if (min === max) {
                                    // If all values are the same, create a flat line at 50%
                                    return dataArray.map(() => 50);
                                }

                                // Scale to 10-90 range to ensure visibility within the viewBox
                                return dataArray.map(value => {
                                    const normalized = ((value - min) / (max - min)) * 80 + 10;
                                    return normalized;
                                });
                            }

                            // Function to update the chart for a specific metric
                            function updateChart(metric, value) {
                                // Add new value to history
                                if (!metricHistory[metric]) {
                                    metricHistory[metric] = [];
                                }

                                metricHistory[metric].push(value);

                                // Keep only the most recent MAX_HISTORY entries
                                if (metricHistory[metric].length > MAX_HISTORY) {
                                    metricHistory[metric].shift();
                                }

                                // Generate path data from the history
                                const pathData = generateSVGPath(metricHistory[metric]);

                                // Update line and area paths
                                const linePath = document.getElementById(metric + 'Line');
                                const areaPath = document.getElementById(metric + 'Area');

                                if (linePath && areaPath) {
                                    linePath.setAttribute('d', pathData);
                                    areaPath.setAttribute('d', pathData + " L100 100 L0 100 Z");
                                }
                            }

                            // Predefined fixed patterns for each metric to ensure consistency
                            const fixedPatterns = {
                                revenue: [30, 50, 80, 65, 95],
                                tips: [40, 60, 45, 70, 85],
                                reviews: [20, 40, 60, 50, 75],
                                sales: [55, 65, 45, 70, 60]
                            };

                            // Function to initialize charts with fixed patterns
                            function generateInitialData() {
                                // Use fixed patterns instead of random data
                                Object.keys(fixedPatterns).forEach(metric => {
                                    // Use the predefined patterns for consistent visualization
                                    metricHistory[metric] = [...fixedPatterns[metric]];

                                    // Update chart with fixed data
                                    updateChart(metric, metricHistory[metric][metricHistory[metric].length - 1]);
                                });
                            }

                            // Function to update charts with real data
                            function updateChartsWithRealData() {
                                fetch('/firstrowsales')
                                    .then(response => {
                                        if (!response.ok) {
                                            throw new Error(`HTTP error! Status: ${response.status}`);
                                        }
                                        return response.json();
                                    })
                                    .then(data => {
                                        if (data && data.length > 0) {
                                            const totalSales = parseFloat(data[0].total_sales) || 0;
                                            const totalTips = parseFloat(data[0].total_tips) || 0;
                                            const totalReviews = parseFloat(data[0].total_reviews) || 0;
                                            // Use total_sale_no for the sales metric if available, otherwise fall back to total_count or total_sales
                                            const totalSaleNo = parseFloat(data[0].total_sale_no) || 0;

                                            // Update charts with new data
                                            updateChart('revenue', totalSales);
                                            updateChart('tips', totalTips);
                                            updateChart('reviews', totalReviews);
                                            updateChart('sales', totalSaleNo);

                                            // Update the displayed values (assuming you have elements with these IDs)
                                            if (document.getElementById('total-sales')) {
                                                document.getElementById('total-sales').textContent = `€${totalSales.toFixed(2)}`;
                                            }
                                            if (document.getElementById('total-tips')) {
                                                document.getElementById('total-tips').textContent = `+${totalTips}`;
                                            }
                                            if (document.getElementById('total-customers')) {
                                                document.getElementById('total-customers').textContent = `+${totalReviews}`;
                                            }

                                            // Update the Total Sales container with total_sale_no
                                            const totalSalesContainer = document.querySelector('[data-metric="sales"] .text-2xl');
                                            if (totalSalesContainer) {
                                                totalSalesContainer.textContent = totalSaleNo;
                                            }
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error fetching data:', error);

                                        // On error, reset charts to fixed patterns
                                        Object.keys(fixedPatterns).forEach(metric => {
                                            updateChart(metric, fixedPatterns[metric][fixedPatterns[metric].length - 1]);
                                        });
                                    });
                            }

                            // Initialize charts when the page loads
                            document.addEventListener('DOMContentLoaded', function () {
                                // Generate initial visualization
                                generateInitialData();

                                // Update with real data immediately
                                updateChartsWithRealData();

                                // Set interval to update charts periodically
                                setInterval(updateChartsWithRealData, 60000); // Update every minute
                            });
                        </script>
                    </div>
                    <!-- Total Sales Card -->
                    <div class="card relative overflow-hidden rounded-lg border ground shadow-sm" data-v0-t="card"
                        data-metric="sales">
                        <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Total Sales</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-chart-spline">
                                <path d="M3 3v16a2 2 0 0 0 2 2h16" />
                                <path d="M7 16c.5-2 1.5-7 4-7 2 0 2 3 4 3 2.5 0 4.5-5 5-7" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div id="total-sales" class="text-2xl font-bold">0</div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Total Lifetime Sales</p>
                            <div
                                class="absolute bottom-7 right-4 flex items-center text-xs text-red-600 border border-red-500  px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-arrow-down-narrow-wide mr-0.5">
                                    <path d="m3 16 4 4 4-4" />
                                    <path d="M7 20V4" />
                                    <path d="M11 4h4" />
                                    <path d="M11 8h7" />
                                    <path d="M11 12h10" />
                                </svg>
                                3.2%
                            </div>
                        </div>
                        <!-- Gradient Chart Overlay with Smoother Curve -->
                        <div class="absolute bottom-6 right-7 w-28 h-20">
                        </div>
                    </div>
                    <!-- Total Tips Card -->
                    <div class="card relative overflow-hidden rounded-lg border ground shadow-sm" data-v0-t="card"
                        data-metric="tips">
                        <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Total tips</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-notepad-text">
                                <path d="M8 2v4" />
                                <path d="M12 2v4" />
                                <path d="M16 2v4" />
                                <rect width="16" height="18" x="4" y="4" rx="2" />
                                <path d="M8 10h6" />
                                <path d="M8 14h8" />
                                <path d="M8 18h5" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div id="total-tips" class="text-2xl font-bold"></div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Total lifetime Tips</p>
                            <div
                                class="absolute bottom-7 right-4 flex items-center text-xs text-red-600 border border-red-500  px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-arrow-down-narrow-wide mr-0.5">
                                    <path d="m3 16 4 4 4-4" />
                                    <path d="M7 20V4" />
                                    <path d="M11 4h4" />
                                    <path d="M11 8h7" />
                                    <path d="M11 12h10" />
                                </svg>
                                53.2%
                            </div>
                        </div>
                        <div class="absolute bottom-6 right-7 w-28 h-20">

                        </div>
                    </div>
                    <div class="card relative overflow-hidden rounded-lg border ground shadow-sm" data-v0-t="card"
                        data-metric="reviews">
                        <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Reviews</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-scan-search">
                                <path d="M3 7V5a2 2 0 0 1 2-2h2" />
                                <path d="M17 3h2a2 2 0 0 1 2 2v2" />
                                <path d="M21 17v2a2 2 0 0 1-2 2h-2" />
                                <path d="M7 21H5a2 2 0 0 1-2-2v-2" />
                                <circle cx="12" cy="12" r="3" />
                                <path d="m16 16-1.9-1.9" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div id="total-customers" class="text-2xl font-bold">+0</div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Total Lifetime Reviews</p>
                            <div
                                class="absolute bottom-7 right-4 flex items-center text-xs text-green-600 border border-green-500 bg-transparent px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-arrow-up-narrow-wide mr-0.5">
                                    <path d="m3 8 4-4 4 4" />
                                    <path d="M7 4v16" />
                                    <path d="M11 12h4" />
                                    <path d="M11 16h7" />
                                    <path d="M11 20h10" />
                                </svg>
                                8.5%
                            </div>
                        </div>
                        <div class="absolute bottom-6 right-7 w-28 h-20">

                        </div>
                    </div>
                </div>
                <script>
                    document.addEventListener('DOMContentLoaded', function () {
                        const salesChartCanvas = document.getElementById('salesOverviewChart');
                        const worldMapDiv = document.getElementById('chartdiv');
                        const showBarChartLink = document.getElementById('show-bar-chart');
                        const showWorldMapLink = document.getElementById('show-world-map');

                        showBarChartLink.addEventListener('click', function (e) {
                            e.preventDefault();
                            salesChartCanvas.style.display = 'block';
                            worldMapDiv.style.display = 'none';
                        });

                        showWorldMapLink.addEventListener('click', function (e) {
                            e.preventDefault();
                            salesChartCanvas.style.display = 'none';
                            worldMapDiv.style.display = 'flex'; // Changed to flex for the layout
                        });

                        am5.ready(function () {
                            // Create root element
                            var root = am5.Root.new("mapdiv");

                            // Disable amCharts logo
                            root._logo.dispose();

                            // Set themes
                            root.setThemes([am5themes_Animated.new(root)]);

                            // Create the map chart
                            var chart = root.container.children.push(
                                am5map.MapChart.new(root, {
                                    panX: "none",
                                    panY: "none",
                                    projection: am5map.geoMercator(),
                                    wheelable: false,
                                    wheelX: "none",
                                    wheelY: "none",
                                    pinchZoom: false,
                                    maxZoomLevel: 1,
                                    minZoomLevel: 1,
                                    maxPanOut: 0,
                                    draggable: false,
                                    centerMapOnZoomOut: true
                                })
                            );

                            // Disable zoom control completely
                            chart.set("zoomControl", false);

                            var polygonSeries = chart.series.push(
                                am5map.MapPolygonSeries.new(root, {
                                    geoJSON: am5geodata_worldLow,
                                    exclude: ["AQ"]
                                })
                            );

                            polygonSeries.mapPolygons.template.setAll({
                                fill: am5.color(0xd0d1d5) // Lighter gray color for better visibility
                            });

                            var pointSeries = chart.series.push(am5map.ClusteredPointSeries.new(root, {}));
                            pointSeries.set("clusteredBullet", function (root) {
                                var container = am5.Container.new(root, {
                                    cursorOverStyle: "pointer"
                                });
                                var circle1 = container.children.push(am5.Circle.new(root, {
                                    radius: 8,
                                    tooltipY: 0,
                                    fill: am5.color(0x1e40af)  // Blue color
                                }));
                                var circle2 = container.children.push(am5.Circle.new(root, {
                                    radius: 12,
                                    fillOpacity: 0.3,
                                    tooltipY: 0,
                                    fill: am5.color(0x1e40af)  // Blue color
                                }));
                                var circle3 = container.children.push(am5.Circle.new(root, {
                                    radius: 16,
                                    fillOpacity: 0.3,
                                    tooltipY: 0,
                                    fill: am5.color(0x1e40af)  // Blue color
                                }));
                                var label = container.children.push(am5.Label.new(root, {
                                    centerX: am5.p50,
                                    centerY: am5.p50,
                                    fill: am5.color(0xffffff),
                                    populateText: true,
                                    fontSize: "8",
                                    text: "{value}"
                                }));
                                container.events.on("click", function (e) {
                                    pointSeries.zoomToCluster(e.target.dataItem);
                                });
                                return am5.Bullet.new(root, {
                                    sprite: container
                                });
                            });

                            pointSeries.bullets.push(function () {
                                var circle = am5.Circle.new(root, {
                                    radius: 6,
                                    tooltipY: 0,
                                    fill: am5.color(0x1e40af),  // Blue color
                                    tooltipText: "{title}: {value}"
                                });
                                return am5.Bullet.new(root, {
                                    sprite: circle
                                });
                            });

                            // Language regions data with strategic geographical placement
                            var languageRegions = [
                                { title: "German", code: "de", latitude: 51.1657, longitude: 10.4515, value: 4400 },
                                { title: "French", code: "fr", latitude: 46.2276, longitude: 2.2137, value: 3600 },
                                { title: "Italian", code: "it", latitude: 41.8719, longitude: 12.5674, value: 3100 },
                                { title: "English", code: "us", latitude: 40.7128, longitude: -95.0060, value: 2900 },
                                { title: "Swedish", code: "se", latitude: 62.1282, longitude: 15.6435, value: 2700 },
                                { title: "Spanish", code: "es", latitude: 40.4637, longitude: -3.7492, value: 1200 }
                            ];

                            languageRegions.forEach(region => {
                                pointSeries.data.push({
                                    geometry: { type: "Point", coordinates: [region.longitude, region.latitude] },
                                    title: region.title,
                                    value: region.value,
                                    tooltipHTML: `<div style="display: flex; align-items: center;">
                                                    <img src="https://flagcdn.com/24x18/${region.code}.png" alt="${region.title} flag" style="margin-right: 8px;">
                                                    <span>${region.title}: ${region.value}</span>
                                                  </div>`
                                });
                            });

                            chart.appear(1000, 100);
                        });
                    });
                </script>
                <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <div class="rounded-lg shadow-sm col-span-2 min-w-[500px] h-fit card" data-v0-t="card">
                        <div class="ggchart-container border card">
                            <!-- Replace the entire chart header dropdown section with this -->
                            <!-- Replace the dropdown section with this -->
                            <div class="ggchart-header border-b card pr-0">
                                <div class="ggchart-title">
                                    <h2 class="whitespace-nowrap text-2xl font-semibold leading-none tracking-tight">
                                        Total sales</h2>
                                    <p class="text-sm text-muted-foreground mt-2">Showing total sales for the last 30 days</p>
                                </div>

                                <!-- Streamlined dropdowns container with consistent styling -->
                                <div class="flex items-center px-2">
                                    <!-- Icon-only buttons first (on the left) -->
                                    <!-- Filter dropdown (icon only) -->
                                    <div class="flex items-center py-1 px-1">
                                        <a href="javascript:void(0);" uk-toggle="target: #filter-type-dropdown"
                                            id="filterTypeToggler"
                                            class="border card flex items-center justify-center universal-hover px-2 py-2 rounded-md"
                                            style="background-color: transparent;" aria-label="Filter Options">
                                            <uk-icon icon="sliders"></uk-icon>
                                            <span class="sr-only">Filter</span>
                                        </a>
                                        <!-- Dropdown Menu -->
                                        <div class="card dropdown-content" id="filter-type-dropdown"
                                            uk-dropdown="mode: click; pos: bottom-right; offset: 10">
                                            <ul class="uk-nav uk-dropdown-nav card">
                                                <li><a href="#" class="filter-type-option"
                                                        data-filter-type="category">Category</a></li>
                                                <li><a href="#" class="filter-type-option"
                                                        data-filter-type="platform">Platform</a></li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="flex items-center py-1 px-1">
                                        <a href="javascript:void(0);" uk-toggle="target: #date-dropdown"
                                            id="dateToggler"
                                            class="border card flex items-center justify-center universal-hover px-2 py-2 rounded-md"
                                            style="background-color: transparent;" aria-label="Select Date">
                                            <uk-icon icon="calendar"></uk-icon>
                                            <span class="sr-only">Date</span>
                                            <span id="selectedDateDisplay"
                                                class="hidden md:inline-block ml-4 text-sm">Today</span>
                                        </a>
                                        <!-- Date Dropdown Content remains the same -->
                                        <div class="card dropdown-content" id="date-dropdown"
                                            uk-dropdown="mode: click; pos: bottom-right; offset: 10; boundary: !.flex; flip: false">
                                            <!-- Custom Tailwind Calendar -->
                                            <div id="sales-calendar" class="w-64 text-foreground p-3">
                                                <div class="flex items-center justify-between mb-2">
                                                    <button id="sales-prev-month"
                                                        class="p-1 rounded hover:bg-muted focus:outline-none focus:ring-2 focus:ring-ring">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round">
                                                            <path d="m15 18-6-6 6-6" />
                                                        </svg>
                                                    </button>
                                                    <div id="sales-month-year" class="font-semibold text-sm"></div>
                                                    <button id="sales-next-month"
                                                        class="p-1 rounded hover:bg-muted focus:outline-none focus:ring-2 focus:ring-ring">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round">
                                                            <path d="m9 18 6-6-6-6" />
                                                        </svg>
                                                    </button>
                                                </div>
                                                <div
                                                    class="grid grid-cols-7 gap-1 text-center text-xs text-muted-foreground mb-1">
                                                    <div>Su</div>
                                                    <div>Mo</div>
                                                    <div>Tu</div>
                                                    <div>We</div>
                                                    <div>Th</div>
                                                    <div>Fr</div>
                                                    <div>Sa</div>
                                                </div>
                                                <div id="sales-calendar-grid" class="grid grid-cols-7 gap-1">
                                                    <!-- Dates will be populated by script -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Data source dropdown (full text) on the right -->
                                    <div class="flex items-center py-1 px-1 ml-auto">
                                        <a href="javascript:void(0);" uk-toggle="target: #data-source-dropdown"
                                            id="dataSourceToggler"
                                            class="border card flex items-center justify-center universal-hover px-3 py-2 rounded-md"
                                            style="background-color: transparent; min-width: 140px;"
                                            aria-label="Select Data Source">
                                            <span id="selectedDataSource"
                                                style="font-size: 14px; white-space: nowrap; margin-right: 8px;">
                                                Guest Genius
                                            </span>
                                            <uk-icon icon="chevrons-up-down"></uk-icon>
                                        </a>
                                        <!-- Dropdown Menu -->
                                        <div class="card dropdown-content" id="data-source-dropdown"
                                            uk-dropdown="mode: click; pos: bottom-right; offset: 10">
                                            <ul class="uk-nav uk-dropdown-nav card">
                                                <li><a href="#" class="data-source-option"
                                                        data-source="guestgenius">Guest
                                                        Genius</a></li>
                                                <li><a href="#" class="data-source-option" data-source="hostify">PMS
                                                        (hostify)</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ggchart-body" id="chart">
                                <div class="ggchart-tooltip">
                                    <div class="ggchart-tooltip-date"></div>
                                    <div class="ggchart-tooltip-value"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Empty container for 1/3 space on the right -->
                    <div class="hidden lg:block h-full col-span-1 card border rounded-lg ml-[10px] -mr-[10px] flex flex-col">
                        <div style="margin-top: 94px;">
                            <hr class="card">
                        </div>
                        <!-- Content can go here -->
                    </div>
                </div>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/7.8.5/d3.min.js"></script>
                <script>
                    // Data Generation and Chart Setup
                    function generateData(type) {
                        const data = [];
                        const endDate = new Date();
                        const startDate = new Date();
                        startDate.setDate(endDate.getDate() - 29);

                        for (let i = 0; i < 30; i++) {
                            const date = new Date(startDate);
                            date.setDate(startDate.getDate() + i);
                            let value = 200 + Math.random() * 150;
                            if (type === 'mobile') {
                                value = 220 + Math.random() * 180;
                            }
                            value += Math.sin(i / 7) * (type === 'mobile' ? 70 : 50);
                            value += Math.sin(i / 30) * (type === 'mobile' ? 100 : 80);
                            if (i % 12 === 0) {
                                value += Math.random() * (type === 'mobile' ? 120 : 100);
                            }
                            data.push({
                                date: date,
                                value: Math.round(value)
                            });
                        }
                        return data;
                    }

                    const desktopData = generateData('desktop');
                    const mobileData = generateData('mobile');
                    let currentData = desktopData;

                    const container = document.getElementById('chart');
                    const margin = { top: 20, right: 10, bottom: 80, left: 10 };
                    const width = container.clientWidth - margin.left - margin.right;
                    const height = container.clientHeight - margin.top - margin.bottom;

                    const svg = d3.select('#chart')
                        .append('svg')
                        .attr('width', '100%')
                        .attr('height', height + margin.top + margin.bottom)
                        .attr('viewBox', `0 0 ${width + margin.left + margin.right} ${height + margin.top + margin.bottom}`)
                        .attr('preserveAspectRatio', 'none')  // Updated here
                        .append('g')
                        .attr('transform', `translate(${margin.left},${margin.top})`);

                    // Create a fixed time domain for x-axis with evenly spaced days
                    const endDate = new Date();
                    const startDate = new Date();
                    startDate.setDate(endDate.getDate() - 29);

                    // Create fixed x-axis with consistent date intervals
                    const x = d3.scaleTime()
                        .domain([startDate, endDate])
                        .range([0, width]);

                    // Generate fixed tick values for more days to show all 30 days
                    const xTickValues = [];
                    for (let i = 0; i <= 29; i += 1) {
                        const date = new Date(startDate);
                        date.setDate(startDate.getDate() + i);
                        xTickValues.push(date);
                    }


                    const y = d3.scaleLinear()
                        .domain([0, d3.max(currentData, d => d.value) * 1.1])
                        .range([height, 0]);

                    const line = d3.line()
                        .x(d => x(d.date))
                        .y(d => y(d.value))
                        .curve(d3.curveMonotoneX);

                    // Create a fixed x-axis with all 30 days but formatted labels
                    svg.append('g')
                        .attr('class', 'axis')
                        .attr('transform', `translate(0,${height})`)
                        .call(d3.axisBottom(x)
                            .tickValues(xTickValues)
                            .tickFormat((d, i) => {
                                // Only show labels for every 3rd day to avoid crowding
                                if (i % 1 === 0) {
                                    return d3.timeFormat('%d')(d);
                                }
                                return '';
                            })
                            .tickSizeOuter(0)
                        )

                    svg.append('g')
                        .attr('class', 'axis')
                        .call(d3.axisLeft(y)
                            .ticks(5)
                            .tickSize(-width)
                            .tickFormat('')
                        )
                        .call(g => g.select('.domain').remove());

                    svg.append('path')
                        .datum(currentData)
                        .attr('class', 'ggchart-line')
                        .attr('stroke', '#16a34a') // Default green color
                        .attr('d', line);

                    const hoverLine = svg.append('line')
                        .attr('class', 'ggchart-hover-line')
                        .attr('y1', 0)
                        .attr('y2', height);

                    const dots = svg.selectAll('.ggchart-dot')
                        .data(currentData)
                        .enter()
                        .append('circle')
                        .attr('class', 'ggchart-dot')
                        .attr('cx', d => x(d.date))
                        .attr('cy', d => y(d.value))
                        .attr('r', 6)
                        .attr('fill', '#16a34a'); // Default green color

                    const tooltip = d3.select('.ggchart-tooltip');

                    svg.append('rect')
                        .attr('class', 'ggchart-overlay')
                        .attr('width', width)
                        .attr('height', height)
                        .on('mousemove', mousemove)
                        .on('mouseout', mouseout);

                    function mousemove(event) {
                        const [mouseX, mouseY] = d3.pointer(event);
                        const bisectDate = d3.bisector(d => d.date).left;

                        const x0 = x.invert(mouseX);
                        const i = bisectDate(currentData, x0, 1);
                        const d0 = currentData[i - 1];
                        const d1 = currentData[i];
                        // Select the closer data point
                        const d = x0 - d0.date > d1.date - x0 ? d1 : d0;
                        const xPos = x(d.date);
                        const yPos = y(d.value);
                        updateTooltipAndHighlights(d, xPos, yPos, mouseY);
                    }

                    function updateTooltipAndHighlights(d, xPos, yPos) {
                        // Set position of the hover line to exactly match the data point
                        hoverLine
                            .attr('transform', `translate(${xPos},0)`)
                            .style('opacity', 1);

                        // Hide all dots first
                        dots.style('opacity', 0);

                        // Show only the selected dot
                        svg.selectAll('.ggchart-dot')
                            .filter(dt => dt.date.getTime() === d.date.getTime())
                            .style('opacity', 1);

                        // Position the tooltip with clear offset from the point
                        const tooltipWidth = tooltip.node().offsetWidth;
                        const containerWidth = container.clientWidth;

                        // Calculate ideal position (right of the point by default)
                        let leftPos = xPos + margin.left + 15;

                        // If tooltip would go off the right edge, place it to the left of the point
                        if (leftPos + tooltipWidth > containerWidth - 10) {
                            leftPos = xPos + margin.left - tooltipWidth - 15;
                        }

                        // Ensure tooltip stays within container bounds
                        leftPos = Math.max(10, Math.min(containerWidth - tooltipWidth - 10, leftPos));

                        // Position tooltip and update content
                        tooltip
                            .style('opacity', 1)
                            .style('left', `${leftPos}px`)
                            .style('top', `${margin.top + 10}px`);

                        const formatDate = d3.timeFormat('%b %d, %Y');
                        tooltip.select('.ggchart-tooltip-date').text(formatDate(d.date));
                        tooltip.select('.ggchart-tooltip-value').text(d.value.toLocaleString());
                    }


                    function mouseout() {
                        hoverLine.style('opacity', 0);
                        dots.style('opacity', 0);
                        tooltip.style('opacity', 0);
                    }

                    function updateChart(newData, color) {
                        console.log("[DEBUG] updateChart called with color:", color, "and data:", newData);

                        currentData = newData;
                        y.domain([0, d3.max(newData, d => d.value) * 1.1]);

                        svg.select('.ggchart-line')
                            .datum(newData)
                            .attr('stroke', color)
                            .transition()
                            .duration(300)
                            .attr('d', line);

                        const dots = svg.selectAll('.ggchart-dot')
                            .data(newData);

                        dots.exit().remove();

                        dots.enter()
                            .append('circle')
                            .attr('class', 'ggchart-dot')
                            .attr('r', 6)
                            .merge(dots)
                            .attr('cx', d => x(d.date))
                            .attr('cy', d => y(d.value))
                            .attr('fill', color);

                        console.log("[DEBUG] Chart updated successfully.");
                    }

                    document.querySelectorAll('.ggchart-stat-box').forEach(box => {
                        box.addEventListener('click', function () {
                            console.log("[DEBUG] Stat box clicked:", this);

                            document.querySelectorAll('.ggchart-stat-box').forEach(b => b.classList.remove('active'));
                            this.classList.add('active');

                            const type = this.querySelector('h3').textContent.toLowerCase();
                            console.log("[DEBUG] Detected type:", type);

                            if (type.includes('pms')) {
                                console.log("[DEBUG] Switching to PMS data.");
                                updateChart(mobileData, '#2662d9'); // PMS data and blue color
                            } else {
                                console.log("[DEBUG] Switching to Guest Genius data.");
                                updateChart(desktopData, '#16a34a'); // Guest Genius data and green color
                            }
                        });
                    });

                    // Set default chart to Guest Genius
                    document.querySelector('.ggchart-stat-box').classList.add('active');
                    updateChart(desktopData, '#16a34a');

                    window.addEventListener('load', function () {
                        setTimeout(() => {
                            const firstPoint = currentData[0];
                            console.log("[DEBUG] First point on load:", firstPoint);
                            const xPosition = x(firstPoint.date);
                            const yPosition = y(firstPoint.value);
                            updateTooltipAndHighlights(firstPoint, xPosition, yPosition);
                            setTimeout(() => {
                                console.log("[DEBUG] Initial tooltip highlight shown");
                            }, 2000);
                        }, 500);
                    });
                    function debounce(func, wait) {
                        let timeout;
                        return function executedFunction(...args) {
                            const later = () => {
                                clearTimeout(timeout);
                                func(...args);
                            };
                            clearTimeout(timeout);
                            timeout = setTimeout(later, wait);
                        };
                    }
                </script>

                <div class="grid gap-3 md:grid-cols-2 lg:grid-cols-2" style="margin-top: 24px;">
                    <!-- First KPI Card Section - Categories -->
                    <div class="w-full p-6 rounded-lg border ground shadow-sm card" style="margin-bottom: 24px;">
                        <!-- Header Section with Export Button -->
                        <div class="mb-4 flex justify-between items-start">
                            <div>
                                <h3 class="text-lg font-medium ">Total sales by category</h3>
                                <p class="text-xl font-semibold">€292,400</p>
                            </div>
                            <button class="uk-button border card default" type="button" aria-haspopup="true"
                                style="display: flex; align-items: center; gap: 8px; padding: 8px 12px; min-width: 110px; justify-content: center;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" style="margin-left: -6px;"
                                    height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-download">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                    <polyline points="7 10 12 15 17 10" />
                                    <line x1="12" x2="12" y1="15" y2="3" />
                                </svg>
                                <span>Export</span>
                            </button>
                        </div>

                        <!-- Distribution Section -->
                        <div class="mt-5">
                            <h4 class="text-sm text-gray-500 mb-3">Sales by category</h4>
                            <!-- Progress Bar -->
                            <div class="mt-3 mb-6 flex h-1.5 w-full rounded-full bg-gray-100 overflow-hidden">
                                <div class="w-[35%] bg-emerald-500"></div>
                                <div class="w-[25%] bg-amber-500"></div>
                                <div class="w-[20%] bg-indigo-500"></div>
                                <div class="w-[15%] bg-rose-500"></div>
                                <div class="w-[8%] bg-cyan-500"></div>
                                <div class="w-[2%] bg-gray-500"></div>
                            </div>
                        </div>

                        <!-- Channel Cards Grid for Categories -->
                        <dl class="mt-0 mb-0 grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
                            <!-- Food Card with enhanced hover and icon - Fixed padding -->
                            <div
                                class="group relative  p-3 rounded-md border card hover:border-gray-300 hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 main-custom-hover-effect">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="w-2.5 h-2.5 rounded-sm bg-emerald-500 group-hover:scale-110 transition-transform"></span>
                                        <dt class="text-sm ">
                                            <a href="#" class="focus:outline-none">
                                                <span class="absolute inset-0" aria-hidden="true"></span>
                                                Food
                                            </a>
                                        </dt>
                                    </div>
                                    <div class="flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="w-6 h-6 text-emerald-500 group-hover:scale-110 transition-transform border card rounded p-0.5">
                                            <path d="M12 3V2" />
                                            <path
                                                d="m15.4 17.4 3.2-2.8a2 2 0 1 1 2.8 2.9l-3.6 3.3c-.7.8-1.7 1.2-2.8 1.2h-4c-1.1 0-2.1-.4-2.8-1.2l-1.302-1.464A1 1 0 0 0 6.151 19H5" />
                                            <path d="M2 14h12a2 2 0 0 1 0 4h-2" />
                                            <path d="M4 10h16" />
                                            <path d="M5 10a7 7 0 0 1 14 0" />
                                            <path d="M5 14v6a1 1 0 0 1-1 1H2" />
                                        </svg>
                                    </div>
                                </div>
                                <dd class="mt-1 text-sm text-gray-700 flex justify-between items-center">
                                    <span class="text-emerald-600">€102.3K</span>
                                    <span class="font-semibold text-emerald-600 transition-colors">35%</span>
                                </dd>
                                <i
                                    class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-emerald-500 transition-all duration-300 group-hover:translate-x-1 group-hover:-translate-y-1"></i>
                            </div>

                            <!-- Beverages Card with enhanced hover and icon -->
                            <div
                                class="group relative  p-3 rounded-md border card hover:border-gray-300 hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 main-custom-hover-effect">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="w-2.5 h-2.5 rounded-sm bg-amber-500 group-hover:scale-110 transition-transform"></span>
                                        <dt class="text-sm ">
                                            <a href="#" class="focus:outline-none">
                                                <span class="absolute inset-0" aria-hidden="true"></span>
                                                Beverages
                                            </a>
                                        </dt>
                                    </div>
                                    <div class="flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="w-6 h-6 text-amber-500 group-hover:scale-110 transition-transform border card rounded p-0.5">
                                            <path d="M9 6 6.6 2.8C6.3 2.4 5.6 2 5 2H2" />
                                            <path d="m18 6-7 8-7-8Z" />
                                            <path d="M15.4 9.1A4 4 0 1 0 14 6" />
                                            <path d="M11 14v8" />
                                            <path d="M7 22h8" />
                                        </svg>
                                    </div>
                                </div>
                                <dd class="mt-1 text-sm text-gray-700 flex justify-between items-center">
                                    <span class="text-amber-600">€73.1K</span>
                                    <span class="font-semibold text-amber-600 transition-colors">25%</span>
                                </dd>
                                <i
                                    class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-amber-500 transition-all duration-300 group-hover:translate-x-1 group-hover:-translate-y-1"></i>
                            </div>

                            <!-- Spa Card with enhanced hover and icon -->
                            <div
                                class="group relative  p-3 rounded-md border card hover:border-gray-300 hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 main-custom-hover-effect">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="w-2.5 h-2.5 rounded-sm bg-indigo-500 group-hover:scale-110 transition-transform"></span>
                                        <dt class="text-sm ">
                                            <a href="#" class="focus:outline-none">
                                                <span class="absolute inset-0" aria-hidden="true"></span>
                                                Spa
                                            </a>
                                        </dt>
                                    </div>
                                    <div class="flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="w-6 h-6 text-indigo-500 group-hover:scale-110 transition-transform border card rounded p-0.5">
                                            <path
                                                d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                                            <path d="M20 3v4" />
                                            <path d="M22 5h-4" />
                                            <path d="M4 17v2" />
                                            <path d="M5 18H3" />
                                        </svg>
                                    </div>
                                </div>
                                <dd class="mt-1 text-sm text-gray-700 flex justify-between items-center">
                                    <span class="text-indigo-600">€43.9K</span>
                                    <span class="font-semibold text-indigo-600 transition-colors">15%</span>
                                </dd>
                                <i
                                    class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-indigo-500 transition-all duration-300 group-hover:translate-x-1 group-hover:-translate-y-1"></i>
                            </div>

                            <!-- Massage Card with enhanced hover and icon -->
                            <div
                                class="group relative  p-3 rounded-md border card hover:border-gray-300 hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 main-custom-hover-effect">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="w-2.5 h-2.5 rounded-sm bg-rose-500 group-hover:scale-110 transition-transform"></span>
                                        <dt class="text-sm ">
                                            <a href="#" class="focus:outline-none">
                                                <span class="absolute inset-0" aria-hidden="true"></span>
                                                Massage
                                            </a>
                                        </dt>
                                    </div>
                                    <div class="flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="w-6 h-6 text-rose-500 group-hover:scale-110 transition-transform border card rounded p-0.5">
                                            <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                                            <circle cx="12" cy="7" r="4" />
                                        </svg>
                                    </div>
                                </div>
                                <dd class="mt-1 text-sm text-gray-700 flex justify-between items-center">
                                    <span class="text-rose-600">€43.9K</span>
                                    <span class="font-semibold text-rose-600 transition-colors">15%</span>
                                </dd>
                                <i
                                    class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-rose-500 transition-all duration-300 group-hover:translate-x-1 group-hover:-translate-y-1"></i>
                            </div>
                            <!-- Room Bookings Card with enhanced hover and icon -->
                            <div
                                class="group relative  p-3 rounded-md border card hover:border-gray-300 hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 main-custom-hover-effect">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="w-2.5 h-2.5 rounded-sm bg-cyan-500 group-hover:scale-110 transition-transform"></span>
                                        <dt class="text-sm ">
                                            <a href="#" class="focus:outline-none">
                                                <span class="absolute inset-0" aria-hidden="true"></span>
                                                Room Bookings
                                            </a>
                                        </dt>
                                    </div>
                                    <div class="flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="w-6 h-6 text-cyan-500 group-hover:scale-110 transition-transform border card rounded p-0.5">
                                            <path d="M2 20v-8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v8" />
                                            <path d="M4 10V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v4" />
                                            <path d="M12 4v6" />
                                            <path d="M2 18h20" />
                                        </svg>
                                    </div>
                                </div>
                                <dd class="mt-1 text-sm text-gray-700 flex justify-between items-center">
                                    <span class="text-cyan-600">€23.4K</span>
                                    <span class="font-semibold text-cyan-600 transition-colors">8%</span>
                                </dd>
                                <i
                                    class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-cyan-500 transition-all duration-300 group-hover:translate-x-1 group-hover:-translate-y-1"></i>
                            </div>

                            <!-- Others Card with enhanced hover and icon -->
                            <div
                                class="group relative  p-3 rounded-md border card hover:border-gray-300 hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 main-custom-hover-effect">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="w-2.5 h-2.5 rounded-sm bg-gray-500 group-hover:scale-110 transition-transform"></span>
                                        <dt class="text-sm ">
                                            <a href="#" class="focus:outline-none">
                                                <span class="absolute inset-0" aria-hidden="true"></span>
                                                Others
                                            </a>
                                        </dt>
                                    </div>
                                    <div class="flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="w-6 h-6 text-gray-500 group-hover:scale-110 transition-transform border card rounded p-0.5">
                                            <path
                                                d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z" />
                                            <path d="M8 12h4" />
                                            <path d="M10 16V9.5a2.5 2.5 0 0 1 5 0" />
                                            <path d="M8 16h7" />
                                        </svg>
                                    </div>
                                </div>
                                <dd class="mt-1 text-sm text-gray-700 flex justify-between items-center">
                                    <span class="text-gray-600">€5.8K</span>
                                    <span class="font-semibold text-gray-600 transition-colors">2%</span>
                                </dd>
                                <i
                                    class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-gray-500 transition-all duration-300 group-hover:translate-x-1 group-hover:-translate-y-1"></i>
                            </div>
                        </dl>
                    </div>

                    <!-- Second KPI Card Section - Platforms -->
                    <div class="w-full p-6 rounded-lg border ground shadow-sm card" style="margin-bottom: 24px;">
                        <!-- Header Section with Export Button -->
                        <div class="mb-4 flex justify-between items-start">
                            <div>
                                <h3 class="text-lg font-medium ">Total sales by platforms</h3>
                                <p class="text-xl font-semibold">€19,219</p>
                            </div>
                            <button class="uk-button border card default" type="button" aria-haspopup="true"
                                style="display: flex; align-items: center; gap: 8px; padding: 8px 12px; min-width: 110px; justify-content: center;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" style="margin-left: -6px;"
                                    height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-download">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                    <polyline points="7 10 12 15 17 10" />
                                    <line x1="12" x2="12" y1="15" y2="3" />
                                </svg>
                                <span>Export</span>
                            </button>
                        </div>

                        <!-- Distribution Section -->
                        <div class="mt-5">
                            <h4 class="text-sm text-gray-500 mb-3">Sales by platform</h4>
                            <!-- Progress Bar -->
                            <div class="mt-3 mb-6 flex h-1.5 w-full rounded-full bg-gray-100 overflow-hidden">
                                <div class="w-[42%] bg-green-600"></div>
                                <div class="w-[25%] bg-blue-500"></div>
                                <div class="w-[15%] bg-pink-500"></div>
                                <div class="w-[10%] bg-purple-500"></div>
                                <div class="w-[5%] bg-yellow-500"></div>
                                <div class="w-[3%] bg-slate-500"></div>
                            </div>
                        </div>

                        <!-- Channel Cards Grid for Platforms - Adjusted sizing and alignment -->
                        <dl class="mt-0 mb-0 grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
                            <!-- Whatsapp Card - Updated size and alignment -->
                            <div
                                class="group relative  p-3 rounded-md border card hover:border-gray-300 hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 main-custom-hover-effect">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="w-2.5 h-2.5 rounded-sm bg-green-600 group-hover:scale-110 transition-transform"></span>
                                        <dt class="text-sm ">
                                            <a href="#" class="focus:outline-none">
                                                <span class="absolute inset-0" aria-hidden="true"></span>
                                                Whatsapp
                                            </a>
                                        </dt>
                                    </div>
                                    <div class="flex items-center justify-center">
                                        <img src="../static/icons/whatsapp2.png"
                                            class="w-6 h-6 text-green-600 group-hover:scale-110 transition-transform border card rounded p-0.5" />
                                    </div>
                                </div>
                                <dd class="mt-1 text-sm text-gray-700 flex justify-between items-center">
                                    <span class="text-green-600">€122.8K</span>
                                    <span class="font-semibold text-green-600 transition-colors">42%</span>
                                </dd>
                                <i
                                    class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-green-500 transition-all duration-300 group-hover:translate-x-1 group-hover:-translate-y-1"></i>
                            </div>

                            <!-- Messenger Card - Updated size and alignment -->
                            <div
                                class="group relative  p-3 rounded-md border card hover:border-gray-300 hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 main-custom-hover-effect">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="w-2.5 h-2.5 rounded-sm bg-blue-500 group-hover:scale-110 transition-transform"></span>
                                        <dt class="text-sm ">
                                            <a href="#" class="focus:outline-none">
                                                <span class="absolute inset-0" aria-hidden="true"></span>
                                                Messenger
                                            </a>
                                        </dt>
                                    </div>
                                    <div class="flex items-center justify-center">
                                        <img src="../static/icons/messenger.png"
                                            class="w-6 h-6 text-blue-500 group-hover:scale-110 transition-transform border card rounded p-0.5" />
                                    </div>
                                </div>
                                <dd class="mt-1 text-sm text-gray-700 flex justify-between items-center">
                                    <span class="text-blue-600">€73.1K</span>
                                    <span class="font-semibold text-blue-600 transition-colors">25%</< /dd>
                                        <i
                                            class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-blue-500 transition-all duration-300 group-hover:translate-x-1 group-hover:-translate-y-1"></i>
                            </div>

                            <!-- Instagram Card - Updated size and alignment -->
                            <div
                                class="group relative  p-3 rounded-md border card hover:border-gray-300 hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 main-custom-hover-effect">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="w-2.5 h-2.5 rounded-sm bg-pink-500 group-hover:scale-110 transition-transform"></span>
                                        <dt class="text-sm ">
                                            <a href="#" class="focus:outline-none">
                                                <span class="absolute inset-0" aria-hidden="true"></span>
                                                Instagram
                                            </a>
                                        </dt>
                                    </div>
                                    <div class="flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="w-6 h-6 text-pink-500 group-hover:scale-110 transition-transform border card rounded p-0.5">
                                            <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                                            <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                                            <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                                        </svg>
                                    </div>
                                </div>
                                <dd class="mt-1 text-sm text-gray-700 flex justify-between items-center">
                                    <span class="text-pink-600">€43.9K</span>
                                    <span class="font-semibold text-pink-600 transition-colors">15%</span>
                                </dd>
                                <i
                                    class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-pink-500 transition-all duration-300 group-hover:translate-x-1 group-hover:-translate-y-1"></i>
                            </div>

                            <!-- Voice Bot Card - Updated size and alignment -->
                            <div
                                class="group relative  p-3 rounded-md border card hover:border-gray-300 hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 main-custom-hover-effect">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="w-2.5 h-2.5 rounded-sm bg-purple-500 group-hover:scale-110 transition-transform"></span>
                                        <dt class="text-sm ">
                                            <a href="#" class="focus:outline-none">
                                                <span class="absolute inset-0" aria-hidden="true"></span>
                                                Voice Bot
                                            </a>
                                        </dt>
                                    </div>
                                    <div class="flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="w-6 h-6 text-purple-500 group-hover:scale-110 transition-transform border card rounded p-0.5">
                                            <path d="M2 10v3" />
                                            <path d="M6 6v11" />
                                            <path d="M10 3v18" />
                                            <path d="M14 8v7" />
                                            <path d="M18 5v13" />
                                            <path d="M22 10v3" />
                                        </svg>
                                    </div>
                                </div>
                                <dd class="mt-1 text-sm text-gray-700 flex justify-between items-center">
                                    <span class="text-purple-600">€29.2K</span>
                                    <span class="font-semibold text-purple-600 transition-colors">10%</span>
                                </dd>
                                <i
                                    class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-purple-500 transition-all duration-300 group-hover:translate-x-1 group-hover:-translate-y-1"></i>
                            </div>

                            <!-- SMS Card - Updated size and alignment -->
                            <div
                                class="group relative  p-3 rounded-md border card hover:border-gray-300 hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 main-custom-hover-effect">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="w-2.5 h-2.5 rounded-sm bg-yellow-500 group-hover:scale-110 transition-transform"></span>
                                        <dt class="text-sm ">
                                            <a href="#" class="focus:outline-none">
                                                <span class="absolute inset-0" aria-hidden="true"></span>
                                                SMS
                                            </a>
                                        </dt>
                                    </div>
                                    <div class="flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="w-6 h-6 text-yellow-500 group-hover:scale-110 transition-transform border card rounded p-0.5">
                                            <path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2z" />
                                            <path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1" />
                                        </svg>
                                    </div>
                                </div>
                                <dd class="mt-1 text-sm text-gray-700 flex justify-between items-center">
                                    <span class="text-yellow-600">€14.6K</span>
                                    <span class="font-semibold text-yellow-600 transition-colors">5%</span>
                                </dd>
                                <i
                                    class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-yellow-500 transition-all duration-300 group-hover:translate-x-1 group-hover:-translate-y-1"></i>
                            </div>

                            <!-- PMS & Others Card - Updated size and alignment -->
                            <div
                                class="group relative  p-3 rounded-md border card hover:border-gray-300 hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 main-custom-hover-effect">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span
                                            class="w-2.5 h-2.5 rounded-sm bg-slate-500 group-hover:scale-110 transition-transform"></span>
                                        <dt class="text-sm ">
                                            <a href="#" class="focus:outline-none">
                                                <span class="absolute inset-0" aria-hidden="true"></span>
                                                PMS and Others
                                            </a>
                                        </dt>
                                    </div>
                                    <div class="flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="w-6 h-6 text-slate-500 group-hover:scale-110 transition-transform border card rounded p-0.5">
                                            <path
                                                d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z" />
                                            <path d="M8 12h4" />
                                            <path d="M10 16V9.5a2.5 2.5 0 0 1 5 0" />
                                            <path d="M8 16h7" />
                                        </svg>
                                    </div>
                                </div>
                                <dd class="mt-1 text-sm text-gray-700 flex justify-between items-center">
                                    <span class="text-slate-600">€8.8K</span>
                                    <span class="font-semibold text-slate-600 transition-colors">3%</span>
                                </dd>
                                <i
                                    class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-slate-500 transition-all duration-300 group-hover:translate-x-1 group-hover:-translate-y-1"></i>
                            </div>
                        </dl>
                    </div>
                </div>

                <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <div class="card rounded-lg border ground shadow-sm col-span-3 order-last" data-v0-t="card">
                        <div class="flex justify-between items-start p-6">
                            <div class="flex flex-col space-y-1.5">
                                <h3 class="whitespace-nowrap text-2xl font-semibold leading-none tracking-tight">Sale
                                    History</h3>
                                <p class="text-sm text-muted-foreground">A list of all the sales and its overview.</p>
                            </div>
                            <button class="uk-button border card default mt-2" type="button" aria-haspopup="true"
                                style="display: flex; align-items: center; gap: 8px; padding: 8px 12px; min-width: 110px; justify-content: center;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" style="margin-left: -6px;"
                                    height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-download">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                    <polyline points="7 10 12 15 17 10" />
                                    <line x1="12" x2="12" y1="15" y2="3" />
                                </svg>
                                <span>Export</span>
                            </button>
                        </div>
                        <hr class="card">
                        <div class="p-6">
                            <div class="relative w-full overflow-auto table-container">
                                <table class="w-full caption-bottom text-sm">
                                    <thead>
                                        <tr class="border-b transition-colors hover:bg-muted/50 card">
                                            <th class="p-4 align-middle text-left font-medium">Guest Name</th>
                                            <th class="p-4 align-middle text-left font-medium">Room No</th>
                                            <th class="p-4 align-middle text-left font-medium">Phone/ID</th>
                                            <th class="p-4 align-middle text-left font-medium">Language</th>
                                            <th class="p-4 align-middle text-left font-medium">Platform</th>
                                            <th class="p-4 align-middle text-left font-medium">Item</th>
                                            <th class="p-4 align-middle text-right font-medium">Price</th>
                                            <th class="p-4 align-middle text-right font-medium">Time</th>
                                        </tr>
                                        <!-- Divider Row -->
                                        <tr class="card">
                                            <td colspan="8" class="p-0"> <!-- Span all columns, remove padding -->
                                                <hr class="card m-0 border-t">
                                                <!-- Apply card class, remove margin, ensure top border -->
                                            </td>
                                        </tr>
                                    </thead>
                                    <tbody id="salesTableBody" class="[&amp;_tr:last-child]:border-0">
                                        <!-- Table rows will be dynamically inserted here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        function fetchSalesList() {
            fetch('/fetch-sales-list')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    const tableBody = document.getElementById('salesTableBody');
                    tableBody.innerHTML = '';

                    // Sort by sale_id in descending order (newest first)
                    data.sort((a, b) => {
                        const idA = parseInt(a.sale_id.replace('SA-', ''));
                        const idB = parseInt(b.sale_id.replace('SA-', ''));
                        return idB - idA;
                    });

                    data.forEach(sale => {
                        const row = document.createElement('tr');
                        row.className = 'border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted card';

                        // Conditional rendering for language column
                        const languageContent = sale.guest_language.toLowerCase() === 'english'
                            ? `<img src="https://cdn-icons-png.flaticon.com/128/197/197374.png" alt="English" class="w-6 h-6 inline-block">`
                            : sale.guest_language;

                        // Conditional rendering for platform column with theme-aware class
                        const platformContent = sale.guest_platform.toLowerCase() === 'whatsapp'
                            ? `<img src="/static/icons/whatsapp2.png" alt="WhatsApp" class="w-6 h-6 inline-block whatsapp-theme-icon">`
                            : sale.guest_platform;

                        row.innerHTML = `
                        <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0 font-medium">${sale.guest_name}</td>
                        <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0"><span style="margin-left:3px" class="uk-label uk-label-primary">${sale.sale_id}</span></td>
                        <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${sale.guest_phone_or_id}</td>
                        <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${languageContent}</td>
                        <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${platformContent}</td>
                        <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${sale.sale_item}</td>
                        <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0 text-right">€${sale.sale_price}</td>
                        <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0 text-right">${sale.sale_time}</td>
                    `;

                        tableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('Error fetching sales list:', error);
                });
        }

        // Initial fetch
        fetchSalesList();

        // Refresh every minute
        setInterval(fetchSalesList, 60000);

        async function fetchSalesData() {
            try {
                const response = await fetch('/saleschart');
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('Error fetching sales data:', error);
                return null;
            }
        }

        function calculateStepSize(maxValue) {
            return Math.ceil(maxValue / 5);
        }

        async function updateChart(chart) {
            const salesData = await fetchSalesData();
            if (!salesData) return;

            const salesChart = salesData.sales_chart[0];
            const salesTips = salesData.sales_tips[0];

            const salesChartData = [
                salesChart.Mon, salesChart.Tue, salesChart.Wed, salesChart.Thu,
                salesChart.Fri, salesChart.Sat, salesChart.Sun
            ];

            const salesTipsData = [
                salesTips.Mon, salesTips.Tue, salesTips.Wed, salesTips.Thu,
                salesTips.Fri, salesTips.Sat, salesTips.Sun
            ];

            const maxDataValue = Math.max(...salesChartData, ...salesTipsData);
            const stepSize = calculateStepSize(maxDataValue);

            chart.data.datasets[0].data = salesChartData;
            chart.data.datasets[1].data = salesTipsData;
            chart.options.scales.y.ticks.stepSize = stepSize;
            chart.update();
        }

        async function initializeChart() {
            const salesData = await fetchSalesData();
            if (!salesData) return;

            const salesChart = salesData.sales_chart[0];
            const salesTips = salesData.sales_tips[0];

            const salesChartData = [
                salesChart.Mon, salesChart.Tue, salesChart.Wed, salesChart.Thu,
                salesChart.Fri, salesChart.Sat, salesChart.Sun
            ];

            const salesTipsData = [
                salesTips.Mon, salesTips.Tue, salesTips.Wed, salesTips.Thu,
                salesTips.Fri, salesTips.Sat, salesTips.Sun
            ];

            const maxDataValue = Math.max(...salesChartData, ...salesTipsData);
            const stepSize = calculateStepSize(maxDataValue);

            const newSignupsData = {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [
                    {
                        label: 'Total Sales',
                        data: salesChartData,
                        backgroundColor: '#18181b'
                    },
                    {
                        label: 'Total Tips',
                        data: salesTipsData,
                        backgroundColor: '#aabdd1'
                    }
                ]
            };

            const ctx = document.getElementById('salesOverviewChart').getContext('2d');
            const newSignupsChart = new Chart(ctx, {
                type: 'bar',
                data: newSignupsData,
                options: {
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)',
                                borderDash: [5, 5]
                            },
                            ticks: {
                                stepSize: stepSize
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(200, 200, 200, 0.3)',
                            titleColor: '#000',
                            bodyColor: '#000'
                        }
                    },
                    elements: {
                        bar: {
                            borderRadius: 8,
                            borderSkipped: false
                        }
                    }
                }
            });

            setInterval(() => {
                updateChart(newSignupsChart);
            }, 60000);
        }

        document.addEventListener('DOMContentLoaded', initializeChart);

        async function fetchData(url) {
            try {
                const response = await fetch(url);
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('Error fetching data:', error);
                return [];
            }
        }

        async function updateFirstRowSales() {
            try {
                const response = await fetch('/firstrowsales');
                const data = await response.json();
                document.getElementById('total-sales').textContent = `€${parseFloat(data[0].total_sales).toFixed(2)}`;
                document.getElementById('total-tips').textContent = `+${data[0].total_tips}`;
                document.getElementById('total-customers').textContent = `+${data[0].total_reviews}`;

                // Update the Total Sales container with total_sale_no
                const totalSalesContainer = document.querySelector('[data-metric="sales"] .text-2xl');
                if (totalSalesContainer) {
                    // Use total_sale_no if available, otherwise default to 0
                    const totalSaleNo = data[0].total_sale_no !== undefined ? data[0].total_sale_no : 0;
                    totalSalesContainer.textContent = totalSaleNo;
                }
            } catch (error) {
                console.error('Error fetching first row sales data:', error);
            }
        }

        async function init() {
            await updateFirstRowSales();
        }

        // Initial call
        init();

        // Refresh data every 5 seconds
        setInterval(init, 60000);
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const dropdownBtn = document.getElementById('sales-overview-dropdown-btn');
            const dropdownMenu = document.getElementById('sales-overview-dropdown-menu');
            const dropdownItems = dropdownMenu.querySelectorAll('.chart-dropdown-item');

            dropdownBtn.addEventListener('click', function () {
                const expanded = this.getAttribute('aria-expanded') === 'true' || false;
                this.setAttribute('aria-expanded', !expanded);
                dropdownMenu.classList.toggle('hidden');
            });

            dropdownItems.forEach(item => {
                item.addEventListener('click', function (e) {
                    e.preventDefault();
                    const selectedText = this.textContent.trim();
                    dropdownBtn.querySelector('span').textContent = selectedText;
                    dropdownMenu.classList.add('hidden');
                    dropdownBtn.setAttribute('aria-expanded', 'false');

                    // Here you would add logic to update the chart based on the selected option
                    updateChart(selectedText);
                });
            });

            // Close the dropdown when clicking outside
            document.addEventListener('click', function (e) {
                if (!dropdownBtn.contains(e.target) && !dropdownMenu.contains(e.target)) {
                    dropdownMenu.classList.add('hidden');
                    dropdownBtn.setAttribute('aria-expanded', 'false');
                }
            });
        });

        function updateChart(timeframe) {
            // Implement the logic to update the chart based on the selected timeframe
            console.log(`Updating chart to show ${timeframe} data`);
            // You would typically fetch new data or filter existing data and redraw the chart here
        }

        // Data source dropdown functionality
        document.addEventListener('DOMContentLoaded', function () {
            const dataSourceOptions = document.querySelectorAll('.data-source-option');
            const selectedDataSource = document.getElementById('selectedDataSource');
            const dataSourceDropdown = document.getElementById('data-source-dropdown');

            dataSourceOptions.forEach(option => {
                option.addEventListener('click', function (e) {
                    e.preventDefault();
                    const source = this.getAttribute('data-source');
                    selectedDataSource.textContent = this.textContent;

                    // Close the dropdown
                    if (UIkit.dropdown(dataSourceDropdown)) {
                        UIkit.dropdown(dataSourceDropdown).hide();
                    }

                    // Update chart based on selection
                    if (source === 'guestgenius') {
                        updateChart(desktopData, '#16a34a'); // Guest Genius data and green color
                    } else if (source === 'hostify') {
                        updateChart(mobileData, '#2662d9'); // PMS data and blue color
                    }
                });
            });
        });

        // Filter type dropdown functionality
        document.addEventListener('DOMContentLoaded', function () {
            const filterTypeOptions = document.querySelectorAll('.filter-type-option');
            const selectedFilterType = document.getElementById('selectedFilterType');
            const filterTypeDropdown = document.getElementById('filter-type-dropdown');

            filterTypeOptions.forEach(option => {
                option.addEventListener('click', function (e) {
                    e.preventDefault();
                    const filterType = this.getAttribute('data-filter-type');
                    selectedFilterType.textContent = this.textContent;

                    // Close the dropdown
                    if (UIkit.dropdown(filterTypeDropdown)) {
                        UIkit.dropdown(filterTypeDropdown).hide();
                    }

                    // Apply the selected filter type
                    applyFilterType(filterType);
                });
            });
        });

        // Function to handle changing the filter type
        function applyFilterType(filterType) {
            console.log(`Changing filter view to: ${filterType}`);

            // Show/hide relevant sections based on filter type
            if (filterType === 'category') {
                // Show category-based filtering options/view
                document.querySelectorAll('.category-card').forEach(el => el.style.display = 'block');
                document.querySelectorAll('.platform-card').forEach(el => el.style.display = 'none');
            } else if (filterType === 'platform') {
                // Show platform-based filtering options/view
                document.querySelectorAll('.category-card').forEach(el => el.style.display = 'none');
                document.querySelectorAll('.platform-card').forEach(el => el.style.display = 'block');
            }

            // You could also update chart data based on the filter type
        }

    </script>
    <!-- Add this script just before the closing body tag -->
    <script>
        // Sales page calendar initialization
        document.addEventListener('DOMContentLoaded', function () {
            // Calendar elements
            const calendarGrid = document.getElementById('sales-calendar-grid');
            const monthYearDisplay = document.getElementById('sales-month-year');
            const prevMonthButton = document.getElementById('sales-prev-month');
            const nextMonthButton = document.getElementById('sales-next-month');
            const selectedDateDisplay = document.getElementById('selectedDateDisplay');
            const dateDropdown = document.getElementById('date-dropdown');

            if (!calendarGrid || !monthYearDisplay || !prevMonthButton || !nextMonthButton) {
                console.error("Calendar elements not found on sales page");
                return;
            }

            let currentDate = new Date();
            let selectedDate = new Date();

            // Format date for display
            function formatDate(date) {
                return date.toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' });
            }

            // Display current date initially
            if (selectedDateDisplay) {
                selectedDateDisplay.textContent = formatDate(selectedDate);
            }

            // Render calendar function
            function renderCalendar(date) {
                if (!calendarGrid) return;

                calendarGrid.innerHTML = ''; // Clear previous grid

                const year = date.getFullYear();
                const month = date.getMonth(); // 0-indexed

                monthYearDisplay.textContent = date.toLocaleDateString(undefined, { month: 'long', year: 'numeric' });

                const firstDayOfMonth = new Date(year, month, 1);
                const lastDayOfMonth = new Date(year, month + 1, 0);
                const daysInMonth = lastDayOfMonth.getDate();
                const startDayOfWeek = firstDayOfMonth.getDay(); // 0 for Sunday, 1 for Monday, etc.

                // Add empty cells for days before the 1st of the month
                for (let i = 0; i < startDayOfWeek; i++) {
                    const emptyCell = document.createElement('div');
                    calendarGrid.appendChild(emptyCell);
                }

                // Add date cells
                for (let day = 1; day <= daysInMonth; day++) {
                    const dateCell = document.createElement('button');
                    dateCell.textContent = day;
                    dateCell.classList.add('text-sm', 'p-1', 'rounded', 'hover:bg-primary', 'hover:text-primary-foreground', 'focus:outline-none', 'focus:ring-2', 'focus:ring-ring', 'focus:z-10');

                    const cellDate = new Date(year, month, day);

                    // Highlight today's date
                    const todayDate = new Date();
                    if (cellDate.toDateString() === todayDate.toDateString()) {
                        dateCell.classList.add('bg-muted', 'font-semibold');
                    }

                    // Highlight selected date
                    if (selectedDate && cellDate.toDateString() === selectedDate.toDateString()) {
                        dateCell.classList.add('bg-primary', 'text-primary-foreground', 'font-bold');
                        dateCell.classList.remove('hover:bg-primary', 'hover:text-primary-foreground'); // Avoid double styling
                    }

                    dateCell.addEventListener('click', (e) => {
                        e.stopPropagation(); // Prevent event bubbling
                        selectedDate = cellDate;

                        // Update date display
                        if (selectedDateDisplay) {
                            selectedDateDisplay.textContent = formatDate(selectedDate);
                        }

                        // Re-render to show selection highlight
                        renderCalendar(currentDate);

                        // Close dropdown after selection
                        if (typeof UIkit !== 'undefined' && UIkit.dropdown && dateDropdown) {
                            UIkit.dropdown(dateDropdown).hide(false);
                        }

                        // Filter data based on selected date (add your data filtering logic here)
                        console.log("Sales page - Selected Date:", selectedDate);

                        // Trigger data refresh for charts and tables based on the new date
                        // refreshSalesData(selectedDate);  // You would implement this function
                    });

                    calendarGrid.appendChild(dateCell);
                }
            }

            // Navigation event listeners
            prevMonthButton.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent dropdown from closing
                currentDate.setMonth(currentDate.getMonth() - 1);
                renderCalendar(currentDate);
            });

            nextMonthButton.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent dropdown from closing
                currentDate.setMonth(currentDate.getMonth() + 1);
                renderCalendar(currentDate);
            });

            // Prevent dropdown from closing when clicking inside calendar
            document.getElementById('sales-calendar')?.addEventListener('click', (e) => {
                e.stopPropagation();
            });

            // Initialize calendar
            renderCalendar(currentDate);
        });
    </script>
    <!-- Category Chart Modal -->
    <!-- Category Chart Modal -->
    <div id="category-chart-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
        <div class="absolute inset-0 bg-black bg-opacity-30 backdrop-blur-sm"></div>
        <div
            class="relative universal-background-color rounded-lg shadow-xl max-w-4xl w-full mx-4 md:mx-auto overflow-hidden card">
            <!-- Modal Header -->
            <div class="flex justify-between items-center p-4 border-b card">
                <h3 id="modal-title" class="text-xl font-semibold">Category Details</h3>
                <button id="close-modal" style="background-color: transparent;"
                    class="text-gray-500 hover:text-gray-700 focus:outline-none">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                </button>
            </div>
            <!-- Modal Body -->
            <div class="p-6">
                <!-- Replace the AmCharts div with a canvas for Chart.js -->
                <div id="popup-chartdiv" style="width: 100%; height: 400px;">
                    <canvas id="categoryBarChart" width="100%" height="400"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Chart.js instance variable
        let categoryChart = null;

        // Function to show the modal with category data
        function showCategoryChart(category, title) {
            const modal = document.getElementById('category-chart-modal');
            const modalTitle = document.getElementById('modal-title');

            // Set the modal title
            modalTitle.textContent = title + ' Sales Details';

            // Show the modal
            modal.classList.remove('hidden');
            document.body.classList.add('overflow-hidden'); // Prevent scrolling

            // Initialize the chart with the category data
            initCategoryChart(category);
        }

        // Function to close the modal
        function closeModal() {
            const modal = document.getElementById('category-chart-modal');
            modal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');

            // Destroy the Chart.js instance to prevent memory leaks
            if (categoryChart) {
                categoryChart.destroy();
                categoryChart = null;
            }
        }

        // Function to initialize the chart with category data
        // Function to initialize the chart with category data
        function initCategoryChart(category) {
            // Generate the data based on category
            const categoryData = getCategoryData(category);

            // Extract labels and values from the data
            const labels = categoryData.map(item => item.name);
            const values = categoryData.map(item => item.value);

            // Check if body has pure-black class and set color accordingly
            const isPureBlack = document.body.classList.contains('pure-black');
            const chartBarColor = isPureBlack ? '#fafafa' : '#18181b';

            // Set grid color based on theme
            const gridColor = isPureBlack ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
            const tickColor = isPureBlack ? '#fafafa' : '#71717a';

            // Find the maximum value and calculate a more precise max
            const maxValue = Math.max(...values);

            // Get the canvas context
            const ctx = document.getElementById('categoryBarChart').getContext('2d');

            // Destroy existing chart if it exists
            if (categoryChart) {
                categoryChart.destroy();
            }

            // Create the Chart.js chart
            categoryChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: chartBarColor,
                        borderColor: chartBarColor,
                        borderWidth: 0, // Remove border completely
                        borderRadius: {
                            topLeft: 5,
                            topRight: 5,
                            bottomLeft: 5,
                            bottomRight: 5
                        },
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    layout: {
                        padding: {
                            left: 10,
                            right: 20,
                            top: 20,
                            bottom: 20
                        }
                    },
                    barPercentage: 0.9,
                    categoryPercentage: 0.8,
                    devicePixelRatio: 2,
                    scales: {
                        x: {
                            grid: {
                                display: false,
                                color: 'transparent'
                            },
                            border: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            suggestedMax: maxValue * 1.05, // Use suggestedMax instead of max with smaller buffer
                            grid: {
                                color: gridColor,
                                borderDash: [5, 5]
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                display: true,
                                color: tickColor,
                                font: {
                                    size: 10
                                },
                                padding: 10,
                                count: 5, // Explicitly set the number of ticks
                                callback: function (value) {
                                    return '€' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function (context) {
                                    return `€${context.parsed.y.toLocaleString()}`;
                                }
                            }
                        },
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }
        // Function to generate category data (keeping your existing function)
        function getCategoryData(category) {
            // This would normally come from an API call
            // For demonstration, we'll create mock data
            const data = [];
            const items = {
                'food': ['Pizza', 'Pasta', 'Burgers', 'Salad', 'Steak', 'Sushi', 'Soup', 'Sandwich'],
                'beverages': ['Water', 'Cola', 'Coffee', 'Wine', 'Beer', 'Juice', 'Tea', 'Cocktails'],
                'spa': ['Facial', 'Massage', 'Body Scrub', 'Sauna', 'Hot Stone', 'Aromatherapy'],
                'massage': ['Deep Tissue', 'Swedish', 'Sports', 'Thai', 'Shiatsu', 'Hot Stone'],
                'rooms': ['Standard', 'Deluxe', 'Suite', 'Executive', 'Presidential'],
                'others': ['Laundry', 'Transport', 'Tours', 'Events', 'Merchandise']
            };

            // Use appropriate items for the category or default to 'others'
            const categoryItems = items[category] || items.others;

            // Create data with random values
            categoryItems.forEach(item => {
                // Base value depends on category for more realistic data
                let baseValue = 0;
                switch (category) {
                    case 'food': baseValue = 2000; break;
                    case 'beverages': baseValue = 1200; break;
                    case 'spa': baseValue = 800; break;
                    case 'massage': baseValue = 600; break;
                    case 'rooms': baseValue = 400; break;
                    default: baseValue = 200;
                }

                // Add randomness
                const value = Math.floor(baseValue + (Math.random() * baseValue * 0.8));

                data.push({
                    name: item,
                    value: value
                });
            });

            // Sort data by value for better visualization
            return data.sort((a, b) => b.value - a.value);
        }

        // Add event listeners once DOM is fully loaded
        document.addEventListener('DOMContentLoaded', function () {
            // Close modal when close button is clicked
            document.getElementById('close-modal').addEventListener('click', closeModal);

            // Close modal when clicking outside the modal content
            document.getElementById('category-chart-modal').addEventListener('click', function (e) {
                if (e.target === this) {
                    closeModal();
                }
            });

            // Add click handlers to category cards
            setupCategoryCardHandlers();
        });

        // Setup click handlers for category cards - keep your existing function
        function setupCategoryCardHandlers() {
            // Select all category cards
            const categoryCards = document.querySelectorAll('.main-custom-hover-effect');

            categoryCards.forEach(card => {
                card.addEventListener('click', function () {
                    // Get category name and ID
                    const categorySpan = this.querySelector('dd span:first-child');
                    if (!categorySpan) return;

                    let categoryTitle = "Unknown";
                    let categoryId = "others";

                    // Try to determine category based on color class
                    if (this.innerHTML.includes('text-emerald-600')) {
                        categoryTitle = "Food";
                        categoryId = "food";
                    } else if (this.innerHTML.includes('text-amber-600')) {
                        categoryTitle = "Beverages";
                        categoryId = "beverages";
                    } else if (this.innerHTML.includes('text-indigo-600')) {
                        categoryTitle = "Spa";
                        categoryId = "spa";
                    } else if (this.innerHTML.includes('text-rose-600')) {
                        categoryTitle = "Massage";
                        categoryId = "massage";
                    } else if (this.innerHTML.includes('text-cyan-600')) {
                        categoryTitle = "Room Bookings";
                        categoryId = "rooms";
                    } else if (this.innerHTML.includes('text-gray-600')) {
                        categoryTitle = "Others";
                        categoryId = "others";
                    }

                    // Show the chart modal
                    showCategoryChart(categoryId, categoryTitle);
                });
            });
        }
    </script>
<script>
    // Food Items Ranking dropdown functionality
    document.addEventListener('DOMContentLoaded', function () {
        const options = [
            "Food",
            "Beverage",
            "Spa",
            "Massage",
            "Room Bookings",
            "Experiences",
            "Others"
        ];
        const selectedDataSource = document.getElementById('foodRankingSelectedDataSource');
        const dataSourceDropdown = document.getElementById('food-ranking-data-source-dropdown');
        const title = document.getElementById('foodRankingTitle');
        const dropdownList = dataSourceDropdown.querySelector('ul');
        // Replace dropdown options
        dropdownList.innerHTML = options.map(opt =>
            `<li><a href="#" class="food-ranking-data-source-option" data-source="${opt}">${opt}</a></li>`
        ).join('');
        // Add click listeners
        dropdownList.querySelectorAll('.food-ranking-data-source-option').forEach(option => {
            option.addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation(); // Prevents the dropdown toggle from re-triggering
                const selected = this.textContent;
                selectedDataSource.textContent = selected;
                if (title) title.textContent = `${selected} Ranking`;
                // Close the dropdown
                if (typeof UIkit !== 'undefined' && UIkit.dropdown(dataSourceDropdown)) {
                    UIkit.dropdown(dataSourceDropdown).hide(false);
                }
            });
        });
    });
</script>
    <style>
        /* Modal backdrop blur effect and animation */
        #category-chart-modal .backdrop-blur-sm {
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
        }

        /* Animation for modal */
        #category-chart-modal {
            transition: opacity 0.2s ease-in-out;
        }

        /* Cursor pointer for category cards */
        .main-custom-hover-effect {
            cursor: pointer;
        }

        #popup-chartdiv {
            z-index: 2000;
        }

        /* Modal z-index fixes */
        #category-chart-modal {
            z-index: 10000 !important;
            /* Higher than any other element */
            position: fixed;
        }
    </style>
</body>

</html>