<!DOCTYPE html>
<html lang="en" class="dark:bg-gray-900">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.28.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.28.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.28.0/components/prism-python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.28.0/plugins/line-numbers/prism-line-numbers.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.28.0/plugins/line-numbers/prism-line-numbers.min.css" rel="stylesheet" />
    <style>
        body {
            background-color: #202123;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .sidebar {
            background-color: #000000;
            width: 260px;
            padding: 1rem;
            height: 100vh;
            position: fixed;
            overflow-y: auto;
        }
        
        .sidebar nav {
            padding: 0;
        }
        
        .sidebar ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar li {
            margin: 4px 0;
        }
        
        .sidebar a {
            color: #ECECF1;
            text-decoration: none;
            display: block;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .sidebar a:hover {
            background-color: #343541;
        }
        
        .sidebar a.active {
            background-color: #343541;
            font-weight: 600;
        }
        .top-nav {
            background-color: #000000;
            border-bottom: 1px solid #353740;
        }
        .section-divider {
            height: 1px;
            background-color: #353740;
            margin: 3rem 0;
        }
        .code-block {
            background-color: #151617;
            border-radius: 8px;
            position: relative;
            margin-top: 0.5rem;
        }
        .code-block-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: #151617;
            border-bottom: 1px solid #2d2d3d;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }
        .code-block-title {
            color: #9ca3af;
            font-size: 13px;
            font-weight: 500;
        }
        .copy-button {
            padding: 4px 8px;
            background-color: #2d2d3d;
            border: 1px solid #454545;
            border-radius: 4px;
            color: #9ca3af;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .copy-button:hover {
            background-color: #353535;
            color: #fff;
        }
        pre {
            background-color: #151617 !important;
            margin: 0 !important;
            padding: 16px !important;
        }
        .line-numbers .line-numbers-rows {
            border-right: 1px solid #404040;
            padding: 1em 0;
        }
        .line-numbers-rows > span:before {
            color: #666;
            text-shadow: none;
        }
        pre[class*="language-"] {
            padding: 1em;
            margin: 0;
            overflow: auto;
            background-color: #151617 !important;
        }
        code[class*="language-"] {
            text-shadow: none;
            background: none;
        }
        .token.comment,
        .token.prolog,
        .token.doctype,
        .token.cdata {
            color: #666;
        }
        .token.function,
        .token.class-name {
            color: #bf2b6a; /* Red */
        }
        .token.keyword {
            color: #2a7eb0; /* Blue */
        }
        .token.string {
            color: #00a57c; /* Green */
        }
        .token.number {
            color: #bd93f9;
        }
        .subsection {
            margin-top: 2rem;
            margin-bottom: 1.5rem;
        }
        .function-title {
            color: #66e9ec;
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        .function-params {
            color: #bd93f9;
            margin-bottom: 0.5rem;
        }
        .function-description {
            color: #a5e844;
            margin-bottom: 1rem;
        }
    </style>
    <script>
        function copyCode(button) {
            const codeBlock = button.closest('.code-block');
            const code = codeBlock.querySelector('pre').textContent;
            navigator.clipboard.writeText(code.trim());
            button.textContent = 'Copied!';
            setTimeout(() => {
                button.textContent = 'Copy';
            }, 2000);
        }

        document.addEventListener('DOMContentLoaded', () => {
            document.querySelectorAll('pre').forEach(pre => {
                if (!pre.parentElement.classList.contains('code-block')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'code-block';
                    
                    const header = document.createElement('div');
                    header.className = 'code-block-header';
                    
                    const title = document.createElement('span');
                    title.className = 'code-block-title';
                    title.textContent = pre.querySelector('code').className.replace('language-', '') + ' example';
                    
                    const button = document.createElement('button');
                    button.className = 'copy-button';
                    button.textContent = 'Copy';
                    button.onclick = function() { copyCode(this); };
                    
                    header.appendChild(title);
                    header.appendChild(button);
                    
                    pre.parentNode.insertBefore(wrapper, pre);
                    wrapper.appendChild(header);
                    wrapper.appendChild(pre);
                    
                    pre.classList.add('line-numbers');
                }
            });

            const sections = document.querySelectorAll('section');
            const navLinks = document.querySelectorAll('.sidebar a');

            window.addEventListener('scroll', () => {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (pageYOffset >= sectionTop - sectionHeight / 3) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href').includes(current)) {
                        link.classList.add('active');
                    }
                });
            });
        });
    </script>
</head>
<body class="bg-[#202123] text-white">
    <nav class="top-nav fixed w-full h-14 z-10">
        <div class="flex items-center justify-between px-4 h-full">
            <div class="flex items-center space-x-2 text-sm text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-blend"><circle cx="9" cy="9" r="7"/><circle cx="15" cy="15" r="7"/></svg>
                <span>Guest Genius - Docs</span>
            </div>
            <div class="flex items-center space-x-4">
                <a href="#" class="text-sm text-gray-400 hover:text-white">Raw code files</a>
                <a href="#" class="text-sm text-gray-400 hover:text-white">GitHub</a>
            </div>
        </div>
    </nav>

    <div class="flex min-h-screen pt-14">
        <aside class="sidebar fixed h-screen overflow-y-auto" style="width: 280px;">
            <div class="flex flex-col h-full">
                <nav class="flex-1">
                    <ul>
                        <li><a href="#overview" class="active">Overview</a></li>
                        <li><a href="#setup">Setup and Configuration</a></li>
                        <li><a href="#authentication">Authentication System</a></li>
                        <li><a href="#routes">Application Routes</a></li>
                        <li><a href="#messaging">Message Handling</a></li>
                        <li><a href="#rate-limiting">Rate Limiting</a></li>
                        <li><a href="#database">Database Operations</a></li>
                        <li><a href="#error-handling">Error Handling</a></li>
                        <li><a href="#utilities">Utility Functions</a></li>
                    </ul>
                </nav>
            </div>
        </aside>
        <main class="flex-1 ml-72 px-32 py-12">
            <div class="max-w-3xl">
                <section id="overview" class="mb-24">
                    <h1 class="text-4xl font-medium mb-4">Flask Backend Documentation</h1>
                    <p class="text-base text-gray-400 mb-6">
                        Comprehensive documentation for the Flask backend application (app.py)
                    </p>

                    <div class="subsection">
                        <h2 class="text-2xl font-medium mb-4">System Architecture</h2>
                        <p class="text-gray-400">
                            The application is built using Flask framework with the following key components:
                        </p>
                        <ul class="list-disc ml-6 mt-2 text-gray-400">
                            <li>Flask for web server and routing</li>
                            <li>Twilio for messaging functionality</li>
                            <li>Supabase for database operations</li>
                            <li>Logging for monitoring and debugging</li>
                        </ul>
                    </div>

                    <h3 class="text-xl font-medium mb-4">Imports and Initialization</h3>
                    <p class="text-base text-gray-400 mb-6">
                        The `app.py` file begins with importing necessary modules and initializing the Flask application. Key imports include Flask, logging, and Twilio for messaging functionality.
                    </p>
                    <div class="code-block">
                        <div class="code-block-header">
                            <span class="code-block-title">python example</span>
                            <button class="copy-button" onclick="copyCode(this)">Copy</button>
                        </div>
                        <pre class="rounded-lg p-4"><code class="language-python">
from flask import Flask, render_template, jsonify, request, session, redirect, url_for
import requests
import json
import os
import logging
from colorama import Fore, Style, init
from datetime import datetime
from dotenv import load_dotenv
from auth import auth, login_required
from datetime import timedelta
from functools import wraps
import time
from twilio.rest import Client
from twilio.twiml.messaging_response import MessagingResponse
from bot import get_assistant_reply

init(autoreset=True)

app = Flask(__name__)
app.secret_key = os.urandom(24)
app.register_blueprint(auth)

# Load environment variables from .env file
load_dotenv()

# Get the values
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY')
TWILIO_ACCOUNT_SID = '**********************************'
TWILIO_AUTH_TOKEN = '005d910f85546392a91f58a3878c437c'
TWILIO_PHONE_NUMBER = '+***********'
                        </code></pre>
                    </div>

                    <h3 class="text-xl font-medium mb-4 mt-6">Configuration and Logging</h3>
                    <p class="text-base text-gray-400 mb-6">
                        Configures session lifetime, rate limiting, and sets up logging for the application. The session lifetime is set to 3000 minutes, and logging is configured to provide detailed information about the application's operations.
                    </p>
                    <div class="code-block">
                        <div class="code-block-header">
                            <span class="code-block-title">python example</span>
                            <button class="copy-button" onclick="copyCode(this)">Copy</button>
                        </div>
                        <pre class="rounded-lg p-4"><code class="language-python">
app.permanent_session_lifetime = timedelta(minutes=3000)

# New variables for request control
NO_OF_REQUESTS = 5
REQUEST_TIMEOUT = 40
rate_limit = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
                        </code></pre>
                    </div>

                    <h3 class="text-xl font-medium mb-4 mt-6">Twilio Client Initialization</h3>
                    <p class="text-base text-gray-400 mb-6">
                        Initializes the Twilio client for sending messages. The Twilio client is used to send WhatsApp messages to users.
                    </p>
                    <div class="code-block">
                        <div class="code-block-header">
                            <span class="code-block-title">python example</span>
                            <button class="copy-button" onclick="copyCode(this)">Copy</button>
                        </div>
                        <pre class="rounded-lg p-4"><code class="language-python">
twilio_client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
                        </code></pre>
                    </div>

                    <h3 class="text-xl font-medium mb-4 mt-6">Routes</h3>
                    <p class="text-base text-gray-400 mb-6">
                        Defines various routes for the application, each rendering different templates. These routes handle different pages such as the dashboard, sales, analytics, and more.
                    </p>

                    <div class="subsection">
                        <h4 class="text-lg font-medium mb-2">Index Route</h4>
                        <p class="text-base text-gray-400 mb-4">
                            The index route renders the dashboard template and sets session variables for tracking request times.
                        </p>
                        <div class="code-block">
                            <div class="code-block-header">
                                <span class="code-block-title">python example</span>
                                <button class="copy-button" onclick="copyCode(this)">Copy</button>
                            </div>
                            <pre class="rounded-lg p-4"><code class="language-python">
@app.route('/')
@login_required
def index():
    session['start_time'] = time.time()
    session['last_request_time'] = time.time()
    return render_template('dashboard.html')
                            </code></pre>
                        </div>
                    </div>

                    <div class="subsection">
                        <h4 class="text-lg font-medium mb-2">Sales Route</h4>
                        <p class="text-base text-gray-400 mb-4">
                            The sales route renders the sales template.
                        </p>
                        <div class="code-block">
                            <div class="code-block-header">
                                <span class="code-block-title">python example</span>
                                <button class="copy-button" onclick="copyCode(this)">Copy</button>
                            </div>
                            <pre class="rounded-lg p-4"><code class="language-python">
@app.route('/sales')
@login_required
def open_sales():
    return render_template('sales.html')
                            </code></pre>
                        </div>
                    </div>

                    <div class="subsection">
                        <h4 class="text-lg font-medium mb-2">Analytics Route</h4>
                        <p class="text-base text-gray-400 mb-4">
                            The analytics route renders the analytics template.
                        </p>
                        <div class="code-block">
                            <div class="code-block-header">
                                <span class="code-block-title">python example</span>
                                <button class="copy-button" onclick="copyCode(this)">Copy</button>
                            </div>
                            <pre class="rounded-lg p-4"><code class="language-python">
@app.route('/analytics')
@login_required
def open_analytics():
    return render_template('analytics.html')
                            </code></pre>
                        </div>
                    </div>

                    <!-- Add more routes as needed -->

                    <h3 class="text-xl font-medium mb-4 mt-6">Helper Functions and Decorators</h3>
                    <p class="text-base text-gray-400 mb-6">
                        Includes decorators for rate limiting and helper functions for handling messages. The `check_request_allowed` decorator ensures that requests are rate-limited, while `handle_incoming_message` and `send_whatsapp_message` handle incoming and outgoing messages.
                    </p>
                    <div class="code-block">
                        <div class="code-block-header">
                            <span class="code-block-title">python example</span>
                            <button class="copy-button" onclick="copyCode(this)">Copy</button>
                        </div>
                        <pre class="rounded-lg p-4"><code class="language-python">
def check_request_allowed(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not rate_limit:
            return f(*args, **kwargs)
        
        current_time = time.time()
        last_request_time = session.get('last_request_time', 0)
        start_time = session.get('start_time', current_time)

        if current_time - start_time > REQUEST_TIMEOUT:
            return jsonify({'error': 'Session expired. Please refresh the page.'}), 403

        session['last_request_time'] = current_time
        return f(*args, **kwargs)
    return decorated_function

def handle_incoming_message(phone_number, message):
    logger.info(f"Handling incoming message from {phone_number}: {message}")
    print(f"Handling incoming message from {phone_number}: {message}")

    with open('chats.json', 'r', encoding='utf-8') as f:
        chat_data = json.load(f)

    user = next((u for u in chat_data['users'] if u.get('phone_number') == phone_number), None)
    if user is None:
        new_user_id = len(chat_data['users'])
        new_user = {
            'id': new_user_id,
            'name': f'User {new_user_id}',
            'status': 'Online',
            'avatar': 'https://generated.vusercontent.net/placeholder-user.jpg',
            'phone_number': phone_number,
            'room_number': 'None',
            'platform': 'WhatsApp'
        }
        chat_data['users'].append(new_user)
        chat_data['chats'][str(new_user_id)] = []
        user = new_user
        logger.info(f"Created new user: {new_user}")
        print(f"Created new user: {new_user}")

    new_message = {
        'sender': user['name'],
        'message': message,
        'timestamp': datetime.now().strftime("%H:%M"),
        'customer': True
    }
    chat_data['chats'][str(user['id'])].append(new_message)

    with open('chats.json', 'w', encoding='utf-8') as f:
        json.dump(chat_data, f, indent=2, ensure_ascii=False)

    logger.info("Added incoming message to chats.json")
    print("Added incoming message to chats.json")

def send_whatsapp_message(phone_number, message):
    logger.info(f"Sending WhatsApp message to {phone_number}: {message}")
    print(f"Sending WhatsApp message to {phone_number}: {message}")

    try:
        message = twilio_client.messages.create(
            body=message,
            from_=f'whatsapp:{TWILIO_PHONE_NUMBER}',
            to=f'whatsapp:+919398760681'
        )
        logger.info(f"Successfully sent WhatsApp message. SID: {message.sid}")
        print(f"Successfully sent WhatsApp message. SID: {message.sid}")
        return {"success": True, "message_sid": message.sid}
    except Exception as e:
        error_message = f"Error sending WhatsApp message: {str(e)}"
        logger.error(error_message)
        print(error_message)
        return {"error": error_message}
                        </code></pre>
                    </div>

                    <h3 class="text-xl font-medium mb-4 mt-6">Running the Application</h3>
                    <p class="text-base text-gray-400 mb-6">
                        Starts the Flask application. The application runs on port 5000 and is set to debug mode for development purposes.
                    </p>
                    <div class="code-block">
                        <div class="code-block-header">
                            <span class="code-block-title">python example</span>
                            <button class="copy-button" onclick="copyCode(this)">Copy</button>
                        </div>
                        <pre class="rounded-lg p-4"><code class="language-python">
if __name__ == '__main__':
    logger.info("Starting Flask application")
    print("Starting Flask application")
    app.run(debug=True, port=5000)
                        </code></pre>
                    </div>
                </section>

                <div class="section-divider"></div>

                
            </div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            document.querySelectorAll('pre').forEach(pre => {
                if (!pre.parentElement.classList.contains('code-block')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'code-block';
                    
                    const header = document.createElement('div');
                    header.className = 'code-block-header';
                    
                    const title = document.createElement('span');
                    title.className = 'code-block-title';
                    title.textContent = pre.querySelector('code').className.replace('language-', '') + ' example';
                    
                    const button = document.createElement('button');
                    button.className = 'copy-button';
                    button.textContent = 'Copy';
                    button.onclick = function() { copyCode(this); };
                    
                    header.appendChild(title);
                    header.appendChild(button);
                    
                    pre.parentNode.insertBefore(wrapper, pre);
                    wrapper.appendChild(header);
                    wrapper.appendChild(pre);
                    
                    pre.classList.add('line-numbers');
                }
            });

            const sections = document.querySelectorAll('section');
            const navLinks = document.querySelectorAll('.sidebar a');

            window.addEventListener('scroll', () => {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (pageYOffset >= sectionTop - sectionHeight / 3) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href').includes(current)) {
                        link.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>
</html>