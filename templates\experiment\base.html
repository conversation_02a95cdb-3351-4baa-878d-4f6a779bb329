<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Guest Genius{% endblock %}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Inter Font -->
    <link rel="preconnect" href="https://rsms.me/" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />

    <!-- Custom Styles -->
    <link rel="stylesheet" href="../static/styles/custom.css">
    <link rel="stylesheet" href="../static/styles/loadinganimations.css">
    <link rel="stylesheet" href="../static/styles/chartdropdown.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- <PERSON>en UI Styles -->
    <link rel="stylesheet" href="https://unpkg.com/franken-ui/dist/css/core.min.css" />

    <!-- Lit Library -->
    <script type="module" src="https://unpkg.com/lit@2.6.1/index.js?module"></script>

    <!-- Franken UI Scripts -->
    <script type="module" src="https://unpkg.com/franken-ui/dist/js/core.iife.js"></script>
    <script type="module" src="https://unpkg.com/franken-ui/dist/js/icon.iife.js"></script>

    <!-- UIKit Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/uikit@latest/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@latest/dist/js/uikit-icons.min.js"></script>

    <!-- Google Translate -->
    <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>

    <!-- Custom Scripts -->
    <script src="../static/js/languagetranslator.js" defer></script>
    <script src="../static/js/themes.js" defer></script>
    <script src="../static/js/loading.js" defer></script>
    <script src="../static/js/dropdownslogic.js" defer></script>
    <script src="../static/js/chartsdropsdown.js" defer></script>

    <script>
        // Theme Initialization
        if (
            localStorage.getItem("color-theme") === "dark" ||
            (!("color-theme" in localStorage) &&
            window.matchMedia("(prefers-color-scheme: dark)").matches)
        ) {
            document.documentElement.classList.add("dark");
        } else {
            document.documentElement.classList.remove("dark");
        }
    </script>

    {% block extra_head %}{% endblock %}
</head>
<body class="bg-background text-foreground">
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loader"></div>
    </div>

    <!-- Main Grid Layout -->
    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] overflow-hidden">
        <!-- Include Sidebar -->
        {% include 'sidebar.html' %}

        <!-- Page Content -->
        <div class="flex flex-col">
            <!-- Page Header -->
            <header class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b px-6 shadow-sm">
                {% block header %}{% endblock %}
            </header>

            <!-- Page Main Content -->
            <main class="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6 h-full">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    {% block extra_scripts %}{% endblock %}
</body>
</html>