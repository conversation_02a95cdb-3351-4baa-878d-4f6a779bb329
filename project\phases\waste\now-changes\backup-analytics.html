<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Sales Dashboard</title>
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
<script src="https://cdn.amcharts.com/lib/4/core.js"></script>
<script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
<script src="https://kit.fontawesome.com/c6c71387b9.js" crossorigin="anonymous"></script>
<script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
<link rel="preconnect" href="https://rsms.me/" />
<link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
<link rel="stylesheet" href="../static/styles/custom.css">
<script src="../static/js/languagetranslator.js" defer></script>
<script src="../static/js/themes.js" defer></script>
<script src="https://cdn.jsdelivr.net/npm/uikit@latest/dist/js/uikit.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/uikit@latest/dist/js/uikit-icons.min.js"></script>
<link rel="stylesheet" href="../static/styles/loadinganimations.css">
<link rel="stylesheet" href="../static/styles/chartdropdown.css">
<script src="../static/js/loading.js" defer></script>
<script src="../static/js/chartsdropsdown.js" defer></script>
<script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
<script src="https://cdn.amcharts.com/lib/4/themes/dark.js"></script>
<link rel="stylesheet" href="../static/styles/scrollbar.css">
<link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/slate.min.css"/>

<script
      type="module"
      src="https://unpkg.com/franken-wc@latest/dist/js/wc.iife.js"
    ></script>
<script>
    tailwind.config = {
      darkMode: 'class',
    }

    document.querySelectorAll('.uk-iconnav a').forEach(icon => {
        icon.addEventListener('click', (e) => {
          e.preventDefault(); // Prevent default action
          setTimeout(() => {
            icon.classList.remove('hidden');
          }, 0);
        });
      });
</script>

<style>
    body { 
        top: 0px !important;
    }
    .chart-container {
        position: relative;
        height: 400px;
        width: 100%;
    }
    .no-animation #loading-overlay {
        display: none;
    }
    .table-container {
        max-height: 640px;
        overflow-y: auto;
    }
    .table-container thead {
        position: sticky;
        top: 0;
        z-index: 1;
        background-color: #fff;
    }
    .icon {
        margin-top: 1px;
    }
    .platform-icon {
        margin-top: 1px;
    }
    
    #languagesContainer {
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* Internet Explorer/Edge */
    }

    #languagesContainer::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }

    .sidebar {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        width: 280px;
        overflow-y: auto;
            height: 100%;

    }
    
    .main-content {
        margin-left: 280px;
        width: calc(100% - 280px);
    }
    
    @media (max-width: 1024px) {
        .sidebar {
            display: none;
        }
        .main-content {
            margin-left: 0;
            width: 100%;
        }
    }
    #languagesList {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .language-card {
        transition: all 0.2s ease;
        width: 100%;
        height: 110px; /* Slightly increased height */
    }

    .language-card:hover {
        transform: translateY(-1px);
    }

    #languagesList {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px; /* Increased gap for better spacing */
    }

    .language-card {
        display: flex;
        flex-direction: column;
        padding: 16px;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        transition: transform 0.2s ease;
        height: 110px; /* Fixed height */
    }

    .language-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    

    .flag-container {
        width: 36px; /* Fixed width for flag */
        height: 28px; /* Fixed height for flag */
        margin-top: -4px;
        margin-bottom: 8px;
    }

    .flag-container img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .language-info {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .language-name {
        font-weight: 500;
        margin-bottom: 0px; /* Reduced from default spacing */
    }

    .language-stats {
        font-size: 0.875rem;
        color: #4b5563;
    }

    .percentage {
        position: absolute;
        top: 12px;
        right: 12px;
        font-size: 0.875rem;
        color: #6b7280;
    }
    .mini-graph {
        position: absolute;
        bottom: 0px;
        right: 0px;
        opacity: 0.7;
    }

    .graph-line {
        position: absolute;
        bottom: 4px;
        left: 4px;
        right: 4px;
        height: 12px;
        fill: none;
        stroke: #5347ce;
        stroke-width: 1.5;
        stroke-linecap: round;
    }
</style>

</head>
<body class="bg-background text-foreground">
    <div id="loading-overlay" class="loading-overlay">
        <div class="loader"></div>
    </div>
    <div id="google_translate_element" style="display:none;"></div>
    <div class="grid min-h-screen w-full overflow-hidden lg:grid-cols-[280px_1fr]">
        {% include 'sidebar.html' %}
        <div class="flex flex-col">
            <header class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-6">
                <div class="flex items-center gap-4">
                    <a class="lg:hidden text-gray-600 hover:text-gray-800" href="#">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                            <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                            <path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1L21 9"></path>
                            <path d="M12 3v6"></path>
                        </svg>
                        <span class="sr-only">Home</span>
                    </a>
                    <h1 class="font-semibold text-lg">Analytics</h1>
                </div>
                {% include 'topright.html' %}
            </header>
            <main class="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6 card">
                <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <!-- Total Sales -->
                    <!-- Include ProgressBar.js -->
                    <script src="https://cdn.jsdelivr.net/npm/progressbar.js"></script>

                    <!-- Total Sales Card with Progress Bar -->
                    <div class="card rounded-lg border relative overflow-hidden" data-v0-t="card" style="height: 150px;">
                        <div class="p-4 pb-1 flex flex-row items-center justify-between space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-large">Total Revenue</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-gray-600">
                                <path d="M12 2v20"></path>
                                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                            </svg>
                        </div>
                        <div class="px-4 pb-2 relative z-10 mt-10">
                            <div class="flex items-baseline">
                                <div style="margin-top: 0px" class="text-2xl font-bold">€13,762</div>
                            </div>
                            <p class="text-xs">Total Lifetime Sales</p>
                        </div>
                        <!-- Progress Bar and Labels -->
                        <div class="px-4" style="width: 100%; height: 50px; position: absolute; bottom: 0;">
                            <div>
                                <div id="progress-bar-container" style="position: absolute; bottom: 0; right: 0; margin-bottom: -10px; width: 200px; height: 100px;">
                                    <div id="pieChartDiv"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Styles -->
                    <style>
                        #pieChartDiv {
                        width: 100%;
                        height: 100%;
                        margin: 0;
                        padding: 0;
                        }
                        #pieChartDiv1 {
                        width: 100%;
                        height: 100%;
                        margin: 0;
                        padding: 0;
                        }
                    </style>
                    
                    <!-- Resources -->
                    <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
                    <script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
                    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>

                    <script>
                        am5.ready(function() {

                            // Create root element
                            var root = am5.Root.new("pieChartDiv");

                            // Remove padding from root container
                            root.container.setAll({
                                paddingLeft: 0,
                                paddingRight: 0,
                                paddingTop: 0,
                                paddingBottom: 0
                            });

                            // Set themes
                            root.setThemes([
                                am5themes_Animated.new(root)
                            ]);
                            root._logo.dispose();

                            // Create chart
                            var chart = root.container.children.push(am5percent.PieChart.new(root, {
                                startAngle: 180,
                                endAngle: 360,
                                layout: root.verticalLayout,
                                innerRadius: am5.percent(60),
                                paddingLeft: 0,
                                paddingRight: 0,
                                paddingTop: 0,
                                paddingBottom: 0
                            }));

                            // Create series
                            var series = chart.series.push(am5percent.PieSeries.new(root, {
                                startAngle: 180,
                                endAngle: 360,
                                valueField: "value",
                                categoryField: "category",
                                alignLabels: false,
                                sliceSpacing: 2 // Add gaps between slices
                            }));

                            // Disable labels and ticks
                            series.labels.template.set("visible", false);
                            series.ticks.template.set("visible", false);

                            series.states.create("hidden", {
                                startAngle: 180,
                                endAngle: 180
                            });

                            series.slices.template.setAll({
                                cornerRadius: 5,
                                stroke: null, // Remove border
                                tooltipText: "{category}: {value}" // Optional: Add tooltip
                            });

                            // Set data with colors
                            series.data.setAll([
                                { value: 10, category: "One" },
                                { value: 9, category: "Two" },
                                { value: 6, category: "Three" },
                                { value: 5, category: "Four" },
                                { value: 4, category: "Five" }
                            ]);

                            series.slices.each(function(slice, index) {
                                var colors = [0x9f95fb, 0x43d7f2, 0xf6cf47, 0x45d3d2, 0xaabbd4];
                                slice.set("fill", am5.color(colors[index]));
                            });

                            series.appear(1000, 100);

                        }); // end am5.ready()
                    </script>
                    
  
                    <script src="https://cdn.jsdelivr.net/npm/progressbar.js"></script> <!-- Ensure ProgressBar.js is included -->
                    
                    <!-- Total Tips -->
                    <div class="card rounded-lg border relative overflow-hidden " data-v0-t="card" style="height: 150px;">
                        <div class="p-4 pb-1 flex flex-row items-center justify-between space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-large">Conversation Outside Business Hours</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-gray-600">
                                <rect width="20" height="12" x="2" y="6" rx="2"/>
                                <circle cx="12" cy="12" r="2"/>
                                <path d="M6 12h.01M18 12h.01"/>
                            </svg>
                        </div>
                        <div class="px-4 pb-2 relative z-10">
                            <div class="flex items-baseline">
                                <div id="total-tips" style="margin-top: 0px" class="text-2xl font-bold">763</div>
                            </div>
                        </div>
                        <!-- Progress Bar and Labels -->
                        <div class="px-4" style="width: 100%; height: 50px; position: absolute; bottom: 0;">
                            <div class="">
                                <!-- Container for the progress bars -->
                                <div id="progress-bar-container-2" style="position: relative; width: 100%; height: 13px; margin-bottom: 2px;"></div>
                                <!-- Labels -->
                                <div class="flex justify-around mt-2">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-black rounded mr-1"></div>
                                        <span class="text-xs text-gray-500">Total: 52</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-[#4A90E2] rounded mr-1"></div>
                                        <span class="text-xs text-gray-500">Instagram: 130</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-[#887cfd] rounded mr-1"></div>
                                        <span class="text-xs text-gray-500">Whatsapp: 170</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-[#F5A623] rounded mr-1"></div>
                                        <span class="text-xs text-gray-500">Facebook: 220</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            // Initialize the first segment for Positive feedback
                            var bar1 = new ProgressBar.Line('#progress-bar-container-2', {
                                strokeWidth: 3,
                                easing: 'easeInOut',
                                duration: 1400,
                                color: '#47a8f8',
                                trailColor: 'transparent',
                                trailWidth: 1,
                                svgStyle: {width: '69%', height: '100%', position: 'absolute', left: '0', borderRadius: '5px'},
                            });

                            // Initialize the second segment for Neutral feedback
                            var bar2 = new ProgressBar.Line('#progress-bar-container-2', {
                                strokeWidth: 3,
                                easing: 'easeInOut',
                                duration: 1400,
                                color: '#7e80e7',
                                trailColor: 'transparent',
                                trailWidth: 1,
                                svgStyle: {width: '19%', height: '100%', position: 'absolute', left: '70.5%', borderRadius: '5px'},
                            });

                            // Initialize the third segment for Negative feedback
                            var bar3 = new ProgressBar.Line('#progress-bar-container-2', {
                                strokeWidth: 3,
                                easing: 'easeInOut',
                                duration: 1400,
                                color: '#f3a23a',
                                trailColor: 'transparent',
                                trailWidth: 1,
                                svgStyle: {width: '12%', height: '100%', position: 'absolute', left: '90.5%', borderRadius: '5px'},
                            });

                            // Set the progress for each segment
                            bar1.animate(1.0);  // Number from 0.0 to 1.0
                            bar2.animate(1.0);
                            bar3.animate(1.0);
                        });
                    </script>

                    <script>
                        async function initializeTotalTipsChart() {
                            // Updated tips data for each day (Monday to Sunday)
                            const tipsData = [130, 170, 150, 200, 250, 180, 300];

                            const tipsChartData = {
                                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                                datasets: [
                                    {
                                        data: tipsData,
                                        backgroundColor: '#aabdd1',
                                        borderColor: '#aabdd1',
                                        borderWidth: 1,
                                        borderRadius: 6,
                                        borderSkipped: 'bottom'
                                    }
                                ]
                            };

                            const ctx = document.getElementById('TotalTipsChart').getContext('2d');
                            const totalTipsChart = new Chart(ctx, {
                                type: 'bar',
                                data: tipsChartData,
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    layout: {
                                        padding: {
                                            bottom: 20,
                                            top: 20
                                        }
                                    },
                                    barPercentage: 1,
                                    categoryPercentage: 0.7,
                                    scales: {
                                        x: {
                                            grid: {
                                                display: false,
                                                color: 'transparent'
                                            }
                                        },
                                        y: {
                                            beginAtZero: true,
                                            min: 130,
                                            max: 300,
                                            grid: {
                                                color: 'rgba(0, 0, 0, 0.1)',
                                                borderDash: [5, 5]
                                            },
                                            ticks: {
                                                stepSize: 30, // Adjusted step size
                                                callback: function(value) {
                                                    return value; // Removed '€'
                                                }
                                            }
                                        }
                                    },
                                    plugins: {
                                        tooltip: {
                                            backgroundColor: '#151519',
                                            titleColor: '#ffffff',
                                            bodyColor: '#ffffff',
                                            callbacks: {
                                                label: function(context) {
                                                    return context.raw; // Removed '€'
                                                }
                                            }
                                        },
                                        legend: {
                                            display: false
                                        }
                                    }
                                }
                            });
                        }

                        document.addEventListener('DOMContentLoaded', function() {
                            initializeTotalTipsChart();
                        });
                    </script>         


                    <div class="grid grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <!-- Total Reviews -->
                        <div class="card rounded-lg border relative overflow-hidden" data-v0-t="card" style="height: 150px;">
                            <div class="p-4 flex flex-col items-center space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium dark:text-gray-200 text-center">Total Reviews</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400 text-center">Lifetime Reviews</p>
                            </div>
                            <div class="px-4 pb-4 relative z-10">
                                <div class="flex flex-col items-center justify-center">
                                    <button style="margin-top: 1px;" class="uk-button uk-button-default">231</button>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">Today : +34</p>
                                </div>
                            </div>
                        </div>
                        <!-- Total Messages -->
                        <div class="card rounded-lg border relative overflow-hidden" data-v0-t="card" style="height: 150px;">
                            <div class="p-4 flex flex-col items-center space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium dark:text-gray-200 text-center">Staff chats</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400 text-center">Lifetime Chats</p>
                            </div>
                            <div class="px-4 pb-4 relative z-10">
                                <div class="flex flex-col items-center justify-center">
                                    <button style="margin-top: 1px;" class="uk-button uk-button-default" id="total-messages">104</button>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">Today : +37</p>
                                </div>
                            </div>
                        </div>
                        <!-- Total Bookings -->
                        <div class="card rounded-lg border relative overflow-hidden" data-v0-t="card" style="height: 150px;">
                            <div class="p-4 flex flex-col items-center space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium dark:text-gray-200 text-center">Total Bookings</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400 text-center">Lifetime Bookings</p>
                            </div>
                            <div class="px-4 pb-4 relative z-10">
                                <div class="flex flex-col items-center justify-center">
                                    <button style="margin-top: 1px;" class="uk-button uk-button-default" id="total-bookings">+217</button>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">Today : +231</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="col-span-1 md:col-span-2">
                        <div class="card rounded-lg border text-card-fore  min-h-[350px] md:h-[350px] v0-t='card' transition-all duration-300 cursor-pointer overflow-hidden ">
                            <div class="p-6 flex flex-row items-center justify-between space-y-0">
                                <h3 class="text-lg font-semibold">AI vs manual interactions</h3>
                                
                            </div>
                            <div class="px-6 pb-6" style="height: calc(100% - 88px);">
                                <div class="chart-container h-full w-full">
                                    <canvas id="salesOverviewChart" style="display: block; box-sizing: border-box; height: 100%; width: 100%;"></canvas>
                                </div> 
                            </div>
                        </div>
                    </div>
                    <div id="languagesContainer" class="col-span-1 rounded-lg p-4 transition-all duration-300 cursor-pointer border card min-h-[350px] md:h-[350px] overflow-y-auto">
                        <div class="flex flex-row items-center justify-between space-y-0 mb-4">
                            <h3 class="text-lg font-semibold">Languages Spoken</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-languages">
                                <path d="m5 8 6 6"/>
                                <path d="m4 14 6-6 2-3"/>
                                <path d="M2 5h12"/>
                                <path d="M7 2h1"/>
                                <path d="m22 22-5-10-5 10"/>
                                <path d="M14 18h6"/>
                            </svg>
                        </div>
                        <div id="languagesList" class="grid grid-cols-2 gap-4">
                            <!-- Language items will be dynamically inserted here -->
                        </div>
                    </div>
                    
                    <script>
                        function generateMountainPath() {
                        // Start from bottom
                        const startY = 16;
                        // Generate 5 points with increasing height trend
                        const points = [];
                        let currentY = startY;
                        
                        for (let i = 0; i < 5; i++) {
                            // Gradually decrease Y (move upward) with some randomness
                            currentY = Math.max(4, currentY - (Math.random() * 4 + 2));
                            points.push(currentY);
                        }
                        
                        // Ensure the last point is near the top
                        points[4] = Math.random() * 3 + 2;
                        
                        // Adjust the starting X position to move the line to the right
                        return `M 50,${startY} ${points.map((p, i) => `L ${(i + 1) * 25},${p}`).join(' ')}`;
                    }

                    </script>
                    <script>
                        function updateLanguages(languages) {
                            const container = document.getElementById('languagesList');
                            container.innerHTML = '';
                    
                            // Sort languages by value in descending order
                            languages.sort((a, b) => b.value - a.value);
                    
                            const totalValue = languages.reduce((sum, lang) => sum + lang.value, 0);
                    
                            languages.forEach(lang => {
                                const percentage = ((lang.value / totalValue) * 100).toFixed(1);
                                const randomUsers = Math.floor(Math.random() * (200 - 100 + 1)) + 100;
                                const pathData = generateMountainPath();
                    
                                const item = document.createElement('div');
                                item.className = 'language-card relative card';
                                item.innerHTML = `
                                    <div class="percentage absolute top-3 right-3">${percentage}%</div>
                                    <div class="flag-container">
                                        <img src="../static/flags/${lang.name.toLowerCase()}.png" 
                                            alt="${lang.name} Flag">
                                    </div>
                                    <div class="language-info">
                                        <div>
                                            <div class="language-name capitalize">${lang.name}</div>
                                            <div class="language-stats">
                                                ${randomUsers} users
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mini-graph-container flex justify-end">
                                        <div class="mini-graph w-2/4">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="10 -24 80 40" class="w-full h-full">
                                                <!-- Define gradient -->
                                                <defs>
                                                    <linearGradient id="fadeGradient" x1="0" y1="0" x2="0" y2="1">
                                                        <stop offset="0%" stop-color="#f97316" stop-opacity="0.3"/>
                                                        <stop offset="100%" stop-color="#f97316" stop-opacity="0"/>
                                                    </linearGradient>
                                                </defs>
                                                
                                                <!-- Gradient fill area -->
                                                <path 
                                                    d="M 10 16 C 15 11, 18 14, 25 8 S 35 1, 45 -2 S 55 -6, 65 -12 S 75 -16, 85 -19 S 88 -22, 90 -24 L 90 16 L 10 16 Z" 
                                                    fill="url(#fadeGradient)"
                                                />
                                                
                                                <!-- Main curve line -->
                                                <path 
                                                    d="M 10 16 C 15 11, 18 14, 25 8 S 35 1, 45 -2 S 55 -6, 65 -12 S 75 -16, 85 -19 S 88 -22, 90 -24" 
                                                    stroke="#f97316" 
                                                    stroke-width="1" 
                                                    fill="none"
                                                />
                                            </svg>
                                        </div>
                                    </div>
                                `;
                                container.appendChild(item);
                            });
                        }
                    
                        const exampleLanguages = [
                            { name: 'english', value: 4800 },  // Added first with higher value
                            { name: 'spanish', value: 4600 },  // Added second with higher value 
                            { name: 'german', value: 4400 },
                            { name: 'dutch', value: 4100 },
                            { name: 'french', value: 3600 },
                            { name: 'italian', value: 3100 },
                            { name: 'swedish', value: 2700 }
                        ];
                    
                        updateLanguages(exampleLanguages);
                    </script>
                </div>
                <style>
                    .platform-selector {
                        position: relative;
                        z-index: 2000;
                    }
                    .platform-selector-btn {
                        display: flex;
                        align-items: center;
                        padding: 0.5rem 1rem;
                        background-color: var(--theme-selector-bg, #e2e8f0);
                        color: var(--theme-selector-color, #212529);
                        border: 1px solid var(--theme-selector-border, #dee2e6);
                        border-radius: 20px;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        white-space: nowrap;
                        font-size: 0.875rem;
                    }
                    .platform-selector-btn:hover {
                        background-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.1));
                    }
                    .platform-selector-btn i {
                        margin-right: 0.5rem;
                    }
                    .platform-selector-menu {
                        display: block;
                        opacity: 0;
                        visibility: hidden;
                        position: absolute;
                        right: 0;
                        top: calc(100% + 5px);
                        background-color: var(--theme-selector-menu-bg, #ffffff);
                        border: 1px solid var(--theme-selector-menu-border, #dee2e6);
                        border-radius: 0.375rem;
                        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                        z-index: 10;
                        transition: all 0.3s ease;
                        transform: translateY(-10px);
                        width: 150px;
                    }
                    .platform-selector:hover .platform-selector-menu,
                    .platform-selector.active .platform-selector-menu {
                        opacity: 1;
                        visibility: visible;
                        transform: translateY(0);
                    }
                    .platform-option {
                        padding: 0.5rem 1rem;
                        cursor: pointer;
                        color: var(--theme-option-color, #212529);
                        transition: background-color 0.2s ease;
                        display: flex;
                        align-items: center;
                    }
                    .platform-option:hover {
                        background-color: var(--theme-option-hover-bg, #f1f5f9);
                    }
                    .platform-option i {
                        margin-right: 0.5rem;
                        width: 20px;
                        text-align: center;
                    }
                </style>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const platformSelector = document.querySelector('.platform-selector');
                        const platformOptions = document.querySelectorAll('.platform-option');
                        const platformSelectorBtn = platformSelector.querySelector('.platform-selector-btn');
                        let timeoutId;

                        function showDropdown() {
                            clearTimeout(timeoutId);
                            platformSelector.classList.add('active');
                        }

                        function hideDropdown() {
                            timeoutId = setTimeout(() => {
                                platformSelector.classList.remove('active');
                            }, 300);
                        }

                        function applyPlatform(platformName, iconClass) {
                            platformSelectorBtn.innerHTML = `<i class="${iconClass}"></i> ${platformName}`;
                            // Add logic to update the chart based on the selected platform
                            console.log('Selected platform:', platformName);
                        }

                        platformSelector.addEventListener('mouseenter', showDropdown);
                        platformSelector.addEventListener('mouseleave', hideDropdown);

                        platformOptions.forEach(option => {
                            option.addEventListener('click', function() {
                                const selectedPlatform = this.getAttribute('data-platform');
                                const iconClass = this.querySelector('i').className;
                                applyPlatform(this.textContent.trim(), iconClass);
                                hideDropdown();
                            });
                            option.addEventListener('mouseenter', showDropdown);
                        });

                        // Set default platform
                        applyPlatform('Facebook', 'fab fa-facebook');
                    });
                </script>
                <style>
                    #aiInteractionsChart {
                        width: 100%;
                        height: 100%;
                    }
                    
                    .chart-container-1 {
                        height: 350px; /* Custom height for the first chart */
                    }
                    
                    .chart-container-2 {
                        height: 350px; /* Custom height for the second chart */
                    }

                    .chart-container-2 .amcharts-ColumnSeries-column {
                        stroke: none; /* Remove the border */
                        stroke-width: 0; /* Ensure no border width */
                        fill: #18181b; /* Set the desired fill color */
                    }
                    
                    .chart-container-3 {
                        height: 350px; /* Custom height for the third chart */
                    }
                </style>
                <div class="grid gap-4 md:grid-cols-3 lg:grid-cols-3">
                    <!-- Chart 1 -->
                    <div class="card rounded-lg border shadow-sm chart-container chart-container-1" data-v0-t="card">
                        <div class="flex justify-between items-start p-4">
                            <div class="flex flex-col">
                                <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">Product Analytics</h3>
                                <p class="text-sm text-muted-foreground mt-1">Pie chart representation</p>
                            </div>
                            <div class="relative">
                                <button style="margin-top: 2px;" id="toggle-chart-btn" class="uk-button border card default" type="button" onclick="window.location.href='/sales'">
                                    <svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 6px;" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-notepad-text"><path d="M8 2v4"/><path d="M12 2v4"/><path d="M16 2v4"/><rect width="16" height="18" x="4" y="4" rx="2"/><path d="M8 10h6"/><path d="M8 14h8"/><path d="M8 18h5"/></svg>
                                    Sales
                                </button>
                            </div>
                        </div>
                        <hr class="my-1"> <!-- Adjusted margin to move the divider up -->
                        <div class="p-4 relative min-h-[260px] md:h-[260px]">
                            <div id="chartdiv" style="width: 100%; height: 100%;"></div>
                        </div>
                    </div>    
                             
                    <script>
                        function toggleOptionsMenu() {
                            const menu = document.getElementById('chart-options-menu');
                            menu.classList.toggle('hidden');
                        }
                        
                        function handleOptionClick(option) {
                            // Prevent the default action
                            event.preventDefault();
                            
                            // Handle different options
                            switch(option) {
                                case 'account':
                                    console.log('Account settings clicked');
                                    // Add your account settings logic here
                                    break;
                                case 'support':
                                    console.log('Support clicked');
                                    // Add your support logic here
                                    break;
                                case 'license':
                                    console.log('License clicked');
                                    // Add your license logic here
                                    break;
                                case 'signout':
                                    console.log('Sign out clicked');
                                    // Add your sign out logic here
                                    break;
                            }
                            
                            // Hide the menu after clicking
                            document.getElementById('chart-options-menu').classList.add('hidden');
                        }
                        
                        // Close the menu when clicking outside
                        document.addEventListener('click', function(event) {
                            const button = document.getElementById('chart-options-button');
                            const menu = document.getElementById('chart-options-menu');
                            
                            if (!button.contains(event.target) && !menu.contains(event.target)) {
                                menu.classList.add('hidden');
                            }
                        });
                    </script>
                
                    <!-- Chart 2 -->
                    <div class="card rounded-lg border shadow-sm chart-container chart-container-2" data-v0-t="card">
                        <div class="flex justify-between items-start p-4">
                            <div class="flex flex-col">
                                <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight dark:" id="mainLabel">Total tips</h3>
                                <p class="text-sm text-muted-foreground mt-1" id="label">Progress bar representation</p>
                            </div>
                            <div class="flex space-x-2">
                                <button style="margin-top: 2px;" class="uk-button uk-button-default" type="button" id="button" onclick="toggleDropdown()">Tips</button>
                                <div class="uk-drop uk-dropdown" id="dropdown" uk-dropdown="mode: click">
                                    <ul class="uk-dropdown-nav uk-nav">
                                        <li><a href="#" onclick="changeLabel('Tips', 'Total tips'); toggleDropdown()">Tips</a></li>
                                        <li><a href="#" onclick="changeLabel('Reviews', 'Total reviews'); toggleDropdown()">Reviews</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <hr class="my-1">
                        <div class="p-4 relative min-h-[280px]">
                            <canvas id="TotalTipsChart" style="width: 100%; height: 100%;"></canvas>
                        </div>
                    </div>
                    
                    <script>
                        let isDropdownOpen = false;
                    
                        function toggleDropdown() {
                            const dropdown = document.getElementById('dropdown');
                            if (isDropdownOpen) {
                                dropdown.classList.remove('uk-open');
                            } else {
                                dropdown.classList.add('uk-open');
                            }
                            isDropdownOpen = !isDropdownOpen;
                        }
                    
                        function changeLabel(label, mainLabel) {
                            document.getElementById('label').textContent = label + ' bar representation';
                            document.getElementById('button').textContent = label;
                            document.getElementById('mainLabel').textContent = mainLabel;
                            toggleDropdown(); // Close the dropdown after clicking an option
                        }
                    </script>
                      
                      <script>
                        async function initializeTotalTipsChart() {
                            // Updated sales data for each day (Monday to Sunday)
                            const salesData = [600, 300, 1200, 2400, 3000, 1800, 1500];
                            
                            const salesChartData = {
                                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                                datasets: [
                                    {
                                        data: salesData,
                                        backgroundColor: '#aabdd1',
                                        borderColor: '#aabdd1',
                                        borderWidth: 1,
                                        borderRadius: 6,
                                        borderSkipped: 'bottom'
                                    }
                                ]
                            };

                            const ctx = document.getElementById('TotalTipsChart').getContext('2d');
                            const totalTipsChart = new Chart(ctx, {
                                type: 'bar',
                                data: salesChartData,
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    layout: {
                                        padding: {
                                            bottom: 20,
                                            top: 20
                                        }
                                    },
                                    barPercentage: 1,
                                    categoryPercentage: 0.7,
                                    scales: {
                                        x: {
                                            grid: {
                                                display: false,
                                                color: 'transparent'
                                            }
                                        },
                                        y: {
                                            beginAtZero: true,
                                            min: 0,
                                            max: 3000, // Updated max value
                                            grid: {
                                                color: 'rgba(0, 0, 0, 0.1)',
                                                borderDash: [5, 5]
                                            },
                                            ticks: {
                                                stepSize: 600, // Adjust step size as needed
                                                callback: function(value) {
                                                    return '' + value; // Display values in euros
                                                }
                                            }
                                        }
                                    },
                                    plugins: {
                                        tooltip: {
                                            backgroundColor: '#151519',
                                            titleColor: '#ffffff',
                                            bodyColor: '#ffffff',
                                            callbacks: {
                                                label: function(context) {
                                                    return '' + context.raw; // Tooltip values in euros
                                                }
                                            }
                                        },
                                        legend: {
                                            display: false
                                        }
                                    }
                                }
                            });
                        }

                        document.addEventListener('DOMContentLoaded', function() {
                            initializeTotalTipsChart();
                        });
                    </script>
                    
                    <!-- Chart 3 -->
                    <div class="card rounded-lg border shadow-sm chart-container chart-container-3" data-v0-t="card">
                        <div class="flex justify-between items-start p-4">
                            <div class="flex flex-col">
                                <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">Automated conversations</h3>
                                <p class="text-sm text-muted-foreground mt-1">AI vs staff (ratio representation)</p>
                            </div>
                            <a href="/settings">
                                <button  style="margin-top: 2px;" class="uk-button uk-button-default">
                                    <svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 5px;" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-left"><path d="M7 17V7h10"/><path d="M17 17 7 7"/></svg>
                                    Config
                                </button>
                            </a>
                        </div>
                        <hr class="my-1"> <!-- Adjusted margin to move the divider up -->
                        <div class="p-4 relative min-h-[260px] md:h-[260px]">
                            <div class="p-4 pt-0 h-full flex flex-col relative">
                                <div class="relative w-11/12 mx-auto h-48 flex items-center justify-center mb-6">
                                    <svg viewBox="0 0 200 100" class="w-full h-full">
                                        <!-- Grey styling circle -->
                                        <path d="M10 100 A 90 90 0 0 1 190 100" fill="none" stroke="#F3F4F6" stroke-width="6"/>
                                        
                                        <!-- Main half circles -->
                                        <path d="M20 100 A 80 80 0 0 1 180 100" fill="none" stroke="#ABBDD3" stroke-width="5" class="transition-all duration-300 hover:filter hover:brightness-105" filter="url(#shadow)"/>
                                        <path d="M20 100 A 80 80 0 0 1 180 100" fill="none" stroke="#5347ce" stroke-width="5" stroke-dasharray="188 251" class="transition-all duration-300 hover:filter hover:brightness-105" filter="url(#shadow)"/>
                                        
                                        <!-- Define shadow filter -->
                                        <defs>
                                            <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                                                <feDropShadow dx="0" dy="1" stdDeviation="2" flood-opacity="0.1"/>
                                            </filter>
                                        </defs>
                                    </svg>
                                    <div class="absolute inset-0 flex flex-col items-center justify-center" style="padding-top: 70px;">
                                        <div class="rounded-full p-1.5 mb-3 border border-gray-200">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-blend"><circle cx="9" cy="9" r="7"/><circle cx="15" cy="15" r="7"/></svg>
                                        </div>
                                        <span class="text-lg font-semibold ">2,324</span>
                                        <p class="text-xs  mt-1">Total conversations</p>
                                    </div>
                                </div>
                                <div class="flex justify-between w-full">
                                    <div class="flex items-center">
                                        <div class="w-1 h-10 bg-[#5347ce] rounded-full mr-2"></div>
                                        <div>
                                            <span class="text-sm font-semibold text-gray-800">1,809</span>
                                            <p class="text-xs text-gray-500">AI Responses</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-end">
                                        <div class="text-right mr-2">
                                            <span class="text-sm font-semibold text-gray-800">314</span>
                                            <p class="text-xs text-gray-500">Manual Responses</p>
                                        </div>
                                        <div class="w-1 h-10 bg-[#ABBDD3] rounded-full"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <style>
                        @keyframes fillAnimation {
                            0% { stroke-dasharray: 0 251; }
                            100% { stroke-dasharray: 188 251; }
                        }
                        svg path:nth-child(3) {
                            animation: fillAnimation 1.5s ease-out forwards;
                        }
                    </style>
                    <script>
                        am4core.ready(function() {
                            am4core.useTheme(am4themes_animated);
                    
                            var currentAIChart;
                            var aiChartType = "pie";
                    
                            var data = [
                                { category: "AI Answered", value: 65, color: am4core.color("#85a9d1") },
                                { category: "Manually Answered", value: 35, color: am4core.color("#18181b") }
                            ];
                    
                            function createAIPieChart() {
                                var chart = am4core.create("questionResponsesChart", am4charts.PieChart);
                                chart.radius = am4core.percent(65); // Adjusted to 70%
                    
                                chart.data = data;
                    
                                var pieSeries = chart.series.push(new am4charts.PieSeries());
                                pieSeries.dataFields.value = "value";
                                pieSeries.dataFields.category = "category";
                                pieSeries.slices.template.propertyFields.fill = "color";
                    
                                chart.logo.disabled = true;
                                chart.innerRadius = am4core.percent(30);
                    
                                pieSeries.slices.template.padding = 1;
                                pieSeries.slices.template.cornerRadius = 5;
                                pieSeries.slices.template.fillOpacity = 0.8;
                                pieSeries.slices.template.strokeWidth = 2;
                                pieSeries.slices.template.stroke = am4core.color("#ffffff");
                    
                                pieSeries.labels.template.disabled = false;
                                pieSeries.labels.template.text = "{category}: {value}%";
                                pieSeries.labels.template.radius = 1;
                                pieSeries.labels.template.fontSize = 12;
                                pieSeries.labels.template.maxWidth = 80;
                                pieSeries.labels.template.wrap = true;
                    
                                pieSeries.ticks.template.disabled = false;
                                pieSeries.ticks.template.strokeOpacity = 0.7;
                                pieSeries.ticks.template.strokeWidth = 2; // Increased stroke width
                                pieSeries.ticks.template.length = 25; // Increased length
                    
                                pieSeries.slices.template.tooltipText = "{category}: {value}%";
                    
                                return chart;
                            }
                    
                            function updateAIChartColors() {
                                var body = document.body;
                                var isDarkTheme = body.classList.contains('pure-black') || 
                                                  body.classList.contains('dark-gray') || 
                                                  body.classList.contains('navy-blue') ||
                                                  body.classList.contains('cool-blue') ||
                                                  body.classList.contains('deep-burgundy') ||
                                                  body.classList.contains('charcoal');
                    
                                if (currentAIChart.series.getIndex(0) instanceof am4charts.PieSeries) {
                                    var pieSeries = currentAIChart.series.getIndex(0);
                                    pieSeries.labels.template.fill = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
                                    pieSeries.ticks.template.stroke = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
                                }
                            }
                    
                            function createAIChart() {
                                if (currentAIChart) {
                                    currentAIChart.dispose();
                                }
                    
                                currentAIChart = createAIPieChart();
                                updateAIChartColors();
                            }
                    
                            // Initial chart creation
                            createAIChart();
                    
                            // Listen for theme changes
                            var observer = new MutationObserver(function(mutations) {
                                mutations.forEach(function(mutation) {
                                    if (mutation.type === "attributes" && mutation.attributeName === "class") {
                                        updateAIChartColors();
                                    }
                                });
                            });
                    
                            observer.observe(document.body, {
                                attributes: true
                            });
                        });
                    </script>
                    
                    
                    <script>
                        async function fetchTPPData() {
                            try {
                                const response = await fetch('/fetch-tpp');
                                if (!response.ok) {
                                    throw new Error(`HTTP error! Status: ${response.status}`);
                                }
                                const data = await response.json();
                                return data[0]; // Assuming the data is an array and we need the first object
                            } catch (error) {
                                console.error('Error fetching TPP data:', error);
                                return null;
                            }
                        }
                    
                        function updatePieChart(chart, data) {
                            chart.data = [
                                {
                                    product: "Food",
                                    sales: parseFloat(data.food_amount),
                                    color: am4core.color("#5347ce")
                                },
                                {
                                    product: "Beverage",
                                    sales: parseFloat(data.beverage_amount),
                                    color: am4core.color("#887cfd")
                                },
                                {
                                    product: "Spa",
                                    sales: parseFloat(data.spa_sales),
                                    color: am4core.color("#4896fe")
                                },
                                {
                                    product: "Massage",
                                    sales: parseFloat(data.massage_sales),
                                    color: am4core.color("#16c8c7")
                                },
                                {
                                    product: "Room Bookings",
                                    sales: parseFloat(data.room_sales),
                                    color: am4core.color("#abbbd3")
                                }
                            ];
                        }
                    
                        am4core.ready(async function() {
                            // Create chart instance
                            var chart = am4core.create("chartdiv", am4charts.PieChart);
                            chart.radius = am4core.percent(60); // Adjust this percentage as needed
                    
                            // Fetch data and update chart
                            const tppData = await fetchTPPData();
                            if (tppData) {
                                updatePieChart(chart, tppData);
                            }
                    
                            // Add and configure Series
                            var pieSeries = chart.series.push(new am4charts.PieSeries());
                            pieSeries.dataFields.value = "sales";
                            pieSeries.dataFields.category = "product";
                            pieSeries.slices.template.propertyFields.fill = "color";
                    
                            // Disable logo
                            chart.logo.disabled = true;
                    
                            // Set inner radius for donut shape
                            chart.innerRadius = am4core.percent(30);
                    
                            // Add spacing between slices and make edges curved
                            pieSeries.slices.template.padding = 1;
                            pieSeries.slices.template.cornerRadius = 5;
                            pieSeries.slices.template.fillOpacity = 0.8;
                            pieSeries.slices.template.strokeWidth = 2;
                            pieSeries.slices.template.stroke = am4core.color("#ffffff");
                    
                            // Configure labels
                            pieSeries.labels.template.disabled = false;
                            pieSeries.labels.template.text = "{category}: €{value}";
                            pieSeries.labels.template.radius = 1;
                            pieSeries.labels.template.fontSize = 12;
                            pieSeries.labels.template.maxWidth = 80;
                            pieSeries.labels.template.wrap = true;
                    
                            // Configure ticks
                            pieSeries.ticks.template.disabled = false;
                            pieSeries.ticks.template.strokeOpacity = 0.7;
                            pieSeries.ticks.template.strokeWidth = 2;
                            pieSeries.ticks.template.length = 20; // Default length for all lines
                    
                            // Adjust the length for the "Spa" label
                            pieSeries.ticks.template.adapter.add("length", function(length, target) {
                                if (target.dataItem && target.dataItem.category === "Spa") {
                                    return 5; // Adjusted length for the "Spa" line
                                }
                                return length;
                            });
                    
                            // Add tooltips
                            pieSeries.slices.template.tooltipText = "{category}: €{value}";
                    
                            // Function to update chart colors based on theme
                            function updateChartColors() {
                                var body = document.body;
                                var isDarkTheme = body.classList.contains('pure-black') || 
                                                  body.classList.contains('dark-gray') || 
                                                  body.classList.contains('navy-blue') ||
                                                  body.classList.contains('cool-blue') ||
                                                  body.classList.contains('deep-burgundy') ||
                                                  body.classList.contains('charcoal');
                    
                                // Update text color for labels and ticks
                                pieSeries.labels.template.fill = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
                                pieSeries.ticks.template.stroke = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
                            }
                    
                            // Initial color update
                            updateChartColors();
                    
                            // Listen for theme changes
                            var observer = new MutationObserver(function(mutations) {
                                mutations.forEach(function(mutation) {
                                    if (mutation.type === "attributes" && mutation.attributeName === "class") {
                                        updateChartColors();
                                    }
                                });
                            });
                    
                            observer.observe(document.body, {
                                attributes: true
                            });
                        });
                    
                    
                    </script>

                
                <script src="https://cdn.amcharts.com/lib/4/core.js"></script>
                <script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
                <script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>

                                </main>
                            </div>
                <script>
                function fetchCustomers() {
                    fetch('/customers')
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Network response was not ok');
                            }
                            return response.json();
                        })
                        .then(data => {
                            const tableBody = document.getElementById('customerTableBody');
                            tableBody.innerHTML = ''; // Clear existing rows
            
                            // Sort data in reverse order (latest first)
                            data.sort((a, b) => b.id - a.id);
            
                            data.forEach(customer => {
                                const row = document.createElement('tr');
                                row.className = 'border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted';
            
                                row.innerHTML = `
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0 font-medium">${customer.Name}</td>
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.room_number}</td>
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.phone_number}</td>
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.language}</td>
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.order}</td>
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.Platform}</td>
                                    <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0 text-right">${customer.spend}</td>
                                `;
            
                                tableBody.appendChild(row);
                            });
                        })
                        .catch(error => {
                            console.error('Error fetching customers:', error);
                        });
                }
            
                // Initial fetch
                fetchCustomers();
            
                // Refresh every 5 seconds
                setInterval(fetchCustomers, 60000);
            
                async function fetchAIvsManualData() {
                    try {
                        const response = await fetch('/aivsmanual');
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        const data = await response.json();
                        return data;
                    } catch (error) {
                        console.error('Error fetching AI vs Manual data:', error);
                        return null;
                    }
                }

                function calculateStepSize(maxValue) {
                    return Math.ceil(maxValue / 5);
                }

                async function updateChart(chart) {
                    const interactionData = await fetchAIvsManualData();
                    if (!interactionData) return;

                    const aiData = interactionData.ai_inter[0];
                    const manualData = interactionData.manual_inter[0];

                    const aiInteractionsData = [
                        aiData.mon, aiData.tue, aiData.wed, aiData.thu, 
                        aiData.fri, aiData.sat, aiData.sun
                    ];

                    const manualInteractionsData = [
                        manualData.mon, manualData.tue, manualData.wed, manualData.thu, 
                        manualData.fri, manualData.sat, manualData.sun
                    ];

                    const maxDataValue = Math.max(...aiInteractionsData, ...manualInteractionsData);
                    const stepSize = calculateStepSize(maxDataValue);

                    chart.data.datasets[0].data = aiInteractionsData;
                    chart.data.datasets[1].data = manualInteractionsData;
                    chart.options.scales.y.ticks.stepSize = stepSize;
                    chart.update();
                }


                async function fetchData(url) {
                    try {
                        const response = await fetch(url);
                        const data = await response.json();
                        return data;
                    } catch (error) {
                        console.error('Error fetching data:', error);
                        return [];
                    }
                }

            
    function updatePlatforms(platforms) {
        const container = document.getElementById('platforms-container');
        container.innerHTML = '';

        const platformItems = [
            {
                name: 'WhatsApp',
                icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 "><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>`,
                bgColor: '#25D366',
                customers: platforms.whatsapp_amount,
                sales: platforms.whatsapp_sales
            },
            {
                name: 'Facebook Messenger',
                icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 "><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1                     1 0 0 1 1-1h3z"></path></svg>`,
            bgColor: '#1877F2',
            customers: platforms.messenger_users,
            sales: platforms.messenger_sales
        },
        {
            name: 'Instagram',
            icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 "><rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line></svg>`,
            bgColor: '#E1306C',
            customers: platforms.instagram_users,
            sales: platforms.instagram_sales
        },
        {
            name: 'Voice',
            icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 "><path d="M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3"></path></svg>`,
            bgColor: '#4A5568',
            customers: platforms.voice_users,
            sales: platforms.voice_sales
        },
        {
            name: 'Web chat',
            icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-6 h-6 "><path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg>`,
            bgColor: '#6B46C1',
            customers: platforms.web_users,
            sales: platforms.web_sales
        }
    ];

    platformItems.forEach((platform, index) => {
        const platformHtml = `
            <div class="flex items-start gap-4">
                <div class="rounded-lg w-12 h-12 bg-[${platform.bgColor}] text-3xl flex items-center justify-center platform-icon">
                    ${platform.icon}
                </div>
                <div class="flex-1">
                    <p class="font-medium">${platform.name}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">No. of sales: ${platform.sales}</p>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">$${platform.sales}</div>
            </div>
            ${index < platformItems.length - 1 ? '<div class="divider"></div>' : ''}
        `;
        container.insertAdjacentHTML('beforeend', platformHtml);
    });
}

async function updateProducts(products) {
    const container = document.getElementById('products-container');
    container.innerHTML = '';

    const productItems = [
        { name: 'Food', emoji: '🍔', amount: products.food_amount, sales: products.food_sales, customers: products.food_cus, bgColor: '#55efc4' },
        { name: 'Beverage', emoji: '🍹', amount: products.beverage_amount, sales: products.beverage_sales, customers: products.beverage__cus, bgColor: '#ffeaa7' },
        { name: 'Spa', emoji: '🛀', amount: products.spa_bookings, sales: products.spa_sales, customers: products.spa_cus, bgColor: '#fdcb6e' },
        { name: 'Massage', emoji: '💆‍♀️', amount: products.massage_amount, sales: products.massage_sales, customers: products.massage_cus, bgColor: '#74b9ff' },
        { name: 'Room Bookings', emoji: '🛏️', amount: products.room_bookings, sales: products.room_sales, customers: products.room__cus, bgColor: '#5347ce' }
    ];

    productItems.forEach((product, index) => {
        const productHtml = `
            <div class="flex items-start gap-4">
                <div class="rounded-lg w-12 h-12 text-3xl flex items-center justify-center icon" style="background-color: ${product.bgColor};">${product.emoji}</div>
                <div class="flex-1">
                    <p class="font-medium">${product.name}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">No. of Sales: ${product.amount}</p>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">€${product.sales}</div>
            </div>
            ${index < productItems.length - 1 ? '<div class="divider"></div>' : ''}
        `;
        container.insertAdjacentHTML('beforeend', productHtml);
    });
}

let totalSalesElement = document.getElementById('total-sales');
let totalTipsElement = document.getElementById('total-tips');
let totalCustomersElement = document.getElementById('total-customers');

function updateFirstRowSales(data) {
    if (data && data.length > 0) {
        const firstRow = data[0];
        totalSalesElement.textContent = firstRow.total_sales;
        totalTipsElement.textContent = `+${firstRow.total_tips}`;
        totalCustomersElement.textContent = `+${firstRow.total_reviews}`;
        document.getElementById('total-messages').textContent = `+${firstRow.total_messages || 0}`;
        document.getElementById('total-bookings').textContent = `+${firstRow.total_bookings || 0}`;
    } else {
        totalSalesElement.textContent = '0';
        totalTipsElement.textContent = '+0';
        totalCustomersElement.textContent = '+0';
        document.getElementById('total-messages').textContent = '+0';
        document.getElementById('total-bookings').textContent = '+0';
    }
}

async function fetchFirstRowData() {
    try {
        const response = await fetch('/firstrowana');
        const data = await response.json();
        updateFirstRowSales(data);
    } catch (error) {
        console.error('Error fetching first row sales data:', error);
        updateFirstRowSales([]);
    }
}

// Initial fetch
fetchFirstRowData();

// Refresh every 5 seconds
setInterval(fetchFirstRowData, 60000);

async function init() {
    const platforms = await fetchData('/fetch-platforms');
    if (platforms.length > 0) {
        updatePlatforms(platforms[0]);
    }

    const products = await fetchData('/fetch-tpp');
    if (products.length > 0) {
        updateProducts(products[0]);
    }

    await updateFirstRowSales();
}

// Initial call
init();

// Refresh data every 5 seconds
setInterval(init, 60000);
                </script>


    </div>
    <script>
        async function initializeChart() {
            const interactionData = await fetchAIvsManualData();
            if (!interactionData) return;
    
            const aiData = interactionData.ai_inter[0];
            const manualData = interactionData.manual_inter[0];
    
            const aiInteractionsData = [
                aiData.mon, aiData.tue, aiData.wed, aiData.thu, 
                aiData.fri, aiData.sat, aiData.sun
            ];
    
            const manualInteractionsData = [
                manualData.mon, manualData.tue, manualData.wed, manualData.thu, 
                manualData.fri, manualData.sat, manualData.sun
            ];
    
            const maxDataValue = Math.max(...aiInteractionsData, ...manualInteractionsData);
            const stepSize = calculateStepSize(maxDataValue);
    
            const interactionChartData = {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [
                    {
                        label: 'AI help (Automated)',
                        data: aiInteractionsData,
                        backgroundColor: '#151519', // Solid color
                        borderRadius: {
                            topLeft: 6,
                            topRight: 6,
                            bottomLeft: 0,
                            bottomRight: 0
                        },
                        borderSkipped: 'bottom'
                    },
                    {
                        label: 'Staff help (manual)',
                        data: manualInteractionsData,
                        backgroundColor: '#abbbd3', // Solid color, no gradient
                        borderRadius: {
                            topLeft: 6,
                            topRight: 6,
                            bottomLeft: 0,
                            bottomRight: 0
                        },
                        borderSkipped: 'bottom'
                    }
                ]
            };
    
            const ctx = document.getElementById('salesOverviewChart').getContext('2d');
            const interactionChart = new Chart(ctx, {
                type: 'bar',
                data: interactionChartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    barPercentage: 1,
                    categoryPercentage: 0.7,
                    scales: {
                        x: {
                            grid: {
                                display: false,
                                color: 'transparent'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)',
                                borderDash: [5, 5]
                            },
                            ticks: {
                                stepSize: stepSize
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(200, 200, 200, 0.3)',
                            titleColor: '#000',
                            bodyColor: '#000'
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });
    
            // Set interval to update the chart every 5 seconds
            setInterval(() => {
                updateChart(interactionChart);
            }, 60000);
        }
    
        document.addEventListener('DOMContentLoaded', initializeChart);
    </script>
</div>
</body>
</html>