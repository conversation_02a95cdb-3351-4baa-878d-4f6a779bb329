document.addEventListener('DOMContentLoaded', function() {
    // Model Selector
    const modelSelector = document.querySelector('.model-selector');
    const modelBtn = modelSelector.querySelector('.model-selector-btn');
    const modelMenu = modelSelector.querySelector('.model-selector-menu');
    const selectedModelSpan = modelSelector.querySelector('.selected-model');

    modelBtn.addEventListener('click', function() {
        modelMenu.style.display = modelMenu.style.display === 'block' ? 'none' : 'block';
    });

    document.addEventListener('click', function(event) {
        if (!modelSelector.contains(event.target)) {
            modelMenu.style.display = 'none';
        }
    });

    modelMenu.querySelectorAll('.model-option').forEach(option => {
        option.addEventListener('click', function() {
            selectedModelSpan.textContent = this.textContent;
            modelMenu.style.display = 'none';
            // Here you can add code to update the selected model in your application
            console.log('Selected model:', this.dataset.model);
        });
    });

    // Temperature Selector
    const temperatureInput = document.querySelector('#temperature');
    const temperatureOutput = document.querySelector('output[for="temperature"]');

    temperatureInput.addEventListener('input', function() {
        temperatureOutput.textContent = this.value;
        // Here you can add code to update the temperature in your application
        console.log('Temperature set to:', this.value);
    });
// Knowledge Base Buttons
    const uploadKbBtn = document.getElementById('upload-kb-btn');
    const editKbBtn = document.getElementById('edit-kb-btn');
    const uploadKbInput = document.getElementById('upload-kb-input');
    const knowledgeBaseEditModal = document.getElementById('knowledge-base-edit-modal');

    if (uploadKbBtn && uploadKbInput) {
        uploadKbBtn.addEventListener('click', function() {
            uploadKbInput.click();
        });
    }

    if (editKbBtn && knowledgeBaseEditModal) {
        editKbBtn.addEventListener('click', function() {
            UIkit.modal(knowledgeBaseEditModal).show();
        });
    }
});


document.addEventListener('DOMContentLoaded', function() {
    const editableFields = document.querySelectorAll('#username, #bio');
    const editIcons = document.querySelectorAll('.uk-position-absolute');
  
    editIcons.forEach((icon, index) => {
      icon.addEventListener('click', function() {
        const field = editableFields[index];
        field.readOnly = !field.readOnly;
        field.classList.toggle('greyed-out-input');
        if (!field.readOnly) {
          field.focus();
        }
      });
    });
  });


  document.addEventListener('DOMContentLoaded', function() {
    const addCustomLinkBtn = document.getElementById('addCustomLink');
    const customLinksContainer = document.getElementById('customLinksContainer');

    addCustomLinkBtn.addEventListener('click', function() {
        const newLinkDiv = document.createElement('div');
        newLinkDiv.className = 'uk-flex uk-flex-middle gap-2 uk-margin-top';
        newLinkDiv.innerHTML = `
            <div class="uk-width-auto" style="width: 200px;">
                <input class="uk-input" type="text" placeholder="Enter link name" style="height: 40px; width: 100%;">
            </div>
            <input class="uk-input uk-width-expand" type="text" placeholder="Enter URL" style="height: 40px;">
            <button class="uk-button uk-button-danger uk-button-small remove-link">Remove</button>
        `;
        customLinksContainer.appendChild(newLinkDiv);

        // Add event listener to the remove button
        newLinkDiv.querySelector('.remove-link').addEventListener('click', function() {
            customLinksContainer.removeChild(newLinkDiv);
        });
    });

    // Adjust existing custom links
    const existingNameInputs = customLinksContainer.querySelectorAll('input[placeholder="Enter link name"]');
    existingNameInputs.forEach(input => {
        const parentDiv = input.parentElement;
        if (!parentDiv.classList.contains('uk-width-auto')) {
            const wrapperDiv = document.createElement('div');
            wrapperDiv.className = 'uk-width-auto';
            wrapperDiv.style.width = '200px';
            parentDiv.insertBefore(wrapperDiv, input);
            wrapperDiv.appendChild(input);
        }
        input.style.width = '100%';
    });
});