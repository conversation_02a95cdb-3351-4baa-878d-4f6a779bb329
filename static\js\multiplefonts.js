// Fonts are currently disabled for the v1 of the dashboard, enable it later ---

function changeFontFamily(selectedFont) {
    document.body.style.fontFamily = selectedFont + ", sans-serif";
    localStorage.setItem('preferredFont', selectedFont);
    updateVisualSelection(selectedFont);
}

function updateVisualSelection(selectedFont) {
    document.querySelectorAll('.font-option').forEach(option => {
        if (option.getAttribute('data-font') === selectedFont) {
            option.classList.add('ring-2', 'ring-primary');
        } else {
            option.classList.remove('ring-2', 'ring-primary');
        }
    });
}

function loadFontPreference() {
    const storedFont = localStorage.getItem('preferredFont');
    if (storedFont) {
        changeFontFamily(storedFont);
    } else {
        // Set Geist as the default font if no preference is stored
        changeFontFamily('Inter');
    }
}

// Apply the font preference on page load
document.addEventListener('DOMContentLoaded', () => {
    loadFontPreference();
    setupFontSelectors();
});

function setupFontSelectors() {
    if (document.querySelector('.font-option')) {
        document.querySelectorAll('.font-option').forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const selectedFont = this.getAttribute('data-font');
                changeFontFamily(selectedFont);
            });
        });
    }
}

// Expose a global function to change font
window.changeFont = changeFontFamily;