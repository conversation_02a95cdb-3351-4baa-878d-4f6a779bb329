<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animated Modal Demo</title>
    <style>
        /* Base styles */
        body {
            margin: 0;
            font-family: system-ui, -apple-system, sans-serif;
            background: white;
            min-height: 100vh;
        }

        .container {
            padding: 160px 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* Modal trigger styles */
        .modal-trigger {
            position: relative;
            background: black;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            overflow: hidden;
            transition: background 0.3s;
        }

        .modal-trigger span {
            display: inline-block;
            transition: transform 0.5s;
        }

        .modal-trigger .emoji {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            transform: translateX(-160%);
            transition: transform 0.5s;
            z-index: 20;
        }

        .modal-trigger:hover span {
            transform: translateX(160%);
        }

        .modal-trigger:hover .emoji {
            transform: translateX(0);
        }

        /* Modal styles */
        .modal {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            display: none;
            justify-content: center;
            align-items: center;
        }

        .modal:target {
            display: flex;
        }

        .modal-content {
            background: white;
            padding: 24px;
            border-radius: 16px;
            max-width: 600px;
            width: 90%;
            margin: 20px;
        }

        .modal-header {
            font-size: 1.5rem;
            font-weight: bold;
            color: #4b5563;
            text-align: center;
            margin-bottom: 32px;
        }

        .badge {
            background: #f3f4f6;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 2px 8px;
            display: inline-block;
        }

        /* Image gallery styles */
        .image-container {
            display: flex;
            justify-content: center;
            margin: 16px -16px 0;
        }

        .image-wrapper {
            transform: rotate(calc(var(--rotation) * 1deg));
            margin-right: -16px;
            transition: transform 0.3s;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 4px;
            flex-shrink: 0;
        }

        .image-wrapper:hover {
            transform: rotate(0) scale(1.1);
            z-index: 100;
        }

        .image-wrapper img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
        }

        /* Features grid */
        .features {
            display: flex;
            flex-wrap: wrap;
            gap: 24px 16px;
            justify-content: center;
            max-width: 400px;
            margin: 40px auto;
            padding: 10px 0;
        }

        .feature-item {
            display: flex;
            align-items: center;
            font-size: 0.875rem;
            color: #374151;
        }

        .feature-item svg {
            width: 16px;
            height: 16px;
            margin-right: 4px;
            stroke: currentColor;
        }

        /* Footer buttons */
        .modal-footer {
            display: flex;
            gap: 16px;
            justify-content: center;
            padding-top: 16px;
        }

        .modal-footer button {
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid #d1d5db;
            background: #f3f4f6;
            cursor: pointer;
            font-size: 0.875rem;
        }

        .modal-footer button:last-child {
            background: black;
            color: white;
            border-color: black;
        }

        /* Responsive adjustments */
        @media (min-width: 768px) {
            .modal-header {
                font-size: 2rem;
            }
            
            .image-wrapper img {
                width: 160px;
                height: 160px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="#modal" class="modal-trigger">
            <span>Book your flight</span>
            <div class="emoji">✈️</div>
        </a>
    </div>

    <div id="modal" class="modal">
        <div class="modal-content">
            <h4 class="modal-header">
                Book your trip to <span class="badge">Bali</span> now! ✈️
            </h4>
            
            <div class="image-container">
                <!-- Add inline rotation values -->
                <div class="image-wrapper" style="--rotation: 5">
                    <img src="https://images.unsplash.com/photo-1517322048670-4fba75cbbb62" alt="Bali">
                </div>
                <div class="image-wrapper" style="--rotation: -7">
                    <img src="https://images.unsplash.com/photo-1573790387438-4da905039392" alt="Bali">
                </div>
                <div class="image-wrapper" style="--rotation: 3">
                    <img src="https://images.unsplash.com/photo-1555400038-63f5ba517a47" alt="Bali">
                </div>
                <div class="image-wrapper" style="--rotation: -2">
                    <img src="https://images.unsplash.com/photo-1554931670-4ebfabf6e7a9" alt="Bali">
                </div>
                <div class="image-wrapper" style="--rotation: 8">
                    <img src="https://images.unsplash.com/photo-1546484475-7f7bd55792da" alt="Bali">
                </div>
            </div>

            <div class="features">
                <!-- Feature items with SVG icons -->
                <div class="feature-item">
                    <svg viewBox="0 0 24 24"><path d="M16 10h4a2 2 0 0 1 0 4h-4l-4 7h-3l2 -7h-4l-2 2h-3l2 -4l-2 -4h3l2 2h4l-2 -7h3z"/></svg>
                    5 connecting flights
                </div>
                <div class="feature-item">
                    <svg viewBox="0 0 24 24"><path d="M5 4m0 1a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-12a1 1 0 0 1 -1 -1z"/><path d="M10 10l2 -2l2 2"/><path d="M10 14l2 2l2 -2"/></svg>
                    12 hotels
                </div>
                <div class="feature-item">
                    <svg viewBox="0 0 24 24"><path d="M17.553 16.75a7.5 7.5 0 0 0 -10.606 0"/><path d="M18 3.804a6 6 0 0 0 -8.196 2.196l10.392 6a6 6 0 0 0 -2.196 -8.196z"/><path d="M16.732 10c1.658 -2.87 2.225 -5.644 1.268 -6.196c-.957 -.552 -3.075 1.326 -4.732 4.196"/><path d="M15 9l-3 5.196"/><path d="M3 19.25a2.4 2.4 0 0 1 1 -.25a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 1 .25"/></svg>
                    69 visiting spots
                </div>
                <div class="feature-item">
                    <svg viewBox="0 0 24 24"><path d="M20 20c0 -3.952 -.966 -16 -4.038 -16s-3.962 9.087 -3.962 14.756c0 -5.669 -.896 -14.756 -3.962 -14.756c-3.065 0 -4.038 12.048 -4.038 16"/></svg>
                    Good food everyday
                </div>
                <div class="feature-item">
                    <svg viewBox="0 0 24 24"><path d="M15 12.9a5 5 0 1 0 -3.902 -3.9"/><path d="M15 12.9l-3.902 -3.899l-7.513 8.584a2 2 0 1 0 2.827 2.83l8.588 -7.515z"/></svg>
                    Open Mic
                </div>
                <div class="feature-item">
                    <svg viewBox="0 0 24 24"><path d="M22 12a10 10 0 1 0 -20 0"/><path d="M22 12c0 -1.66 -1.46 -3 -3.25 -3c-1.8 0 -3.25 1.34 -3.25 3c0 -1.66 -1.57 -3 -3.5 -3s-3.5 1.34 -3.5 3c0 -1.66 -1.46 -3 -3.25 -3c-1.8 0 -3.25 1.34 -3.25 3"/><path d="M2 12l10 10l-3.5 -10"/><path d="M15.5 12l-3.5 10l10 -10"/></svg>
                    Paragliding
                </div>
            </div>

            <div class="modal-footer">
                <a href="#" class="button">Cancel</a>
                <button class="button">Book Now</button>
            </div>
        </div>
    </div>
</body>
</html>