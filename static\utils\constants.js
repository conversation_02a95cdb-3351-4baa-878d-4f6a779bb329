// API Routes for PMS Configuration
console.log("Constants.js loaded at:", new Date().toLocaleTimeString());

const API_ROUTES = {
    PMS_CONFIG: {
        SAVE: '/api/pms/config/save',
        LOAD: '/api/pms/config/load'
    },
    // Add other API routes categories here as needed
    AUTH: {
        LOGIN: '/api/auth/login',
        LOGOUT: '/api/auth/logout'
    },
    SETTINGS: {
        LANGUAGE: '/api/settings/language',
        THEME: '/api/settings/theme',
        GENERAL: {
            SAVE: '/api/settings/general/save',
            LOAD: '/api/settings/general/load'
        },
        NOTIFICATIONS: {
            SAVE: '/api/settings/notifications/save',
            LOAD: '/api/settings/notifications/load'
        },
        STAFF: {
            SAVE: '/api/settings/staff/save',
            LOAD: '/api/settings/staff/load'
        }
    }
};

// For browser usage
window.API_ROUTES = API_ROUTES;
console.log("API_ROUTES initialized:", API_ROUTES);