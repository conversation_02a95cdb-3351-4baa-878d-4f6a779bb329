from flask import Flask, request, jsonify
from twilio.twiml.messaging_response import MessagingResponse
from twilio.rest import Client
import os
from dotenv import load_dotenv
import json
import re
from time import sleep
from prompt import system_prompt  # Import the system prompt
from groq import Groq  # Import Groq for AI responses
from datetime import datetime

# Path to the chats.json file
CHATS_FILE_PATH = 'chats.json'

def load_chats():
    """Load chats from the JSON file."""
    if not os.path.exists(CHATS_FILE_PATH):
        with open(CHATS_FILE_PATH, 'w') as file:
            json.dump({"users": [], "chats": {}}, file, indent=4)
    with open(CHATS_FILE_PATH, 'r') as file:
        return json.load(file)

def save_chats(data):
    """Save chats to the JSON file."""
    with open(CHATS_FILE_PATH, 'w') as file:
        json.dump(data, file, indent=4)

# Load environment variables from the .env file
load_dotenv()

# Create Flask app
app = Flask(__name__)

# Twilio account credentials from the .env file
account_sid = os.getenv('ACCOUNT_SID')
auth_token = os.getenv('AUTH_TOKEN')
client = Client(account_sid, auth_token)

# WhatsApp numbers from the .env file
from_whatsapp_number = os.getenv('FROM_WHATSAPP_NUMBER')

# Path to the users.json file
USERS_FILE_PATH = 'users.json'

# Simple messages to send to users
WELCOME_MESSAGE = "Welcome to Can Marques! How may I assist you today?"
REQUEST_ROOM_MESSAGE = (
    "Hello, welcome to Can Marques!\nPlease enter your room number before proceeding."
)
ROOM_NUMBER_SAVED_MESSAGE = "Your room number has been saved. How may I assist you today?"

SPA_BOOKING_OPTION_SELECTOR_SID=""

FOOD_ALLERGY_MESSAGE = "Food allergies option selected "

# Content SID for the starter option selector
STARTER_OPTION_SELECTOR_SID = "HX885693aec6843ed0a49042e1b252685f"

FOOD_ORDER_CONIFORMATION = "Good choice. Your order has been registered.\nPlease make the payment here: https://guestgenius.es/foodorder. \nYour dish will be delivered shortly once you complete the payment."
BEVERAGE_ORDER_CONIFORMATION = "Good choice. Your order has been coniformed. Estimated time is 15-20 minutes."
MASSAGE_ORDER_CONIFORMATION = "Good choice. Your order has been registered.\nPlease make the payment here: https://guestgenius.es/massageorder. \nYour massage will be scheduled shortly once you complete the payment."
SPA_ORDER_CONIFORMATION = "Good choice. Your order has been registered.\nPlease make the payment here: https://guestgenius.es/spaorder. \nYour spa will be scheduled shortly once you complete the payment."
ROOM_BOOKING_CONIFORMATION = "Good choice. Your order has been registered.\nPlease make the payment here: https://guestgenius.es/roomorder. \nYour room will be reserved shortly once you complete the payment."

# FOOD ALLERGIES MESSAGES --------------------------------------------------
# Import all allergy messages
BURRATA_ALLERGY_MESSAGE = os.getenv('Burrata_allergie_message')
PUMPKIN_ALLERGY_MESSAGE = os.getenv('Roasted_Pumpkin_allergie_message')
LOBSTER_ROLL_ALLERGY_MESSAGE = os.getenv('Lobster_Roll_allergie_message')
SIRLOIN_ALLERGY_MESSAGE = os.getenv('Sirloin_allergie_message')
CHICKEN_ALLERGY_MESSAGE = os.getenv('Cornfed_Chicken_allergie_message')
TBONE_ALLERGY_MESSAGE = os.getenv('Tbone_allergie_message')
STEAK_LOBSTER_ALLERGY_MESSAGE = os.getenv('Steak_Lobster_allergie_message')
SALMON_ALLERGY_MESSAGE = os.getenv('Grilled_Salmon_allergie_message')
# --------------------------------------------------------------------------
# Import all order confirmation messages
BURRATA_ORDER_MESSAGE = os.getenv('Burrata_order_message')
PUMPKIN_ORDER_MESSAGE = os.getenv('Roasted_Pumpkin_order_message')
LOBSTER_ROLL_ORDER_MESSAGE = os.getenv('Lobster_Roll_order_message')
SIRLOIN_ORDER_MESSAGE = os.getenv('Sirloin_order_message')
CHICKEN_ORDER_MESSAGE = os.getenv('Cornfed_Chicken_order_message')
TBONE_ORDER_MESSAGE = os.getenv('Tbone_order_message')
STEAK_LOBSTER_ORDER_MESSAGE = os.getenv('Steak_Lobster_order_message')
SALMON_ORDER_MESSAGE = os.getenv('Salmon_order_message')

# Groq API key from the .env file
GROQ_API_KEY = os.getenv('GROQ_API_KEY')
groq_client = Groq(api_key=GROQ_API_KEY)  # Initialize Groq client

def load_users():
    """Load users from the JSON file."""
    if not os.path.exists(USERS_FILE_PATH):
        with open(USERS_FILE_PATH, 'w') as file:
            json.dump({"guests": []}, file, indent=4)
    with open(USERS_FILE_PATH, 'r') as file:
        return json.load(file)

def save_users(data):
    """Save users to the JSON file."""
    with open(USERS_FILE_PATH, 'w') as file:
        json.dump(data, file, indent=4)

def find_guest(phone_number, users_data):
    """Find a guest by phone number."""
    for guest in users_data.get('guests', []):
        if guest.get('phone_number') == phone_number:
            return guest
    return None

def is_valid_room_number(room_number):
    """Validate if the room number is a valid integer."""
    return re.fullmatch(r'\d+', room_number) is not None

def starter_option_selector(to_number):
    try:
        # Add a small delay to ensure the welcome message is sent first
        message = client.messages.create(
            from_=from_whatsapp_number,
            to=to_number,
            content_sid=STARTER_OPTION_SELECTOR_SID
        )
        print(f"Starter option selector message sent! SID: {message.sid}")
    except Exception as e:
        print(f"Error sending starter option selector message: {str(e)}")

def get_ai_response(user_input, retries=3, backoff=5):
    """
    Generate AI response with retry logic for handling rate limits.
    """
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_input}
    ]
    for attempt in range(retries):
        try:
            response = groq_client.chat.completions.create(
                model="llama3-70b-8192",
                messages=messages,
                max_tokens=1024,  # Reduced tokens to limit usage
                temperature=0.7
            )
            assistant_response = response.choices[0].message.content
            return assistant_response
        except Exception as e:
            error_message = str(e).lower()
            if 'rate_limit_exceeded' in error_message or 'rate limit' in error_message:
                print(f"Rate limit exceeded. Attempt {attempt +1}/{retries}. Waiting for {backoff} seconds.")
                sleep(backoff)
            else:
                print(f"Error generating AI response: {e}")
                break
    return "Sorry, I'm currently experiencing high traffic. Please try again later."

@app.route("/", methods=['POST'])
def handle_incoming_message():
    # Initialize 'user' to None at the start
    user = None

    # Get the incoming data from the request
    incoming_data = request.get_json() if request.is_json else request.form

    # Log the entire received API request
    print("Received API Request:")
    print(json.dumps(incoming_data, indent=4))

    # Extract relevant fields from the incoming data
    sender = incoming_data.get('From', '')
    message_body = incoming_data.get('Body', '').strip()
    message_type = incoming_data.get('MessageType', '').lower()
    button_payload = incoming_data.get('ButtonPayload', '')
    list_title = incoming_data.get('ListTitle', '')
    list_id = incoming_data.get('ListId', '')

    # Prevent responding to messages sent from the bot's own number
    if sender == from_whatsapp_number:
        print("Received message from the bot itself. Ignoring to prevent loop.")
        return '', 204  # No Content

    # Load users data
    users_data = load_users()

    # Find the guest by phone number
    guest = find_guest(sender, users_data)

    # Assign 'user' based on 'guest'
    user = guest

    if not guest:
        # New User: Add to guests and send WELCOME_MESSAGE and starter SID
        new_guest = {
            "phone_number": sender,
            "room_number": None
        }
        users_data['guests'].append(new_guest)
        save_users(users_data)
        guest = new_guest
        try:
            # Send WELCOME_MESSAGE
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=WELCOME_MESSAGE
            )
            print(f'Sent WELCOME_MESSAGE to {sender}.')

            # Send starter_option_selector using Content SID
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid=STARTER_OPTION_SELECTOR_SID
            )
            print(f"Starter option selector message sent to {sender} with SID: {STARTER_OPTION_SELECTOR_SID}")
        except Exception as e:
            print(f"Error sending welcome and starter messages to {sender}: {str(e)}")

    # Load chats data
    chats_data = load_chats()

    # Extract phone number
    phone_number = incoming_data.get('WaId', '').replace('whatsapp:', '')

    # Function to get user from users.json
    user = find_guest(phone_number, users_data)

    # Ensure user exists in chats.json users
    chat_user = next((u for u in chats_data['users'] if u['phone_number'] == phone_number), None)
    if not chat_user:
        # Add user to chats.json users
        chat_user = {
            "id": len(chats_data['users']) + 1,  # Simple ID assignment
            "name": "Guest",  # Default name
            "status": "active",
            "avatar": "",
            "phone_number": phone_number,
            "room_number": guest.get('room_number'),
            "platform": "whatsapp"
        }
        chats_data['users'].append(chat_user)
        save_chats(chats_data)
    else:
        # Handle case where user is not found in users.json
        # Optionally, automatically add the user
        new_guest = {
            "phone_number": phone_number,
            "room_number": None
        }
        users_data['guests'].append(new_guest)
        save_users(users_data)
        print("Automatically added new user to users.json.")

    #------------------------------- Starter option selector options --------------------------------------------------

    # **New Condition: Handle Interactive Messages for Room Service Option** 
    if message_type == "interactive" and message_body == "room_service_option":
        try:
            # Send the specific Content SID for room service option
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HXb9b169a221a86232e0491f9e190710bc"
            )
            print(f"Room service option selector message sent to {sender} with SID: HXb9b169a221a86232e0491f9e190710bc")
        except Exception as e:
            print(f"Error sending room service option selector message to {sender}: {str(e)}")

        # Return a no-content response since we've handled messaging
        return '', 204

    if message_type == "button" and button_payload == "order_food_option":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HX83af47729a5139a537223e6a3a889261" #TO-DO: Add content SID for food order option
            )
            print(f"Order food option selector message sent to {sender} with SID: HX83af47729a5139a537223e6a3a889261")
        except Exception as e:
            print(f"Error sending order food option selector message to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_beverage_option":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HX2c677e28821bc1a644f994b406724995"  # TO-DO: Add content SID for beverage order option
            )
            print(f"Order beverage option selector message sent to {sender} with SID: HX2c677e28821bc1a644f994b406724995")
        except Exception as e:
            print(f"Error sending order beverage option selector message to {sender}: {str(e)}")
        
        return '', 204

    if message_type == "button" and button_payload == "massage_service_option":
        print(f"Handling 'massage_service_option' for sender: {sender}")
        try:
            # Send the massage service option selector message using Content SID
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HX1cd65c8409a60ae217d719c486818ce6"  # Ensure this SID is correct
            )
            print(f"Massage service option selector message sent to {sender} with SID: HX1cd65c8409a60ae217d719c486818ce6")
        except Exception as e:
            print(f"Error sending massage service option selector message to {sender}: {str(e)}")
        return '', 204

    if message_type == "button" and button_payload == "spa_booking_option":
        try:
            # Send the image content first
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HX480d34248555c778b10e0634e0996eec"
            )
            print(f"Spa image and description sent to {sender} with SID: HX480d34248555c778b10e0634e0996eec")

        except Exception as e:
            print(f"Error sending spa booking messages to {sender}: {str(e)}")

        return '', 204

    # After handling other conditions, log incoming messages if in chat with staff
    # Check if the user has initiated a chat with staff
    # Handle 'chat_with_staff_option'
    if message_type == "button" and button_payload == "chat_with_staff_option":
        try:
            # Send prompt message to the user
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body="Thank you for reaching out. We will get back to you shortly."
            )
        except Exception as e:
            print(f"Error handling chat with staff option for {sender}: {str(e)}")

        return '', 204

    # FOOD ALLERGIES --------------------------------------------------

    if message_type == "button" and button_payload in ["burrata_allergies", "pumpkin_allergies", "lobsterroll_allergies", "sirloin_allergies", "chicken_allergies", "tbone_allergies", "steaklobster_allergies", "salmon_allergies"]:
        try:
            # Dictionary mapping button payloads to their corresponding allergy messages
            allergy_messages = {
                "burrata_allergies": BURRATA_ALLERGY_MESSAGE,
                "pumpkin_allergies": PUMPKIN_ALLERGY_MESSAGE,
                "lobsterroll_allergies": LOBSTER_ROLL_ALLERGY_MESSAGE,
                "sirloin_allergies": SIRLOIN_ALLERGY_MESSAGE,
                "chicken_allergies": CHICKEN_ALLERGY_MESSAGE,
                "tbone_allergies": TBONE_ALLERGY_MESSAGE,
                "steaklobster_allergies": STEAK_LOBSTER_ALLERGY_MESSAGE,
                "salmon_allergies": SALMON_ALLERGY_MESSAGE
            }

            # Get the appropriate message based on the button payload
            message_to_send = allergy_messages.get(button_payload)

            if message_to_send:
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    body=message_to_send
                )
                print(f"Food allergy message for {button_payload} sent to {sender}.")
            else:
                print(f"No matching allergy message found for payload: {button_payload}")
                
        except Exception as e:
            print(f"Error sending food allergy message to {sender}: {str(e)}")

        return '', 204  # Fixed return statement with proper tuple format

    # Handler code
    if message_type == "button" and button_payload in ["order_burrata", "order_pumpkin", "order_lobsterroll", "order_sirloin", "order_chicken", "order_tbone", "order_steaklobster", "order_salmon"]:
        try:
            # Dictionary mapping button payloads to their corresponding order messages
            order_messages = {
                "order_burrata": BURRATA_ORDER_MESSAGE,
                "order_pumpkin": PUMPKIN_ORDER_MESSAGE,
                "order_lobsterroll": LOBSTER_ROLL_ORDER_MESSAGE,
                "order_sirloin": SIRLOIN_ORDER_MESSAGE,
                "order_chicken": CHICKEN_ORDER_MESSAGE,
                "order_tbone": TBONE_ORDER_MESSAGE,
                "order_steaklobster": STEAK_LOBSTER_ORDER_MESSAGE,
                "order_salmon": SALMON_ORDER_MESSAGE
            }

            # Get the appropriate message based on the button payload
            message_to_send = order_messages.get(button_payload)

            if message_to_send:
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    body=message_to_send
                )
                print(f"Order confirmation for {button_payload} sent to {sender}.")
            else:
                print(f"No matching order message found for payload: {button_payload}")
                
        except Exception as e:
            print(f"Error sending order confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "interactive" and message_body == "sweet_wine_option":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HX97b6cceff9200fa4a10c5a938f8224fb"  # TO-DO: Add content SID for sweet wine order option
            )
            print(f"Sweet wine option selector message sent to {sender} with SID: HX97b6cceff9200fa4a10c5a938f8224fb")
        except Exception as e:
            print(f"Error sending sweet wine option selector message to {sender}: {str(e)}")

        return '', 204

    if message_type == "interactive" and message_body == "alcoholic_beverages_option":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HXea619ac8554b6d334b0eb7799135c7ba"  # TO-DO: Add content SID for beverage order option
            )
            print(f"Alcoholic beverages option selector message sent to {sender} with SID: HXea619ac8554b6d334b0eb7799135c7ba")
        except Exception as e:
            print(f"Error sending alcoholic beverages option selector message to {sender}: {str(e)}")

        return '', 204

    if message_type == "interactive" and message_body == "soft_drinks_option":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HXe7d674074cb5b5532e6c19c28b2bb826"  # TO-DO: Add content SID for beverage order option
            )
            print(f"Non-alcoholic beverages option selector message sent to {sender} with SID: HXe7d674074cb5b5532e6c19c28b2bb826")
        except Exception as e:
            print(f"Error sending non-alcoholic beverages option selector message to {sender}: {str(e)}")

        return '', 204

    if message_type == "interactive" and message_body == "beer_options":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HX0d849bd3c947d1d6b234391e3d32a299"  # TO-DO: Add content SID for signature cocktails option
            )
            print(f"Beer option selector message sent to {sender} with SID: HX0d849bd3c947d1d6b234391e3d32a299")
        except Exception as e:
            print(f"Error sending beer option selector message to {sender}: {str(e)}")

        return '', 204

    if message_type == "interactive" and message_body == "signature_mocktails_option":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HX536747bbdcd030716986b7d203c42ec5"  # TO-DO: Add content SID for signature cocktails option
            )
            print(f"Signature mocktails option selector message sent to {sender} with SID: HX536747bbdcd030716986b7d203c42ec5")
        except Exception as e:
            print(f"Error sending signature mocktails option selector message to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_coronita":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=BEVERAGE_ORDER_CONIFORMATION
            )
            print(f"Order coronita confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order coronita confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_mr_ginger":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=BEVERAGE_ORDER_CONIFORMATION
            )
            print(f"Order mr ginger confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order mr ginger confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_berries_mojito":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=BEVERAGE_ORDER_CONIFORMATION
            )
            print(f"Order berries mojito confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order berries mojito confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_summer_cooler":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=BEVERAGE_ORDER_CONIFORMATION
            )
            print(f"Order summer cooler confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order summer cooler confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_estrella_galicia":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=BEVERAGE_ORDER_CONIFORMATION
            )
            print(f"Order estrella galicia confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order estrella galicia confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_estrella_galicia_0_0":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=BEVERAGE_ORDER_CONIFORMATION
            )
            print(f"Order estrella galicia 0.0 confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order estrella galicia 0.0 confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_peroni":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=BEVERAGE_ORDER_CONIFORMATION
            )
            print(f"Order peroni confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order peroni confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_summer_cooler":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=BEVERAGE_ORDER_CONIFORMATION
            )
            print(f"Order summer cooler confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order summer cooler confirmation to {sender}: {str(e)}")

        return '', 204

    if button_payload == "spa_go_back" and message_type == "button":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HXb49d9f1081f3c2b14614fe59bb8b04c3"
            )
            print(f"Go back option selector message sent to {sender}.")
        except Exception as e:
            print(f"Error sending go back option selector message to {sender}: {str(e)}")
        
        return '', 204  # Add this line to prevent further processing

    if message_type == "button" and button_payload == "order_creative_tonic_water":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=BEVERAGE_ORDER_CONIFORMATION
            )
            print(f"Order creative tonic water confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order creative tonic water confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_exotic_yuzu_sensation":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=BEVERAGE_ORDER_CONIFORMATION
            )
            print(f"Order exotic yuzu sensation confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order exotic yuzu sensation confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_fever_tree_tonic":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=BEVERAGE_ORDER_CONIFORMATION
            )
            print(f"Order fever tree tonic confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order fever tree tonic confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_zero_azucar_tonic":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=BEVERAGE_ORDER_CONIFORMATION
            )
            print(f"Order zero azucar tonic confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order zero azucar tonic confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and message_body == "Book Now" and button_payload in ["book_alquimist_90", "book_facial_60", "book_alquimist_60"]:
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HXd842a9216b1b3aad23dccfe555ba87d6"
            )
            print(f"Date and time selector message sent to {sender} with SID: HXd842a9216b1b3aad23dccfe555ba87d6")
        except Exception as e:
            print(f"Error sending date and time selector message to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_pumpkin":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=FOOD_ORDER_CONIFORMATION
            )
            print(f"Order pumpkin confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order pumpkin confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_lobsterroll":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=FOOD_ORDER_CONIFORMATION
            )
            print(f"Order lobster roll confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order lobster roll confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_sirloin":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=FOOD_ORDER_CONIFORMATION
            )
            print(f"Order sirloin confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order sirloin confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_chicken":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=FOOD_ORDER_CONIFORMATION
            )
            print(f"Order chicken confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order chicken confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_tbone":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=FOOD_ORDER_CONIFORMATION
            )
            print(f"Order t-bone confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order t-bone confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_steaklobster":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=FOOD_ORDER_CONIFORMATION
            )
            print(f"Order steak & lobster confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order steak & lobster confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "order_salmon":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=FOOD_ORDER_CONIFORMATION
            )
            print(f"Order salmon confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending order salmon confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "go_back":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HXb9b169a221a86232e0491f9e190710bc"
            )
            print(f"Go back option selector message sent to {sender}.")
        except Exception as e:
            print(f"Error sending go back option selector message to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "go_back_massage":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HXb49d9f1081f3c2b14614fe59bb8b04c3"
            )
            print(f"Go back option selector message sent to {sender}.")
        except Exception as e:
            print(f"Error sending go back option selector message to {sender}: {str(e)}")

        return '', 204

    # Load users data
    users_data = load_users()

    # Find the guest by phone number
    guest = find_guest(sender, users_data)

    if not guest:
        # **New User: Add to guests and send WELCOME_MESSAGE and starter SID**
        new_guest = {
            "phone_number": sender,
            "room_number": None
        }
        users_data['guests'].append(new_guest)
        save_users(users_data)
        try:
            # Send WELCOME_MESSAGE
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=WELCOME_MESSAGE
            )
            print(f'Sent WELCOME_MESSAGE to {sender}.')

            # Send starter_option_selector using Content SID
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid=STARTER_OPTION_SELECTOR_SID
            )
            print(f"Starter option selector message sent to {sender} with SID: {STARTER_OPTION_SELECTOR_SID}")
        except Exception as e:
            print(f"Error sending welcome and starter messages to {sender}: {str(e)}")

        return '', 204

    if list_id.startswith("date_"):
        selected_date_key = list_id  # e.g., "date_1"
        
        # Massage Date Mapping
        massage_date_mapping = {
            "date_1": "21-10-24 (10-12 AM)",
            "date_2": "21-10-24 (4-6 PM)",
            "date_3": "22-10-24 (10-12 AM)",
            "date_4": "22-10-24 (4-6 PM)",
            # Add more mappings as needed
        }
        
        # Spa Date Mapping (Renamed for Clarity)
        spa_date_mapping = {
            "date_21": "21-10-24 (2-3 PM)",
            "date_22": "21-10-24 (3-4 PM)",
            "date_23": "22-10-24 (2-3 PM)",
            "date_24": "22-10-24 (3-4 PM)",
            # Add more mappings as needed
        }
        
        if selected_date_key in massage_date_mapping:
            selected_date = massage_date_mapping[selected_date_key]
            try:
                # Send the massage appointment confirmation message
                confirmation_message = MASSAGE_ORDER_CONIFORMATION
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    body=confirmation_message
                )
                print(f"Massage order confirmation for {selected_date} sent to {sender}.")
                
                # Send additional message with SID
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    content_sid="HXf61ee1cab7b7b36b7403be0f79f34bcc"
                )
                print(f"Additional confirmation message sent to {sender} with SID: HXf61ee1cab7b7b36b7403be0f79f34bcc")
                
            except Exception as e:
                print(f"Error sending massage order confirmation to {sender}: {str(e)}")
            return '', 204  # Exit after handling

        elif selected_date_key in spa_date_mapping:
            selected_date = spa_date_mapping[selected_date_key]
            try:
                # Send the spa appointment confirmation message
                confirmation_message = SPA_ORDER_CONIFORMATION
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    body=confirmation_message
                )
                print(f"Spa order confirmation for {selected_date} sent to {sender}.")
                
                # Send additional message with SID (if needed)
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    content_sid="HXf61ee1cab7b7b36b7403be0f79f34bcc"
                )
                print(f"Additional confirmation message sent to {sender} with SID: HXf61ee1cab7b7b36b7403be0f79f34bcc")
                
            except Exception as e:
                print(f"Error sending spa order confirmation to {sender}: {str(e)}")
            return '', 204  # Exit after handling

        else:
            print(f"No matching date found for ListId: {list_id}")
            return '', 204  # Exit after handling

    if message_type == "interactive" and list_title in ["22-10-24 (4-6 PM)", "22-10-24 (10-12 AM)", "21-10-24 (10-12 AM)", "22-10-24 (4-6 PM)"]:
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=MASSAGE_ORDER_CONIFORMATION
            )
            print(f"Massage order confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending massage order confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "interactive" and list_title in ["22-10-24 (2-3 PM)", "22-10-24 (3-4 PM)", "21-10-24 (2-3 PM)", "21-10-24 (3-4 PM)"]:
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=SPA_ORDER_CONIFORMATION
            )
            print(f"Spa order confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending spa order confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "interactive" and message_body in ["1_nights", "2_nights", "3_nights", "4_nights", "5_nights", "6_nights"]:
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=ROOM_BOOKING_CONIFORMATION 
            )
            print(f"Room type selector message sent to {sender}.")
        except Exception as e:
            print(f"Error sending room type selector message to {sender}: {str(e)}")

        return '', 204

    if button_payload == "book_spa_option":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HX291b469365c8cbeac916dcd50261a0ea"
            )
            print(f"Spa date and time selector message sent to {sender}.")
        except Exception as e:
            print(f"Error sending spa date and time selector message to {sender}: {str(e)}")

        return '', 204

    # ROOM BOOKING LOGIC

    elif message_type == "button" and button_payload == "room_booking_option":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HXc87a9790fe9ea31f5929e49ee0785b7b"  # Replace with your actual Content SID for room booking
            )
            print(f"Room booking option message sent to {sender} with SID: HXc87a9790fe9ea31f5929e49ee0785b7b")
        except Exception as e:
            print(f"Error sending room booking option message to {sender}: {str(e)}")
        return '', 204

    # Load chats data
    chats_data = load_chats()

    # Extract phone number
    phone_number = incoming_data.get('WaId', '').replace('whatsapp:', '')
    
    # Function to get user from users.json
    users_data = load_users()
    user = find_guest(phone_number, users_data)

    # Ensure user exists in chats.json
    if user:
        # Check if user exists in chats.json users
        chat_user = next((u for u in chats_data['users'] if u['phone_number'] == phone_number), None)
        if not chat_user:
            # Add user to chats.json users
            chat_user = {
                "id": user.get('id'),
                "name": user.get('name'),
                "status": user.get('status'),
                "avatar": user.get('avatar'),
                "phone_number": user.get('phone_number'),
                "room_number": user.get('room_number'),
                "platform": user.get('platform')
            }
            chats_data['users'].append(chat_user)
            save_chats(chats_data)
    else:
        # Handle case where user is not found in users.json
        # Optionally, automatically add the user
        new_guest = {
            "phone_number": phone_number,
            "room_number": None
        }
        users_data['guests'].append(new_guest)
        save_users(users_data)
        print("Automatically added new user to users.json.")

    # Handle 'chat_with_staff_option'
    if list_id == "chat_with_staff_option" and message_body == "chat_with_staff_option":
        try:
            # Send prompt message to the user
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body="Please tell us your request."
            )
            print(f"Prompted {sender} to tell their request.")

            # Initialize chat history for the user if not present
            user_id = str(user.get('id'))
            if user_id not in chats_data['chats']:
                chats_data['chats'][user_id] = []
            save_chats(chats_data)
        except Exception as e:
            print(f"Error handling chat with staff option for {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "book_junior_suite":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid='HX4ade4714876d858178ab241d5bd6a34e'
            )
            print(f"Room booking confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending room booking confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "book_the_raid":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid='HX4ade4714876d858178ab241d5bd6a34e'
            )
            print(f"Room booking confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending room booking confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "book_junior_suite_deluxe":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid='HX4ade4714876d858178ab241d5bd6a34e'
            )
            print(f"Room booking confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending room booking confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "button" and button_payload == "book_executive_suite":
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid='HX4ade4714876d858178ab241d5bd6a34e'
            )
            print(f"Room booking confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending room booking confirmation to {sender}: {str(e)}")

        return '', 204

    if message_type == "interactive" and list_title in ["21-10-2024", "22-10-2024", "23-10-2024", "24-10-2024", "25-10-2024"]:
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HX747bc731f2315a49d178d755aac67866"
            )
            print(f"Massage order confirmation sent to {sender}.")
        except Exception as e:
            print(f"Error sending massage order confirmation to {sender}: {str(e)}")

        return '', 204

    # Example AI Response Handling
    room_number = guest.get('room_number')
    if room_number:
        # User has a room number, proceed to AI interaction
        if message_body.upper() == "GGTEST":
            try:
                # Send WELCOME_MESSAGE
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    body=WELCOME_MESSAGE
                )
                print(f'Sent WELCOME_MESSAGE to {sender} due to GGTEST.')

                # Send starter_option_selector using Content SID
                starter_option_selector(sender)
            except Exception as e:
                print(f"Error sending GGTEST messages to {sender}: {str(e)}")

            return '', 204
        else:
            # Respond with AI-generated answer
            assistant_response = get_ai_response(message_body)
            if assistant_response:
                try:
                    # Send the AI-generated response to the user
                    client.messages.create(
                        from_=from_whatsapp_number,
                        to=sender,
                        body=assistant_response
                    )
                    print(f'Sent AI response to {sender}.')
                except Exception as e:
                    print(f"Error sending AI response to {sender}: {str(e)}")

            return '', 204
    else:
        # User Does Not Have a Room Number
        if is_valid_room_number(message_body):
            # User is sending their room number
            guest['room_number'] = int(message_body)
            save_users(users_data)
            try:
                # Send ROOM_NUMBER_SAVED_MESSAGE
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    body=ROOM_NUMBER_SAVED_MESSAGE
                )
                print(f'Saved room number "{message_body}" for {sender} and sent confirmation.')

                # Send the starter option selector after saving room number
                starter_option_selector(sender)
            except Exception as e:
                print(f"Error sending messages to {sender}: {str(e)}")

            return '', 204
        else:
            # Prompt user to enter their room number
            try:
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    body=REQUEST_ROOM_MESSAGE
                )
                print(f'Prompted {sender} to enter room number.')
            except Exception as e:
                print(f"Error sending room number request to {sender}: {str(e)}")

            return '', 204

if __name__ == '__main__':
    app.run(debug=False, port=5000)