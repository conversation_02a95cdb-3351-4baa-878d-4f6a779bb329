<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Status</title>
    {% include 'imports.html' %}
    <script>
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
    <style>
        body {
            visibility: hidden;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
        }

        .glow-green {
            box-shadow: 0 0 5px 1px rgba(16, 185, 129, 0.7);
        }

        .glow-yellow {
            box-shadow: 0 0 5px 1px rgba(245, 158, 11, 0.7);
        }

        .glow-red {
            box-shadow: 0 0 5px 1px rgba(239, 68, 68, 0.7);
        }

        .glow-blue {
            box-shadow: 0 0 5px 1px rgba(59, 130, 246, 0.7);
        }

        .incident-timeline-item {
            position: relative;
            padding-left: 28px;
            padding-bottom: 32px;
        }

        .incident-timeline-item::before {
            content: '';
            position: absolute;
            left: 4px;
            top: 8px;
            bottom: -8px;
            width: 1px;
            background-color: var(--border-color);
        }

        .pure-black .incident-timeline-item::before {
            background-color: var(--border-color);
        }

        .incident-timeline-item:last-child::before {
            display: none;
        }

        .incident-dot {
            position: absolute;
            left: 0;
            top: 8px;
            width: 9px;
            height: 9px;
            border-radius: 50%;
            box-shadow: 0 0 5px 1px rgba(16, 185, 129, 0.7);
        }

        .chart-bar {
            height: 6px;
            border-radius: 3px;
            margin: 8px 0;
        }

        .chart-legend {
            display: flex;
            gap: 1rem;
            margin-top: 0.5rem;
            font-size: 0.75rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .sidebar {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 280px;
            overflow-y: auto;
            height: 100%;
        }
    </style>
</head>

<body class="bg-background text-foreground">
    {% include 'components/loading.html' %}

    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr]">
        {% include 'sidebar.html' %}

        <div class="flex flex-col">
            <header
                class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
                <div style="margin-left: 8px;" class="flex items-center gap-2 px-4 pl-0">
                    <button id="toggle-btn" class="opacity-100 transition-opacity duration-300 focus:outline-none"
                        style="background-color: transparent !important;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-panel-left">
                            <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                            <path d="M9 3v18"></path>
                        </svg>
                    </button>
                    <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-3 h-4"
                        style="background-color: var(--border-color);"></div>
                    <nav aria-label="breadcrumb">
                        <ol
                            class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">

                            <!-- Sales Report link -->
                            <div class="menubar" role="menubar">
                                <div class="menubar-indicator"></div>
                                <a href="/sales" role="menuitem">Sales Report</a>
                                <a href="/pmsanalytics" role="menuitem">PMS Analytics</a>
                                <a href="/googleana" role="menuitem">Google Analytics</a>
                                <a href="/status" role="menuitem" class="active">Status Report</a>
                            </div>
                        </ol>
                    </nav>
                </div>
                {% include 'topright.html' %}
            </header>

            <main class="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6 card">
                <!-- Overall Status Card -->
                <div class="rounded-lg border shadow-sm card p-6">
                    <div class="flex flex-row justify-between items-center mb-6">
                        <h2 class="text-xl font-semibold">System Overview</h2>
                        <p class="text-sm text-gray-500">Last updated : <span id="last-updated"></span></p>
                    </div>

                    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                        <!-- Overall Uptime -->
                        <div class="rounded-lg border p-4 shadow-sm card"
                            style="height: 148px; display: flex; flex-direction: column;">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-sm font-medium">Overall Uptime</h3>
                                </div>
                                <div style="margin-top: 3px;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-hard-drive-upload-icon lucide-hard-drive-upload">
                                        <path d="m16 6-4-4-4 4" />
                                        <path d="M12 2v8" />
                                        <rect width="20" height="8" x="2" y="14" rx="2" />
                                        <path d="M6 18h.01" />
                                        <path d="M10 18h.01" />
                                    </svg>
                                </div>
                            </div>
                            <div style="margin-bottom: 3px;" class="mt-auto">
                                <p class="text-xl font-semibold">99.98%</p>
                                <div class="bg-gray-100 rounded-full h-1.5 w-full mt-2">
                                    <div class="bg-green-500 h-1.5 rounded-full" style="width: 99.98%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Open Incidents -->
                        <div class="rounded-lg border p-4 shadow-sm card"
                            style="height: 148px; display: flex; flex-direction: column;">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-sm font-medium">Open Incidents</h3>
                                </div>
                                <div style="margin-top: 3px;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-octagon-alert-icon lucide-octagon-alert">
                                        <path d="M12 16h.01" />
                                        <path d="M12 8v4" />
                                        <path
                                            d="M15.312 2a2 2 0 0 1 1.414.586l4.688 4.688A2 2 0 0 1 22 8.688v6.624a2 2 0 0 1-.586 1.414l-4.688 4.688a2 2 0 0 1-1.414.586H8.688a2 2 0 0 1-1.414-.586l-4.688-4.688A2 2 0 0 1 2 15.312V8.688a2 2 0 0 1 .586-1.414l4.688-4.688A2 2 0 0 1 8.688 2z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="mt-auto">
                                <p class="text-xl font-semibold">1</p>
                                <p class="text-xs text-gray-500 mt-1">Minor issue with Instagram integration</p>
                            </div>
                        </div>

                        <!-- Completed Maintenance -->
                        <div class="rounded-lg border p-4 shadow-sm card"
                            style="height: 148px; display: flex; flex-direction: column;">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-sm font-medium">Weekly Maintenance</h3>
                                </div>
                                <div style="margin-top: 3px;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-construction-icon lucide-construction">
                                        <rect x="2" y="6" width="20" height="8" rx="1" />
                                        <path d="M17 14v7" />
                                        <path d="M7 14v7" />
                                        <path d="M17 3v3" />
                                        <path d="M7 3v3" />
                                        <path d="M10 14 2.3 6.3" />
                                        <path d="m14 6 7.7 7.7" />
                                        <path d="m8 6 8 8" />
                                    </svg>
                                </div>
                            </div>
                            <div class="mt-auto">
                                <p class="text-xl font-semibold">Completed</p>
                                <p class="text-xs text-gray-500 mt-1">Last performed on <span
                                        id="last-maintenance"></span></p>
                            </div>
                        </div>

                        <!-- Response Time -->
                        <div class="rounded-lg border p-4 shadow-sm card"
                            style="height: 148px; display: flex; flex-direction: column;">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-sm font-medium">Avg Response Time</h3>
                                </div>
                                <div style="margin-top: 3px;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-clock4-icon lucide-clock-4">
                                        <circle cx="12" cy="12" r="10" />
                                        <polyline points="12 6 12 12 16 14" />
                                    </svg>
                                </div>
                            </div>
                            <div class="mt-auto">
                                <p class="text-xl font-semibold">125ms</p>
                                <div class="text-xs text-green-600 mt-1">▼ 15ms from last week</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Service Categories -->
                <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <!-- Chat Integrations -->
                    <div class="rounded-lg border shadow-sm card">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center gap-2">
                                    <h3 class="font-medium" style="margin-bottom: 15px;">Chat Integrations</h3>
                                </div>
                                <div class="bg-yellow-100 text-yellow-700 px-2 py-1 rounded text-xs font-medium">
                                    Minor Issues
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">WhatsApp</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Facebook</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Instagram</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-yellow-500 glow-yellow"></span>
                                        <span class="text-xs text-yellow-600">Degraded</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Web Chat</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <hr class="card uk-hr" />
                                <div style="margin-top: 15px !important;">
                                    <div class="text-sm font-medium mb-2">7-Day Uptime</div>
                                    <div class="chart-bar bg-gray-100">
                                        <div class="chart-bar bg-green-500" style="width: 98.5%"></div>
                                    </div>
                                    <div class="chart-legend text-gray-500">
                                        <div class="legend-item">
                                            <span class="status-dot bg-green-500"></span>
                                            <span>Operational: 98.5%</span>
                                        </div>
                                        <div class="legend-item">
                                            <span class="status-dot bg-yellow-500"></span>
                                            <span>Degraded: 1.5%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Voice Services -->
                    <div class="rounded-lg border shadow-sm card">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center gap-2">
                                    <h3 class="font-medium" style="margin-bottom: 15px;">Voice Services</h3>
                                </div>
                                <div class="bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium">
                                    Operational
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Voice Bot</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">IVR System</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Call Routing</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Voice Analytics</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <hr class="card uk-hr" />
                                <div style="margin-top: 15px !important;">
                                    <div class="text-sm font-medium mb-2">7-Day Uptime</div>
                                    <div class="chart-bar bg-gray-100">
                                        <div class="chart-bar bg-green-500" style="width: 99.9%"></div>
                                    </div>
                                    <div class="chart-legend text-gray-500">
                                        <div class="legend-item">
                                            <span class="status-dot bg-green-500"></span>
                                            <span>Operational: 99.9%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Core Services -->
                    <div class="rounded-lg border shadow-sm card">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center gap-2">
                                    <h3 class="font-medium" style="margin-bottom: 15px;">Core Services</h3>
                                </div>
                                <div class="bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium">
                                    Operational
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Dashboard</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Supabase DB</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">AI Services</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">API Gateway</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <hr class="card uk-hr" />
                                <div style="margin-top: 15px !important;">
                                    <div class="text-sm font-medium mb-2">7-Day Uptime</div>
                                    <div class="chart-bar bg-gray-100">
                                        <div class="chart-bar bg-green-500" style="width: 100%"></div>
                                    </div>
                                    <div class="chart-legend text-gray-500">
                                        <div class="legend-item">
                                            <span class="status-dot bg-green-500"></span>
                                            <span>Operational: 100%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- PMS Integrations -->
                    <div class="rounded-lg border shadow-sm card">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center gap-2">
                                    <h3 class="font-medium" style="margin-bottom: 15px;">PMS Integrations</h3>
                                </div>
                                <div class="bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium">
                                    Operational
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">RoomCloud</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">RoomRaccoon</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Sihot</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Vertical Booking</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <hr class="card uk-hr" />
                                <div style="margin-top: 15px !important;">
                                    <div class="text-sm font-medium mb-2">7-Day Uptime</div>
                                    <div class="chart-bar bg-gray-100">
                                        <div class="chart-bar bg-green-500" style="width: 99.7%"></div>
                                    </div>
                                    <div class="chart-legend text-gray-500">
                                        <div class="legend-item">
                                            <span class="status-dot bg-green-500"></span>
                                            <span>Operational: 99.7%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Services -->
                    <div class="rounded-lg border shadow-sm card">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center gap-2">
                                    <h3 class="font-medium" style="margin-bottom: 15px;">Mobile Services</h3>
                                </div>
                                <div class="bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium">
                                    Operational
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">iOS App</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Android App</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Push Notifications</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Mobile API</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <hr class="card uk-hr" />
                                <div style="margin-top: 15px !important;">
                                    <div class="text-sm font-medium mb-2">7-Day Uptime</div>
                                    <div class="chart-bar bg-gray-100">
                                        <div class="chart-bar bg-green-500" style="width: 99.5%"></div>
                                    </div>
                                    <div class="chart-legend text-gray-500">
                                        <div class="legend-item">
                                            <span class="status-dot bg-green-500"></span>
                                            <span>Operational: 99.5%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- API Services -->
                    <div class="rounded-lg border shadow-sm card">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center gap-2">
                                    <h3 class="font-medium" style="margin-bottom: 15px;">API Services</h3>
                                </div>
                                <div class="bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium">
                                    Operational
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Twilio Integration</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">REST API</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Webhooks</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Auth Service</span>
                                    <div class="flex items-center gap-1.5">
                                        <span class="status-dot bg-green-500 glow-green"></span>
                                        <span class="text-xs text-green-600">Operational</span>
                                    </div>
                                </div>

                                <hr class="card uk-hr" />
                                <div style="margin-top: 15px !important;">
                                    <div class="text-sm font-medium mb-2">7-Day Uptime</div>
                                    <div class="chart-bar bg-gray-100">
                                        <div class="chart-bar bg-green-500" style="width: 99.8%"></div>
                                    </div>
                                    <div class="chart-legend text-gray-500">
                                        <div class="legend-item">
                                            <span class="status-dot bg-green-500"></span>
                                            <span>Operational: 99.8%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Incident Timeline -->
                <div class="rounded-lg border shadow-sm card p-6">
                    <h2 class="text-xl font-semibold mb-6">Recent Incidents</h2>

                    <div class="space-y-1">
                        <!-- Incident 1 -->
                        <div class="incident-timeline-item">
                            <div class="incident-dot bg-green-500"></div>
                            <div class="rounded-lg border p-4 shadow-sm card">
                                <div class="flex justify-between items-center mb-2">
                                    <h3 class="text-base font-medium">Investigating: Instagram Integration Issues</h3>
                                    <span class="text-xs text-gray-500">2 hours ago</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">
                                    We're investigating reports of intermittent connectivity issues with the Instagram
                                    Chatbot service. Some users may experience delayed responses.
                                </p>
                                <div class="flex items-center text-xs text-gray-500">
                                    <span class="mr-3"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="inline mr-1">
                                            <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
                                            <line x1="3" x2="21" y1="9" y2="9"></line>
                                            <line x1="9" x2="9" y1="21" y2="9"></line>
                                        </svg><span id="incident-date-1"></span></span>
                                    <span><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="inline mr-1">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <polyline points="12 6 12 12 16 14"></polyline>
                                        </svg><span id="incident-time-1"></span></span>
                                </div>
                            </div>
                        </div>

                        <!-- Incident 2 -->
                        <div class="incident-timeline-item">
                            <div class="incident-dot bg-green-500"></div>
                            <div class="rounded-lg border p-4 shadow-sm card">
                                <div class="flex justify-between items-center mb-2">
                                    <h3 class="text-base font-medium">Completed Maintenance: Dashboard</h3>
                                    <span class="text-xs text-gray-500">1 day ago</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">
                                    Scheduled maintenance on the Dashboard has been completed successfully. Performance
                                    improvements and security updates have been applied.
                                </p>
                                <div class="flex items-center text-xs text-gray-500">
                                    <span class="mr-3"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="inline mr-1">
                                            <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
                                            <line x1="3" x2="21" y1="9" y2="9"></line>
                                            <line x1="9" x2="9" y1="21" y2="9"></line>
                                        </svg><span id="incident-date-2"></span></span>
                                    <span><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="inline mr-1">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <polyline points="12 6 12 12 16 14"></polyline>
                                        </svg><span id="incident-time-2"></span></span>
                                </div>
                            </div>
                        </div>

                        <!-- Incident 3 -->
                        <div class="incident-timeline-item">
                            <div class="incident-dot bg-green-500"></div>
                            <div class="rounded-lg border p-4 shadow-sm card">
                                <div class="flex justify-between items-center mb-2">
                                    <h3 class="text-base font-medium">Resolved: Twilio Webhook Latency</h3>
                                    <span class="text-xs text-gray-500">3 days ago</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">
                                    The increased latency affecting Twilio webhook processing has been resolved. All
                                    message processing has returned to normal operation.
                                </p>
                                <div class="flex items-center text-xs text-gray-500">
                                    <span class="mr-3"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="inline mr-1">
                                            <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
                                            <line x1="3" x2="21" y1="9" y2="9"></line>
                                            <line x1="9" x2="9" y1="21" y2="9"></line>
                                        </svg><span id="incident-date-3"></span></span>
                                    <span><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="inline mr-1">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <polyline points="12 6 12 12 16 14"></polyline>
                                        </svg><span id="incident-time-3"></span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Make the body visible after content loads
            document.body.style.visibility = 'visible';

            // Update last updated time
            const now = new Date();
            const formattedDate = now.toLocaleDateString('en-US', {
                day: 'numeric',
                month: 'short',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('last-updated').textContent = formattedDate;

            // Set last maintenance date
            const maintenanceDate = new Date();
            maintenanceDate.setDate(maintenanceDate.getDate() - 1);
            const formattedMaintenance = maintenanceDate.toLocaleDateString('en-US', {
                day: 'numeric',
                month: 'short',
                year: 'numeric'
            });
            document.getElementById('last-maintenance').textContent = formattedMaintenance;

            // Update incident dates
            const incidents = [
                { days: 0, id: 1 },
                { days: 1, id: 2 },
                { days: 3, id: 3 }
            ];

            incidents.forEach(incident => {
                const incidentDate = new Date();
                incidentDate.setDate(incidentDate.getDate() - incident.days);

                document.getElementById(`incident-date-${incident.id}`).textContent =
                    incidentDate.toLocaleDateString('en-US', { day: 'numeric', month: 'short' });

                document.getElementById(`incident-time-${incident.id}`).textContent =
                    incidentDate.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
            });
        });
    </script>
</body>

</html>