import os
import csv
from supabase import create_client

# Use the credentials from main.py
SUPABASE_URL = 'https://nuqxdjuaoccswunhqixz.supabase.co'
SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im51cXhkanVhb2Njc3d1bmhxaXh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTcxMjI2OTcsImV4cCI6MjAzMjY5ODY5N30.sHkkzEb5oCTlLB3MQ0420XtJpURXW1DIHuHm4M9kDPI'

client = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)

# Query to get all public table names
tables_response = client.table('pg_tables').select('tablename').eq('schemaname', 'public').execute()
tables = [row['tablename'] for row in tables_response.data]
print(f"Found tables: {tables}")

# Define output directory for CSV files
BASE_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'database')
os.makedirs(BASE_DIR, exist_ok=True)

for table in tables:
    print(f"Exporting table: {table}")
    data_response = client.table(table).select('*').execute()
    data = data_response.data
    if not data:
        print(f"No data in table {table}. Skipping.")
        continue
    
    csv_path = os.path.join(BASE_DIR, f"{table}.csv")
    with open(csv_path, mode='w', newline='', encoding='utf-8') as csvfile:
        # Use fieldnames from the first row's keys
        writer = csv.DictWriter(csvfile, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)
    print(f"Exported {table} to {csv_path}")
