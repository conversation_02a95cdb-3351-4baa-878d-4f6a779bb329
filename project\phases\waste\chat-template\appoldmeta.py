from flask import Flask, render_template, jsonify, request, session, redirect, url_for
import requests
import json
import os
import logging
from colorama import Fore, Style, init
from datetime import datetime
from dotenv import load_dotenv
from auth import auth, login_required
from datetime import timedelta
from functools import wraps
import time
from twilio.rest import Client
from twilio.twiml.messaging_response import MessagingResponse

init(autoreset=True)

app = Flask(__name__)
app.secret_key = os.urandom(24)
app.register_blueprint(auth)

# Load environment variables from .env file
load_dotenv()

# Get the values
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY')
TWILIO_ACCOUNT_SID = os.getenv('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.getenv('TWILIO_AUTH_TOKEN')
TWILIO_PHONE_NUMBER = os.getenv('TWILIO_PHONE_NUMBER')

app.permanent_session_lifetime = timedelta(minutes=3000)

# New variables for request control
NO_OF_REQUESTS = 5
REQUEST_TIMEOUT = 40
rate_limit = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize Twilio client
twilio_client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

# Initialize empty JSON file if it doesn't exist
if not os.path.exists('chats.json'):
    with open('chats.json', 'w', encoding='utf-8') as f:
        json.dump({"users": [], "chats": {}}, f, ensure_ascii=False)
    logger.info(f"{Fore.GREEN}Initialized empty chats.json file{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Initialized empty chats.json file{Style.RESET_ALL}")


#------------------comments--------------------------
# Decorator to check if requests are allowed
#------------------comments--------------------------
def check_request_allowed(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not rate_limit:
            return f(*args, **kwargs)
        
        current_time = time.time()
        last_request_time = session.get('last_request_time', 0)
        start_time = session.get('start_time', current_time)

        if current_time - start_time > REQUEST_TIMEOUT:
            return jsonify({'error': 'Session expired. Please refresh the page.'}), 403

        session['last_request_time'] = current_time
        return f(*args, **kwargs)
    return decorated_function

#------------------comments--------------------------
# Route for the main page
#------------------comments--------------------------
@app.route('/')
@login_required
def index():
    session['start_time'] = time.time()
    session['last_request_time'] = time.time()
    return render_template('dashboard.html')

#------------------comments--------------------------
# Route for the test page
#------------------comments--------------------------
@app.route('/test')
def test():
    return render_template('mail.html')

#------------------comments--------------------------
# Route for the sales page
#------------------comments--------------------------
@app.route('/sales')
@login_required
def open_sales():
    return render_template('sales.html')

#------------------comments--------------------------
# Route for the analytics page
#------------------comments--------------------------
@app.route('/analytics')
@login_required
def open_analytics():
    return render_template('analytics.html')

#------------------comments--------------------------
# Route for the settings page
#------------------comments--------------------------
@app.route('/settings')
@login_required
def settings():
    return render_template('settings.html')

#------------------comments--------------------------
# Route for the users page
#------------------comments--------------------------
@app.route('/users')
@login_required
def open_users():
    return render_template('users.html')

#------------------comments--------------------------
# Route for the issues page
#------------------comments--------------------------
@app.route('/issue')
@login_required
def open_issuepage():
    return render_template('issues.html')

#------------------comments--------------------------
# Route for the mail page
#------------------comments--------------------------
@app.route('/mail')
@login_required
def open_mail():
    return render_template('mail.html')

#------------------comments--------------------------
# Route for the live chat page
#------------------comments--------------------------
@app.route('/livechat')
@login_required
def open_taksks():
    return render_template('livechat.html')

#------------------comments--------------------------
# Route for the tasks page
#------------------comments--------------------------
@app.route('/tasks')
@login_required
def open_tasks():  # Changed from open_taksks to open_tasks
    return render_template('tasks.html')

#------------------comments--------------------------
# Route for the configuration page
#------------------comments--------------------------
@app.route('/config')
@login_required
def open_config():
    return render_template('configuration.html')

#----------------------------------------------------------MANYCHAT CONNECTION--------------------------------------------------------------

headers = {
    "apikey": SUPABASE_ANON_KEY,
    "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=minimal"
}

#------------------comments--------------------------
# Route to update stats
#------------------comments--------------------------
@app.route('/update_stats', methods=['POST'])
@check_request_allowed
def update_stats():
    data = request.json
    
    # Get the current day of the week (lowercase)
    current_day = datetime.now().strftime('%a').lower()
 
    # Fetch the current row from the Total_users_chart table
    response = requests.get(f"{SUPABASE_URL}/rest/v1/Total_users_chart?select=*", headers=headers)
    db_data = response.json()

    if db_data:
        row = db_data[0]
        
        # Increment the count for the current day
        new_count = int(row[current_day]) + 1
        
        # Update the row in the database
        update_data = {current_day: str(new_count)}
        update_response = requests.patch(
            f"{SUPABASE_URL}/rest/v1/Total_users_chart?id=eq.{row['id']}",
            headers=headers,
            json=update_data
        )
        
        if update_response.status_code == 204:
            return jsonify({"message": f"Updated {current_day} count to {new_count}", "status": "success"}), 200
        else:
            return jsonify({"message": "Error updating data", "status": "error"}), 500
    else:
        return jsonify({"message": "No data found in the Total_users_chart table", "status": "error"}), 404
    
#--------------------------------------------------------------------------------------------------------------------------

#------------------comments--------------------------
# Route to fetch first row data
#------------------comments--------------------------
@app.route('/firstrowana')
@check_request_allowed
def fetch_firstrowana():
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }

    response = requests.get(f'{SUPABASE_URL}/rest/v1/firstrowana', headers=headers)

    if response.status_code != 200:
        print("Error fetching data:", response.text)
        return jsonify([]), response.status_code
    
    firstrowana = response.json()
    return jsonify(firstrowana)

#--------------------------------------------------------------------------------------------------------------------------

#------------------comments--------------------------
# Route to fetch tasks
#------------------comments--------------------------
@app.route('/fetch-tasks')
@check_request_allowed
def fetch_tasks():
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }
    response = requests.get(f'{SUPABASE_URL}/rest/v1/Tasks', headers=headers)

    if response.status_code != 200:
        print("Error fetching tasks:", response.text)
        return jsonify([]), response.status_code

    tasks = response.json()
    return jsonify(tasks)

#------------------comments--------------------------
# Route to fetch user list
#------------------comments--------------------------
@app.route('/getuserlist')
@check_request_allowed
def fetch_userlist():
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }
    response = requests.get(f'{SUPABASE_URL}/rest/v1/Users', headers=headers)

    if response.status_code != 200:
        print("Error fetching tasks:", response.text)
        return jsonify([]), response.status_code

    userlist = response.json()
    return jsonify(userlist)

#------------------comments--------------------------
# Route to fetch sales chart data
#------------------comments--------------------------
@app.route('/saleschart')
@check_request_allowed
def fetch_data_for_chart():
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }
    response = requests.get(f'{SUPABASE_URL}/rest/v1/salescharttips', headers=headers)
    sales_response = requests.get(f'{SUPABASE_URL}/rest/v1/sales_chartts', headers=headers)
    if sales_response.status_code != 200:
        print("Error fetching data:", sales_response.text)
        return jsonify([]), sales_response.status_code

    if response.status_code != 200:
        print("Error fetching tasks:", response.text)
        return jsonify([]), response.status_code

    sales_tips = response.json()
    sales_chart = sales_response.json()
    return jsonify({"sales_chart": sales_chart, "sales_tips": sales_tips})


#------------------comments--------------------------
# Route to fetch ai vs manual chart data
#------------------comments--------------------------

@app.route('/aivsmanual')
def fetch_data_for_aivsmanual():
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }
    response = requests.get(f'{SUPABASE_URL}/rest/v1/ai_interactions', headers=headers)
    man_response = requests.get(f'{SUPABASE_URL}/rest/v1/manual_interactions', headers=headers)
    if man_response.status_code != 200:
        print("Error fetching data:", man_response.text)
        return jsonify([]), man_response.status_code

    if response.status_code != 200:
        print("Error fetching tasks:", response.text)
        return jsonify([]), response.status_code

    ai_interactions = response.json()
    manual_interactions = man_response.json()
    return jsonify({"ai_inter": ai_interactions, "manual_inter": manual_interactions})


#------------------comments--------------------------
# Route to fetch first row sales data
#------------------comments--------------------------
@app.route('/firstrowsales')
@check_request_allowed
def fetch_firstrowsales():
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }
    response = requests.get(f'{SUPABASE_URL}/rest/v1/firstrowsales', headers=headers)

    if response.status_code != 200:
        print("Error fetching tasks:", response.text)
        return jsonify([]), response.status_code

    tasks = response.json()
    return jsonify(tasks)

#------------------comments--------------------------
# Route to delete a task
#------------------comments--------------------------
@app.route('/delete-task/<int:task_id>', methods=['DELETE'])
@check_request_allowed
def delete_task(task_id):
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }
    response = requests.delete(f'{SUPABASE_URL}/rest/v1/Tasks?id=eq.{task_id}', headers=headers)

    if response.status_code != 204:
        print(f"Error deleting task {task_id}:", response.text)
        return jsonify({"error": "Failed to delete task"}), response.status_code

    return jsonify({"message": "Task deleted successfully"}), 200

#------------------comments--------------------------
# Route to fetch users
#------------------comments--------------------------
@app.route('/fetch-users')
@check_request_allowed
def fetch_users():
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }
    response = requests.get(f'{SUPABASE_URL}/rest/v1/Users', headers=headers)

    if response.status_code != 200:
        print("Error fetching users:", response.text)
        return jsonify([]), response.status_code

    users = response.json()
    return jsonify(users)

#------------------comments--------------------------
# Route to fetch platforms
#------------------comments--------------------------
@app.route('/fetch-platforms')
@check_request_allowed
def fetch_platforms():
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }
    response = requests.get(f'{SUPABASE_URL}/rest/v1/platforms', headers=headers)

    if response.status_code != 200:
        print("Error fetching platforms data:", response.text)
        return jsonify([]), response.status_code

    platforms_data = response.json()
    return jsonify(platforms_data)

#------------------comments--------------------------
# Route to fetch data
#------------------comments--------------------------
@app.route('/fetch-data')
@check_request_allowed
def fetch_data():
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }
    response = requests.get(f'{SUPABASE_URL}/rest/v1/firstrowdash', headers=headers)

    if response.status_code != 200:
        print("Error fetching data:", response.text)
        return jsonify([]), response.status_code

    rows = response.json()
    print("Fetched data:", rows)
    return jsonify(rows)

#------------------comments--------------------------
# Route to fetch customers
#------------------comments--------------------------
@app.route('/customers')
@check_request_allowed
def fetch_customers():
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }
    response = requests.get(f'{SUPABASE_URL}/rest/v1/customers', headers=headers)

    if response.status_code != 200:
        print("Error fetching data:", response.text)
        return jsonify([]), response.status_code

    rows = response.json()
    print("Fetched data:", rows)
    return jsonify(rows)

#------------------comments--------------------------
# Route to fetch tpp data
#------------------comments--------------------------
@app.route('/fetch-tpp')
@check_request_allowed
def fetch_tpp():
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }
    response = requests.get(f'{SUPABASE_URL}/rest/v1/tppd', headers=headers)

    if response.status_code != 200:
        print("Error fetching tpp data:", response.text)
        return jsonify([]), response.status_code

    tpp_data = response.json()
    return jsonify(tpp_data)

#------------------comments--------------------------
# Route to add a user
#------------------comments--------------------------
@app.route('/add_user', methods=['POST'])
@check_request_allowed
def add_user():
    data = request.json
    name = data.get('name')
    phone = data.get('phone')
    room_no = data.get('room_no')
    first_message = data.get('first_message')
    platform = data.get('platform')

    if not all([name, phone, room_no, first_message, platform]):
        return jsonify({'success': False, 'error': 'Missing required fields'}), 400

    with open('chats.json', 'r', encoding='utf-8') as f:
        chat_data = json.load(f)
        existing_user = next((u for u in chat_data['users'] if u.get('phone_number') == phone), None)
        
        if existing_user:
            existing_user['name'] = name
            existing_user['room_number'] = room_no
            existing_user['status'] = 'Online'
            existing_user['platform'] = platform
            user_id = existing_user['id']
        else:
            new_user_id = len(chat_data['users'])
            new_user = {
                'id': new_user_id,
                'name': name,
                'status': 'Online',
                'avatar': 'https://generated.vusercontent.net/placeholder-user.jpg',
                'phone_number': phone,
                'room_number': room_no,
                'platform': platform
            }
            chat_data['users'].append(new_user)
            user_id = new_user_id

        new_message = {
            'sender': name,
            'message': first_message,
            'timestamp': datetime.now().strftime("%H:%M"),
            'customer': True
        }
        
        if str(user_id) not in chat_data['chats']:
            chat_data['chats'][str(user_id)] = []
        
        chat_data['chats'][str(user_id)].append(new_message)

        with open('chats.json', 'w', encoding='utf-8') as f:
            json.dump(chat_data, f, indent=2, ensure_ascii=False)

    whatsapp_message = f"We are working on your request:\n\n{first_message}\n\nThank you for your patience. One of our staff members will contact you shortly."

    send_whatsapp_message(phone, whatsapp_message)

    logger.info(f"{Fore.GREEN}Added or updated user and first message for user ID: {user_id}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Added or updated user and first message for user ID: {user_id}{Style.RESET_ALL}")

    return jsonify({'success': True, 'user_id': user_id})

#------------------comments--------------------------
# Route to get chat data
#------------------comments--------------------------
@app.route('/get_chat_data')
@check_request_allowed
def get_chat_data():
    try:
        with open('chats.json', 'r', encoding='utf-8') as f:
            chat_data = json.load(f)
        logger.info(f"{Fore.YELLOW}Fetched chat data{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Fetched chat data{Style.RESET_ALL}")
        return jsonify(chat_data)
    except json.JSONDecodeError as e:
        logger.error(f"{Fore.RED}Error decoding JSON: {str(e)}{Style.RESET_ALL}")
        print(f"{Fore.RED}Error decoding JSON: {str(e)}{Style.RESET_ALL}")
        return jsonify({'error': 'Invalid JSON data'}), 500
    except IOError as e:
        logger.error(f"{Fore.RED}Error reading file: {str(e)}{Style.RESET_ALL}")
        print(f"{Fore.RED}Error reading file: {str(e)}{Style.RESET_ALL}")
        return jsonify({'error': 'Could not read chat data'}), 500

#------------------comments--------------------------
# Route to mark conversation as closed
#------------------comments--------------------------
@app.route('/mark_conversation_closed', methods=['POST'])
@check_request_allowed
def mark_conversation_closed():
    data = request.json
    user_id = data.get('userId')
    
    if user_id is None:
        return jsonify({'success': False, 'error': 'User ID is required'}), 400

    with open('chats.json', 'r', encoding='utf-8') as f:
        chat_data = json.load(f)

    user = next((u for u in chat_data['users'] if u['id'] == int(user_id)), None)
    if user is None:
        return jsonify({'success': False, 'error': 'User not found'}), 404

    close_message = "Your conversation has been closed. If you need further assistance, please start a new chat."
    
    if 'phone_number' in user:
        send_whatsapp_message(user['phone_number'], close_message)

    chat_data['users'] = [u for u in chat_data['users'] if u['id'] != int(user_id)]

    if str(user_id) in chat_data['chats']:
        del chat_data['chats'][str(user_id)]

    with open('chats.json', 'w', encoding='utf-8') as f:
        json.dump(chat_data, f, indent=2, ensure_ascii=False)

    logger.info(f"{Fore.GREEN}Marked conversation as closed and removed user ID: {user_id}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Marked conversation as closed and removed user ID: {user_id}{Style.RESET_ALL}")

    return jsonify({'success': True})

#------------------comments--------------------------
# Route to send a message
#------------------comments--------------------------
@app.route('/send_message', methods=['POST'])
def send_message():
    try:
        data = request.json
        logger.info(f"{Fore.MAGENTA}Received message data from dashboard: {data}{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}Received message data from dashboard: {data}{Style.RESET_ALL}")
        
        user_id = str(data.get('userId'))  # Convert to string and use .get() for safety
        message = data.get('message')
        timestamp = data.get('timestamp')
        sender = data.get('sender')
        
        if not all([user_id, message, timestamp, sender]):
            raise ValueError("Missing required fields in the request data")
        
        with open('chats.json', 'r', encoding='utf-8') as f:
            chat_data = json.load(f)
        
        new_message = {
            'sender': sender,
            'message': message,
            'timestamp': timestamp,
            'customer': False
        }
        
        if user_id not in chat_data['chats']:
            chat_data['chats'][user_id] = []
        
        chat_data['chats'][user_id].append(new_message)
        
        with open('chats.json', 'w', encoding='utf-8') as f:
            json.dump(chat_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"{Fore.GREEN}Added message to chats.json{Style.RESET_ALL}")
        print(f"{Fore.GREEN}Added message to chats.json{Style.RESET_ALL}")
        
        user = next((u for u in chat_data['users'] if str(u['id']) == user_id), None)
        if user and user.get('phone_number'):
            print(f"{Fore.CYAN}Message from dashboard to {user['phone_number']}: {message}{Style.RESET_ALL}")
            
            result = send_whatsapp_message(user['phone_number'], message)
            logger.info(f"{Fore.BLUE}WhatsApp API response: {result}{Style.RESET_ALL}")
            print(f"{Fore.BLUE}WhatsApp API response: {result}{Style.RESET_ALL}")
            return jsonify(success=True, whatsapp_response=result)
        else:
            error_message = f"User not found or phone number missing for user ID: {user_id}"
            logger.warning(f"{Fore.RED}{error_message}{Style.RESET_ALL}")
            print(f"{Fore.RED}{error_message}{Style.RESET_ALL}")
            return jsonify(success=False, error=error_message), 400
    
    except json.JSONDecodeError:
        error_message = "Invalid JSON in chats.json file"
        logger.error(f"{Fore.RED}{error_message}{Style.RESET_ALL}")
        return jsonify(success=False, error=error_message), 500
    
    except Exception as e:
        error_message = f"An unexpected error occurred: {str(e)}"
        logger.error(f"{Fore.RED}{error_message}{Style.RESET_ALL}")
        return jsonify(success=False, error=error_message), 500

#------------------comments--------------------------
# Webhook route for incoming messages
#------------------comments--------------------------
@app.route('/webhook', methods=['GET', 'POST'])
def webhook():
    if request.method == 'POST':
        incoming_msg = request.values.get('Body', '').lower()
        from_number = request.values.get('From', '').replace('whatsapp:', '')
        
        logger.info(f"{Fore.YELLOW}Received webhook data: {incoming_msg} from {from_number}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Received webhook data: {incoming_msg} from {from_number}{Style.RESET_ALL}")
        
        handle_incoming_message(from_number, incoming_msg)
        
        resp = MessagingResponse()
        return str(resp)
    elif request.method == 'GET':
        # Twilio doesn't require verification in the same way as Meta's Webhook
        # You can implement any necessary verification logic here
        return '', 200

#------------------comments--------------------------
# Function to handle incoming messages
#------------------comments--------------------------
def handle_incoming_message(phone_number, message):
    logger.info(f"{Fore.CYAN}Handling incoming message from {phone_number}: {message}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Handling incoming message from {phone_number}: {message}{Style.RESET_ALL}")
    with open('chats.json', 'r', encoding='utf-8') as f:
        chat_data = json.load(f)
    
    user = next((u for u in chat_data['users'] if u.get('phone_number') == phone_number), None)
    
    if user is None:
        new_user_id = len(chat_data['users'])
        new_user = {
            'id': new_user_id,
            'name': f'User {new_user_id}',
            'status': 'Online',
            'avatar': 'https://generated.vusercontent.net/placeholder-user.jpg',
            'phone_number': phone_number,
            'room_number': 'None',
            'platform': 'WhatsApp'
        }
        chat_data['users'].append(new_user)
        chat_data['chats'][str(new_user_id)] = []
        user = new_user
        logger.info(f"{Fore.GREEN}Created new user: {new_user}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}Created new user: {new_user}{Style.RESET_ALL}")
    
    new_message = {
        'sender': user['name'],
        'message': message,
        'timestamp': datetime.now().strftime("%H:%M"),
        'customer': True
    }
    
    chat_data['chats'][str(user['id'])].append(new_message)
    
    with open('chats.json', 'w', encoding='utf-8') as f:
        json.dump(chat_data, f, indent=2, ensure_ascii=False)
    
    logger.info(f"{Fore.GREEN}Added incoming message to chats.json{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Added incoming message to chats.json{Style.RESET_ALL}")

#------------------comments--------------------------
# Function to send WhatsApp messages
#------------------comments--------------------------
def send_whatsapp_message(phone_number, message):
    logger.info(f"{Fore.BLUE}Sending WhatsApp message to {phone_number}: {message}{Style.RESET_ALL}")
    print(f"{Fore.BLUE}Sending WhatsApp message to {phone_number}: {message}{Style.RESET_ALL}")
    try:
        message = twilio_client.messages.create(
            body=message,
            from_=f'whatsapp:{TWILIO_PHONE_NUMBER}',
            to=f'whatsapp:{phone_number}'
        )
        logger.info(f"{Fore.GREEN}Successfully sent WhatsApp message. SID: {message.sid}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}Successfully sent WhatsApp message. SID: {message.sid}{Style.RESET_ALL}")
        return {"success": True, "message_sid": message.sid}
    except Exception as e:
        error_message = f"Error sending WhatsApp message: {str(e)}"
        logger.error(f"{Fore.RED}{error_message}{Style.RESET_ALL}")
        print(f"{Fore.RED}{error_message}{Style.RESET_ALL}")
        return {"error": error_message}

if __name__ == '__main__':
    logger.info(f"{Fore.GREEN}Starting Flask application{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Starting Flask application{Style.RESET_ALL}")
    app.run(debug=True, port=5000)