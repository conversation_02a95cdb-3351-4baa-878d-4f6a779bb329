<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidebar</title>
    <link rel="stylesheet" href="../static/styles/custom.css">

    <!-- Franken UI -->
    <link rel="preconnect" href="https://rsms.me/" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />

    <link
      rel="stylesheet"
      href="https://unpkg.com/franken-ui/dist/css/core.min.css"
    />

    <script
      type="module"
      src="https://unpkg.com/franken-ui/dist/js/core.iife.js"
    ></script>
    <script
      type="module"
      src="https://unpkg.com/franken-ui/dist/js/icon.iife.js"
    ></script>
</head>
<body class="bg-background text-foreground">
  <div class="border-r card">
    <div class="flex h-full max-h-screen flex-col gap-2">
        <div class="card flex h-[60px] items-center border-b px-6">
            <a class="flex items-center gap-2 font-semibold" href="#">
                <img src="../static/images/logo.png" width="40" height="40" alt="Logo">
                <span>Guest Genius</span>
            </a>
        </div>
        <div class="card flex-1 overflow-auto py-2">
            <nav class="grid items-start px-4 text-sm font-medium">
                <a class="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900" href="/">
                    <uk-icon icon="monitor-cog"></uk-icon> Dashboard
                </a>
                <a class="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900" href="/users">
                    <uk-icon icon="user"></uk-icon> Users
                </a>
                <a class="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900" href="/sales">
                    <uk-icon icon="dollar-sign"></uk-icon> Sales
                </a>
                <a class="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900" href="/analytics">
                    <uk-icon icon="chart-spline"></uk-icon> Analytics
                </a>
                <a class="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900" href="/tasks">
                    <uk-icon icon="calendar-check-2"></uk-icon> Tasks
                </a>
                <a class="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900" href="/livechat">
                    <uk-icon icon="message-circle"></uk-icon> Live Chat
                </a>
                <a class="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900" href="/issue">
                    <uk-icon icon="circle-alert"></uk-icon> Issues
                </a>
                <a class="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900" href="/pmsmanagement">
                    <uk-icon icon="cpu"></uk-icon> PMS System
                </a>
                <a class="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900" href="/pmsanalytics">
                    <uk-icon icon="chart-column"></uk-icon> PMS Analytics
                </a>
                <a class="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900" href="/settings">
                    <uk-icon icon="settings"></uk-icon> Settings
                </a>
            </nav>
        </div>
    </div>
</div>
</body>
</html>