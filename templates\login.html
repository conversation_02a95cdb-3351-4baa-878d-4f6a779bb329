<!-- TESTING -->
<!--Dix testing-->
<!--(The UI is done and perfect, Backend is done and perfect.)-->
<!--Just test out the security and make sure its perfect-->
<!--ESTIMATED TIME : 5-10 min-->

<!-- TESTED OUT SECURITY, IT'S NOT TOP NOTCH BUT IT'S ENOUGH SINCE WE DON'T HAVE TO USE DATABASE -->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guest <PERSON><PERSON> - Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.7.6/dist/css/uikit.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.7.6/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.7.6/dist/js/uikit-icons.min.js"></script>
    <!-- <script src="../static/js/themes.js" defer></script> -->
    {% include 'imports.html' %}
    <style>
        .pure-black .login-button {
            background-color: #4F46E5;
            color: #fff;
        }

        .pure-black .login-button:hover {
            background-color: #403ac2;
            color: #fff;
        }
        
        /* Enhanced autofill style overrides */
        input:-webkit-autofill,
        input:-webkit-autofill:hover, 
        input:-webkit-autofill:focus,
        input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
            -webkit-text-fill-color: inherit !important;
            transition: background-color 5000s ease-in-out 0s;
            background-color: transparent !important;
            background: transparent !important;
            caret-color: inherit;
        }
        
        /* For Firefox and other browsers */
        input:autofill {
            background-color: transparent !important;
            color: inherit !important;
            appearance: none !important;
        }
        
        /* Dark mode specific overrides */
        .dark-mode input:-webkit-autofill,
        .pure-black input:-webkit-autofill,
        .dark input:-webkit-autofill {
            -webkit-text-fill-color: #fff !important;
            background-color: transparent !important;
            color: #fff !important;
        }
        
        /* Enforce transparent backgrounds on inputs */
        .dark-mode input, 
        .pure-black input,
        .dark input {
            background-color: transparent !important;
            color: inherit !important;
        }
    </style>

</head>

<body>
    <div class="flex min-h-screen flex-col justify-center px-6 py-12 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-sm">

            <h3 class="text-center text-2xl/9 font-bold tracking-tight ">Sign in to your account</h3>
        </div>

        <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
            {% if error %}
            <p class="text-center text-red-600 mb-4">{{ error }}</p>
            {% endif %}
            <form class="space-y-6" action="/login" method="POST" autocomplete="off">
                <div>
                    <label for="username" class="block text-sm/6 font-medium ">Email address</label>
                    <div class="mt-2">
                        <input type="text" name="username" id="username" autocomplete="off" required
                            class="block w-full rounded-md  px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6">
                    </div>
                </div>

                <div>
                    <div class="flex items-center justify-between">
                        <label for="password" class="block text-sm/6 font-medium ">Password</label>
                        <div class="text-sm">
                            <a href="#" class="font-semibold text-indigo-600 hover:text-indigo-500">Forgot password?</a>
                        </div>
                    </div>
                    <div class="mt-2">
                        <input type="password" name="password" id="password" autocomplete="off" required
                            class="block w-full rounded-md px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6">
                    </div>
                </div>

                <div>
                    <button type="submit"
                        class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm/6 font-semibold text-white shadow-xs bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 login-button">Sign
                        in</button>
                </div>
            </form>

            <p class="mt-10 text-center text-sm/6 text-gray-500">
                Not a member?   
                <a href="#" style="margin-left: 5px;" class="font-semibold text-indigo-600 hover:text-indigo-500">Reach out to us today!</a>
            </p>
        </div>
    </div>
    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
          e.preventDefault();
          const formData = new FormData(e.target);
          try {
            const response = await fetch('/login', {
              method: 'POST',
              body: formData
            });
            const result = await response.json(); // Adjust if needed
            if (result.error) {
              // show error message without reloading
              alert(result.error);
            } else {
              // login successful
              window.location.href = '/'; // or wherever
            }
          } catch (err) {
            console.error(err);
          }
        });
        </script>
</body>

</html>
