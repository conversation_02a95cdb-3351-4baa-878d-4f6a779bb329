document.querySelectorAll('.file-upload-area').forEach(area => {
    const input = area.querySelector('input[type="file"]');
    const label = area.querySelector('p');
 
    area.addEventListener('dragover', (e) => {
       e.preventDefault();
       area.classList.add('border-blue-500');
    });
 
    area.addEventListener('dragleave', () => {
       area.classList.remove('border-blue-500');
    });
 
    area.addEventListener('drop', (e) => {
       e.preventDefault();
       area.classList.remove('border-blue-500');
       input.files = e.dataTransfer.files;
       updateFileName(input, label);
    });
 
    input.addEventListener('change', () => {
       updateFileName(input, label);
    });
 });
 
 function updateFileName(input, label) {
    if (input.files.length > 0) {
       label.textContent = input.files[0].name;
    }
 }