<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Tasks</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/slate.min.css"/>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit-icons.min.js"></script>
    <link rel="stylesheet" href="../static/styles/custom.css">
    <link rel="stylesheet" href="../static/styles/scrollbar.css">
    <script src="../static/js/languagetranslator.js" defer></script>
    <script src="../static/js/themes.js" defer></script>
    <link rel="stylesheet" href="../static/styles/loadinganimations.css">
    <script src="../static/js/loading.js" defer></script>
    <script src="../static/js/dropdownslogic.js" defer></script>

    <!-- Franken UI -->
    <link rel="preconnect" href="https://rsms.me/" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />

    <link
      rel="stylesheet"
      href="https://unpkg.com/franken-ui/dist/css/core.min.css"
    />

    <script
      type="module"
      src="https://unpkg.com/franken-ui/dist/js/core.iife.js"
    ></script>
    <script
      type="module"
      src="https://unpkg.com/franken-ui/dist/js/icon.iife.js"
    ></script>
  </head>
  <body class="bg-background text-foreground">

    <!-- Franken UI -->


    <style>
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }
        .table-container {
            max-height: calc(100vh - 200px);
            overflow-y: visible;
        }
        
        .task-item {
            transition: transform 0.5s ease-out, opacity 0.5s ease-out;
        }
        .task-disappear {
            opacity: 0;
            transform: translateY(-20px);
        }
        .table-container thead {
            position: sticky;
            top: 0;
            z-index: 1;
            background-color: #fff;
        }
        .platform-tag {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: #d1fae5;
            color: #065f46;
        }
        .platform-tag-icon {
            width: 10px;
            height: 10px;
            margin-right: 4px;
            opacity: 0.7;
        }
        .task-disappear {
            animation: disappear 0.5s ease-out forwards;
        }
        @keyframes disappear {
            0% {
                opacity: 1;
                transform: translateY(0);
            }
            100% {
                opacity: 0;
                transform: translateY(-20px);
            }
        }
        .checked {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
        }
        .line-through {
            text-decoration: line-through;
        }
        .opacity-0 {
            opacity: 0;
            transition: opacity 0.5s ease-out;
        }
        .hidden {
            display: none;
        }
        .checked::after {
            content: '';
            display: block;
            position: absolute;
            width: 1.2em;
            height: 1.2em;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
        }
        #taskPopup {
            transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
            visibility: hidden;
            opacity: 0;
        }
        #taskPopup.show {
            visibility: visible;
            opacity: 1;
        }
        #taskPopup > div {
            transition: transform 0.3s ease-in-out;
            transform: scale(0.9);
        }
        #taskPopup.show > div {
            transform: scale(1);
        }
        
        .task-item:not(:last-child) {
            border-bottom: 1px solid #e4e4e7;
            padding-bottom: 16px;
            margin-bottom: 16px;
        }
        :root {
            --checkbox-border-color: #2a2a2d; /* or any other dark color you prefer */
        }
        .checkboxwomp.checked::after {
            content: '\2715'; /* Unicode for "×" */
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            color: white;
            font-size: 0.8em;
        }

        .checkboxwomp.disabled {
            pointer-events: none;
            opacity: 0.6;
        }

        .task-text.line-through {
            text-decoration: line-through;
            opacity: 0.7;
        }

        .checkboxwomp[data-state=unchecked] {
            border-color: var(--checkbox-border-color);
        }
        .error-message {
            color: red;
            font-weight: bold;
            margin-top: 20px;
        }
        .open-new-tab {
            margin-top: 10px;
            display: inline-block;
            color: blue;
            text-decoration: underline;
            cursor: pointer;
        }
        .task-text {
        margin-left: 10px; /* Add this line to create space between checkbox and text */
    }
    </style>
</head>
<body class="light">
    <div id="loading-overlay" class="loading-overlay">
        <div class="loader"></div>
    </div>

    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] overflow-hidden">
        {% include 'sidebar.html' %}

        <div class="flex flex-col">
            <header class="card flex h-14 lg:h-[60px] items-center justify-between px-6 border-b border-gray-200">
                <a class="lg:hidden" href="#">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                        <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                        <path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9"></path>
                        <path d="M12 3v6"></path>
                    </svg>
                    <span class="sr-only">Home</span>
                </a>
                
                <div class="flex-1 flex items-center justify-center lg:justify-start">
                    <form id="searchForm" class="relative w-full max-w-md">
                        <input id="searchInput" class="uk-input pr-10 pl-4 py-2 w-full rounded border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500" type="text" placeholder="Search..." aria-label="Search" />
                        <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 bg-transparent border-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-4.35-4.35M17 11a6 6 0 11-12 0 6 6 0 0112 0z" />
                            </svg>
                        </button>
                    </form>
                </div>
            </header>
            <div id="contentArea" class="flex-grow">
                <!-- Iframe to display search results and loaded URLs -->
                <iframe
                    id="searchResultsFrame"
                    name="searchResultsFrame"
                    style="width: 100%; height: 600px; border: none;"
                ></iframe>
                <!-- Google Custom Search Engine -->
                <script async src="https://cse.google.com/cse.js?cx=0441a6c4c1bed4254"></script>
                <div class="gcse-search" data-linktarget="searchResultsFrame"></div>
            </div>
        </div>
    </div>
    <!-- templates/pmsmanagement.html -->
    <!-- Single Script Block -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchForm = document.getElementById('searchForm');
        const searchInput = document.getElementById('searchInput');
        const searchButton = document.getElementById('searchButton');
        const contentArea = document.getElementById('contentArea');
        const loadingOverlay = document.getElementById('loading-overlay');

        function validateURL(url) {
            const pattern = new RegExp('^(https?:\\/\\/)?' + // protocol
                '((([a-zA-Z\\d]([a-zA-Z\\d-]*[a-zA-Z\\d])*)\\.)+[a-zA-Z]{2,})' + // domain name
                '(\\:\\d+)?(\\/[-a-zA-Z\\d%_.~+]*)*' + // port and path
                '(\\?[;&a-zA-Z\\d%_.~+=-]*)?' + // query string
                '(\\#[-a-zA-Z\\d_]*)?$', 'i');
            return !!pattern.test(url);
        }

        function formatURL(url) {
            if (!/^https?:\/\//i.test(url)) {
                return 'https://' + url;
            }
            return url;
        }

        function performSearch() {
            let searchTerm = searchInput.value.trim();
            if (searchTerm) {
                if (!validateURL(searchTerm)) {
                    alert('Please enter a valid URL.');
                    return;
                }

                searchTerm = formatURL(searchTerm);
                loadingOverlay.style.display = 'flex';

                // Load the URL in the existing iframe
                const iframe = document.getElementById('searchResultsFrame');
                iframe.src = searchTerm;

                iframe.onload = function() {
                    loadingOverlay.style.display = 'none';
                };

                iframe.onerror = function() {
                    loadingOverlay.style.display = 'none';
                    showErrorMessage(searchTerm);
                };
            }
        }

        function showErrorMessage(url) {
            const message = document.createElement('div');
            message.className = 'error-message';
            message.textContent = `Cannot load "${url}". This site may not allow embedding.`;

            const openNewTab = document.createElement('div');
            openNewTab.className = 'open-new-tab';
            openNewTab.textContent = 'Open in a new tab';
            openNewTab.addEventListener('click', function() {
                window.open(url, '_blank');
            });

            contentArea.innerHTML = '';
            contentArea.appendChild(message);
            contentArea.appendChild(openNewTab);
        }

        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            performSearch();
        });

        searchButton.addEventListener('click', function(e) {
            e.preventDefault();
            performSearch();
        });
    });
</script>
</body>
</html>