from flask import Flask, request
from twilio.rest import Client
from twilio.twiml.messaging_response import MessagingResponse
import os

app = Flask(__name__)

# Twilio account credentials
account_sid = '**********************************'
auth_token = '005d910f85546392a91f58a3878c437c'
client = Client(account_sid, auth_token)

# WhatsApp numbers (from your Twilio sandbox)
from_whatsapp_number = 'whatsapp:+***********'  # Twilio Sandbox WhatsApp number
to_whatsapp_number = 'whatsapp:+************'  # Replace with your WhatsApp number

# Food service message SID
food_service_sid = 'HXbca814f4d04dd16d2b9e48e84d9bbdfc'  # SID for the food service message template

def send_carousel():
    # Send the message with the carousel content
    message = client.messages.create(
        from_=from_whatsapp_number,
        to=to_whatsapp_number,
        content_sid=food_service_sid
    )
    print(f"Message sent! SID: {message.sid}")

send_carousel()