import requests
import os

# Twilio account details (replace with actual Account SID and Auth Token)
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')

variable = "HX74a4f19dc28d264a2f110d1259544995"

room_book = "HX90086324ff4757c28a22446152eab6e6"

food_or_bev = "HX8753a155bd88ef79de7699c8e6558362"

experiences = "HX50460e90855bb0fd7417eccb26e4e63a"

# Twilio API endpoint for the GET request
url = f'https://content.twilio.com/v1/Content/{food_or_bev}/ApprovalRequests'

# Making the GET request
response = requests.get(
    url,
    auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN),
    headers={'Content-Type': 'application/json'}
)

# Check the response status
if response.status_code == 200:
    # If the request was successful, print the response JSON
    print(response.json())
else:
    print(f"Request failed with status: {response.status_code}")
    print(response.text)
