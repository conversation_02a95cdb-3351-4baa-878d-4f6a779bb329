<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../static/styles/custom.css">
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <style>
        .sidebar-transition {
            transition: width 0.3s ease, padding 0.3s ease;
        }
        .menu-item-transition {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .sidebar-label {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .hidden {
            transform: translateX(-10px);
        }
        .opacity-0 {
            opacity: 0;
        }
        /* Center icons when sidebar is collapsed */
        .w-20 .sidebar-link {
            justify-content: center;
        }
        /* Adjust the logo container */
        .logo-container {
            transition: all 0.3s ease;
        }
        .w-20 .logo-container {
            justify-content: center;
        }
        
        /* Update Partition Styles */
        .partition {
            display: block;
            height: 1px;
            background-color: var(--theme-selector-border);
            margin: 1px -16px; /* Reduced from 4px to 1px */
            width: calc(100% + 32px);
            position: relative;
            left: 0;
            /* Add these properties to prevent flickering */
            transition: none !important;
            opacity: 1 !important;
            transform: none !important;
            visibility: visible !important;
        }
        #sidebar.w-20 .partition {
            width: calc(100% + 32px);
            margin: 1px -16px;
            /* Add these properties to prevent flickering */
            transition: none !important;
            opacity: 1 !important;
            transform: none !important;
            visibility: visible !important;
        }

        /* Update label styles */
        .text-gray-500 {
            color: rgb(107 114 128) !important; /* Reverting to original gray-500 color */
            font-size: 0.75rem !important; /* Reducing font size */
        }
        .grid {
            transition: grid-template-columns 0.3s ease;
        }
        .grid.collapsed {
            grid-template-columns: 60px 1fr; /* Adjusted for collapsed state */
        }
    
        /* Hide scrollbar for sidebar */
        #sidebar::-webkit-scrollbar {
            display: none;
        }
        #sidebar {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
        #logo-img {
            min-width: 40px !important;
            min-height: 40px !important;
        }
        .logo-image.collapsed {
            width: 20px;
            height: 20px;
        }
        /* Custom styles for toggle button positioning */
        #toggle-btn {
            /* Ensures the button is vertically centered */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Optional: Increase the clickable area of the toggle button */
        #toggle-btn {
            padding: 0.8rem;
        }

        /* Update label color */
        .text-gray-500 {
            color: rgb(107 114 128) !important;
            font-size: 0.75rem !important;
        }

        /* Update or add these styles */
        .flex-1 {
            overflow-y: hidden !important; /* Change from overflow-auto to hidden */
        }
        
        /* Ensure existing scrollbar hiding styles are working */
        #sidebar::-webkit-scrollbar,
        .flex-1::-webkit-scrollbar {
            display: none;
            width: 0;
        }
        
        #sidebar,
        .flex-1 {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }

        /* Add these new styles */
        .sidebar-link {
            position: relative;
            transition: background-color 0.2s ease;
        }

        .sidebar-link:hover {
            background-color: var(--accent);
            color: var(--accent-foreground);
        }

        .sidebar-link.active {
            background-color: var(--dropdown-hover-bg);
            color: var(--accent-foreground);
            font-weight: 500;
        }

        .sidebar-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background-color: var(--primary);
            border-radius: 0 4px 4px 0;
        }
    </style>
</head>
<body class="bg-background text-foreground">
    <div id="sidebar" class="border-r card sidebar-transition w-full">
        <div class="flex h-full flex-col">
            <!-- Header Section with 'group' class applied to enable group-hover -->
            <div class="flex h-[60px] items-center border-b px-2 card justify-between group">
                <a class="flex items-center gap-2 font-semibold logo-container" href="#">
                    <img src="../static/images/logo.png" class="logo-image w-5" alt="Guest Genius Logo" id="logo-img">
                    <span class="text-base sidebar-label" id="brand-text">Guest Genius</span>
                </a>
                <!-- Toggle Button Hidden by Default and Visible on Hover -->
                <button id="toggle-btn" class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 focus:outline-none" style="background-color: transparent !important;">
                    <svg id="toggle-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevrons-left">
                        <path d="m11 17-5-5 5-5"/>
                        <path d="m18 17-5-5 5-5"/>
                    </svg>
                </button>
            </div>
            <!-- Navigation -->
            <div class="flex-1 overflow-auto py-2">
                <nav class="flex flex-col gap-4 px-3">
                    <!-- Main Pages Section -->
                    <div>
                        <div class="mt-4 flex items-center justify-between px-2">
                            <h3 class="mb-0 text-sm font-semibold text-gray-500 sidebar-label">MAIN</h3>
                        </div>
                        <div class="flex flex-col gap-1">
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                                </svg>
                                <span class="sidebar-label">Dashboard</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/users">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="9" cy="7" r="4"></circle>
                                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                </svg>
                                <span class="sidebar-label">Users</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/sales">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="12" x2="12" y1="2" y2="22"></line>
                                    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                                </svg>
                                <span class="sidebar-label">Sales</span>
                            </a>
                        </div>
                    </div>

                    <!-- Partition -->
                    <div class="partition"></div>

                    <!-- Analytics Section -->
                    <div>
                        <h3 class="mb-1 px-2 text-sm font-semibold text-gray-500 sidebar-label">ANALYTICS</h3>
                        <div class="flex flex-col gap-1">
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/analytics">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 3v18h18"></path>
                                    <path d="m19 9-5 5-4-4-3 3"></path>
                                </svg>
                                <span class="sidebar-label">Analytics</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/pmsanalytics">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-scatter"><circle cx="7.5" cy="7.5" r=".5" fill="currentColor"/><circle cx="18.5" cy="5.5" r=".5" fill="currentColor"/><circle cx="11.5" cy="11.5" r=".5" fill="currentColor"/><circle cx="7.5" cy="16.5" r=".5" fill="currentColor"/><circle cx="17.5" cy="14.5" r=".5" fill="currentColor"/><path d="M3 3v16a2 2 0 0 0 2 2h16"/></svg>
                                <span class="sidebar-label">PMS Analytics</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="#">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 3h7v7H3z"></path>
                                    <path d="M14 3h7v7h-7z"></path>
                                    <path d="M14 14h7v7h-7z"></path>
                                    <path d="M3 14h7v7H3z"></path>
                                </svg>
                                <span class="sidebar-label">GG x PMS Analytics</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="#">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="4" y1="20" x2="4" y2="10"></line>
                                    <line x1="12" y1="20" x2="12" y2="4"></line>
                                    <line x1="20" y1="20" x2="20" y2="14"></line>
                                </svg>
                                <span class="sidebar-label">Google Analytics</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="#">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-pie">
                                    <path d="M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z"/>
                                    <path d="M21.21 15.89A10 10 0 1 1 8 2.83"/>
                                </svg>
                                <span class="sidebar-label">Platform Analytics</span>
                            </a>
                        </div>
                    </div>

                    <!-- Partition -->
                    <div class="partition"></div>

                    <!-- Tools Section -->
                    <div>
                        <h3 class="mb-1 px-2 text-sm font-semibold text-gray-500 sidebar-label">TOOLS</h3>
                        <div class="flex flex-col gap-1">
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/tasks">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M8 2v4"></path>
                                    <path d="M16 2v4"></path>
                                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                    <path d="M3 10h18"></path>
                                </svg>
                                <span class="sidebar-label">Tasks</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/livechat">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path>
                                </svg>
                                <span class="sidebar-label">Live Chat</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="#">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" x2="12" y1="8" y2="12"></line>
                                    <line x1="12" x2="12.01" y1="16" y2="16"></line>
                                </svg>
                                <span class="sidebar-label">Issues</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="#">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-history"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/><path d="M3 3v5h5"/><path d="M12 7v5l4 2"/></svg>
                                <span class="sidebar-label">History</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="#">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bot-message-square"><path d="M12 6V2H8"/><path d="m8 18-4 4V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2Z"/><path d="M2 12h2"/><path d="M9 11v2"/><path d="M15 11v2"/><path d="M20 12h2"/></svg>
                                <span class="sidebar-label">GG - AI</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/products">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shopping-cart"><circle cx="8" cy="21" r="1"/><circle cx="19" cy="21" r="1"/><path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"/></svg>
                                <span class="sidebar-label">Products</span>
                            </a>
                        </div>
                    </div>

                    <!-- Partition -->
                    <div class="partition"></div>

                    <!-- Settings Section -->
                    <div>
                        <h3 class="mb-1 px-2 text-sm font-semibold text-gray-500 sidebar-label">SETTINGS</h3>
                        <div class="flex flex-col gap-1">
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/settings">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="3"></circle>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                </svg>
                                <span class="sidebar-label">Settings</span>
                            </a>
                        </div>
                    </div>
                </nav>
            </div>
        </div>
    </div>
    <script>
        // JavaScript for Toggle Functionality with Persistent State
        const toggleBtn = document.getElementById('toggle-btn');
        const sidebar = document.getElementById('sidebar');
        const sidebarLabels = document.querySelectorAll('.sidebar-label');
        const toggleIcon = document.getElementById('toggle-icon');
        const brandText = document.getElementById('brand-text');
        const gridContainer = document.querySelector('.grid');

        document.addEventListener('DOMContentLoaded', () => {
            const logoImg = document.getElementById('logo-img');
    
            const updateLogo = () => {
                if (document.body.classList.contains('light')) {
                    logoImg.src = '../static/images/logo.png';
                } else {
                    logoImg.src = '../static/images/logodark.png';
                }
            };
    
            // Initial check
            updateLogo();
    
            // Observe changes to the body's class attribute
            const observer = new MutationObserver(updateLogo);
            observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });
        });
        
        // Function to set sidebar state based on localStorage
        function setSidebarState(isCollapsed) {
            if (isCollapsed) {
                sidebar.classList.add('collapsedsidebar');
                gridContainer.classList.add('collapsed');
                sidebarLabels.forEach(label => {
                    label.classList.add('opacity-0', 'hidden');
                });
                toggleIcon.classList.add('rotate-180');
            } else {
                sidebar.classList.remove('collapsedsidebar');
                gridContainer.classList.remove('collapsed');
                sidebarLabels.forEach(label => {
                    label.classList.remove('hidden');
                    setTimeout(() => {
                        label.classList.remove('opacity-0');
                    }, 50);
                });
                toggleIcon.classList.remove('rotate-180');
            }
        }
    
        // Initialize sidebar state on page load
        document.addEventListener('DOMContentLoaded', () => {
            const isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
            setSidebarState(isCollapsed);
        });
    
        toggleBtn.addEventListener('click', () => {
            sidebar.classList.toggle('w-full');
            sidebar.classList.toggle('collapsedsidebar');
            gridContainer.classList.toggle('collapsed');
    
            const isCollapsedNow = sidebar.classList.contains('collapsedsidebar');
    
            sidebarLabels.forEach(label => {
                if (isCollapsedNow) {
                    label.classList.add('opacity-0', 'hidden');
                } else {
                    label.classList.remove('hidden');
                    setTimeout(() => {
                        label.classList.remove('opacity-0');
                    }, 50);
                }
            });
    
            toggleIcon.classList.toggle('rotate-180');
            localStorage.setItem('sidebar-collapsed', isCollapsedNow);
        });

        // Add this to your existing script section
        document.addEventListener('DOMContentLoaded', () => {
            // Get current path
            const currentPath = window.location.pathname;
            
            // Find all sidebar links
            const sidebarLinks = document.querySelectorAll('.sidebar-link');
            
            // Add active class to matching link
            sidebarLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>