<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>ubar Example</title>
    <!-- Onest font from Google Fonts (closest available to "Onset") -->
    <link href="https://fonts.googleapis.com/css2?family=Onest:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #09090b;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            font-family: 'Onest', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        }

        .menubar {
            background-color: #09090b;
            padding: 2px;
            display: inline-flex;
            align-items: center;
            border-radius: 6px;
            border: 1px solid #27272a;
            width: 250px;
            height: 34px;
            box-sizing: border-box;
            position: relative;
        }

        .menubar-indicator {
            position: absolute;
            top: 4px;
            left: 4px;
            height: calc(100% - 8px);
            width: 48px;
            background: #27272a;
            border-radius: 4px;
            transition: left 0.25s cubic-bezier(.4,1,.7,1), width 0.25s cubic-bezier(.4,1,.7,1);
            z-index: 1;
            pointer-events: none;
        }

        .menubar button {
            background-color: transparent;
            color: #cccccc;
            border: none;
            padding: 5px 12px;
            margin: 0 2px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s ease, color 0.2s;
            font-family: inherit;
            position: relative;
            z-index: 2;
        }

        .menubar button:hover,
        .menubar button.active {
            background-color: #27272a;
            color: #ffffff;
        }

        .menubar button:focus {
            outline: none;
            box-shadow: 0 0 0 1px #27272a;
        }

        /* Prevent "extra zoom" on active focused items by removing the box-shadow */
        .menubar button.active:focus {
            box-shadow: none;
        }
    </style>
</head>
<body>

    <div class="menubar">
        <div class="menubar-indicator"></div>
        <button>File</button>
        <button class="active">Edit</button>
        <button>View</button>
        <button>Profiles</button>
    </div>

    <script>
        // Responsive shifting indicator for hover/click with 4px margin on all sides
        const menubar = document.querySelector('.menubar');
        const indicator = menubar.querySelector('.menubar-indicator');
        const buttons = Array.from(menubar.querySelectorAll('button'));

        function moveIndicator(target) {
            const rect = target.getBoundingClientRect();
            const parentRect = menubar.getBoundingClientRect();
            indicator.style.width = rect.width + 'px';
            indicator.style.left = (rect.left - parentRect.left + menubar.scrollLeft) + 'px';
            indicator.style.top = '4px';
            indicator.style.height = `calc(100% - 8px)`;
        }

        // Set initial position to active
        const activeBtn = menubar.querySelector('button.active') || buttons[0];
        moveIndicator(activeBtn);

        buttons.forEach(btn => {
            btn.addEventListener('mouseenter', () => moveIndicator(btn));
            btn.addEventListener('focus', () => moveIndicator(btn));
            btn.addEventListener('click', () => {
                buttons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                moveIndicator(btn);
            });
        });

        menubar.addEventListener('mouseleave', () => {
            const active = menubar.querySelector('button.active') || buttons[0];
            moveIndicator(active);
        });

        window.addEventListener('resize', () => {
            const active = menubar.querySelector('button.active') || buttons[0];
            moveIndicator(active);
        });
    </script>
</body>
</html>
