document.addEventListener('DOMContentLoaded', function() {
    const themeOptions = document.querySelectorAll('.theme-options');
    const darkModeToggler = document.getElementById('darkModeToggler');

    // Function to apply theme
    function applyTheme(themeName) {
        document.body.className = themeName;
        localStorage.setItem('selectedTheme', themeName);
        
        // Update visual indication of selected theme
        themeOptions.forEach(option => {
            if (option.getAttribute('data-theme') === themeName) {
                option.classList.add('selected-theme');
            } else {
                option.classList.remove('selected-theme');
            }
        });
    }

    // Check if there's a saved theme in localStorage
    const savedTheme = localStorage.getItem('selectedTheme');

    // Apply saved theme or default to 'light'
    if (savedTheme) {
        applyTheme(savedTheme);
    } else {
        applyTheme('light');
    }

    // Add click event listeners to theme options
    themeOptions.forEach(option => {
        option.addEventListener('click', function() {
            const selectedTheme = this.getAttribute('data-theme');
            applyTheme(selectedTheme);
        });
    });

    // Add event listener for the dark mode toggler
    if (darkModeToggler) {
        darkModeToggler.addEventListener('click', function() {
            const currentTheme = localStorage.getItem('selectedTheme');
            const newTheme = currentTheme === 'pure-black' ? 'light' : 'pure-black';
            applyTheme(newTheme);
        });
    }
});