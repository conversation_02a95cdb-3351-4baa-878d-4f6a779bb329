<script src="https://cdn.tailwindcss.com"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
<link rel="stylesheet" href="../static/styles/custom.css">
<link rel="stylesheet" href="../static/styles/loadinganimations.css">
<link rel="stylesheet" href="../static/styles/scrollbar.css">
<script src="../static/js/languagetranslator.js" defer"></script>
<script src="../static/js/loading.js" defer"></script>
<script src="../static/js/themes.js" defer"></script>
<link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/slate.min.css"/>
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
<script src="https://cdn.amcharts.com/lib/4/core.js"></script>
<script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
<script src="https://kit.fontawesome.com/c6c71387b9.js" crossorigin="anonymous"></script>
<script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
<link rel="preconnect" href="https://rsms.me/" />
<link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
<link rel="stylesheet" href="../static/styles/custom.css">
<script src="../static/js/languagetranslator.js" defer></script>
<script src="../static/js/themes.js" defer></script>
<script src="https://cdn.jsdelivr.net/npm/uikit@latest/dist/js/uikit.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/uikit@latest/dist/js/uikit-icons.min.js"></script>
<link rel="stylesheet" href="../static/styles/loadinganimations.css">
<link rel="stylesheet" href="../static/styles/chartdropdown.css">
<script src="../static/js/loading.js" defer></script>
<script src="../static/js/chartsdropsdown.js" defer></script>
<script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
<script src="https://cdn.amcharts.com/lib/4/themes/dark.js"></script>
<link rel="stylesheet" href="../static/styles/scrollbar.css">
<link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/slate.min.css"/>
<script src="https://unpkg.com/@phosphor-icons/web"></script>
<style>
  /* Hide scrollbar for Chrome, Safari and Opera */
  #pms-options::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  #pms-options {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
</style>
<script src="https://cdn.tailwindcss.com"></script>
<body class="light">
  <div id="loading-overlay" class="loading-overlay">
    <div class="typing-indicator">
      <div class="typing-circle"></div>
      <div class="typing-circle"></div>
      <div class="typing-circle"></div>
      <div class="typing-shadow"></div>
      <div class="typing-shadow"></div>
      <div class="typing-shadow"></div>
    </div>
  </div>
  <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] overflow-hidden">
    {% include 'sidebar.html' %}
    <div class="flex flex-col">
      <header class="flex h-14 lg:h-[60px] items-center gap-4 border-b  px-6 card justify-between">
        <a class="lg:hidden" href="#">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                <path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9"></path>
                <path d="M12 3v6"></path>
            </svg>
            <span class="sr-only">Home</span>
        </a>
        <h1 class="font-semibold text-lg dark:">PMS Configuration</h1>   
        {% include 'topright.html' %}             
    </header>
      <main class="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6">
        <div class="grid gap-4 md:grid-cols-1 lg:grid-cols-1">
          <div class="rounded-lg border  shadow-sm card" data-v0-t="card">
            <div class="flex flex-col space-y-1.5 p-6">
              <h3 class="whitespace-nowrap text-2xl font-semibold leading-none tracking-tight">PMS configuration</h3>
              <p class="text-sm text-muted-foreground">
                Configure the PMS settings as needed.
              </p>
            </div>
            <div class="p-6">
              <form class="grid gap-4">
                <div class="grid gap-2">
                  <label
                    class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    for="pms-name"
                  >
                    API key
                  </label>
                  <input
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    id="pms-name"
                    placeholder="Enter PMS API key..."
                  />
                </div>
                <div class="grid grid-cols-2 gap-4">
                  <div class="grid gap-2">
                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="urgency">
                        Model
                    </label>
                    <div class="relative w-full">
                      <div id="pms-selector" class="theme-responsive-button card theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer">
                        <span id="selected-pms">Select a PMS</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                      </div>
                      <ul id="pms-options" class="absolute z-50 hidden w-full py-1 mt-1 overflow-auto text-sm bg-background rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="amadeus">Amadeus</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="apaleo">Apaleo</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="beds24">Beds24</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="beonprice">Beonprice</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="bookassist">Bookassist</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="cloudbeds">Cloudbeds</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="dunes-factory">Dunes Factory</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="eviivo">Eviivo</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="ezee-frontdesk">eZee Frontdesk</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="frontavenue-pms">FrontAvenue PMS</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="frontdesk-anywhere">Frontdesk Anywhere</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="hetras-pms">Hetras PMS</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="hoteliga">Hoteliga</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="hotelogix">Hotelogix</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="hotelrunner">HotelRunner</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="hoteltime">Hoteltime</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="hostify">Hostify</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="icnea">ICNEA</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="innquest">InnQuest</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="little-hotelier">Little Hotelier</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="lodgify">Lodgify</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="maestro-pms">Maestro PMS</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="mews">Mews</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="millenium-pms">Millenium PMS</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="mmi-hot-inn">MMI HOT inn</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="newhotel-pms">Newhotel PMS</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="opera">Opera PMS</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="oscar-pms">Oscar PMS</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="plandok">Plandok</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="profitroom">Profitroom</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="protel-pms">Protel PMS</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="rms-cloud">RMS Cloud</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="roomcloud">RoomCloud</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="roomraccoon">RoomRaccoon</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="sihot">Sihot</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="siteminder">Siteminder</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="stayntouch">StayNTouch</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="vertical-booking">Vertical Booking</li>
                        <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="zennio">Zennio</li>
                      </ul>
                    </div>

                    <script>
                      document.addEventListener('DOMContentLoaded', function() {
                        const selector = document.getElementById('pms-selector');
                        const options = document.getElementById('pms-options');
                        const selectedOption = document.getElementById('selected-pms'); // Corrected ID
                    
                        selector.addEventListener('click', function(event) {
                          event.stopPropagation();
                          options.classList.toggle('hidden');
                        });
                    
                        options.querySelectorAll('li').forEach(option => {
                          option.addEventListener('click', function(event) {
                            event.stopPropagation();
                            selectedOption.textContent = this.textContent;
                            options.classList.add('hidden');
                          });
                        });
                    
                        // Close the dropdown when clicking outside
                        document.addEventListener('click', function() {
                          options.classList.add('hidden');
                        });
                      });
                    </script>
                </div>  
                <div class="grid gap-2">
                  <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="urgency">
                      Model
                  </label>
                  <div class="relative w-full">
                    <div id="permission-selector" class="theme-responsive-button card theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer">
                      <span id="permission-option">Select a Model</span>
                      <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    </div>
                    <ul id="permission-options" class="absolute z-50 hidden w-full py-1 mt-1 overflow-auto text-sm bg-background rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none">
                      <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="admin">Admin</li>
                      <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="default">Default</li>
                      <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="read">Read</li>
                      <li class="px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground" data-value="read/write">Read/Write</li>
                    </ul>
                  </div>

                  <script>
                    document.addEventListener('DOMContentLoaded', function() {
                      const selector = document.getElementById('permission-selector');
                      const options = document.getElementById('permission-options');
                      const selectedOption = document.getElementById('permission-option');
                    
                      selector.addEventListener('click', function(event) {
                        event.stopPropagation();
                        options.classList.toggle('hidden');
                      });
                    
                      options.querySelectorAll('li').forEach(option => {
                        option.addEventListener('click', function(event) {
                          event.stopPropagation();
                          selectedOption.textContent = this.textContent;
                          options.classList.add('hidden');
                        });
                      });
                    
                      // Close the dropdown when clicking outside
                      document.addEventListener('click', function() {
                        options.classList.add('hidden');
                      });
                    });
                  </script>
              </div>  
                </div>
                <button
                  class="inline-flex items-center justify-center whitespace-nowrap rounded-md bg-black px-4 py-2 text-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-gray-800"
                  type="submit"
                >
                  Submit PMS changes
                </button>
              </form>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
  <script>
    // Theme switching functionality
    document.addEventListener('DOMContentLoaded', function() {
        const themeSelector = document.querySelector('.theme-selector');
        const themeOptions = document.querySelectorAll('.theme-option');
        const themeSelectorBtn = themeSelector.querySelector('.theme-selector-btn');

        // Function to apply theme
        function applyTheme(themeName) {
            document.body.className = themeName;
            localStorage.setItem('selectedTheme', themeName);
            themeSelectorBtn.innerHTML = '<i class="fas fa-palette"></i> ' + themeName.charAt(0).toUpperCase() + themeName.slice(1).replace('-', ' ');
        }

        // Check if there's a saved theme in localStorage
        const savedTheme = localStorage.getItem('selectedTheme');

        // Apply saved theme or default to 'light'
        if (savedTheme) {
            applyTheme(savedTheme);
        } else {
            applyTheme('light');
        }

        themeOptions.forEach(option => {
            option.addEventListener('click', function() {
                const selectedTheme = this.getAttribute('data-theme');
                applyTheme(selectedTheme);
            });
        });
    });
  </script>
</body>
</html>