<html class="dark uk-theme-zinc uk-radii-md uk-shadows-sm uk-font-sm" lang="en">

<head>
    <meta charset="utf-8">
    <link rel="icon" href="https://frames.franken-ui.dev/favicon.png">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="preload" href="/fonts/Geist/Sans.woff2" as="font" type="font/woff2" crossorigin="">
    <link rel="preload" href="/fonts/Geist/Mono.woff2" as="font" type="font/woff2" crossorigin="">
    <link rel="stylesheet" href="/fonts/Geist/style.css">
    {%include 'imports.html'%}
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Franken UI Core -->
    <link rel="stylesheet" href="https://unpkg.com/franken-ui@1.1.0/dist/css/core.min.css" />
    <script src="https://unpkg.com/franken-ui@1.1.0/dist/js/core.iife.js" type="module"></script>
    <script src="https://unpkg.com/franken-ui@1.1.0/dist/js/icon.iife.js" type="module"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <!-- UIkit Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit-icons.min.js"></script>
    <!-- Custom Styles -->
    <link rel="stylesheet" href="../static/styles/custom.css">
    <link rel="stylesheet" href="../static/styles/loadinganimations.css">
    <link rel="stylesheet" href="../static/styles/scrollbar.css">
    <!-- Custom Scripts -->
    <script src="../static/js/themes.js" defer></script>
    <script src="../static/js/sidebarcollapse.js" defer></script>
    <!-- Fonts -->
    <link rel="preconnect" href="https://rsms.me/" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
    <style>
        .pure-black .uk-dropdown.themer-icon uk-icon {
            color: white !important;
        }

        .light .uk-dropdown.themer-icon uk-icon {
            color: black !important;
        }

        .light .uk-dropdown .themer-input {
            background-color: transparent !important;
            color: black !important;
        }

        .pure-black .uk-dropdown .themer-input {
            background-color: transparent !important;
            color: white !important;
            border: transparent !important;
        }

        /* Remove outline and border from dropdown search inputs */
        .uk-dropdown .themer-input,
        .uk-drop .themer-input {
            outline: none !important;
            border: none !important;
            box-shadow: none !important;
        }

        .uk-dropdown .themer-input:focus,
        .uk-drop .themer-input:focus,
        .uk-dropdown .themer-input:active,
        .uk-drop .themer-input:active {
            outline: none !important;
            border: none !important;
            box-shadow: none !important;
        }

        /* Ensure no outline appears in light mode */
        .light .uk-dropdown .themer-input,
        .light .uk-drop .themer-input {
            outline: none !important;
            border: none !important;
            box-shadow: none !important;
        }

        /* Ensure we don't get a browser-default focus ring */
        .uk-dropdown input:focus-visible,
        .uk-drop input:focus-visible {
            outline: none !important;
        }

        /* LINES TO FIX THE OVERFLOW ISSUE SEARACh */
        .pure-black .uk-drop.uk-dropdown .uk-dropdown-nav li:hover {
            background-color: transparent !important;
        }
    </style>
    <style type="text/css">
        @font-face {
            font-family: 'Atlassian Sans';
            font-style: normal;
            font-weight: 400 653;
            font-display: swap;
            src:
                local('AtlassianSans'),
                local('Atlassian Sans Text'),
                url('chrome-extension://liecbddmkiiihnedobmlmillhodjkdmb/fonts/AtlassianSans-latin.woff2') format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
                U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        .no-scrollbar {
            -ms-overflow-style: none;
            /* IE and Edge */
            scrollbar-width: none;
            /* Firefox */
        }

        /* Hide WebKit (Chrome, Safari, etc) scrollbar */
        .no-scrollbar::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>

<body class="bg-background font-geist-sans text-sm text-foreground" data-sveltekit-preload-data="hover">
    <div style="display: contents">
        <div class="hidden p-6 lg:p-10 xl:block">
            <div class="flex justify-between items-center mb-6">
                <a href="/" class="uk-button special-button-3 flex items-center gap-2">
                    <uk-icon icon="arrow-left"></uk-icon>
                    Back to Dashboard
                </a>
                <div class="space-y-0.5 text-right">
                    <h2 class="text-2xl font-bold tracking-tight">Settings</h2>
                    <p class="">Manage your account settings and set e-mail preferences.</p>
                </div>
            </div>
            <div class="my-6 border-t border-border card"></div>
            <div class="flex gap-x-12">
                <!-- Fix for the navigation sidebar IDs -->
                <aside class="w-1/5">
                    <ul class="uk-nav uk-nav-secondary"
                        data-uk-switcher="connect: #component-nav; animation: uk-anmt-fade" role="tablist"
                        aria-orientation="vertical">
                        <li role="presentation"><a href="#" aria-selected="false" role="tab" id="uk-switcher-5"
                                aria-controls="uk-switcher-6" tabindex="-1">PMS Configuration</a></li>
                        <li role="presentation"><a href="#" aria-selected="false" role="tab" id="uk-switcher-tab-9"
                                aria-controls="uk-switcher-10" tabindex="-1">General settings</a></li>
                        <li role="presentation"><a href="#" aria-selected="false" role="tab" id="uk-switcher-tab-11"
                                aria-controls="uk-switcher-12" tabindex="-1">Staff Management</a></li>
                        <li><a href="#" id="uk-switcher-tab-15" aria-controls="uk-switcher-16" role="tab">Notifications</a></li>
                        <li><a href="#" id="uk-switcher-tab-19" aria-controls="uk-switcher-20" role="tab">Bot Configuration</a></li>
                        <li><a href="#" id="uk-switcher-tab-17" aria-controls="uk-switcher-18" role="tab">Appearance</a></li>
                    </ul>
                </aside>
                <div class="flex-1">
                    <ul id="component-nav" class="uk-switcher max-w-2xl" role="presentation"
                        style="touch-action: pan-y pinch-zoom;">
                        <!-- Remove PMS Integration section -->
                        <!-- Start with Staff Management section -->
                        <!-- Remove Hotel Profile and Room Configuration sections -->
                        <!-- Start with Menu & Services section -->
                        <li class="space-y-6" id="uk-switcher-8" role="tabpanel" aria-labelledby="uk-switcher-7">
                            <div>
                                <h3 class="text-lg font-medium">PMS Integration</h3>
                                <p class="text-sm">Connect Guest Genius to your Property Management System.</p>
                            </div>
                            <div class="border-t border-border card"></div>

                            <div class="space-y-2">
                                <label class="uk-form-label block" for="pms-provider">PMS Provider</label>
                                <div class="h-9">
                                    <div class="uk-position-relative">
                                        <button type="button" id="pms-dropdown-btn"
                                            class="uk-input card w-full flex justify-between items-center focus:outline-none focus:ring-0 "
                                            aria-haspopup="true"
                                            style="background-color: transparent; color: var(--dropdown-text);">
                                            Select PMS
                                            <uk-icon class="" icon="chevrons-up-down"></uk-icon>
                                        </button>
                                        <div id="pms-dropdown" uk-dropdown="mode: click; pos: bottom-justify"
                                            class="uk-dropdown w-full">
                                            <ul class="uk-nav uk-dropdown-nav uk-overflow-auto uk-cs-options no-scrollbar"
                                                style="max-height: 200px;">
                                                <li><a class="pms-option" data-value="amadeus">Amadeus</a></li>
                                                <li><a class="pms-option" data-value="apaleo">Apaleo</a></li>
                                                <li><a class="pms-option" data-value="beds24">Beds24</a></li>
                                                <li><a class="pms-option" data-value="beonprice">Beonprice</a></li>
                                                <li><a class="pms-option" data-value="bookassist">Bookassist</a></li>
                                                <li><a class="pms-option" data-value="cloudbeds">Cloudbeds</a></li>
                                                <li><a class="pms-option" data-value="dunes-factory">Dunes Factory</a>
                                                </li>
                                                <li><a class="pms-option" data-value="eviivo">Eviivo</a></li>
                                                <li><a class="pms-option" data-value="ezee-frontdesk">eZee Frontdesk</a>
                                                </li>
                                                <li><a class="pms-option" data-value="frontavenue-pms">FrontAvenue
                                                        PMS</a></li>
                                                <li><a class="pms-option" data-value="frontdesk-anywhere">Frontdesk
                                                        Anywhere</a></li>
                                                <li><a class="pms-option" data-value="hetras-pms">Hetras PMS</a></li>
                                                <li><a class="pms-option" data-value="hoteliga">Hoteliga</a></li>
                                                <li><a class="pms-option" data-value="hotelogix">Hotelogix</a></li>
                                                <li><a class="pms-option" data-value="hotelrunner">HotelRunner</a></li>
                                                <li><a class="pms-option" data-value="hoteltime">Hoteltime</a></li>
                                                <li><a class="pms-option" data-value="hostify">Hostify</a></li>
                                                <li><a class="pms-option" data-value="icnea">ICNEA</a></li>
                                                <li><a class="pms-option" data-value="innquest">InnQuest</a></li>
                                                <li><a class="pms-option" data-value="little-hotelier">Little
                                                        Hotelier</a></li>
                                                <li><a class="pms-option" data-value="lodgify">Lodgify</a></li>
                                                <li><a class="pms-option" data-value="maestro-pms">Maestro PMS</a></li>
                                                <li><a class="pms-option" data-value="mews">Mews</a></li>
                                                <li><a class="pms-option" data-value="millenium-pms">Millenium PMS</a>
                                                </li>
                                                <li><a class="pms-option" data-value="mmi-hot-inn">MMI HOT inn</a></li>
                                                <li><a class="pms-option" data-value="newhotel-pms">Newhotel PMS</a>
                                                </li>
                                                <li><a class="pms-option" data-value="opera-pms">Opera PMS</a></li>
                                                <li><a class="pms-option" data-value="oscar-pms">Oscar PMS</a></li>
                                                <li><a class="pms-option" data-value="plandok">Plandok</a></li>
                                                <li><a class="pms-option" data-value="profitroom">Profitroom</a></li>
                                                <li><a class="pms-option" data-value="protel-pms">Protel PMS</a></li>
                                                <li><a class="pms-option" data-value="rms-cloud">RMS Cloud</a></li>
                                                <li><a class="pms-option" data-value="roomcloud">RoomCloud</a></li>
                                                <li><a class="pms-option" data-value="roomraccoon">RoomRaccoon</a></li>
                                                <li><a class="pms-option" data-value="sihot">Sihot</a></li>
                                                <li><a class="pms-option" data-value="siteminder">Siteminder</a></li>
                                                <li><a class="pms-option" data-value="stayntouch">StayNTouch</a></li>
                                                <li><a class="pms-option" data-value="vertical-booking">Vertical
                                                        Booking</a></li>
                                                <li><a class="pms-option" data-value="zennio">Zennio</a></li>
                                                <li><a class="pms-option" data-value="other">Other</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="space-y-2">
                                <label class="uk-form-label" for="api-key">API Key</label>
                                <input class="uk-input card focus:outline-none focus:ring-0" id="api-key"
                                    type="password" placeholder="Enter your PMS API key">
                                <div class="uk-form-help">Find this key in your PMS account settings.</div>
                            </div>

                            <div class="space-y-2">
                                <label class="uk-form-label" for="pms-endpoint">PMS API Endpoint</label>
                                <input class="uk-input card focus:outline-none focus:ring-0" id="pms-endpoint"
                                    type="text" placeholder="https://api.your-pms.com/v1/">
                            </div>

                            <div class="space-y-2">
                                <label class="uk-form-label block" for="permission-level">Permission Level</label>
                                <div class="h-9">
                                    <div class="uk-position-relative">
                                        <button type="button" id="permission-level-btn"
                                            class="uk-input card w-full flex justify-between items-center focus:outline-none focus:ring-0"
                                            aria-haspopup="true"
                                            style="background-color: transparent; color: var(--dropdown-text);">
                                            Read Only
                                            <uk-icon class="" icon="chevrons-up-down"></uk-icon>
                                        </button>
                                        <div id="permission-level-dropdown"
                                            uk-dropdown="mode: click; pos: bottom-justify"
                                            class="uk-dropdown w-full dropdown-content">
                                            <ul
                                                class="uk-nav uk-dropdown-nav uk-overflow-auto uk-cs-options no-scrollbar">
                                                <li class="uk-active">
                                                    <a class="permission-level-option" data-value="read">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">Read Only</span>
                                                        </div>
                                                        <uk-icon class="uk-cs-check" icon="check"></uk-icon>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="permission-level-option" data-value="read/write">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">Read/Write</span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="permission-level-option" data-value="admin">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">Admin</span>
                                                        </div>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="uk-form-help">What level of access should Guest Genius have to your PMS?
                                </div>
                            </div>


                            <div class="space-y-2 mt-4">
                                <div class="flex items-center">
                                    <button class="uk-button uk-button-primary mr-2" id="save-pms-config">Save
                                        Configuration</button>
                                    <div class="uk-form-help ml-2">
                                        <span class="text-gray-500">Last sync: Today at 14:32</span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="space-y-6" id="uk-switcher-14" role="tabpanel" aria-labelledby="uk-switcher-tab-13">

                            <div>
                                <h3 class="text-lg font-medium">Language & Region</h3>
                                <p class="text-sm">Configure language, timezone and regional settings.</p>
                            </div>
                            <div class="border-t border-border card"></div>

                            <div class="space-y-2">
                                <label class="uk-form-label block" for="language-dropdown">Primary Language</label>
                                <div class="h-9">
                                    <div class="uk-position-relative">
                                        <button type="button" id="language-dropdown-btn"
                                            class="uk-input card w-full flex justify-between items-center focus:outline-none focus:ring-0"
                                            aria-haspopup="true"
                                            style="background-color: transparent; color: var(--dropdown-text);">
                                            English
                                            <uk-icon class="" icon="chevrons-up-down"></uk-icon>
                                        </button>
                                        <div id="language-dropdown" uk-dropdown="mode: click; pos: bottom-justify"
                                            class="uk-dropdown w-full dropdown-content">
                                            <ul class="uk-nav uk-dropdown-nav uk-overflow-auto uk-cs-options">
                                                <li class="uk-active">
                                                    <a class="language-option" data-value="English">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">English</span>
                                                        </div>
                                                        <uk-icon class="uk-cs-check" icon="check"></uk-icon>
                                                    </a>
                                                </li>
                                                <li><a class="language-option" data-value="Spanish">Spanish</a></li>
                                                <li><a class="language-option" data-value="French">French</a></li>
                                                <li><a class="language-option" data-value="Italian">Italian</a></li>
                                                <li><a class="language-option" data-value="Swedish">Swedish</a></li>
                                                <li><a class="language-option" data-value="German">German</a></li>
                                                <li><a class="language-option" data-value="Dutch">Dutch</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="uk-form-help">Primary language for the admin dashboard.</div>
                            </div>

                            <div class="space-y-2">
                                <label class="uk-form-label block" for="guest-languages">Guest-Facing Languages</label>
                                <div class="p-4 border rounded-md card">
                                    <div class="space-y-2">
                                        <div class="checkbox-wrapper-46">
                                            <input type="checkbox" id="lang_en" class="inp-cbx" checked>
                                            <label for="lang_en" class="cbx">
                                                <span>
                                                    <svg viewBox="0 0 12 10">
                                                        <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                                    </svg>
                                                </span>
                                                <span>English</span>
                                            </label>
                                        </div>
                                        <div class="checkbox-wrapper-46">
                                            <input type="checkbox" id="lang_es" class="inp-cbx">
                                            <label for="lang_es" class="cbx">
                                                <span>
                                                    <svg viewBox="0 0 12 10">
                                                        <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                                    </svg>
                                                </span>
                                                <span>Spanish</span>
                                            </label>
                                        </div>
                                        <div class="checkbox-wrapper-46">
                                            <input type="checkbox" id="lang_fr" class="inp-cbx">
                                            <label for="lang_fr" class="cbx">
                                                <span>
                                                    <svg viewBox="0 0 12 10">
                                                        <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                                    </svg>
                                                </span>
                                                <span>French</span>
                                            </label>
                                        </div>
                                        <div class="checkbox-wrapper-46">
                                            <input type="checkbox" id="lang_it" class="inp-cbx">
                                            <label for="lang_it" class="cbx">
                                                <span>
                                                    <svg viewBox="0 0 12 10">
                                                        <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                                    </svg>
                                                </span>
                                                <span>Italian</span>
                                            </label>
                                        </div>
                                        <div class="checkbox-wrapper-46">
                                            <input type="checkbox" id="lang_sv" class="inp-cbx">
                                            <label for="lang_sv" class="cbx">
                                                <span>
                                                    <svg viewBox="0 0 12 10">
                                                        <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                                    </svg>
                                                </span>
                                                <span>Swedish</span>
                                            </label>
                                        </div>
                                        <div class="checkbox-wrapper-46">
                                            <input type="checkbox" id="lang_de" class="inp-cbx">
                                            <label for="lang_de" class="cbx">
                                                <span>
                                                    <svg viewBox="0 0 12 10">
                                                        <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                                    </svg>
                                                </span>
                                                <span>German</span>
                                            </label>
                                        </div>
                                        <div class="checkbox-wrapper-46">
                                            <input type="checkbox" id="lang_nl" class="inp-cbx">
                                            <label for="lang_nl" class="cbx">
                                                <span>
                                                    <svg viewBox="0 0 12 10">
                                                        <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                                    </svg>
                                                </span>
                                                <span>Dutch</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="space-y-2">
                                <label class="uk-form-label block" for="timezone-dropdown">Time Zone</label>
                                <div class="h-9">
                                    <div class="uk-position-relative">
                                        <button type="button" id="timezone-dropdown-btn"
                                            class="uk-input card w-full flex justify-between items-center focus:outline-none focus:ring-0"
                                            aria-haspopup="true"
                                            style="background-color: transparent; color: var(--dropdown-text);">
                                            UTC+05:30 (Mumbai, New Delhi)
                                            <uk-icon class="" icon="chevrons-up-down"></uk-icon>
                                        </button>
                                        <div id="timezone-dropdown" uk-dropdown="mode: click; pos: bottom-justify"
                                            class="uk-dropdown w-full dropdown-content">
                                            <ul class="uk-nav uk-dropdown-nav uk-overflow-auto uk-cs-options">
                                                <li>
                                                    <a class="timezone-option" data-value="UTC+00:00">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">UTC+00:00 (London,
                                                                Lisbon)</span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="timezone-option" data-value="UTC+01:00">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">UTC+01:00 (Paris, Berlin,
                                                                Rome)</span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="timezone-option" data-value="UTC+02:00">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">UTC+02:00 (Athens,
                                                                Cairo)</span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="timezone-option" data-value="UTC+03:00">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">UTC+03:00 (Moscow,
                                                                Istanbul)</span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li class="uk-active">
                                                    <a class="timezone-option" data-value="UTC+05:30">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">UTC+05:30 (Mumbai, New
                                                                Delhi)</span>
                                                        </div>
                                                        <uk-icon class="uk-cs-check" icon="check"></uk-icon>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="timezone-option" data-value="UTC+08:00">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">UTC+08:00 (Beijing,
                                                                Singapore)</span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="timezone-option" data-value="UTC-05:00">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">UTC-05:00 (New York,
                                                                Toronto)</span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="timezone-option" data-value="UTC-08:00">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">UTC-08:00 (Los Angeles,
                                                                Vancouver)</span>
                                                        </div>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="uk-form-help">Time zone used for booking times and reports.</div>
                            </div>
                            <script>
                                // Add this to your existing JavaScript or at the end of the file
                                document.addEventListener('DOMContentLoaded', function () {
                                    // Timezone dropdown functionality (similar to language dropdown)
                                    const timezoneDropdownBtn = document.getElementById('timezone-dropdown-btn');
                                    const timezoneDropdown = document.getElementById('timezone-dropdown');
                                    const timezoneOptions = document.querySelectorAll('#timezone-dropdown .timezone-option');

                                    // Initialize UIkit dropdown if not already done
                                    if (typeof UIkit !== 'undefined' && timezoneDropdown) {
                                        const dropdown = UIkit.dropdown(timezoneDropdown, {
                                            mode: 'click',
                                            pos: 'bottom-justify'
                                        });

                                        // Add animation classes
                                        timezoneDropdown.classList.add('dropdown-content');

                                        // Add animation handlers
                                        UIkit.util.on(timezoneDropdown, 'beforeshow', function () {
                                            this.style.transformOrigin = 'top center';
                                            this.setAttribute('data-state', '');

                                            requestAnimationFrame(() => {
                                                this.setAttribute('data-state', 'open');
                                            });
                                        });

                                        UIkit.util.on(timezoneDropdown, 'beforehide', function () {
                                            this.setAttribute('data-state', '');

                                            const dropdown = this;
                                            const component = UIkit.dropdown(dropdown);

                                            if (component) {
                                                const originalHide = component.hide;
                                                component.hide = function () { };

                                                setTimeout(() => {
                                                    component.hide = originalHide;
                                                    component.hide();
                                                }, 200);
                                            }

                                            return false;
                                        });
                                    }

                                    // Handle timezone selection
                                    timezoneOptions.forEach(option => {
                                        option.addEventListener('click', function (e) {
                                            e.preventDefault();

                                            // Get the display text and value
                                            const selectedValue = this.getAttribute('data-value');
                                            const selectedText = this.querySelector('.uk-cs-item-text').textContent;

                                            // Update button text
                                            timezoneDropdownBtn.innerHTML = selectedText + ' <uk-icon icon="chevrons-up-down"></uk-icon>';

                                            // Store the selected value (for form submission)
                                            timezoneDropdownBtn.setAttribute('data-selected', selectedValue);

                                            // Update active state
                                            timezoneOptions.forEach(opt => {
                                                const parentLi = opt.closest('li');
                                                if (parentLi) {
                                                    parentLi.classList.remove('uk-active');

                                                    // Remove check icon if exists
                                                    const checkIcon = opt.querySelector('.uk-cs-check');
                                                    if (checkIcon) {
                                                        checkIcon.remove();
                                                    }
                                                }
                                            });

                                            // Add active state and check icon to selected item
                                            const parentLi = this.closest('li');
                                            if (parentLi) {
                                                parentLi.classList.add('uk-active');

                                                // Add check icon if not exists
                                                if (!this.querySelector('.uk-cs-check')) {
                                                    const checkIcon = document.createElement('uk-icon');
                                                    checkIcon.className = 'uk-cs-check';
                                                    checkIcon.setAttribute('icon', 'check');
                                                    this.appendChild(checkIcon);
                                                }
                                            }
                                        });
                                    });

                                    // When saving settings, you'll need to get the value from the data-selected attribute
                                    document.querySelector('button.uk-button').addEventListener('click', function () {
                                        const selectedTimezone = timezoneDropdownBtn.getAttribute('data-selected');
                                        // Here you would include the timezone value in your form submission
                                        console.log('Selected timezone:', selectedTimezone);
                                    });
                                });
                            </script>

                            <div class="space-y-2">
                                <label class="uk-form-label block" for="currency-dropdown">Currency</label>
                                <div class="h-9 relative">
                                    <div id="currency-trigger"
                                        class="theme-responsive-button card theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer ">
                                        <span id="selected-currency">Indian Rupee (INR)</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </div>
                                    <div id="currency-dropdown"
                                        class="uk-drop uk-dropdown w-full hidden themer-icon dropdown-content"
                                        uk-drop="mode: click; pos: bottom-right">
                                        <div class="m-1 flex items-center px-2">
                                            <uk-icon class="opacity-50" icon="search"></uk-icon>
                                            <input
                                                class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                                style="background-color: transparent;" placeholder="Search currency"
                                                type="text">
                                        </div>
                                        <ul class="uk-dropdown-nav">
                                            <li class="uk-nav-divider"></li>
                                            <li>
                                                <a class="uk-drop-close currency-option" href="#" data-value="USD"
                                                    role="button">
                                                    <div class="dropdown-option-3">US Dollar (USD)</div>
                                                </a>
                                            </li>
                                            <li>
                                                <a class="uk-drop-close currency-option" href="#" data-value="EUR"
                                                    role="button">
                                                    <div class="dropdown-option-3">Euro (EUR)</div>
                                                </a>
                                            </li>
                                            <li>
                                                <a class="uk-drop-close currency-option" href="#" data-value="GBP"
                                                    role="button">
                                                    <div class="dropdown-option-3">British Pound (GBP)</div>
                                                </a>
                                            </li>
                                            <li>
                                                <a class="uk-drop-close currency-option" href="#" data-value="INR"
                                                    role="button">
                                                    <div class="dropdown-option-3">Indian Rupee (INR)</div>
                                                </a>
                                            </li>
                                            <li>
                                                <a class="uk-drop-close currency-option" href="#" data-value="AUD"
                                                    role="button">
                                                    <div class="dropdown-option-3">Australian Dollar (AUD)</div>
                                                </a>
                                            </li>
                                            <li>
                                                <a class="uk-drop-close currency-option" href="#" data-value="CAD"
                                                    role="button">
                                                    <div class="dropdown-option-3">Canadian Dollar (CAD)</div>
                                                </a>
                                            </li>
                                            <li>
                                                <a class="uk-drop-close currency-option" href="#" data-value="JPY"
                                                    role="button">
                                                    <div class="dropdown-option-3">Japanese Yen (JPY)</div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="uk-form-help">Primary currency for rates and payments.</div>
                            </div>
                            <script>
                                // Add this to your existing script section or create a new one
                                document.addEventListener('DOMContentLoaded', function () {
                                    // Currency dropdown functionality
                                    initializeDropdown('currency-trigger', 'currency-dropdown');

                                    // Handle currency selection
                                    document.querySelectorAll('.currency-option').forEach(option => {
                                        option.addEventListener('click', function (e) {
                                            e.preventDefault();
                                            const currencyText = this.querySelector('.dropdown-option-3').textContent;
                                            const currencyValue = this.getAttribute('data-value');

                                            // Update the selected text
                                            document.getElementById('selected-currency').textContent = currencyText;

                                            // Store the value (for form submission)
                                            document.getElementById('currency-trigger').setAttribute('data-value', currencyValue);

                                            // Make sure the hidden input is updated if you need to submit the form
                                            const hiddenInput = document.getElementById('currency-hidden') ||
                                                createHiddenInput('currency-hidden', currencyValue);
                                            hiddenInput.value = currencyValue;
                                        });
                                    });

                                    function createHiddenInput(id, value) {
                                        const input = document.createElement('input');
                                        input.type = 'hidden';
                                        input.id = id;
                                        input.name = 'currency';
                                        input.value = value;
                                        document.getElementById('currency-trigger').parentNode.appendChild(input);
                                        return input;
                                    }

                                    function initializeDropdown(triggerId, dropdownId) {
                                        const trigger = document.getElementById(triggerId);
                                        const dropdown = document.getElementById(dropdownId);

                                        if (!trigger || !dropdown) return;

                                        // Add animation class if not present
                                        if (!dropdown.classList.contains('dropdown-content')) {
                                            dropdown.classList.add('dropdown-content');
                                        }

                                        // Add click handler to trigger
                                        trigger.addEventListener('click', function (e) {
                                            e.preventDefault();
                                            e.stopPropagation();

                                            if (dropdown.classList.contains('hidden')) {
                                                openDropdown(dropdown);
                                            } else {
                                                closeDropdown(dropdown);
                                            }
                                        });

                                        // Search functionality
                                        const searchInput = dropdown.querySelector('input[type="text"]');
                                        if (searchInput) {
                                            const listItems = dropdown.querySelectorAll('.uk-dropdown-nav > li:not(.uk-nav-divider)');

                                            searchInput.addEventListener('input', function (e) {
                                                e.stopPropagation();
                                                const query = e.target.value.toLowerCase();

                                                listItems.forEach(li => {
                                                    const optionEl = li.querySelector('.dropdown-option-3');
                                                    if (optionEl) {
                                                        const text = optionEl.textContent.toLowerCase();
                                                        li.style.display = text.includes(query) ? '' : 'none';
                                                    }
                                                });
                                            });

                                            // Prevent dropdown from closing when searching
                                            searchInput.addEventListener('click', e => e.stopPropagation());
                                        }

                                        // Close dropdown when clicking outside
                                        document.addEventListener('click', function () {
                                            if (!dropdown.classList.contains('hidden')) {
                                                closeDropdown(dropdown);
                                            }
                                        });

                                        // Prevent dropdown from closing when clicking inside
                                        dropdown.addEventListener('click', e => e.stopPropagation());
                                    }

                                    function openDropdown(dropdown) {
                                        // Set transform origin for animation
                                        dropdown.style.transformOrigin = 'top right';
                                        dropdown.classList.remove('hidden');

                                        // Use requestAnimationFrame to ensure the browser processes the unhide before animating
                                        requestAnimationFrame(() => {
                                            dropdown.setAttribute('data-state', 'open');
                                        });
                                    }

                                    function closeDropdown(dropdown) {
                                        // Start the fade out animation
                                        dropdown.setAttribute('data-state', '');

                                        // Wait for animation to complete before hiding
                                        setTimeout(() => {
                                            dropdown.classList.add('hidden');
                                        }, 200); // Match this to your animation duration
                                    }
                                });
                            </script>

                            <!-- Replace the existing date format dropdown -->
                            <div class="space-y-2">
                                <label class="uk-form-label block" for="date-format-dropdown">Date Format</label>
                                <div class="h-9">
                                    <div class="uk-position-relative">
                                        <button type="button" id="date-format-dropdown-btn"
                                            class="uk-input card w-full flex justify-between items-center focus:outline-none focus:ring-0"
                                            aria-haspopup="true"
                                            style="background-color: transparent; color: var(--dropdown-text);">
                                            DD/MM/YYYY
                                            <uk-icon class="" icon="chevrons-up-down"></uk-icon>
                                        </button>
                                        <div id="date-format-dropdown" uk-dropdown="mode: click; pos: bottom-justify"
                                            class="uk-dropdown w-full dropdown-content">
                                            <ul class="uk-nav uk-dropdown-nav uk-overflow-auto uk-cs-options">
                                                <li class="uk-active">
                                                    <a class="date-format-option" data-value="DD/MM/YYYY">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">DD/MM/YYYY</span>
                                                        </div>
                                                        <uk-icon class="uk-cs-check" icon="check"></uk-icon>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="date-format-option" data-value="MM/DD/YYYY">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">MM/DD/YYYY</span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="date-format-option" data-value="YYYY-MM-DD">
                                                        <div class="uk-cs-item-wrapper">
                                                            <span class="uk-cs-item-text">YYYY-MM-DD</span>
                                                        </div>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="uk-form-help">Select the date format for the dashboard.</div>
                            </div>

                            <script>
                                document.addEventListener('DOMContentLoaded', function () {
                                    const dateFormatDropdownBtn = document.getElementById('date-format-dropdown-btn');
                                    const dateFormatDropdown = document.getElementById('date-format-dropdown');
                                    const dateFormatOptions = document.querySelectorAll('#date-format-dropdown .date-format-option');

                                    // Initialize UIkit dropdown if not already done
                                    if (typeof UIkit !== 'undefined' && dateFormatDropdown) {
                                        const dropdown = UIkit.dropdown(dateFormatDropdown, {
                                            mode: 'click',
                                            pos: 'bottom-justify'
                                        });

                                        // Add animation classes
                                        dateFormatDropdown.classList.add('dropdown-content');

                                        // Add animation handlers
                                        UIkit.util.on(dateFormatDropdown, 'beforeshow', function () {
                                            this.style.transformOrigin = 'top center';
                                            this.setAttribute('data-state', '');

                                            requestAnimationFrame(() => {
                                                this.setAttribute('data-state', 'open');
                                            });
                                        });

                                        UIkit.util.on(dateFormatDropdown, 'beforehide', function () {
                                            this.setAttribute('data-state', '');

                                            const dropdown = this;
                                            const component = UIkit.dropdown(dropdown);

                                            if (component) {
                                                const originalHide = component.hide;
                                                component.hide = function () { };

                                                setTimeout(() => {
                                                    component.hide = originalHide;
                                                    component.hide();
                                                }, 200);
                                            }

                                            return false;
                                        });
                                    }

                                    // Handle date format selection
                                    dateFormatOptions.forEach(option => {
                                        option.addEventListener('click', function (e) {
                                            e.preventDefault();

                                            // Get the display text and value
                                            const selectedValue = this.getAttribute('data-value');
                                            const selectedText = this.querySelector('.uk-cs-item-text').textContent;

                                            // Update button text
                                            dateFormatDropdownBtn.innerHTML = selectedText + ' <uk-icon icon="chevrons-up-down"></uk-icon>';

                                            // Store the selected value (for form submission)
                                            dateFormatDropdownBtn.setAttribute('data-selected', selectedValue);

                                            // Update active state
                                            dateFormatOptions.forEach(opt => {
                                                const parentLi = opt.closest('li');
                                                if (parentLi) {
                                                    parentLi.classList.remove('uk-active');

                                                    // Remove check icon if exists
                                                    const checkIcon = opt.querySelector('.uk-cs-check');
                                                    if (checkIcon) {
                                                        checkIcon.remove();
                                                    }
                                                }
                                            });

                                            // Add active state and check icon to selected item
                                            const parentLi = this.closest('li');
                                            if (parentLi) {
                                                parentLi.classList.add('uk-active');

                                                // Add check icon if not exists
                                                if (!this.querySelector('.uk-cs-check')) {
                                                    const checkIcon = document.createElement('uk-icon');
                                                    checkIcon.className = 'uk-cs-check';
                                                    checkIcon.setAttribute('icon', 'check');
                                                    this.appendChild(checkIcon);
                                                }
                                            }
                                        });
                                    });
                                });
                            </script>

                            <div><button class="uk-button uk-button-default">Save Language Settings</button></div>
                        </li>
                        <li class="space-y-6" id="uk-switcher-12" role="tabpanel" aria-labelledby="uk-switcher-11">
                            <div>
                                <h3 class="text-lg font-medium">Staff Members</h3>
                                <p class="text-sm ">View and manage staff members with access to
                                    this system.
                                </p>
                            </div>
                            <div class="border-t border-border card"></div>
                            <div id="staff-members" class="uk-card card shadow-fixer">
                                <div class="uk-card-header">
                                    <h3 class="font-semibold leading-none tracking-tight">
                                        Add staff members
                                    </h3>
                                    <p class="mt-1.5 text-sm ">
                                        Anyone with the link can view this document.
                                    </p>
                                </div>
                                <div class="uk-card-body pt-0">
                                    <div class="card flex gap-x-2">
                                        <div class="flex-1"> <input class="uk-input card card" readonly=""
                                                value="http://guestgenius.es/hotelname/#/04" id="share-link"
                                                style="color: var(--dropdown-text);"> </div>
                                        <div class="flex-none"> <button class="uk-button border card default"
                                                onclick="copyToClipboard()" id="copy-button"
                                                style="display: flex; align-items: center; gap: 8px; padding: 8px 12px; min-width: 110px; justify-content: center;">
                                                <span>Copy Link</span>
                                            </button>
                                        </div>
                                    </div>
                                    <hr class="card my-4">
                                    <div class="space-y-4">
                                        <h4 class="text-sm font-medium">Staff members with access : </h4>
                                        <div class="flex items-center space-x-4">
                                            <span
                                                class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                                                <img class="aspect-square h-full w-full"
                                                    src="https://api.dicebear.com/8.x/lorelei/svg?seed=Olivia Martin">
                                            </span>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium leading-none">Dixith Mediga</p>
                                                <p class="text-sm "><EMAIL></p>
                                            </div>
                                            <div class="h-9 w-[200px] relative">
                                                <div
                                                    class="theme-responsive-button card theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer universal-hover">
                                                    <span>Select Permissions</span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M19 9l-7 7-7-7">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <div class="uk-drop uk-dropdown w-[252px] hidden themer-icon"
                                                    uk-drop="mode: click; pos: bottom-right">
                                                    <div class="m-1 flex items-center px-2 ">
                                                        <uk-icon class="opacity-50" icon="search"></uk-icon>
                                                        <input
                                                            class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                                            style="background-color: transparent;"
                                                            placeholder="Select a new role" type="text">
                                                    </div>
                                                    <ul class="uk-dropdown-nav">
                                                        <li class="uk-nav-divider"></li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Owner</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Admin-level to all resources.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Management</div>
                                                                    <div class="text-sm "
                                                                        style="color: var(--dropdown-text);">
                                                                        Management access to hotel operations.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Development</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Technical development access.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Housekeeping</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Housekeeping staff access.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Receptionist</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Front desk reception access.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <span
                                                class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                                                <img class="aspect-square h-full w-full"
                                                    src="https://api.dicebear.com/8.x/lorelei/svg?seed=Isabella Nguyen">
                                            </span>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium leading-none">Harsh Jadhav</p>
                                                <p class="text-sm "><EMAIL></p>
                                            </div>
                                            <div class="h-9 w-[200px] relative">
                                                <div
                                                    class="theme-responsive-button card theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer universal-hover">
                                                    <span>Select Permissions</span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M19 9l-7 7-7-7">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <div class="uk-drop uk-dropdown w-[252px] hidden themer-icon"
                                                    uk-drop="mode: click; pos: bottom-right">
                                                    <div class="m-1 flex items-center px-2 ">
                                                        <uk-icon class="opacity-50" icon="search"></uk-icon>
                                                        <input
                                                            class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                                            style="background-color: transparent;"
                                                            placeholder="Select a new role" type="text">
                                                    </div>
                                                    <ul class="uk-dropdown-nav">
                                                        <li class="uk-nav-divider"></li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Owner</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Admin-level to all resources.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Management</div>
                                                                    <div class="text-sm "
                                                                        style="color: var(--dropdown-text);">
                                                                        Management access to hotel operations.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Development</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Technical development access.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Housekeeping</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Housekeeping staff access.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Receptionist</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Front desk reception access.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <span
                                                class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                                                <img class="aspect-square h-full w-full"
                                                    src="https://api.dicebear.com/8.x/lorelei/svg?seed=Sofia Davis">
                                            </span>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium leading-none">George Garriga</p>
                                                <p class="text-sm "><EMAIL></p>
                                            </div>
                                            <div class="h-9 w-[200px] relative">
                                                <div
                                                    class="theme-responsive-button card theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer universal-hover">
                                                    <span>Select Permissions</span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M19 9l-7 7-7-7">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <div class="uk-drop uk-dropdown w-[252px] hidden themer-icon"
                                                    uk-drop="mode: click; pos: bottom-right">
                                                    <div class="m-1 flex items-center px-2 ">
                                                        <uk-icon class="opacity-50" icon="search"></uk-icon>
                                                        <input
                                                            class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                                            style="background-color: transparent;"
                                                            placeholder="Select a new role" type="text">
                                                    </div>
                                                    <ul class="uk-dropdown-nav">
                                                        <li class="uk-nav-divider"></li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Owner</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Admin-level to all resources.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Management</div>
                                                                    <div class="text-sm "
                                                                        style="color: var(--dropdown-text);">
                                                                        Management access to hotel operations.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Development</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Technical development access.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Housekeeping</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Housekeeping staff access.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Receptionist</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Front desk reception access.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Insert or replace Jackon Lee's block with the following -->
                                        <div class="flex items-center space-x-4">
                                            <span
                                                class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                                                <img class="aspect-square h-full w-full"
                                                    src="https://api.dicebear.com/8.x/lorelei/svg?seed=Jackon Lee">
                                            </span>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium leading-none">Jackon Lee</p>
                                                <p class="text-sm "><EMAIL></p>
                                            </div>
                                            <div class="h-9 w-[200px] relative">
                                                <div id="jackon-permissions-selector"
                                                    class="theme-responsive-button card theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer universal-hover">
                                                    <span id="jackon-selected-permission">Select Permissions</span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none"
                                                        viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M19 9l-7 7-7-7">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <div id="jackon-permission-dropdown"
                                                    class="uk-drop uk-dropdown dropdown-content w-[252px] hidden themer-icon"
                                                    uk-drop="mode: click; pos: bottom-right">
                                                    <div class="m-1 flex items-center px-2 ">
                                                        <uk-icon class="opacity-50" icon="search">
                                                        </uk-icon>
                                                        <input
                                                            class="block w-full bg-transparent pl-2 text-sm focus:outline-none themer-input"
                                                            style="background-color: transparent;"
                                                            placeholder="Select a new role" type="text">
                                                    </div>
                                                    <ul class="uk-dropdown-nav">
                                                        <li class="uk-nav-divider"></li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Owner</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Admin-level to all resources.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Management</div>
                                                                    <div class="text-sm "
                                                                        style="color: var(--dropdown-text);">
                                                                        Management access to hotel operations.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Development</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Technical development access.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Housekeeping</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Housekeeping staff access.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="uk-drop-close" href="#demo" role="button">
                                                                <div>
                                                                    <div class="dropdown-option-3">Receptionist</div>
                                                                    <div class="text-sm  "
                                                                        style="color: var(--dropdown-text);">
                                                                        Front desk reception access.
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <script>
                                            document.addEventListener('DOMContentLoaded', function () {
                                                let currentOpenDropdown = null;

                                                // Attach handlers to each .theme-dropdown trigger
                                                document.querySelectorAll('.theme-dropdown').forEach(trigger => {
                                                    const container = trigger.closest('.relative');
                                                    if (!container) return;
                                                    const dropdown = container.querySelector('.uk-dropdown');
                                                    if (!dropdown) return;

                                                    // Add the dropdown-content class if it doesn't exist
                                                    if (!dropdown.classList.contains('dropdown-content')) {
                                                        dropdown.classList.add('dropdown-content');
                                                    }

                                                    trigger.addEventListener('click', e => {
                                                        e.preventDefault();
                                                        e.stopPropagation();

                                                        if (dropdown.classList.contains('hidden')) {
                                                            if (currentOpenDropdown && currentOpenDropdown !== dropdown) {
                                                                closeDropdown(currentOpenDropdown);
                                                            }
                                                            openDropdown(dropdown);
                                                            currentOpenDropdown = dropdown;
                                                        } else {
                                                            closeDropdown(dropdown);
                                                            currentOpenDropdown = null;
                                                        }
                                                    });

                                                    // Close dropdown if a .uk-drop-close link is clicked
                                                    dropdown.querySelectorAll('.uk-drop-close').forEach(item => {
                                                        item.addEventListener('click', e => {
                                                            e.preventDefault();
                                                            closeDropdown(dropdown);
                                                            if (currentOpenDropdown === dropdown) {
                                                                currentOpenDropdown = null;
                                                            }
                                                        });
                                                    });

                                                    // Prevent dropdown from closing when clicking inside
                                                    dropdown.addEventListener('click', e => e.stopPropagation());

                                                    // Search filter logic
                                                    const searchInput = dropdown.querySelector('input[type="text"]');
                                                    if (searchInput) {
                                                        const listItems = dropdown.querySelectorAll('ul.uk-dropdown-nav > li:not(.uk-nav-divider)');
                                                        searchInput.addEventListener('input', e => {
                                                            e.stopPropagation();
                                                            const query = e.target.value.toLowerCase();
                                                            listItems.forEach(li => {
                                                                const optionEl = li.querySelector('.dropdown-option-3');
                                                                const text = (optionEl ? optionEl.textContent : '').toLowerCase();
                                                                li.style.display = text.includes(query) ? '' : 'none';
                                                            });
                                                        });
                                                    }
                                                });

                                                // Close any open dropdown if clicking outside
                                                document.addEventListener('click', () => {
                                                    if (currentOpenDropdown) {
                                                        closeDropdown(currentOpenDropdown);
                                                        currentOpenDropdown = null;
                                                    }
                                                });

                                                function openDropdown(dropdown) {
                                                    // Set transform origin for proper animation
                                                    dropdown.style.transformOrigin = 'top right';
                                                    // Remove hidden class first
                                                    dropdown.classList.remove('hidden');
                                                    // Use requestAnimationFrame to ensure the browser processes the unhide before animating
                                                    requestAnimationFrame(() => {
                                                        dropdown.setAttribute('data-state', 'open');
                                                    });
                                                }

                                                function closeDropdown(dropdown) {
                                                    // Start the fade out animation
                                                    dropdown.setAttribute('data-state', '');
                                                    // Wait for animation to complete before hiding
                                                    setTimeout(() => {
                                                        dropdown.classList.add('hidden');
                                                    }, 200); // Match this to your animation duration
                                                }
                                            });
                                        </script>
                                        <script>
                                            function copyLink() {
                                                const linkInput = document.getElementById('share-link');
                                                linkInput.select();
                                                linkInput.setSelectionRange(0, 99999); // For mobile devices

                                                navigator.clipboard.writeText(linkInput.value)
                                                    .then(() => {
                                                        const button = event.target;
                                                        const originalText = button.textContent;
                                                        button.textContent = 'Copied!';

                                                        setTimeout(() => {
                                                            button.textContent = originalText;
                                                        }, 2000);
                                                    })
                                                    .catch(err => {
                                                        console.error('Failed to copy:', err);
                                                        alert('Failed to copy link');
                                                    });
                                            }

                                            function copyToClipboard() {
                                                // Get the text field and button
                                                const copyText = document.getElementById("share-link");
                                                const copyButton = document.getElementById("copy-button");

                                                // Try the modern clipboard API first
                                                if (navigator.clipboard && window.isSecureContext) {
                                                    navigator.clipboard.writeText(copyText.value)
                                                        .then(() => {
                                                            copyButton.textContent = "Copied!";
                                                            setTimeout(() => {
                                                                copyButton.textContent = "Copy Link";
                                                            }, 2000);
                                                        })
                                                        .catch(() => {
                                                            // Fallback to older method if clipboard API fails
                                                            fallbackCopyToClipboard(copyText, copyButton);
                                                        });
                                                } else {
                                                    // Use fallback for non-HTTPS or unsupported browsers
                                                    fallbackCopyToClipboard(copyText, copyButton);
                                                }
                                            }

                                            function fallbackCopyToClipboard(copyText, copyButton) {
                                                try {
                                                    // Select the text
                                                    copyText.select();
                                                    copyText.setSelectionRange(0, 99999); // For mobile devices

                                                    // Execute copy command
                                                    document.execCommand('copy');

                                                    // Update button text
                                                    copyButton.textContent = "Copied!";
                                                    setTimeout(() => {
                                                        copyButton.textContent = "Copy Link";
                                                    }, 2000);
                                                } catch (err) {
                                                    console.error('Failed to copy:', err);
                                                    copyButton.textContent = "Failed to copy";
                                                    setTimeout(() => {
                                                        copyButton.textContent = "Copy Link";
                                                    }, 2000);
                                                }
                                            }

                                        </script>
                                    </div>
                                </div>
                            </div>
                            <div><button class="uk-button uk-button-default" id="save-staff">Save Staff
                                    Configuration</button></div>
                        </li>

                        <li class="space-y-6" id="uk-switcher-16" role="tabpanel" aria-labelledby="uk-switcher-tab-15">
                            <div>
                                <h3 class="text-lg font-medium">Notifications</h3>
                                <p class="text-sm ">Configure how you receive notifications.</p>
                            </div>
                            <div class="border-t border-border card"></div>
                            <div class="space-y-2">
                                <span class="uk-form-label">Notify me about</span>

                                <!-- Replace the first radio button -->
                                <div class="checkbox-wrapper-46">
                                    <input type="radio" id="notification_all_messages" class="inp-cbx"
                                        name="notification_type">
                                    <label for="notification_all_messages" class="cbx">
                                        <span>
                                            <svg viewBox="0 0 12 10" height="10px" width="12px">
                                                <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                            </svg>
                                        </span>
                                        <span>All new messages</span>
                                    </label>
                                </div>

                                <div class="checkbox-wrapper-46">
                                    <input type="radio" id="notification_direct_messages" class="inp-cbx"
                                        name="notification_type">
                                    <label for="notification_direct_messages" class="cbx">
                                        <span>
                                            <svg viewBox="0 0 12 10" height="10px" width="12px">
                                                <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                            </svg>
                                        </span>
                                        <span>Direct messages and mentions</span>
                                    </label>
                                </div>

                                <div class="checkbox-wrapper-46">
                                    <input type="radio" id="notification_none" class="inp-cbx" name="notification_type">
                                    <label for="notification_none" class="cbx">
                                        <span>
                                            <svg viewBox="0 0 12 10" height="10px" width="12px">
                                                <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                            </svg>
                                        </span>
                                        <span>Nothing</span>
                                    </label>
                                </div>
                            </div>
                            <div>
                                <h3 class="mb-4 text-lg font-medium">Email Notifications</h3>
                                <div class="space-y-4">
                                    <!--[-->
                                    <div
                                        class="flex items-center justify-between rounded-lg border border-border p-4 card">
                                        <div class="space-y-0.5">
                                            <label class="text-base font-medium"
                                                for="email_notification_0">Communication emails</label>
                                            <div class="uk-form-help">Receive emails about your account activity.</div>
                                        </div>
                                        <!-- Replace with custom toggle -->
                                        <div class="cl-toggle-switch">
                                            <label class="cl-switch">
                                                <input type="checkbox" id="email_communication"
                                                    class="email-notification-toggle">
                                                <span></span>
                                            </label>
                                        </div>
                                    </div>

                                    <div
                                        class="flex items-center justify-between rounded-lg border border-border p-4 card">
                                        <div class="space-y-0.5">
                                            <label class="text-base font-medium" for="email_notification_1">Marketing
                                                emails</label>
                                            <div class="uk-form-help">Receive emails about new products, features, and
                                                more.</div>
                                        </div>
                                        <!-- Replace with custom toggle -->

                                        <div class="cl-toggle-switch">
                                            <label class="cl-switch">
                                                <input type="checkbox" id="email_marketing"
                                                    class="email-notification-toggle">
                                                <span></span>
                                            </label>
                                        </div>
                                    </div>

                                    <div
                                        class="flex items-center justify-between rounded-lg border border-border p-4 card">
                                        <div class="space-y-0.5">
                                            <label class="text-base font-medium" for="email_notification_2">Social
                                                emails</label>
                                            <div class="uk-form-help">Receive emails for friend requests, follows, and
                                                more.</div>
                                        </div>
                                        <!-- Replace with custom toggle -->
                                        <div class="cl-toggle-switch">
                                            <label class="cl-switch">
                                                <input type="checkbox" id="email_social"
                                                    class="email-notification-toggle">
                                                <span></span>
                                            </label>
                                        </div>
                                    </div>

                                    <!--]-->
                                </div>
                            </div>



                            <div><button id="update-notifications-btn" class="uk-button uk-button-default">Update
                                    notifications</button></div>
                        </li>





                        <!-- Bot Configuration Tab -->
                        <li id="uk-switcher-20" role="tabpanel" aria-labelledby="uk-switcher-tab-19" class="p-6">
                            <div class="mb-6">
                                <h3 class="text-lg font-medium mb-2">Bot Configuration</h3>
                                <p class="text-sm text-gray-500">Configure your AI bot settings and knowledge base.</p>
                            </div>
                            <div class="border-t border-border card mb-8"></div>

                            <div class="max-w-3xl mx-auto">
                                <div class="grid gap-8">
                                    <!-- Bot Name -->
                                    <div class="form-group">
                                        <label class="uk-form-label text-base font-medium mb-2 block" for="bot-name">Bot Name</label>
                                        <input class="uk-input card focus:outline-none focus:ring-0 h-10" id="bot-name"
                                            type="text" placeholder="Enter bot name">
                                    </div>

                                    <!-- Bot Prompt -->
                                    <div class="form-group">
                                        <label class="uk-form-label text-base font-medium mb-2 block" for="bot-prompt">Prompt</label>
                                        <textarea class="uk-textarea card focus:outline-none focus:ring-0 p-3" id="bot-prompt"
                                            rows="5" placeholder="Enter bot prompt instructions"></textarea>
                                        <div class="text-xs text-gray-500 mt-1">Instructions that define how your bot should respond.</div>
                                    </div>

                                    <!-- Temperature Dropdown -->
                                    <div class="form-group">
                                        <label class="uk-form-label text-base font-medium mb-2 block" for="temperature-dropdown">Temperature</label>
                                        <div class="h-10">
                                            <div class="uk-position-relative">
                                                <button type="button" id="temperature-dropdown-btn"
                                                    class="uk-input card w-full flex justify-between items-center focus:outline-none focus:ring-0 h-10"
                                                    aria-haspopup="true"
                                                    style="background-color: transparent; color: var(--dropdown-text);">
                                                    0.7
                                                    <uk-icon class="" icon="chevrons-up-down"></uk-icon>
                                                </button>
                                                <div id="temperature-dropdown" uk-dropdown="mode: click; pos: bottom-justify"
                                                    class="uk-dropdown w-full dropdown-content">
                                                    <ul class="uk-nav uk-dropdown-nav uk-overflow-auto uk-cs-options">
                                                        <li>
                                                            <a class="temperature-option" data-value="0.1">
                                                                <div class="uk-cs-item-wrapper">
                                                                    <span class="uk-cs-item-text">0.1 (Most deterministic)</span>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="temperature-option" data-value="0.3">
                                                                <div class="uk-cs-item-wrapper">
                                                                    <span class="uk-cs-item-text">0.3 (Less creative)</span>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="temperature-option" data-value="0.5">
                                                                <div class="uk-cs-item-wrapper">
                                                                    <span class="uk-cs-item-text">0.5 (Balanced)</span>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li class="uk-active">
                                                            <a class="temperature-option" data-value="0.7">
                                                                <div class="uk-cs-item-wrapper">
                                                                    <span class="uk-cs-item-text">0.7 (Default)</span>
                                                                </div>
                                                                <uk-icon class="uk-cs-check" icon="check"></uk-icon>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="temperature-option" data-value="0.9">
                                                                <div class="uk-cs-item-wrapper">
                                                                    <span class="uk-cs-item-text">0.9 (More creative)</span>
                                                                </div>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="temperature-option" data-value="1.0">
                                                                <div class="uk-cs-item-wrapper">
                                                                    <span class="uk-cs-item-text">1.0 (Most creative)</span>
                                                                </div>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <input type="file" id="knowledge-base-upload-input" accept=".txt" style="display: none;">
                                        </div>
                                        <div class="text-xs text-gray-500 mt-1">Controls randomness: lower values are more deterministic, higher values are more creative.</div>
                                    </div>

                                    <!-- Knowledge Base -->
                                    <div class="form-group">
                                        <label class="uk-form-label text-base font-medium mb-2 block" for="knowledge-base">Knowledge Base</label>
                                        <div class="flex items-center space-x-4 mb-1">
                                            <button class="uk-button uk-button-default h-10 px-4" id="upload-kb-btn">Upload Knowledge Base</button>
                                            <div class="h-6 border-l border-border card"></div> <!-- Vertical Divider -->
                                            <button class="uk-button uk-button-default h-10 px-4" id="edit-kb-btn">Edit Knowledge Base</button>
                                        </div>
                                        <div class="text-xs text-gray-500 mt-1">Custom information that will be available to the bot.</div>
                                    </div>
                                </div>
                                
                                <div class="mt-10 pt-6 border-t border-border card">
                                    <button id="save-bot-config" class="uk-button uk-button-primary px-6 py-2">Save Bot Configuration</button>
                                </div>
                            </div>
                        </li>

                        <li class="space-y-6" id="uk-switcher-18" role="tabpanel" aria-labelledby="uk-switcher-tab-17">
                            <div>
                                <h3 class="text-lg font-medium">Appearance</h3>
                                <p class="text-sm ">Customize the appearance of the app.
                                    Automatically switch between day and night
                                    themes.
                                </p>
                            </div>
                            <div class="border-t border-border card"></div>
                            <script>
                                document.addEventListener('DOMContentLoaded', function () {
                                    // Make sure UIkit is loaded first
                                    if (typeof UIkit !== 'undefined') {
                                        // Initialize the font dropdown specifically
                                        const fontDropdown = document.getElementById('font-dropdown');
                                        if (fontDropdown) {
                                            const dropdown = UIkit.dropdown(fontDropdown, {
                                                mode: 'click',
                                                pos: 'bottom-justify'
                                            });

                                            // Store the dropdown instance on the element
                                            fontDropdown.ukDropdown = dropdown;
                                        }

                                        // Get all font options
                                        const fontOptions = document.querySelectorAll('.font-option');
                                        fontOptions.forEach(option => {
                                            option.addEventListener('click', function (event) {
                                                event.preventDefault();

                                                // Update the button text with selected value
                                                const selectedValue = this.getAttribute('data-value');
                                                document.getElementById('font-dropdown-btn').innerText = selectedValue;

                                                // Update active state
                                                fontOptions.forEach(opt => {
                                                    opt.parentElement.classList.remove('uk-active');
                                                });
                                                this.parentElement.classList.add('uk-active');

                                                // Add the check icon to the selected option
                                                fontOptions.forEach(opt => {
                                                    const checkIcon = opt.querySelector('.uk-cs-check');
                                                    if (checkIcon) {
                                                        checkIcon.style.display = 'none';
                                                    }
                                                });

                                                const selectedCheckIcon = this.querySelector('.uk-cs-check');
                                                if (selectedCheckIcon) {
                                                    selectedCheckIcon.style.display = 'block';
                                                } else {
                                                    // Create check icon if it doesn't exist
                                                    const checkIcon = document.createElement('uk-icon');
                                                    checkIcon.className = 'uk-cs-check';
                                                    checkIcon.setAttribute('icon', 'check');




                                                    checkIcon.appendChild(svg);
                                                    this.appendChild(checkIcon);
                                                }

                                                // Close dropdown
                                                if (fontDropdown.ukDropdown) {
                                                    fontDropdown.ukDropdown.hide();
                                                }

                                                // Optional: Actually apply the selected font to the page
                                                document.body.style.fontFamily = selectedValue + ", sans-serif";
                                            });
                                        });
                                    } else {
                                        console.error('UIkit is not loaded. Make sure it\'s loaded before this script runs.');
                                    }
                                });
                            </script>
                            <div class="space-y-2">
                                <span class="uk-form-label">Theme</span>
                                <div class="uk-form-help">Select the theme for the dashboard.</div>
                                <div class="grid max-w-md grid-cols-2 gap-8">
                                    <a class="block cursor-pointer items-center rounded-md border-2 border-muted p-1 ring-ring theme-options"
                                        data-theme="light">
                                        <div class="space-y-2 rounded-sm bg-[#ecedef] p-2">
                                            <div class="space-y-2">
                                                <div class="h-2 w-[80px] rounded bg-slate-400"></div>
                                                <div class="h-2 w-[100px] rounded bg-slate-400"></div>
                                            </div>
                                            <div class="flex items-center space-x-2 rounded-md bg-white p-2 shadow">
                                                <div class="h-4 w-4 rounded-full bg-slate-900"></div>
                                                <div class="h-2 w-[100px] rounded bg-slate-400"></div>
                                            </div>
                                            <div class="flex items-center space-x-2 rounded-md bg-white p-2 shadow">
                                                <div class="h-4 w-4 rounded-full bg-slate-900"></div>
                                                <div class="h-2 w-[100px] rounded bg-slate-400"></div>
                                            </div>
                                            <span class="block text-center text-sm font-medium">Light</span>
                                        </div>
                                    </a>
                                    <a class="block cursor-pointer items-center rounded-md border-2 border-muted bg-popover p-1 ring-ring theme-options"
                                        data-theme="pure-black">
                                        <div class="space-y-2 rounded-sm bg-slate-950 p-2">
                                            <div class="space-y-2">
                                                <div class="h-2 w-[80px] rounded bg-slate-700"></div>
                                                <div class="h-2 w-[100px] rounded bg-slate-700"></div>
                                            </div>
                                            <div class="flex items-center space-x-2 rounded-md bg-slate-800 p-2 shadow">
                                                <div class="h-4 w-4 rounded-full bg-slate-400"></div>
                                                <div class="h-2 w-[100px] rounded bg-slate-700"></div>
                                            </div>
                                            <div class="flex items-center space-x-2 rounded-md bg-slate-800 p-2 shadow">
                                                <div class="h-4 w-4 rounded-full bg-slate-400"></div>
                                                <div class="h-2 w-[100px] rounded bg-slate-700"></div>
                                            </div>
                                            <span class="block text-center text-sm font-medium">Dark</span>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </li>

                    </ul>
                </div>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize UIkit switcher for the tabs
            if (typeof UIkit !== 'undefined') {
                // Make sure the switcher is properly initialized
                const navElement = document.querySelector('.uk-nav-secondary');
                if (navElement) {
                    const switcher = UIkit.switcher(navElement);
                    if (switcher) {
                        console.log('Switcher initialized successfully');
                    }
                }

                // Create a custom tab switcher to ensure it works correctly
                const allTabs = document.querySelectorAll('.uk-nav-secondary > li > a');
                const allPanels = document.querySelectorAll('#component-nav > li');

                // Add click handlers to all tabs
                allTabs.forEach((tab, index) => {
                    tab.addEventListener('click', function(e) {
                        e.preventDefault();

                        // Hide all panels
                        allPanels.forEach(panel => {
                            panel.style.display = 'none';
                        });

                        // Remove active class from all tabs
                        allTabs.forEach(t => {
                            t.parentElement.classList.remove('uk-active');
                            t.setAttribute('aria-selected', 'false');
                        });

                        // Show the selected panel
                        const targetId = tab.getAttribute('aria-controls');
                        const targetPanel = document.getElementById(targetId);
                        if (targetPanel) {
                            targetPanel.style.display = 'block';
                        }

                        // Add active class to selected tab
                        tab.parentElement.classList.add('uk-active');
                        tab.setAttribute('aria-selected', 'true');

                        console.log('Switched to tab:', targetId);
                    });
                });

                // Specifically handle the Bot Configuration tab
                const botConfigTab = document.getElementById('uk-switcher-tab-19');
                if (botConfigTab) {
                    botConfigTab.addEventListener('click', function(e) {
                        e.preventDefault();

                        // Hide all panels
                        allPanels.forEach(panel => {
                            panel.style.display = 'none';
                        });

                        // Show the bot configuration panel
                        const botConfigPanel = document.getElementById('uk-switcher-20');
                        if (botConfigPanel) {
                            botConfigPanel.style.display = 'block';
                        }

                        // Update active state
                        allTabs.forEach(t => {
                            t.parentElement.classList.remove('uk-active');
                            t.setAttribute('aria-selected', 'false');
                        });
                        botConfigTab.parentElement.classList.add('uk-active');
                        botConfigTab.setAttribute('aria-selected', 'true');

                        console.log('Switched to Bot Configuration tab');
                    });
                }

                // Initialize the tabs - hide all panels except the first one
                if (allPanels.length > 0) {
                    allPanels.forEach((panel, index) => {
                        if (index !== 0) {
                            panel.style.display = 'none';
                        }
                    });

                    // Set the first tab as active
                    if (allTabs.length > 0) {
                        allTabs[0].parentElement.classList.add('uk-active');
                        allTabs[0].setAttribute('aria-selected', 'true');
                    }
                }

                // Temperature dropdown functionality for Bot Configuration
                const temperatureDropdownBtn = document.getElementById('temperature-dropdown-btn');
                const temperatureDropdown = document.getElementById('temperature-dropdown');
                const temperatureOptions = document.querySelectorAll('#temperature-dropdown .temperature-option');

                // Initialize UIkit dropdown if not already done
                if (typeof UIkit !== 'undefined' && temperatureDropdown) {
                    const dropdown = UIkit.dropdown(temperatureDropdown, {
                        mode: 'click',
                        pos: 'bottom-justify'
                    });

                    // Add animation classes
                    temperatureDropdown.classList.add('dropdown-content');

                    // Add animation handlers
                    UIkit.util.on(temperatureDropdown, 'beforeshow', function () {
                        this.style.transformOrigin = 'top center';
                        this.setAttribute('data-state', '');

                        requestAnimationFrame(() => {
                            this.setAttribute('data-state', 'open');
                        });
                    });

                    UIkit.util.on(temperatureDropdown, 'beforehide', function () {
                        this.setAttribute('data-state', '');

                        const dropdown = this;
                        const component = UIkit.dropdown(dropdown);

                        if (component) {
                            const originalHide = component.hide;
                            component.hide = function () { };

                            setTimeout(() => {
                                component.hide = originalHide;
                                component.hide();
                            }, 200);
                        }

                        return false;
                    });
                }

                // Handle temperature selection
                if (temperatureOptions) {
                    temperatureOptions.forEach(option => {
                        option.addEventListener('click', function (e) {
                            e.preventDefault();

                            // Get the display text and value
                            const selectedValue = this.getAttribute('data-value');
                            const selectedText = this.querySelector('.uk-cs-item-text').textContent;

                            // Update button text
                            temperatureDropdownBtn.innerHTML = selectedValue + ' <uk-icon icon="chevrons-up-down"></uk-icon>';

                            // Store the selected value (for form submission)
                            temperatureDropdownBtn.setAttribute('data-selected', selectedValue);

                            // Update active state
                            temperatureOptions.forEach(opt => {
                                const parentLi = opt.closest('li');
                                if (parentLi) {
                                    parentLi.classList.remove('uk-active');

                                    // Remove check icon if exists
                                    const checkIcon = opt.querySelector('.uk-cs-check');
                                    if (checkIcon) {
                                        checkIcon.remove();
                                    }
                                }
                            });

                            // Add active state and check icon to selected item
                            const parentLi = this.closest('li');
                            if (parentLi) {
                                parentLi.classList.add('uk-active');

                                // Add check icon if not exists
                                if (!this.querySelector('.uk-cs-check')) {
                                    const checkIcon = document.createElement('uk-icon');
                                    checkIcon.className = 'uk-cs-check';
                                    checkIcon.setAttribute('icon', 'check');
                                    this.appendChild(checkIcon);
                                }
                            }
                        });
                    });
                }

                // Update button when form is submitted
                const saveConfigBtn = document.getElementById('save-bot-config');
                if (saveConfigBtn) {
                    saveConfigBtn.addEventListener('click', function() {
                        // Get bot configuration values
                        const botName = document.getElementById('bot-name').value;
                        const botPrompt = document.getElementById('bot-prompt').value;
                        const temperature = temperatureDropdownBtn.getAttribute('data-selected') || '0.7';
                        const knowledgeBase = document.getElementById('knowledge-base').value;

                        console.log('Bot Configuration:', {
                            name: botName,
                            prompt: botPrompt,
                            temperature: temperature,
                            knowledgeBase: knowledgeBase
                        });

                        // Here you would send this data to your backend
                        alert('Bot configuration saved successfully!');
                    });
                }
            }

            const selectedTheme = localStorage.getItem('selectedTheme') || 'light';
            const themeOptions = document.querySelectorAll('.theme-options');

            // Update UI to reflect current theme
            themeOptions.forEach(option => {
                const isSelected = option.getAttribute('data-theme') === selectedTheme;
                if (isSelected) {
                    option.classList.add('ring-2');
                } else {
                    option.classList.remove('ring-2');
                }
            });

            // Apply theme to body when an option is clicked
            themeOptions.forEach(option => {
                option.addEventListener('click', function () {
                    const theme = this.getAttribute('data-theme');

                    // Add visual indication of selection
                    themeOptions.forEach(opt => opt.classList.remove('ring-2'));
                    this.classList.add('ring-2');

                    // Apply theme
                    document.body.className = theme;
                    localStorage.setItem('selectedTheme', theme);
                });
            });
        });
    </script>
    <!-- Add this to the head section of your HTML -->
    <script src="../templates/constants/constants.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Reference to PMS configuration form elements
            const pmsDropdownBtn = document.getElementById('pms-dropdown-btn');
            const apiKeyInput = document.getElementById('api-key');
            const pmsEndpointInput = document.getElementById('pms-endpoint');
            const permissionLevelBtn = document.getElementById('permission-level-btn');
            const saveConfigBtn = document.getElementById('save-pms-config');

            // Permission level dropdown setup
            const permissionLevelDropdown = document.getElementById('permission-level-dropdown');
            const permissionLevelOptions = document.querySelectorAll('#permission-level-dropdown .permission-level-option');

            // Initialize hidden input to store actual value for form submission
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.id = 'permission-level';
            hiddenInput.name = 'permission-level';
            hiddenInput.value = 'read'; // Default value
            permissionLevelBtn.parentNode.appendChild(hiddenInput);

            // Initialize UIkit dropdown for permission level if not already done
            if (typeof UIkit !== 'undefined' && permissionLevelDropdown) {
                const dropdown = UIkit.dropdown(permissionLevelDropdown, {
                    mode: 'click',
                    pos: 'bottom-justify'
                });

                // Add animation handlers
                UIkit.util.on(permissionLevelDropdown, 'beforeshow', function () {
                    this.style.transformOrigin = 'top center';
                    this.setAttribute('data-state', '');

                    requestAnimationFrame(() => {
                        this.setAttribute('data-state', 'open');
                    });
                });

                UIkit.util.on(permissionLevelDropdown, 'beforehide', function () {
                    this.setAttribute('data-state', '');

                    const dropdown = this;
                    const component = UIkit.dropdown(dropdown);

                    if (component) {
                        const originalHide = component.hide;
                        component.hide = function () { };

                        setTimeout(() => {
                            component.hide = originalHide;
                            component.hide();
                        }, 200);
                    }

                    return false;
                });
            }

            // Handle permission level selection
            permissionLevelOptions.forEach(option => {
                option.addEventListener('click', function (e) {
                    e.preventDefault();

                    // Get the display text and value
                    const selectedValue = this.getAttribute('data-value');
                    const selectedText = this.querySelector('.uk-cs-item-text').textContent;

                    // Update button text
                    permissionLevelBtn.innerHTML = selectedText + ' <uk-icon icon="chevrons-up-down"></uk-icon>';

                    // Update hidden input value
                    document.getElementById('permission-level').value = selectedValue;

                    // Update active state
                    permissionLevelOptions.forEach(opt => {
                        const parentLi = opt.closest('li');
                        if (parentLi) {
                            parentLi.classList.remove('uk-active');

                            // Remove check icon if exists
                            const checkIcon = opt.querySelector('.uk-cs-check');
                            if (checkIcon) {
                                checkIcon.remove();
                            }
                        }
                    });

                    // Add active state and check icon to selected item
                    const parentLi = this.closest('li');
                    if (parentLi) {
                        parentLi.classList.add('uk-active');

                        // Add check icon if not exists
                        if (!this.querySelector('.uk-cs-check')) {
                            const checkIcon = document.createElement('uk-icon');
                            checkIcon.className = 'uk-cs-check';
                            checkIcon.setAttribute('icon', 'check');
                            this.appendChild(checkIcon);
                        }
                    }
                });
            });

            // Load the saved configuration immediately
            loadPmsConfig();

            // Handle PMS dropdown selection
            const pmsOptions = document.querySelectorAll('.pms-option');
            pmsOptions.forEach(option => {
                option.addEventListener('click', function (e) {
                    e.preventDefault();
                    const selectedValue = this.getAttribute('data-value');
                    pmsDropdownBtn.innerHTML = selectedValue + ' <uk-icon class="" icon="chevrons-up-down"></uk-icon>';
                    pmsDropdownBtn.setAttribute('data-selected', selectedValue);
                });
            });

            // Function to load PMS configuration from Python backend
            function loadPmsConfig() {
                fetch(API_ROUTES.PMS_CONFIG.LOAD)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(config => {
                        console.log("Received config data:", config);

                        // Set PMS provider dropdown
                        if (config.pmsProvider) {
                            pmsDropdownBtn.innerHTML = config.pmsProvider + ' <uk-icon class="" icon="chevrons-up-down"></uk-icon>';
                            pmsDropdownBtn.setAttribute('data-selected', config.pmsProvider);
                        }

                        // Set API Key
                        if (config.apiKey) {
                            apiKeyInput.value = config.apiKey;
                        }

                        // Set API Endpoint
                        if (config.apiEndpoint) {
                            pmsEndpointInput.value = config.apiEndpoint;
                        }

                        // Set Permission Level for custom dropdown
                        if (config.permissionLevel) {
                            // Find the matching option to get the display text
                            const option = Array.from(permissionLevelOptions).find(
                                opt => opt.getAttribute('data-value') === config.permissionLevel
                            );

                            if (option) {
                                const text = option.querySelector('.uk-cs-item-text').textContent;
                                permissionLevelBtn.innerHTML = text + ' <uk-icon icon="chevrons-up-down"></uk-icon>';
                                document.getElementById('permission-level').value = config.permissionLevel;

                                // Update active state
                                permissionLevelOptions.forEach(opt => {
                                    const parentLi = opt.closest('li');
                                    if (parentLi) {
                                        parentLi.classList.remove('uk-active');

                                        // Remove check icon if exists
                                        const checkIcon = opt.querySelector('.uk-cs-check');
                                        if (checkIcon) {
                                            checkIcon.remove();
                                        }
                                    }
                                });

                                // Add active state and check icon to the matching option
                                const optionLi = option.closest('li');
                                if (optionLi) {
                                    optionLi.classList.add('uk-active');

                                    // Add check icon if not exists
                                    if (!option.querySelector('.uk-cs-check')) {
                                        const checkIcon = document.createElement('uk-icon');
                                        checkIcon.className = 'uk-cs-check';
                                        checkIcon.setAttribute('icon', 'check');
                                        option.appendChild(checkIcon);
                                    }
                                }
                            }
                        }

                        // Update last sync time
                        if (config.lastUpdated) {
                            const date = new Date(config.lastUpdated);
                            const timeString = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                            document.querySelector('.uk-form-help .text-gray-500').textContent = `Last sync: Today at ${timeString}`;
                        }

                        console.log('PMS configuration loaded successfully');
                    })
                    .catch(error => {
                        console.error('Error loading saved configuration:', error);
                        showNotification('Failed to load configuration from server. Check that your Python backend is running.', 'error');
                    });
            }

            saveConfigBtn.addEventListener('click', function () {
                // Collect form data
                const pmsConfig = {
                    pmsProvider: pmsDropdownBtn.getAttribute('data-selected') || 'Not selected',
                    apiKey: apiKeyInput.value,
                    apiEndpoint: pmsEndpointInput.value,
                    permissionLevel: document.getElementById('permission-level').value,
                    lastUpdated: new Date().toISOString()
                };

                // Validate required fields
                if (!pmsConfig.pmsProvider || pmsConfig.pmsProvider === 'Not selected') {
                    showNotification('Please select a PMS provider', 'error');
                    return;
                }

                if (!pmsConfig.apiKey) {
                    showNotification('API Key is required', 'error');
                    return;
                }

                if (!pmsConfig.apiEndpoint) {
                    showNotification('API Endpoint is required', 'error');
                    return;
                }

                // Send the data to the server to save as JSON
                savePmsConfigToJson(pmsConfig);
            });

            // Function to save configuration to Python backend
            function savePmsConfigToJson(configData) {
                // Show loading state
                saveConfigBtn.disabled = true;
                saveConfigBtn.innerHTML = 'Saving...';

                fetch(API_ROUTES.PMS_CONFIG.SAVE, {  // <-- Fixed: removed quotes to use the actual variable
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(configData)
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Show success notification
                        showNotification('PMS configuration saved successfully', 'success');

                        // Update last sync time display
                        const now = new Date();
                        const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                        document.querySelector('.uk-form-help .text-gray-500').textContent = `Last sync: Today at ${timeString}`;
                    })
                    .catch(error => {
                        console.error('Error saving configuration:', error);
                        showNotification('Failed to save to server. Check that your Python backend is running.', 'error');
                    })
                    .finally(() => {
                        // Reset button state
                        saveConfigBtn.disabled = false;
                        saveConfigBtn.innerHTML = 'Save Configuration';
                    });
            }

            // Simple notification function
            function showNotification(message, type = 'info') {
                // Use UIkit notification if available
                if (typeof UIkit !== 'undefined') {
                    UIkit.notification({
                        message: message,
                        status: type,
                        pos: 'bottom-right',
                        timeout: 5000
                    });
                } else {
                    alert(message);
                }
            }
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Language dropdown functionality
            const languageDropdownBtn = document.getElementById('language-dropdown-btn');
            const languageDropdown = document.getElementById('language-dropdown');
            const languageOptions = document.querySelectorAll('#language-dropdown .language-option');

            // Initialize UIkit dropdown if not already done
            if (typeof UIkit !== 'undefined' && languageDropdown) {
                const dropdown = UIkit.dropdown(languageDropdown, {
                    mode: 'click',
                    pos: 'bottom-justify'
                });

                // Add animation classes
                languageDropdown.classList.add('dropdown-content');

                // Add animation handlers
                UIkit.util.on(languageDropdown, 'beforeshow', function () {
                    this.style.transformOrigin = 'top center';
                    this.setAttribute('data-state', '');

                    requestAnimationFrame(() => {
                        this.setAttribute('data-state', 'open');
                    });
                });

                UIkit.util.on(languageDropdown, 'beforehide', function () {
                    this.setAttribute('data-state', '');

                    const dropdown = this;
                    const component = UIkit.dropdown(dropdown);

                    if (component) {
                        const originalHide = component.hide;
                        component.hide = function () { };

                        setTimeout(() => {
                            component.hide = originalHide;
                            component.hide();
                        }, 200);
                    }

                    return false;
                });
            }

            // Handle language selection
            languageOptions.forEach(option => {
                option.addEventListener('click', function (e) {
                    e.preventDefault();

                    // Update button text
                    const selectedValue = this.getAttribute('data-value');
                    languageDropdownBtn.innerHTML = selectedValue + ' <uk-icon icon="chevrons-up-down"></uk-icon>';

                    // Update active state
                    languageOptions.forEach(opt => {
                        const parentLi = opt.closest('li');
                        if (parentLi) {
                            parentLi.classList.remove('uk-active');

                            // Remove check icon if exists
                            const checkIcon = opt.querySelector('.uk-cs-check');
                            if (checkIcon) {
                                checkIcon.remove();
                            }
                        }
                    });

                    // Add active state and check icon to selected item
                    const parentLi = this.closest('li');
                    if (parentLi) {
                        parentLi.classList.add('uk-active');

                        // Add check icon if not exists
                        if (!this.querySelector('.uk-cs-check')) {
                            const checkIcon = document.createElement('uk-icon');
                            checkIcon.className = 'uk-cs-check';
                            checkIcon.setAttribute('icon', 'check');
                            this.appendChild(checkIcon);
                        }
                    }
                });
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Get references to form elements
            const languageDropdownBtn = document.getElementById('language-dropdown-btn');
            // New: include English, Spanish, French, Italian, Swedish, German, Dutch
            const guestLanguageCheckboxes = document.querySelectorAll('#lang_en, #lang_es, #lang_fr, #lang_it, #lang_sv, #lang_de, #lang_nl'); const timezoneDropdownBtn = document.getElementById('timezone-dropdown-btn');
            const currencyTrigger = document.getElementById('currency-trigger');
            const dateFormatDropdownBtn = document.getElementById('date-format-dropdown-btn');
            const saveButton = document.querySelector('#uk-switcher-14 .uk-button');

            // Load settings when page loads
            loadLanguageSettings();

            // Add event listener for save button
            saveButton.addEventListener('click', function () {
                saveLanguageSettings();
            });

            // Function to save language settings
            function saveLanguageSettings() {
                // Create settings object
                const settings = {
                    primaryLanguage: languageDropdownBtn.getAttribute('data-selected') || 'English',
                    guestLanguages: Array.from(guestLanguageCheckboxes)
                        .filter(checkbox => checkbox.checked)
                        .map(checkbox => checkbox.id.replace('lang_', '')),
                    timezone: timezoneDropdownBtn.getAttribute('data-selected') || 'UTC+05:30',
                    currency: currencyTrigger.getAttribute('data-value') || 'INR',
                    dateFormat: dateFormatDropdownBtn.getAttribute('data-selected') || 'DD/MM/YYYY'
                };

                // Show loading state
                saveButton.disabled = true;
                saveButton.innerHTML = 'Saving...';

                // Send data to backend
                fetch(window.API_ROUTES.SETTINGS.LANGUAGE, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(settings)
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Show success notification
                        UIkit.notification({
                            message: 'Language settings saved successfully!',
                            status: 'success',
                            pos: 'bottom-right',
                            timeout: 3000
                        });
                    })
                    .catch(error => {
                        console.error('Error saving language settings:', error);
                        // Show error notification
                        UIkit.notification({
                            message: 'Failed to save language settings',
                            status: 'danger',
                            pos: 'bottom-right',
                            timeout: 3000
                        });
                    })
                    .finally(() => {
                        // Restore button state
                        saveButton.disabled = false;
                        saveButton.innerHTML = 'Save Language Settings';
                    });
            }

            // Function to load language settings
            function loadLanguageSettings() {
                fetch(window.API_ROUTES.SETTINGS.LANGUAGE)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(settings => {
                        // Set primary language
                        if (settings.primaryLanguage) {
                            languageDropdownBtn.innerHTML = settings.primaryLanguage + ' <uk-icon icon="chevrons-up-down"></uk-icon>';
                            languageDropdownBtn.setAttribute('data-selected', settings.primaryLanguage);

                            // Update active state in dropdown
                            document.querySelectorAll('.language-option').forEach(option => {
                                const parentLi = option.closest('li');
                                const value = option.getAttribute('data-value');

                                if (parentLi) {
                                    if (value === settings.primaryLanguage) {
                                        parentLi.classList.add('uk-active');
                                        if (!option.querySelector('.uk-cs-check')) {
                                            const checkIcon = document.createElement('uk-icon');
                                            checkIcon.className = 'uk-cs-check';
                                            checkIcon.setAttribute('icon', 'check');
                                            option.appendChild(checkIcon);
                                        }
                                    } else {
                                        parentLi.classList.remove('uk-active');
                                        const checkIcon = option.querySelector('.uk-cs-check');
                                        if (checkIcon) checkIcon.remove();
                                    }
                                }
                            });
                        }

                        // Set guest languages
                        if (settings.guestLanguages && Array.isArray(settings.guestLanguages)) {
                            guestLanguageCheckboxes.forEach(checkbox => {
                                const lang = checkbox.id.replace('lang_', '');
                                checkbox.checked = settings.guestLanguages.includes(lang);
                            });
                        }

                        // Set timezone
                        if (settings.timezone) {
                            // Find the display text for this timezone
                            let timezoneText = settings.timezone;
                            document.querySelectorAll('.timezone-option').forEach(option => {
                                if (option.getAttribute('data-value') === settings.timezone) {
                                    timezoneText = option.querySelector('.uk-cs-item-text').textContent;
                                }
                            });

                            timezoneDropdownBtn.innerHTML = timezoneText + ' <uk-icon icon="chevrons-up-down"></uk-icon>';
                            timezoneDropdownBtn.setAttribute('data-selected', settings.timezone);

                            // Update the dropdown UI
                            document.querySelectorAll('.timezone-option').forEach(option => {
                                const parentLi = option.closest('li');
                                const value = option.getAttribute('data-value');

                                if (parentLi) {
                                    if (value === settings.timezone) {
                                        parentLi.classList.add('uk-active');
                                        if (!option.querySelector('.uk-cs-check')) {
                                            const checkIcon = document.createElement('uk-icon');
                                            checkIcon.className = 'uk-cs-check';
                                            checkIcon.setAttribute('icon', 'check');
                                            option.appendChild(checkIcon);
                                        }
                                    } else {
                                        parentLi.classList.remove('uk-active');
                                        const checkIcon = option.querySelector('.uk-cs-check');
                                        if (checkIcon) checkIcon.remove();
                                    }
                                }
                            });
                        }

                        // Set currency
                        if (settings.currency) {
                            // Find the display text for this currency
                            let currencyText = settings.currency;
                            document.querySelectorAll('.currency-option').forEach(option => {
                                if (option.getAttribute('data-value') === settings.currency) {
                                    currencyText = option.querySelector('.dropdown-option-3').textContent;
                                }
                            });

                            document.getElementById('selected-currency').textContent = currencyText;
                            currencyTrigger.setAttribute('data-value', settings.currency);
                        }

                        // Set date format
                        if (settings.dateFormat) {
                            dateFormatDropdownBtn.innerHTML = settings.dateFormat + ' <uk-icon icon="chevrons-up-down"></uk-icon>';
                            dateFormatDropdownBtn.setAttribute('data-selected', settings.dateFormat);

                            // Update the dropdown UI
                            document.querySelectorAll('.date-format-option').forEach(option => {
                                const parentLi = option.closest('li');
                                const value = option.getAttribute('data-value');

                                if (parentLi) {
                                    if (value === settings.dateFormat) {
                                        parentLi.classList.add('uk-active');
                                        if (!option.querySelector('.uk-cs-check')) {
                                            const checkIcon = document.createElement('uk-icon');
                                            checkIcon.className = 'uk-cs-check';
                                            checkIcon.setAttribute('icon', 'check');
                                            option.appendChild(checkIcon);
                                        }
                                    } else {
                                        parentLi.classList.remove('uk-active');
                                        const checkIcon = option.querySelector('.uk-cs-check');
                                        if (checkIcon) checkIcon.remove();
                                    }
                                }
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error loading language settings:', error);
                        UIkit.notification({
                            message: 'Failed to load language settings',
                            status: 'warning',
                            pos: 'bottom-right',
                            timeout: 3000
                        });
                    });
            }
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Add proper event listeners to permission dropdowns
            const staffButtons = document.querySelectorAll('#staff-members .theme-responsive-button');
            staffButtons.forEach(button => {
                // Get the span that displays the text
                const textSpan = button.querySelector('span');

                // Find the dropdown container
                const dropdownContainer = button.closest('.relative, .h-9');
                if (!dropdownContainer) return;

                const dropdown = dropdownContainer.querySelector('.uk-dropdown');
                if (!dropdown) return;

                // Find all options inside the dropdown
                const options = dropdown.querySelectorAll('a.uk-drop-close');

                // Add click handlers to each option
                options.forEach(option => {
                    option.addEventListener('click', function (e) {
                        e.preventDefault();

                        // Get the option text
                        const permissionText = this.querySelector('.dropdown-option-3').textContent;

                        // Update the button text
                        textSpan.textContent = permissionText;
                    });
                });
            });

            // Function to save staff settings
            function saveStaffSettings() {
                // Get all staff members from the DOM
                const staffMembers = [];
                const staffRows = document.querySelectorAll('#staff-members .flex.items-center.space-x-4');

                staffRows.forEach(row => {
                    const nameElement = row.querySelector('.flex-1 p.text-sm.font-medium');
                    const emailElement = row.querySelector('.flex-1 p.text-sm:not(.font-medium)');
                    const permissionButton = row.querySelector('.theme-responsive-button');

                    if (nameElement && emailElement && permissionButton) {
                        const permissionSpan = permissionButton.querySelector('span');

                        staffMembers.push({
                            name: nameElement.textContent.trim(),
                            email: emailElement.textContent.trim(),
                            permission: permissionSpan.textContent.trim()
                        });
                    }
                });

                // Get share link
                const shareLink = document.getElementById('share-link').value;

                // Create settings object
                const settings = {
                    shareLink: shareLink,
                    staffMembers: staffMembers,
                    lastUpdated: new Date().toISOString()
                };

                // Show loading state
                const saveStaffButton = document.getElementById('save-staff');
                saveStaffButton.disabled = true;
                saveStaffButton.innerHTML = 'Saving...';

                // Send data to backend
                fetch(window.API_ROUTES.SETTINGS.STAFF.SAVE, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(settings)
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Show success notification
                        UIkit.notification({
                            message: 'Staff settings saved successfully!',
                            status: 'success',
                            pos: 'bottom-right',
                            timeout: 3000
                        });
                    })
                    .catch(error => {
                        console.error('Error saving staff settings:', error);
                        // Show error notification
                        UIkit.notification({
                            message: 'Failed to save staff settings',
                            status: 'danger',
                            pos: 'bottom-right',
                            timeout: 3000
                        });
                    })
                    .finally(() => {
                        // Restore button state
                        saveStaffButton.disabled = false;
                        saveStaffButton.innerHTML = 'Save Staff Configuration';
                    });
            }

            // Load staff settings from server
            function loadStaffSettings() {
                fetch(window.API_ROUTES.SETTINGS.STAFF.LOAD)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(settings => {
                        // Set share link
                        if (settings.shareLink) {
                            document.getElementById('share-link').value = settings.shareLink;
                        }

                        // Set staff permissions
                        if (settings.staffMembers && Array.isArray(settings.staffMembers)) {
                            settings.staffMembers.forEach(staff => {
                                const staffRows = document.querySelectorAll('#staff-members .flex.items-center.space-x-4');

                                staffRows.forEach(row => {
                                    const emailEl = row.querySelector('.flex-1 p.text-sm:not(.font-medium)');
                                    if (emailEl && emailEl.textContent.trim() === staff.email) {
                                        const button = row.querySelector('.theme-responsive-button');
                                        if (button) {
                                            const textSpan = button.querySelector('span');
                                            if (textSpan) {
                                                // Update the displayed text with the saved permission
                                                textSpan.textContent = staff.permission;
                                            }
                                        }
                                    }
                                });
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error loading staff settings:', error);
                        UIkit.notification({
                            message: 'Failed to load staff settings',
                            status: 'warning',
                            pos: 'bottom-right',
                            timeout: 3000
                        });
                    });
            }

            // Load settings when page loads
            loadStaffSettings();

            // Add event listener to Save Staff Configuration button
            const saveStaffButton = document.getElementById('save-staff');
            if (saveStaffButton) {
                saveStaffButton.addEventListener('click', saveStaffSettings);
            }
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Reference to notification settings form elements using proper IDs
            const notificationRadios = document.querySelectorAll('input[name="notification_type"]');
            const emailCommunicationToggle = document.getElementById('email_communication');
            const emailMarketingToggle = document.getElementById('email_marketing');
            const emailSocialToggle = document.getElementById('email_social');
            const updateNotificationsButton = document.getElementById('update-notifications-btn');

            // Load notification settings when page loads
            loadNotificationSettings();

            // Add event listener for save button using the proper ID
            if (updateNotificationsButton) {
                updateNotificationsButton.addEventListener('click', saveNotificationSettings);
            }

            // Function to save notification settings
            function saveNotificationSettings() {
                // Get selected notification type
                let selectedNotificationType = '';
                notificationRadios.forEach(radio => {
                    if (radio.checked) {
                        selectedNotificationType = radio.id.replace('notification_', '');
                    }
                });

                // Get email notification preferences using proper IDs
                const emailNotifications = {
                    communication: emailCommunicationToggle.checked,
                    marketing: emailMarketingToggle.checked,
                    social: emailSocialToggle.checked
                };

                // Create settings object
                const settings = {
                    notificationType: selectedNotificationType,
                    emailNotifications: emailNotifications,
                    lastUpdated: new Date().toISOString()
                };

                // Show loading state
                updateNotificationsButton.disabled = true;
                updateNotificationsButton.innerHTML = 'Saving...';

                // Send data to backend
                fetch(window.API_ROUTES.SETTINGS.NOTIFICATIONS.SAVE, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(settings)
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Show success notification
                        UIkit.notification({
                            message: 'Notification settings saved successfully!',
                            status: 'success',
                            pos: 'bottom-right',
                            timeout: 3000
                        });
                    })
                    .catch(error => {
                        console.error('Error saving notification settings:', error);
                        // Show error notification
                        UIkit.notification({
                            message: 'Failed to save notification settings',
                            status: 'danger',
                            pos: 'bottom-right',
                            timeout: 3000
                        });
                    })
                    .finally(() => {
                        // Restore button state
                        updateNotificationsButton.disabled = false;
                        updateNotificationsButton.innerHTML = 'Update notifications';
                    });
            }

            // Function to load notification settings
            function loadNotificationSettings() {
                fetch(window.API_ROUTES.SETTINGS.NOTIFICATIONS.LOAD)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(settings => {
                        // Set notification type using proper IDs
                        if (settings.notificationType) {
                            const radioId = 'notification_' + settings.notificationType;
                            const radio = document.getElementById(radioId);
                            if (radio) {
                                radio.checked = true;
                            }
                        }

                        // Set email notification toggles using proper IDs
                        if (settings.emailNotifications) {
                            if (settings.emailNotifications.communication !== undefined) {
                                emailCommunicationToggle.checked = settings.emailNotifications.communication;
                            }
                            if (settings.emailNotifications.marketing !== undefined) {
                                emailMarketingToggle.checked = settings.emailNotifications.marketing;
                            }
                            if (settings.emailNotifications.social !== undefined) {
                                emailSocialToggle.checked = settings.emailNotifications.social;
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error loading notification settings:', error);
                        UIkit.notification({
                            message: 'Failed to load notification settings',
                            status: 'warning',
                            pos: 'bottom-right',
                            timeout: 3000
                        });
                    });
            }
        });
    </script>

</body>

</html>
<script>
        document.addEventListener('DOMContentLoaded', function () {
            const uploadButton = document.getElementById('upload-kb-btn'); // Select the first button in the flex container
            const fileInput = document.getElementById('knowledge-base-upload-input');

            fileInput.addEventListener('change', function () {
                const file = this.files[0];
                if (file) {
                    // Check file type (although accept=".txt" helps, double-check)
                    if (file.name.endsWith('.txt')) {
                        uploadKnowledgeBase(file);
                    } else {
                        alert('Please select a .txt file.');
                        // Clear the file input so the same file can be selected again if needed
                        this.value = '';
                    }
                }
            });

            function uploadKnowledgeBase(file) {
                const formData = new FormData();
                formData.append('knowledge_base_file', file);

                // Placeholder URL - replace with your actual backend endpoint
                const uploadUrl = '/api/settings/knowledge_base/upload'; // Example endpoint

                fetch(uploadUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Upload failed');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Upload successful:', data);
                    alert('Knowledge base uploaded successfully!');
                    // Optionally update the "Last edited" text on the other button
                    // document.querySelector('#uk-switcher-20 button.flex-1:last-child .text-xs').textContent = 'Last edited: Just now';
                })
                .catch(error => {
                    console.error('Upload error:', error);
                    alert('Failed to upload knowledge base.');
                });
            }

            // Delegated click listener for upload and edit buttons
            document.addEventListener('click', function (e) {
                const uploadButton = e.target.closest('#upload-kb-btn');
                const editButton = e.target.closest('#edit-kb-btn');

                if (uploadButton) {
                    const fileInput = document.getElementById('knowledge-base-upload-input');
                    if (fileInput) {
                        fileInput.click();
                    }
                } else if (editButton) {
                    const editModal = document.getElementById('knowledge-base-edit-modal');
                    if (editModal) {
                        UIkit.modal(editModal).show();
                    }
                }
            });
const editButton = document.getElementById('edit-kb-btn'); // Select the second button
        const editModal = document.getElementById('knowledge-base-edit-modal');
        const editorTextarea = document.getElementById('knowledge-base-editor');
        const saveEditButton = document.getElementById('save-knowledge-base-btn');

        const botConfigTab = document.getElementById('uk-switcher-tab-19');
        if (botConfigTab) {
            botConfigTab.addEventListener('click', function(e) {
                e.stopPropagation(); // Stop event propagation for the tab click
            });
        }

            UIkit.util.on(editModal, 'beforeshow', function () {
                // Fetch knowledge base text when modal opens
                const loadUrl = '/api/settings/knowledge_base/load_text'; // Placeholder endpoint
                fetch(loadUrl)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Failed to load knowledge base text');
                        }
                        return response.json();
                    })
                    .then(data => {
                        editorTextarea.value = data.text || ''; // Populate textarea
                        // Make sure the textarea has no visible scrollbar
                        editorTextarea.classList.add('no-scrollbar');
                    })
                    .catch(error => {
                        console.error('Error loading knowledge base text:', error);
                        alert('Failed to load knowledge base text.');
                    });
            });

            saveEditButton.addEventListener('click', function () {
                const updatedText = editorTextarea.value;
                const saveUrl = '/api/settings/knowledge_base/save_text'; // Placeholder endpoint

                fetch(saveUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ text: updatedText })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to save knowledge base text');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Knowledge base saved successfully:', data);
                    alert('Knowledge base saved successfully!');
                    UIkit.modal(editModal).hide(); // Hide the modal on success
                    // Optionally update the "Last edited" text on the edit button
                    // document.querySelector('#uk-switcher-20 button.flex-1:last-child .text-xs').textContent = 'Last edited: Just now';
                })
                .catch(error => {
                    console.error('Error saving knowledge base text:', error);
                    alert('Failed to save knowledge base text.');
                });
            });
        });
    </script>
<!-- Knowledge Base Edit Modal -->
<div id="knowledge-base-edit-modal" uk-modal>
    <div class="uk-modal-dialog" style="width: 60%; height: 80vh; max-height: 80vh;">
        <div style="padding: 24px 24px 24px 24px; height: 100%; display: flex; flex-direction: column;">
            <h2 class="uk-modal-title" style="margin-top: 0; margin-bottom: 24px;">Edit Knowledge Base</h2>
            <div style="flex-grow: 1; margin-bottom: 24px;">
                <textarea id="knowledge-base-editor" class="uk-textarea no-scrollbar" style="height: 100%; width: 100%; resize: none;"></textarea>
            </div>
            <div class="uk-text-right" style="margin-top: 0;">
                <button class="uk-button uk-button-default uk-modal-close mr-3" type="button">Cancel</button>
                <button class="uk-button uk-button-primary" type="button" id="save-knowledge-base-btn">Save</button>
            </div>
        </div>
    </div>
</div>