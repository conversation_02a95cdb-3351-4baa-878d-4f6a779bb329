import requests
import json

# Your Twilio account details (replace with your actual Account SID and Auth Token)
ACCOUNT_SID = '**********************************'
AUTH_TOKEN = '005d910f85546392a91f58a3878c437c'

# Twilio API endpoint for content creation
url = 'https://content.twilio.com/v1/Content'

# Payload data for the carousel with updated beverage menu items and descriptions
payload = {
    "friendly_name": "v1_cocktail_menu_carousel",
    "language": "en",
    "types": {
        "twilio/carousel": {
            "body": "Enjoy exceptional drinks and beverages with outstanding service!",
            "cards": [
                {
                    "title": "Beatnik spiritz",
                    "body": "strawberry & watermelon puree",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/drinks.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_beatnik_spiritz"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Go Back",
                            "id": "go_back"
                        }
                    ]
                },
                {
                    "title": "Floral fusion",
                    "body": "Bombay Sapphire, Cynar Vermouth, homemade orange syrup, lime juice...",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/drinks.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_floral_fusion"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Go Back",
                            "id": "go_back"
                        }
                    ]
                },
                {
                    "title": "Blake Vission",
                    "body": "Absolut vodka, Cointreau, Velvet Falernum, pineapple mixed berry puree...",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/drinks.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_blake_vission"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Go Back",
                            "id": "go_back"
                        }
                    ]
                },
                {
                    "title": "Duck pond",
                    "body": "Bombay Sapphire, Bacardi Blanca, peach liqueur, peach & lemon juice...",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/drinks.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_duck_pond"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Go Back",
                            "id": "go_back"
                        }
                    ]
                },
                {
                    "title": "Ciudad Dei SoL",
                    "body": "Espolon Blanco Tequila, creme de Cassis, violet, orange & lime juice...",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/drinks.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_ciudad_dei_sol"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Go Back",
                            "id": "go_back"
                        }
                    ]
                },
                {
                    "title": "Tiki punch",
                    "body": "Malibu Rum, banana liqueur, Fernet Branca, homemade Guinness reduction...",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/drinks.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_tiki_punch"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Go Back",
                            "id": "go_back"
                        }
                    ]
                },
                {
                    "title": "La passion",
                    "body": "Jack Daniel, Bailey's, passion fruit puree, double cream, sugar syrup...",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/drinks.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_la_passion"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Go Back",
                            "id": "go_back"
                        }
                    ]
                }
            ]
        }
    }
}

# Make the POST request to the Twilio API
response = requests.post(
    url,
    auth=(ACCOUNT_SID, AUTH_TOKEN),
    headers={'Content-Type': 'application/json'},
    data=json.dumps(payload)
)

# Check the response status
if response.status_code == 201:
    print("Carousel created successfully!")
    print(response.json())
else:
    print(f"Failed to create carousel: {response.status_code}")
    print(response.text)