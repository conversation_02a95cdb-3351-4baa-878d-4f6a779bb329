/* <PERSON><PERSON>r Styles */
.menubar {
    background-color: var(--menubar-bg, transparent);
    padding: 2px;
    display: inline-flex;
    align-items: center;
    border-radius: 6px;
    border: 1px solid var(--menubar-border, #e5e5e5);
    height: 34px;
    box-sizing: border-box;
    position: relative;
    font-family: 'Onest', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.menubar-indicator {
    position: absolute;
    top: 2px;
    left: 0;
    height: calc(100% - 4px);
    background: var(--menubar-indicator-bg, #f4f4f5);
    border-radius: 4px;
    transition: left 0.25s cubic-bezier(.4,1,.7,1), width 0.25s cubic-bezier(.4,1,.7,1);
    z-index: 1;
    pointer-events: none;
}

.menubar a {
    background-color: transparent;
    color: var(--menubar-link-color, #212529);
    border: none;
    padding: 0 12px;
    margin: 0;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    border-radius: 4px;
    transition: color 0.2s ease;
    font-family: inherit;
    position: relative;
    z-index: 2;
    text-decoration: none;
    white-space: nowrap;
    display: flex;
    align-items: center;
    height: 100%;
    box-sizing: border-box;
}

.menubar a:hover,
.menubar a:focus {
    color: var(--theme-selector-color, #212529);
}

.menubar a.active {
    color: var(--theme-selector-color, #212529);
}

.menubar a:active {
    opacity: 0.85;
}

/* Dark theme variables */
.pure-black .menubar {
    --menubar-bg: #09090b;
    --menubar-border: #27272a;
    --menubar-indicator-bg: #27272a;
    --menubar-link-color: #cccccc;
}

/* Add other theme overrides as needed */
.dark-gray .menubar,
.navy-blue .menubar,
.cool-blue .menubar,
.deep-burgundy .menubar,
.charcoal .menubar {
    --menubar-indicator-bg: #27272a;
    --menubar-link-color: #cccccc;
}
