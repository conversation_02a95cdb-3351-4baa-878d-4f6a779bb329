# Menubar Component Usage Guide

This document explains how to add the menubar component to different pages in your application.

## Overview

The menubar component provides a navigation bar with an animated indicator that highlights the active menu item. It's designed to be responsive and works with both light and dark themes.

## Prerequisites

Make sure the following files are included in your project:
- `static/styles/menubar.css` - Contains the menubar styles
- `static/js/menubar.js` - Contains the menubar functionality

These files should be included in your imports.html file or directly in the head section of your HTML pages.

## Basic Usage

To add the menubar to a page, follow these steps:

### 1. Include the Required Files

If you're using the `imports.html` include file, make sure it contains references to the menubar CSS and JS files:

```html
<link rel="stylesheet" href="../static/styles/menubar.css">
<script src="../static/js/menubar.js" defer></script>
```

### 2. Add the Menubar HTML Structure

Add the following HTML structure where you want the menubar to appear:

```html
<div class="menubar" role="menubar">
    <div class="menubar-indicator"></div>
    <a href="/page1" role="menuitem">Menu Item 1</a>
    <a href="/page2" role="menuitem">Menu Item 2</a>
    <a href="/page3" role="menuitem">Menu Item 3</a>
    <a href="/page4" role="menuitem">Menu Item 4</a>
</div>
```

### 3. Mark the Active Menu Item

Add the `active` class to the menu item that corresponds to the current page:

```html
<div class="menubar" role="menubar">
    <div class="menubar-indicator"></div>
    <a href="/page1" role="menuitem">Menu Item 1</a>
    <a href="/page2" role="menuitem" class="active">Menu Item 2</a>
    <a href="/page3" role="menuitem">Menu Item 3</a>
    <a href="/page4" role="menuitem">Menu Item 4</a>
</div>
```

## Example Implementation

Here's an example of how the menubar is implemented in the header section of a page:

```html
<header class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
    <div style="margin-left: 8px;" class="flex items-center gap-2 px-4 pl-0">
        <button id="toggle-btn" class="opacity-100 transition-opacity duration-300 focus:outline-none"
            style="background-color: transparent !important;">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-panel-left">
                <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                <path d="M9 3v18"></path>
            </svg>
        </button>
        <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-3 h-4"
            style="background-color: var(--border-color);"></div>
        <!-- Menubar -->
        <div class="menubar" role="menubar">
            <div class="menubar-indicator"></div>
            <a href="/sales" role="menuitem">Sales Report</a>
            <a href="/pmsanalytics" role="menuitem" class="active">PMS Analytics</a>
            <a href="/googleana" role="menuitem">Google Analytics</a>
            <a href="/status" role="menuitem">Status Report</a>
        </div>
    </div>
    {% include 'topright.html' %}
</header>
```

## Customization

### Styling

The menubar component uses CSS variables for theming. You can customize the appearance by overriding these variables in your theme CSS:

```css
:root {
    --menubar-bg: transparent;
    --menubar-border: #e5e5e5;
    --menubar-indicator-bg: #f4f4f5;
    --menubar-link-color: #212529;
}

/* Dark theme example */
.dark-theme {
    --menubar-bg: #09090b;
    --menubar-border: #27272a;
    --menubar-indicator-bg: #27272a;
    --menubar-link-color: #cccccc;
}
```

### Adding Icons

You can add icons to menu items by including SVG or icon elements inside the anchor tags:

```html
<a href="/sales" role="menuitem">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
        class="mr-2">
        <path d="M3 3v18h18" />
        <path d="m19 9-5 5-4-4-3 3" />
    </svg>
    Sales Report
</a>
```

## Troubleshooting

1. **Indicator not moving**: Make sure the menubar JavaScript is loaded and there are no console errors.

2. **Indicator position incorrect**: The indicator might need time to calculate positions correctly, especially if fonts are still loading. Try adding a small delay with `setTimeout`.

3. **Styles not applying**: Check that the menubar CSS file is correctly included and that there are no CSS conflicts.

4. **Active item not highlighted**: Ensure you've added the `active` class to the correct menu item.

## Browser Compatibility

The menubar component works in all modern browsers that support CSS variables and basic JavaScript.
