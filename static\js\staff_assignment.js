// Staff members data
const staffMembers = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON> Me<PERSON>",
    email: "<EMAIL>",
    avatar: "https://api.dicebear.com/8.x/lorelei/svg?seed=<PERSON>",
    permission: null
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "https://api.dicebear.com/8.x/lorelei/svg?seed=<PERSON>",
    permission: null
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://api.dicebear.com/8.x/lorelei/svg?seed=<PERSON>",
    permission: null
  },
  {
    id: 4,
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "https://api.dicebear.com/8.x/lorelei/svg?seed=<PERSON><PERSON>",
    permission: null
  }
];

// Make functions available globally so they can be overridden
window.renderStaffMembers = renderStaffMembers;
window.openStaffModal = openStaffModal;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  // Check if we're on the livechat page with hardcoded staff members
  const staffContainer = document.getElementById('staff-container');
  const isLivechatWithHardcodedStaff = staffContainer && staffContainer.querySelectorAll('.flex.items-center.space-x-4').length > 0;

  if (!isLivechatWithHardcodedStaff) {
    // Only initialize if we're not on the livechat page with hardcoded staff
    initStaffModal();

    // Find the "Assign Staff" link in dropdowns
    document.querySelectorAll('a.uk-drop-close').forEach(link => {
      if (link.textContent.trim().includes('Assign Staff')) {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          openStaffModal();
        });
      }
    });
  } else {
    console.log("Detected livechat page with hardcoded staff members - skipping dynamic rendering");
  }
});

function initStaffModal() {
  const staffModal = document.getElementById('staff-assignment-modal');
  const closeStaffModalBtn = document.getElementById('close-staff-modal');
  const cancelStaffBtn = document.getElementById('cancel-staff-assignment');
  const confirmStaffBtn = document.getElementById('confirm-staff-assignment');

  // If modal doesn't exist on this page, return early
  if (!staffModal) return;

  // Set up close buttons
  if (closeStaffModalBtn) {
    closeStaffModalBtn.addEventListener('click', closeStaffModal);
  }

  if (cancelStaffBtn) {
    cancelStaffBtn.addEventListener('click', closeStaffModal);
  }

  if (confirmStaffBtn) {
    confirmStaffBtn.addEventListener('click', function() {
      console.log('Staff Assignments:', staffMembers);
      alert('Staff assignments saved!');
      closeStaffModal();
    });
  }

  // Close if clicking outside the modal content
  staffModal.addEventListener('click', function(e) {
    if (e.target === staffModal || e.target === staffModal.querySelector('.absolute')) {
      closeStaffModal();
    }
  });
}

function openStaffModal() {
  const staffModal = document.getElementById('staff-assignment-modal');
  if (!staffModal) return;

  // Render staff members
  renderStaffMembers();

  // Setup button functionality
  setupDropdowns(); // We kept the function name for compatibility

  // Show modal
  staffModal.classList.remove('hidden');
  document.body.classList.add('overflow-hidden'); // Prevent background scrolling
}

function closeStaffModal() {
  const staffModal = document.getElementById('staff-assignment-modal');
  if (!staffModal) return;

  staffModal.classList.add('hidden');
  document.body.classList.remove('overflow-hidden'); // Allow scrolling again
}

function renderStaffMembers() {
  const container = document.getElementById('staff-container');
  if (!container) return;

  // Keep the heading
  const heading = container.querySelector('h4');

  // Clear existing staff members, but keep the heading
  container.innerHTML = '';
  container.appendChild(heading);

  // Add each staff member
  staffMembers.forEach(staff => {
    const staffElement = createStaffElement(staff);
    container.appendChild(staffElement);
  });
}

function createStaffElement(staff) {
  const element = document.createElement('div');
  element.className = 'flex items-center space-x-4';
  element.innerHTML = `
      <span class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
          <img class="aspect-square h-full w-full" src="${staff.avatar}">
      </span>
      <div class="flex-1">
          <p class="text-sm font-medium leading-none">${staff.name}</p>
          <p class="text-sm ">${staff.email}</p>
      </div>
      <div class="h-9 w-[200px] relative">
          <button id="staff-${staff.id}-assign-button"
              class="theme-responsive-button card flex items-center justify-center w-full px-3 py-2 text-sm border rounded-md cursor-pointer universal-hover bg-black text-white">
              Assign Chat
          </button>
      </div>
  `;

  return element;
}

function setupDropdowns() {
  // Attach handlers to each assign button
  document.querySelectorAll('[id^="staff-"][id$="-assign-button"]').forEach(button => {
    button.addEventListener('click', e => {
      e.preventDefault();

      // Extract the staff ID from the button ID
      const staffId = button.id.replace('staff-', '').replace('-assign-button', '');
      const staffMember = staffMembers.find(s => s.id === parseInt(staffId));

      if (staffMember) {
        console.log(`Assigning chat to ${staffMember.name}`);
        // Here you would add the logic to assign the chat to this staff member

        // Visual feedback that the button was clicked
        const originalText = button.textContent;
        button.textContent = "Assigned!";
        button.classList.add("bg-green-600");

        // Reset after a short delay
        setTimeout(() => {
          button.textContent = originalText;
          button.classList.remove("bg-green-600");
        }, 1500);
      }
    });
  });
}