import os
from twilio.rest import Client
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Twilio credentials
account_sid = os.getenv('TWILIO_ACCOUNT_SID')
auth_token = os.getenv('TWILIO_AUTH_TOKEN')
twilio_whatsapp_number = os.getenv('TWILIO_WHATSAPP_NUMBER')

# Ensure credentials and phone number are loaded
if not all([account_sid, auth_token, twilio_whatsapp_number]):
    print("Error: Missing Twilio credentials or WhatsApp number in environment variables.")
    exit()

# Initialize Twilio client
client = Client(account_sid, auth_token)

# Define the recipient phone number (replace with the actual number you want to send to)
# For testing, you might use your own WhatsApp number in the format 'whatsapp:+**********'
# Or you can get this from another environment variable if needed.
# For now, I will use the TWILIO_PHONE_NUMBER from the .env file as a placeholder recipient.
# You should replace 'RECIPIENT_WHATSAPP_NUMBER' with the actual number.
recipient_whatsapp_number = os.getenv('TWILIO_PHONE_NUMBER') # Placeholder, replace with actual recipient

if not recipient_whatsapp_number:
     print("Error: RECIPIENT_WHATSAPP_NUMBER is not set. Please update the script or .env file.")
     exit()

# Define template SIDs for the requested templates
FOOD_TEMPLATE_SID = "HX74a4f19dc28d264a2f110d1259544995"
BEVERAGE_TEMPLATE_SID = "HXa95d31210871c5d9f80465bd7177d3bc"
SPA_TEMPLATE_SID = "HX0ea1611c7a662becc730c2b9dc788554" # Using the main booking template
MASSAGE_TEMPLATE_SID = "HX37984b929b741f01a1d97c120da1cefb" # Using the type selection template
ROOM_BOOKING_TEMPLATE_SID = "HX90086324ff4757c28a22446152eab6e6" # Using the initial selection template
EXPERIENCES_TEMPLATE_SID = "HX50460e90855bb0fd7417eccb26e4e63a"

# List of templates to send
templates_to_send = {
    "Food": FOOD_TEMPLATE_SID,
    "Beverage": BEVERAGE_TEMPLATE_SID,
    "Spa": SPA_TEMPLATE_SID,
    "Massage": MASSAGE_TEMPLATE_SID,
    "Room Booking": ROOM_BOOKING_TEMPLATE_SID,
    "Experiences": EXPERIENCES_TEMPLATE_SID
}

def send_template_message(to_number, from_number, content_sid, template_name):
    """Sends a template message using Twilio."""
    try:
        message = client.messages.create(
            from_=from_number,
            to=to_number,
            content_sid=content_sid
        )
        print(f"Sent '{template_name}' template message to {to_number}. SID: {message.sid}")
    except Exception as e:
        print(f"Error sending '{template_name}' template message: {e}")

# Send each template message
if __name__ == "__main__":
    print(f"Attempting to send template messages to {recipient_whatsapp_number} from {twilio_whatsapp_number}...")
    for name, sid in templates_to_send.items():
        send_template_message(recipient_whatsapp_number, twilio_whatsapp_number, sid, name)