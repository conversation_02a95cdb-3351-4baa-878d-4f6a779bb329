<style>
    /* styles.css */
    .no-hover-bg:hover {
      background-color: transparent !important;
    }
  </style>
  <div class="relative">
    <div class="uk-navbar-item flex items-center ">
 
      <!-- Divider -->
      <div class="border-r border-gray-300 h-8"></div>
  
      <!-- Dark Mode Toggle -->
      <button id="darkModeToggler" class="flex items-center justify-center p-2 rounded-md" style="background-color: transparent;" aria-label="Toggle Dark Mode">
        <i id="sunIcon" style="font-size: 25px" class="ph ph-sun hidden"></i>
        <i id="moonIcon" style="font-size: 25px;" class="ph ph-moon-stars"></i>
      </button>
      
      <!-- Profile Icon with Dropdown -->
      <a class="inline-flex h-8 w-8 items-center justify-center rounded-full  ring-ring border-2  focus:outline-none " href="#" role="button" aria-haspopup="true" aria-label="User Profile"> 
        <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-full"> 
          <img class="aspect-square h-full w-full" alt="User Avatar" src="https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult"> 
        </span> 
      </a> 
      <div class="uk-drop uk-dropdown" uk-dropdown="mode: click; pos: bottom-right">
        <ul class="uk-dropdown-nav uk-nav">
          <li class="px-2 py-1.5 text-sm">
            <div class="flex flex-col space-y-1">
              <p class="text-sm font-medium leading-none">Guest Genius</p>
              <p class="text-xs leading-none text-muted-foreground">
                <EMAIL>
              </p>
            </div>
          </li>
          <li class="uk-nav-divider"></li>
          <li> 
            <a class="justify-between language-option" href="#demo" role="button" data-lang="en">
              Profile 
            </a> 
          </li>
          <li> 
            <a class="justify-between language-option" href="/issue" role="button" data-lang="en">
              Contact Support 
            </a> 
          </li>
          <li class="uk-nav-divider"></li>
          <li> 
            <a class="uk-drop-close justify-between language-option" href="/logout" role="button" data-lang="en">
              Logout
            </a> 
          </li>
        </ul>
      </div>
    </div>
  </div>