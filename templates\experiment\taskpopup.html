<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Popup Demo</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --animation-duration: 0.9s;
            --animation-easing: cubic-bezier(0.16, 1, 0.3, 1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Inter', sans-serif;
        }

        body {
            min-height: 100vh;
            background: #f7f7f7;
            padding: 20px;
        }

        .popup-container {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0);
            backdrop-filter: blur(0px);
            justify-content: flex-end;
            flex-direction: column;
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
            transition: opacity var(--animation-duration) var(--animation-easing),
                        backdrop-filter var(--animation-duration) var(--animation-easing),
                        visibility 0s linear var(--animation-duration),
                        background-color var(--animation-duration) var(--animation-easing);
            transform-origin: bottom;
            display: flex;
            z-index: 1000;
        }

        .popup-container.show {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            transition: opacity var(--animation-duration) var(--animation-easing),
                        backdrop-filter var(--animation-duration) var(--animation-easing),
                        visibility 0s linear 0s,
                        background-color var(--animation-duration) var(--animation-easing);
        }

        .popup-content {
            background-color: white;
            padding: 24px;
            border-radius: 24px 24px 0 0;
            position: relative;
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            min-height: 80vh;
            transform: translateY(100%) translateZ(0);
            transition: transform var(--animation-duration) var(--animation-easing);
            box-shadow: 0 -8px 16px rgba(0, 0, 0, 0.1);
            will-change: transform;
        }

        .popup-container.show .popup-content {
            transform: translateY(0) translateZ(0);
        }

        .close-btn {
            position: absolute;
            top: 24px;
            right: 24px;
            cursor: pointer;
            font-size: 24px;
            color: #666;
            transition: color 0.2s;
        }

        .close-btn:hover {
            color: #000;
        }

        .trigger-btn {
            padding: 12px 24px;
            font-size: 14px;
            cursor: pointer;
            background: #000;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            transition: background 0.2s;
        }

        .trigger-btn:hover {
            background: #333;
        }

        h2 {
            font-weight: 600;
            margin-bottom: 16px;
            color: #111;
        }

        p {
            color: #555;
            line-height: 1.6;
        }
        
        body.no-scroll {
            overflow: hidden;
        }
    </style>
</head>
<body>
    <button class="trigger-btn" onclick="togglePopup()">Open Popup</button>

    <div class="popup-container" id="popup">
        <div class="popup-content">
            <span class="close-btn" onclick="togglePopup()">&times;</span>
            <h2>Popup Title</h2>
            <p>This is your popup content. You can add any HTML elements here.</p>
        </div>
    </div>

    <script>
        function togglePopup() {
            const popup = document.getElementById('popup');
            const body = document.body;
            
            popup.classList.toggle('show');
            body.classList.toggle('no-scroll');
            
            // Add escape key functionality
            if (popup.classList.contains('show')) {
                document.addEventListener('keydown', closeOnEscape);
            } else {
                document.removeEventListener('keydown', closeOnEscape);
            }
        }
        
        function closeOnEscape(e) {
            if (e.key === 'Escape') {
                togglePopup();
            }
        }
        
        // Function to adjust animation speed
        function setAnimationSpeed(duration) {
            document.documentElement.style.setProperty('--animation-duration', duration + 's');
        }
        
        // Optional: You can call this to change animation speed dynamically
        // setAnimationSpeed(1.2); // Example: even slower animation
    </script>
</body>
</html>
