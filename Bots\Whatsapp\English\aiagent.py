import os
import sys
from openai import OpenAI
from typing import List, Dict, Any, Optional
import time
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Check if the API key is available
if not os.getenv("OPENAI_API_KEY"):
    print("Error: OPENAI_API_KEY not found in .env file")
    sys.exit(1)

# Define system prompt with optimized instructions for better human responses
SYSTEM_PROMPT = """You are <PERSON> (your name), a professional hotel assistant. Follow these guidelines:

1. Be warm, professional, and personable in your responses
2. Provide accurate information based only on the hotel data provided
3. Keep responses concise and direct (2-3 sentences when possible)
4. Maintain complete URLs without shortening them
5. Format responses with clear spacing between different topics
6. Avoid using emojis
7. Proactively offer relevant additional information when appropriate
8. Use natural conversational language that sounds human
9. Vary your greeting and closing phrases to sound more natural
10. Use contractions (I'm, we're, you'll) to sound more conversational
11. Occasionally ask follow-up questions to engage the guest
12. When providing location information, mention approximate walking time

IMPORTANT RESTRICTIONS:
13. NEVER offer to contact hotel staff on behalf of the guest unless explicitly requested
14. NEVER claim you have notified staff about any issue
15. NEVER make decisions or take actions that would require staff involvement
16. NEVER schedule services, make bookings, or confirm reservations yourself
17. Instead, provide information about how the guest can contact staff themselves
18. For service requests, simply provide information about the service without offering to arrange it

Your goal is to provide helpful, accurate information that sounds like it comes from a knowledgeable hotel concierge who genuinely cares about the guest's experience, while respecting the boundaries of your role as an information provider."""

# Define data file path
# Look for data.txt in the data folder first, then in the current directory
DATA_FILE_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), "data", "data.txt")
# Fallback to the current directory if not found in data folder
FALLBACK_DATA_PATH = os.path.join(os.path.dirname(__file__), "data.txt")

# Initialize OpenAI client
client = OpenAI()

# Global variable to store vector store ID
vector_store_id = None

# Flag to indicate if initialization has been attempted
_initialization_attempted = False

# Global variable to store hotel data
hotel_data = ""

# Simple conversation history storage (limited to last exchange)
conversation_history = {}

def setup_vector_store(data_file_path: str) -> str:
    """
    Create a vector store and upload data file

    This is a simplified version that just reads the data file
    and returns a dummy ID since vector_stores is not available
    in the current OpenAI version.

    Args:
        data_file_path: Path to the data file to upload

    Returns:
        A dummy vector store ID
    """
    try:
        print(f"Reading data file: {data_file_path}")
        # Just read the file to make sure it exists
        with open(data_file_path, "r", encoding="utf-8") as file:
            global hotel_data
            hotel_data = file.read()

        print(f"Data file loaded successfully")
        return "dummy_vector_store_id"
    except Exception as e:
        print(f"Error reading data file: {e}")
        sys.exit(1)

def search_vector_store(vector_store_id: str, query: str) -> Dict[str, Any]:
    """
    Search the data with a user query

    This is a simplified version that just returns the loaded data
    since vector_stores is not available in the current OpenAI version.

    Args:
        vector_store_id: ID of the vector store to search (not used)
        query: User's query (not used for searching, but passed to response generation)

    Returns:
        A dummy results object with the hotel data
    """
    try:
        # Create a simple dummy results object with the hotel data
        class DummyContent:
            def __init__(self, text):
                self.text = text

        class DummyResult:
            def __init__(self, filename, content):
                self.filename = filename
                self.content = [content]

        class DummyResults:
            def __init__(self, data):
                self.data = data

        # Create a dummy content object with the hotel data
        content = DummyContent(hotel_data)

        # Create a dummy result object
        result = DummyResult("data.txt", content)

        # Create a dummy results object with the result
        results = DummyResults([result])

        return results
    except Exception as e:
        print(f"Error searching data: {e}")
        return None

def format_search_results(results) -> str:
    """
    Format search results for the model with improved structure

    Args:
        results: Results from vector store search

    Returns:
        Formatted string of results with better organization
    """
    if not results or not results.data:
        return "No relevant information found."

    formatted_text = "HOTEL INFORMATION:\n\n"

    # Group content by topics for better organization
    for i, result in enumerate(results.data):
        formatted_text += f"SECTION {i+1}:\n"
        for content_part in result.content:
            if hasattr(content_part, 'text'):
                # Clean up the text and format it better
                content_text = content_part.text.strip()
                # Highlight Q&A format more clearly
                content_text = content_text.replace("Q. ", "QUESTION: ")
                content_text = content_text.replace("A. ", "ANSWER: ")
                formatted_text += f"{content_text}\n\n"

    return formatted_text

def generate_response(query: str, search_results, conversation_context: Optional[List] = None) -> str:
    """
    Generate a response based on search results with improved context handling

    Args:
        query: User's original query
        search_results: Results from the vector store search
        conversation_context: Previous conversation context if available

    Returns:
        Generated response
    """
    formatted_results = format_search_results(search_results)

    # Analyze query to determine the type of question
    query_lower = query.lower()

    # Check for different types of queries
    is_greeting = any(word in query_lower for word in ['hello', 'hi', 'hey', 'greetings', 'good morning', 'good afternoon', 'good evening'])
    is_location = any(word in query_lower for word in ['location', 'where', 'how far', 'distance', 'nearby', 'close', 'walking'])
    is_time = any(word in query_lower for word in ['time', 'when', 'hours', 'schedule', 'open', 'close'])
    is_amenity = any(word in query_lower for word in ['amenity', 'pool', 'gym', 'spa', 'restaurant', 'wifi', 'breakfast', 'service'])
    is_booking = any(word in query_lower for word in ['book', 'reserve', 'reservation', 'schedule', 'appointment'])
    is_price = any(word in query_lower for word in ['price', 'cost', 'fee', 'charge', 'rate', 'euro', 'dollar', 'payment'])
    is_thanks = any(word in query_lower for word in ['thank', 'thanks', 'appreciate', 'grateful'])

    # Check for service requests that might lead to inappropriate staff contact
    is_service_request = any(word in query_lower for word in ['can you', 'could you', 'please', 'help me', 'assist', 'need', 'want', 'request'])
    is_complaint = any(word in query_lower for word in ['broken', 'not working', 'issue', 'problem', 'complaint', 'wrong', 'bad', 'terrible', 'horrible', 'fix'])
    is_staff_related = any(word in query_lower for word in ['staff', 'manager', 'reception', 'front desk', 'housekeeping', 'cleaning', 'service', 'employee'])

    # Build messages with conversation history if available
    messages = [
        {
            "role": "system",
            "content": SYSTEM_PROMPT
        }
    ]

    # Add conversation context if available
    if conversation_context and len(conversation_context) > 0:
        messages.extend(conversation_context)

    # Add current query with context and additional instructions based on query type
    prompt_content = f"Based on this hotel information:\n\n{formatted_results}\n\nGuest question: {query}\n\n"

    # Add specific instructions based on query type
    if is_greeting:
        prompt_content += "This appears to be a greeting. Respond warmly but briefly, and ask how you can assist them today."
    elif is_thanks:
        prompt_content += "This is a thank you message. Respond graciously and ask if there's anything else you can help with."
    elif is_location:
        prompt_content += "This is a location question. Include walking time estimates and specific directions if available in your response."
    elif is_time:
        prompt_content += "This is a timing question. Be precise about times and schedules in your response."
    elif is_amenity:
        prompt_content += "This is a question about hotel amenities. Provide specific details about availability, location, and hours."
    elif is_booking:
        prompt_content += "This is a booking-related question. Provide clear information about the booking process and available options. DO NOT offer to make the booking yourself or claim you've contacted staff."
    elif is_price:
        prompt_content += "This is a pricing question. Be specific about costs and any included services or amenities."
    elif is_service_request:
        prompt_content += "This appears to be a service request. Provide information about the service but DO NOT offer to arrange it yourself. Instead, explain how the guest can contact staff directly."
    elif is_complaint:
        prompt_content += "This appears to be a complaint or issue. Express empathy but DO NOT claim you will notify staff. Instead, provide information on how the guest can contact the appropriate staff member themselves."
    elif is_staff_related:
        prompt_content += "This is a staff-related question. Provide information about staff roles and how to contact them, but DO NOT offer to relay messages or make arrangements with staff on the guest's behalf."

    messages.append({
        "role": "user",
        "content": prompt_content
    })

    try:
        completion = client.chat.completions.create(
            model="gpt-4o-2024-11-20",
            messages=messages,
            temperature=0.6,  # Slightly higher temperature for more natural variation
            max_tokens=2000,  # Limit token count to enforce brevity
            presence_penalty=0.2,  # Increased penalty to avoid repetitive responses
            frequency_penalty=0.2,  # Increased penalty to encourage varied language
            top_p=0.9  # Add top_p to focus on more likely tokens while allowing some creativity
        )

        response = completion.choices[0].message.content

        # Filter out problematic phrases that might suggest the AI is taking action
        problematic_phrases = [
            "I'll notify", "I will notify", "I've notified", "I have notified",
            "I'll contact", "I will contact", "I've contacted", "I have contacted",
            "I'll arrange", "I will arrange", "I've arranged", "I have arranged",
            "I'll book", "I will book", "I've booked", "I have booked",
            "I'll schedule", "I will schedule", "I've scheduled", "I have scheduled",
            "I'll reserve", "I will reserve", "I've reserved", "I have reserved",
            "I'll tell the staff", "I will tell the staff",
            "I'll let them know", "I will let them know",
            "I'll make sure", "I will make sure"
        ]

        # Check if any problematic phrases are in the response
        if any(phrase.lower() in response.lower() for phrase in problematic_phrases):
            # Replace the response with a safer alternative
            response = "I can provide information about that, but I'm not able to contact staff directly. You can reach out to our staff by calling the front desk or using the 'Contact Staff' option in the menu. Would you like me to provide more details about this service?"

        return response
    except Exception as e:
        print(f"Error generating response: {e}")
        return "I apologize, but I'm unable to access that information at the moment. Is there something else I can help you with?"

def initialize_vector_store():
    """
    Initialize the vector store if it hasn't been initialized yet

    Returns:
        True if initialization was successful, False otherwise
    """
    global vector_store_id, _initialization_attempted, hotel_data

    # Only attempt initialization once
    if _initialization_attempted:
        return vector_store_id is not None

    _initialization_attempted = True

    # First try the data folder path
    if os.path.exists(DATA_FILE_PATH):
        print(f"Found data.txt in data folder, using: {DATA_FILE_PATH}")
        try:
            vector_store_id = setup_vector_store(DATA_FILE_PATH)
            return True
        except Exception as e:
            print(f"Error initializing vector store from data folder: {e}")
    else:
        print(f"Error: File '{DATA_FILE_PATH}' not found in data folder.")

    # Try the fallback path in the current directory
    if os.path.exists(FALLBACK_DATA_PATH):
        print(f"Found data.txt in current directory, using: {FALLBACK_DATA_PATH}")
        try:
            vector_store_id = setup_vector_store(FALLBACK_DATA_PATH)
            return True
        except Exception as e:
            print(f"Error initializing vector store from current directory: {e}")
    else:
        print(f"Error: File '{FALLBACK_DATA_PATH}' not found in current directory.")

    # If we can't find the file in either location, create a minimal dummy data set
    print("Creating minimal dummy data set for testing.")
    hotel_data = """
    You are a friendly and helpful hotel concierge. Respond in a warm, conversational tone.

    Q. What time is check-in?
    A. Our Check-in policy begins at 15.00

    Q. What time is Check-out?
    A. Our Check-out policy is at 11.00
    """
    vector_store_id = "dummy_vector_store_id"
    return True


def generate_reply(query: str, user_id: str = "default_user") -> str:
    """
    Generate a reply to the user's query using the knowledge base
    with conversation history support

    Args:
        query: User's query
        user_id: Identifier for the user to maintain conversation history

    Returns:
        Generated response
    """
    global vector_store_id, conversation_history

    # Initialize vector store if not already loaded
    if vector_store_id is None:
        if not initialize_vector_store():
            return "I apologize, but our hotel information system is currently unavailable. Please try again in a few moments or contact our front desk for immediate assistance."

    # Get conversation history for this user
    user_history = conversation_history.get(user_id, [])

    # Search the vector store
    results = search_vector_store(vector_store_id, query)

    if results:
        # Generate response with conversation context
        response = generate_response(query, results, user_history)

        # Update conversation history (keep only last exchange for simplicity)
        conversation_history[user_id] = [
            {"role": "user", "content": query},
            {"role": "assistant", "content": response}
        ]

        return response
    else:
        fallback_response = "I don't have specific information on that topic in our hotel database. Is there something else about our hotel services or amenities I can help you with?"

        # Update conversation history even for fallback
        conversation_history[user_id] = [
            {"role": "user", "content": query},
            {"role": "assistant", "content": fallback_response}
        ]

        return fallback_response

# Vector store initialization will be called explicitly from app.py

if __name__ == "__main__":
    print("Guest Genius Ready")
    print("------------------")

    # Check if data.txt exists in either location
    if not os.path.exists(DATA_FILE_PATH) and not os.path.exists(FALLBACK_DATA_PATH):
        print(f"Error: File 'data.txt' not found in data folder or current directory.")
        print(f"Looked in: {DATA_FILE_PATH}")
        print(f"Looked in: {FALLBACK_DATA_PATH}")
        sys.exit(1)

    if vector_store_id is None:
        print("Initializing...")
        initialize_vector_store()

    print("\nReady. Type 'exit' to quit.")

    while True:
        query = input("\n> ").strip()

        if query.lower() in ['exit', 'quit']:
            print("Goodbye.")
            break

        if not query:
            continue

        response = generate_reply(query)
        print(response)
