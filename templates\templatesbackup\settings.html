<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="utf-8">
      <link rel="icon" type="image/svg+xml" href="/favicon.svg">
      <meta name="viewport" content="width=device-width">
      <meta name="generator" content="Astro v4.13.1">
      <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
      <meta http-equiv="Pragma" content="no-cache">
      <meta http-equiv="Expires" content="0">
      <script src="../static/js/themes.js"></script>
      <link rel="stylesheet" href="../static/styles/custom.css">
      <script src="../static/js/languagetranslator.js"></script>
      <script src="../static/js/multiplefonts.js" defer></script>
      <link rel="stylesheet" href="../static/styles/multiplefonts.css">
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
      <link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/yellow.min.css"/>
      <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit.min.js"></script>
      <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit-icons.min.js"></script>  <script src="https://cdn.tailwindcss.com"></script>
      <title>Guest Genius - Settings</title>
      <link rel="preload" href="/fonts/geist-font/fonts/GeistVariableVF.woff2" as="font" type="font/woff2" crossorigin>
      <link rel="preload" href="/fonts/geist-font/fonts/GeistMonoVariableVF.woff2" as="font" type="font/woff2" crossorigin>
      <link rel="stylesheet" href="/fonts/geist-font/style.css">
      <script src="/js/uikit@3.21.6/uikit.min.js"></script><script src="/js/uikit@3.21.6/icons.min.js"></script><script src="/js/htmx@2.0.0/htmx.min.js"></script><script type="module" src="/js/franken-wc@0.0.6/wc.iife.js"></script>
      <link rel="stylesheet" href="/_astro/master.CZ5-T1HD.css">
      <link rel="preload" href="/fonts/geist-font/fonts/GeistVariableVF.woff2" as="font" type="font/woff2" crossorigin>
      <link rel="stylesheet" href="../static/styles/urlselector.css">
      <script src="../static/js/urlselector.js"></script>
      <script src="../static/js/botconfig.js"></script>
      <link rel="stylesheet" href="../static/styles/botconfig.css">
      <link rel="stylesheet" href="../static/styles/scrollbar.css">
      <!-- Google Fonts for other fonts -->
       <script src="../static/js/configurations.js"></script>
      <link href="https://fonts.googleapis.com/css2?family=Inter&family=Lato&family=Open+Sans&family=Roboto&display=swap" rel="stylesheet">
   </head>
   <body class="hidden bg-background font-geist-sans text-foreground antialiased md:block">
      <div class="p-6 lg:p-10">
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <a href="/" class="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 text-sm font-medium rounded-md">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Dashboard
            </a>
            <div class="text-right">
              <h2 class="text-2xl font-bold tracking-tight">Settings</h2>
              <p class="">Manage your account settings and set e-mail preferences.</p>
            </div>
          </div>
        </div>
         <div class="my-6 border-t border-border"></div>
         <div class="flex gap-x-12">
            <aside class="w-1/5">
               <ul class="uk-nav uk-nav-secondary" uk-switcher="connect: #component-nav; animation: uk-animation-fade">
                  <li class="uk-active"> <a href="#">Profile</a> </li>
                  <li> <a href="#">Account</a> </li>
                  <li> <a href="#">Appearance</a> </li>
                  
                  <li> <a href="#">Contact</a> </li>
               </ul>
            </aside>
            <div class="flex-1">
               <ul id="component-nav" class="uk-switcher max-w-4xl">
                <li class="uk-active space-y-6">
                  <div>
                    <h3 class="text-lg font-medium">Profile</h3>
                    <p class="text-sm ">
                      This is how others will see you on the site.
                    </p>
                  </div>
                  <div class="border-t border-border"></div>
                  <div class="space-y-2">
                    <label class="uk-form-label" for="username">Bot Name</label> 
                    <div class="uk-position-relative">
                      <input class="uk-input editable-input greyed-out-input" id="username" type="text" value="TravelBot 3000" readonly>
                      <span class="uk-position-absolute uk-position-center-right uk-padding-small" style="cursor: pointer;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path></svg>
                      </span>
                    </div>
                  </div>
                  <div class="space-y-2">
                    <label class="uk-form-label" for="email">Email</label> 
                    <div class="h-9">
                      <input type="email" name="email" id="email" value="<EMAIL>" readonly class="uk-input greyed-out-input" />
                    </div>
                    <div class="uk-form-help ">
                      This is your verified email address.
                    </div>
                  </div>
                  <div class="space-y-2">
                    <label class="uk-form-label" for="bio">Enter the Hotel's Tagline</label> 
                    <div class="uk-position-relative">
                      <textarea class="uk-textarea editable-input greyed-out-input" style="max-height: 220px;" id="bio" readonly>Your Perfect Stay, Every Day!</textarea>
                      <span class="uk-position-absolute uk-position-bottom-right uk-padding-small" style="cursor: pointer;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path></svg>
                      </span>
                    </div>
                  </div>
                  <div class="space-y-4">
                    <span class="uk-form-label">Hotel Links</span>
                    <div class="uk-form-help ">Add links to your hotel's online presence and services.</div>
                    
                    <!-- Instagram -->
                    <div class="uk-flex uk-flex-middle gap-2">
                        <div class="uk-width-auto uk-flex uk-flex-middle link-selector-btn" style="width: 200px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line></svg>
                            <span class="uk-margin-small-left">Instagram</span>
                        </div>
                        <input class="uk-input uk-width-expand" type="text" placeholder="Enter Instagram URL" style="height: 40px;">
                    </div>
                
                    <!-- Hotel Menu -->
                    <div class="uk-flex uk-flex-middle gap-2">
                        <div class="uk-width-auto uk-flex uk-flex-middle link-selector-btn" style="width: 200px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 3h18v18H3zM12 3v18"></path><path d="M3 12h18"></path></svg>
                            <span class="uk-margin-small-left">Hotel Menu</span>
                        </div>
                        <input class="uk-input uk-width-expand" type="text" placeholder="Enter Hotel Menu URL" style="height: 40px;">
                    </div>
                
                    <!-- Hotel Website -->
                    <div class="uk-flex uk-flex-middle gap-2">
                        <div class="uk-width-auto uk-flex uk-flex-middle link-s link-selector-btn" style="width: 200px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="2" y1="12" x2="22" y2="12"></line><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path></svg>
                            <span class="uk-margin-small-left">Hotel Website</span>
                        </div>
                        <input class="uk-input uk-width-expand" type="text" placeholder="Enter Hotel Website URL" style="height: 40px;">
                    </div>
                

                
                    <!-- Container for dynamically added custom links -->
                    <div id="customLinksContainer"></div>
                    <!-- Add Custom Link -->
                    <div class="uk-margin-top">
                      <button id="addCustomLink" class="uk-button ">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                          Add Custom Link
                      </button>
                    </div>
                </div>
                  <div class> <button class="uk-button">
                     Update profile
                     </button> 
                  </div>
               </li>
               <li class="space-y-6 w-full">
                <div>
                  <h3 class="text-lg font-medium">Billing & Subscription</h3>
                  <p class="text-sm text-muted-foreground">
                    Manage your subscription, payment methods, and billing history.
                  </p>
                </div>
                <div class="border-t border-border mb-6"></div>
              
                <!-- Main container -->
                <div class="w-full space-y-8">
                  <!-- User Information -->
                  <div class="card rounded-lg border shadow-sm w-full" data-v0-t="card">
                    <div class="flex justify-between items-center p-6">
                      <div>
                        <h3 class="text-xl font-semibold">John Doe</h3>
                        <p class="text-sm text-muted-foreground"><EMAIL></p>
                      </div>
                      <button class="uk-button uk-button-primary">Edit Profile</button>
                    </div>
                    <div class="border-t border-border"></div>
                    <div class="grid grid-cols-2 gap-4 p-6">
                      <div>
                        <h4 class="text-sm font-medium text-muted-foreground">Billing Address</h4>
                        <p class="mt-1">123 Main Street<br>Anytown, ST 12345<br>United States</p>
                      </div>
                      <div>
                        <h4 class="text-sm font-medium text-muted-foreground">Shipping Address</h4>
                        <p class="mt-1">456 Oak Avenue<br>Somewhere, ST 67890<br>United States</p>
                      </div>
                    </div>
                  </div>
              
                  <!-- Current Plan -->
                  <div class="card rounded-lg border shadow-sm w-full" data-v0-t="card">
                    <div class="p-6">
                      <h3 class="text-lg font-semibold mb-2">Current Plan</h3>
                      <div class="flex justify-between items-center">
                        <div>
                          <p class="text-2xl font-bold">Premium Plan</p>
                          <p class="text-sm text-muted-foreground">Billed monthly on the 1st</p>
                        </div>
                        <div class="text-right">
                          <p class="text-2xl font-bold">$49.99 / month</p>
                          <p class="text-sm text-muted-foreground">Next billing date: Sep 1, 2024</p>
                        </div>
                      </div>
                      <div class="mt-6 flex space-x-4">
                        <button class="uk-button uk-button-primary">Upgrade Plan</button>
                        <button class="uk-button uk-button">View Plan Details</button>
                      </div>
                    </div>
                  </div>
              
                  <!-- Payment Methods -->
                  <div class="card rounded-lg border shadow-sm w-full" data-v0-t="card">
                    <div class="p-6">
                      <h3 class="text-lg font-semibold mb-4">Payment Methods</h3>
                      <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 border rounded-lg">
                          <div class="flex items-center space-x-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect><line x1="1" y1="10" x2="23" y2="10"></line></svg>
                            <div>
                              <p class="font-medium">Visa ending in 1234</p>
                              <p class="text-sm text-muted-foreground">Expires 12/2025</p>
                            </div>
                          </div>
                          <span class="uk-button">Primary</span>
                        </div>
                        <div class="flex items-center justify-between p-4 border rounded-lg">
                          <div class="flex items-center space-x-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect><line x1="1" y1="10" x2="23" y2="10"></line></svg>
                            <div>
                              <p class="font-medium">Mastercard ending in 5678</p>
                              <p class="text-sm text-muted-foreground">Expires 08/2026</p>
                            </div>
                          </div>
                          <button class="uk-button">Make Primary</button>
                        </div>
                      </div>
                      <button class="uk-button uk-button mt-4">Add Payment Method</button>
                    </div>
                  </div>
              
                  <!-- Billing History -->
                  <div class="card rounded-lg border shadow-sm w-full" data-v0-t="card">
                    <div class="p-6">
                      <h3 class="text-lg font-semibold mb-4">Billing History</h3>
                      <div class="overflow-x-auto">
                        <table class="uk-table uk-table-divider uk-table-responsive">
                          <thead>
                            <tr>
                              <th class="uk-text-left">Date</th>
                              <th class="uk-text-left">Description</th>
                              <th class="uk-text-left">Amount</th>
                              <th class="uk-text-left">Status</th>
                              <th class="uk-text-left">Invoice</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>Aug 1, 2024</td>
                              <td>Premium Plan - Monthly</td>
                              <td>$49.99</td>
                              <td><span class=" uk-label-success">Paid</span></td>
                              <td><a href="#" class="uk-button uk-button-text">Download</a></td>
                            </tr>
                            <tr>
                              <td>Jul 1, 2024</td>
                              <td>Premium Plan - Monthly</td>
                              <td>$49.99</td>
                              <td><span class="uk-label-success">Paid</span></td>
                              <td><a href="#" class="uk-button uk-button-text">Download</a></td>
                            </tr>
                            <tr>
                              <td>Jun 1, 2024</td>
                              <td>Premium Plan - Monthly</td>
                              <td>$49.99</td>
                              <td><span class=" uk-label-success">Paid</span></td>
                              <td><a href="#" class="uk-button uk-button-text">Download</a></td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <button class="uk-button uk-button mt-4">View All Transactions</button>
                    </div>
                  </div>
              
                  <!-- Subscription Management -->
                  <div class="card rounded-lg border shadow-sm w-full" data-v0-t="card">
                    <div class="p-6">
                      <h3 class="text-lg font-semibold mb-4">Subscription Management</h3>
                      <div class="space-y-4">
                        <div class="flex items-center justify-between">
                          <span class="font-medium">Auto-renew subscription</span>
                          <label class="uk-switch">
                            <input type="checkbox" checked>
                            <div class="uk-switch-slider"></div>
                          </label>
                        </div>
                        <div class="flex items-center justify-between">
                          <span class="font-medium">Email notifications for billing</span>
                          <label class="uk-switch">
                            <input type="checkbox" checked>
                            <div class="uk-switch-slider"></div>
                          </label>
                        </div>
                      </div>
                      <div class="mt-6">
                        <button class="uk-button uk-button-danger">Cancel Subscription</button>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
                  <li class="space-y-6">
                     <div>
                        <h3 class="text-lg font-medium">Appearance</h3>
                        <p class="text-sm ">
                           Customize the appearance of the app. Change Themes, Fonts or Languages. All in your Hands.
                        </p>
                     </div>
                     <div class="border-t border-border"></div>
                     <div class="space-y-4 w-full mb-8">
                      <span class="uk-form-label">Font Family</span>
                      <div class="uk-form-help ">
                        Select a font to use throughout the dashboard.
                      </div>
                      <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                        <a class="font-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors ring-2 ring-primary" data-font="Inter">
                          <div class="flex items-center justify-center">
                            <span class="font-medium">Inter</span>
                          </div>
                        </a>
                        <a class="font-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-font="Geist">
                          <div class="flex items-center justify-center">
                            <span class="font-medium">Geist</span>
                          </div>
                        </a>
                        <a class="font-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-font="Arial">
                          <div class="flex items-center justify-center">
                            <span class="font-medium">Arial</span>
                          </div>
                        </a>
                        <a class="font-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-font="Helvetica">
                          <div class="flex items-center justify-center">
                            <span class="font-medium">Helvetica</span>
                          </div>
                        </a>
                        <a class="font-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-font="Georgia">
                          <div class="flex items-center justify-center">
                            <span class="font-medium">Georgia</span>
                          </div>
                        </a>
                        <a class="font-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-font="Verdana">
                          <div class="flex items-center justify-center">
                            <span class="font-medium">Verdana</span>
                          </div>
                        </a>
                        <a class="font-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-font="Palatino">
                          <div class="flex items-center justify-center">
                            <span class="font-medium">Palatino</span>
                          </div>
                        </a>
                        <a class="font-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-font="Baskerville">
                          <div class="flex items-center justify-center">
                            <span class="font-medium">Baskerville</span>
                          </div>
                        </a>
                        <a class="font-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-font="Cambria">
                          <div class="flex items-center justify-center">
                            <span class="font-medium">Cambria</span>
                          </div>
                        </a>
                        <a class="font-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-font="Didot">
                          <div class="flex items-center justify-center">
                            <span class="font-medium">Didot</span>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div class="space-y-4 w-full mb-8">
                      <span class="uk-form-label">Language</span>
                      <div class="uk-form-help ">
                        Select a language to translate the page content.
                      </div>
                      <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                        <a class="language-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-lang="en">
                          <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w40/gb.png" width="30" alt="UK Flag" class="rounded-sm">
                            <span class="font-medium">English</span>
                          </div>
                        </a>
                        <a class="language-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-lang="es">
                          <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w40/es.png" width="30" alt="Spain Flag" class="rounded-sm">
                            <span class="font-medium">Español</span>
                          </div>
                        </a>
                        <a class="language-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-lang="fr">
                          <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w40/fr.png" width="30" alt="France Flag" class="rounded-sm">
                            <span class="font-medium">Français</span>
                          </div>
                        </a>
                        <a class="language-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-lang="de">
                          <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w40/de.png" width="30" alt="Germany Flag" class="rounded-sm">
                            <span class="font-medium">Deutsch</span>
                          </div>
                        </a>
                        <a class="language-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-lang="it">
                          <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w40/it.png" width="30" alt="Italy Flag" class="rounded-sm">
                            <span class="font-medium">Italiano</span>
                          </div>
                        </a>
                        <a class="language-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-lang="sv">
                          <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w40/se.png" width="30" alt="Sweden Flag" class="rounded-sm">
                            <span class="font-medium">Svenska</span>
                          </div>
                        </a>
                        <a class="language-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-lang="nl">
                          <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w40/nl.png" width="30" alt="Netherlands Flag" class="rounded-sm">
                            <span class="font-medium">Nederlands</span>
                          </div>
                        </a>
                        <a class="language-option flex flex-col justify-between cursor-pointer rounded-md border-muted p-3 ring-ring hover:bg-muted transition-colors" data-lang="id">
                          <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w40/id.png" width="30" alt="Indonesia Flag" class="rounded-sm">
                            <span class="font-medium">Bahasa Indonesia</span>
                          </div>
                        </a>
                      </div>
                    </div>
                     <div class="space-y-4 w-full">
                        <span class="uk-form-label">Theme</span> 
                        <div class="uk-form-help ">
                          Select the theme for the dashboard.
                        </div>
                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                          <a class="theme-options flex flex-col justify-between cursor-pointer rounded-md  border-muted p-3 ring-ring hover:bg-muted transition-colors" data-theme="light">
                            <div class="space-y-2 rounded-sm bg-[#ecedef] p-2">
                              <div class="space-y-2 rounded-md bg-white p-2 shadow-sm">
                                <div class="h-2 w-full rounded-lg bg-[#ecedef]"></div>
                                <div class="h-2 w-4/5 rounded-lg bg-[#ecedef]"></div>
                              </div>
                              <div class="flex items-center space-x-2 rounded-md bg-white p-2 shadow-sm">
                                <div class="h-4 w-4 rounded-full bg-[#ecedef]"></div>
                                <div class="h-2 flex-grow rounded-lg bg-[#ecedef]"></div>
                              </div>
                            </div>
                            <span class="mt-2 block text-center text-sm font-medium">Light</span>
                          </a>
                      
                          <a class="theme-options flex flex-col justify-between cursor-pointer rounded-md  border-muted p-3 ring-ring hover:bg-muted transition-colors" data-theme="pure-black">
                            <div class="space-y-2 rounded-sm bg-black p-2">
                              <div class="space-y-2 rounded-md bg-gray-900 p-2 shadow-sm">
                                <div class="h-2 w-full rounded-lg bg-gray-800"></div>
                                <div class="h-2 w-4/5 rounded-lg bg-gray-800"></div>
                              </div>
                              <div class="flex items-center space-x-2 rounded-md bg-gray-900 p-2 shadow-sm">
                                <div class="h-4 w-4 rounded-full bg-gray-800"></div>
                                <div class="h-2 flex-grow rounded-lg bg-gray-800"></div>
                              </div>
                            </div>
                            <span class="mt-2 block text-center text-sm font-medium">Pure Black</span>
                          </a>
                      
                          <a class="theme-options flex flex-col justify-between cursor-pointer rounded-md  border-muted p-3 ring-ring hover:bg-muted transition-colors" data-theme="dark-gray">
                            <div class="space-y-2 rounded-sm bg-[#212529] p-2">
                              <div class="space-y-2 rounded-md bg-[#343a40] p-2 shadow-sm">
                                <div class="h-2 w-full rounded-lg bg-[#495057]"></div>
                                <div class="h-2 w-4/5 rounded-lg bg-[#495057]"></div>
                              </div>
                              <div class="flex items-center space-x-2 rounded-md bg-[#343a40] p-2 shadow-sm">
                                <div class="h-4 w-4 rounded-full bg-[#495057]"></div>
                                <div class="h-2 flex-grow rounded-lg bg-[#495057]"></div>
                              </div>
                            </div>
                            <span class="mt-2 block text-center text-sm font-medium">Dark Gray</span>
                          </a>
                      
                          <a class="theme-options flex flex-col justify-between cursor-pointer rounded-md  border-muted p-3 ring-ring hover:bg-muted transition-colors" data-theme="navy-blue">
                            <div class="space-y-2 rounded-sm bg-[#0e253a] p-2">
                              <div class="space-y-2 rounded-md bg-[#1a3a54] p-2 shadow-sm">
                                <div class="h-2 w-full rounded-lg bg-[#2c5c7d]"></div>
                                <div class="h-2 w-4/5 rounded-lg bg-[#2c5c7d]"></div>
                              </div>
                              <div class="flex items-center space-x-2 rounded-md bg-[#1a3a54] p-2 shadow-sm">
                                <div class="h-4 w-4 rounded-full bg-[#2c5c7d]"></div>
                                <div class="h-2 flex-grow rounded-lg bg-[#2c5c7d]"></div>
                              </div>
                            </div>
                            <span class="mt-2 block text-center text-sm font-medium">Navy Blue</span>
                          </a>
                      
                          <a class="theme-options flex flex-col justify-between cursor-pointer rounded-md  border-muted p-3 ring-ring hover:bg-muted transition-colors" data-theme="slate-gray">
                            <div class="space-y-2 rounded-sm bg-[#3f4d5a] p-2">
                              <div class="space-y-2 rounded-md bg-[#576574] p-2 shadow-sm">
                                <div class="h-2 w-full rounded-lg bg-[#6f7f8f]"></div>
                                <div class="h-2 w-4/5 rounded-lg bg-[#6f7f8f]"></div>
                              </div>
                              <div class="flex items-center space-x-2 rounded-md bg-[#576574] p-2 shadow-sm">
                                <div class="h-4 w-4 rounded-full bg-[#6f7f8f]"></div>
                                <div class="h-2 flex-grow rounded-lg bg-[#6f7f8f]"></div>
                              </div>
                            </div>
                            <span class="mt-2 block text-center text-sm font-medium">Slate Gray</span>
                          </a>
                      
                          <a class="theme-options flex flex-col justify-between cursor-pointer rounded-md  border-muted p-3 ring-ring hover:bg-muted transition-colors" data-theme="warm-gray">
                            <div class="space-y-2 rounded-sm bg-[#57534e] p-2">
                              <div class="space-y-2 rounded-md bg-[#78716c] p-2 shadow-sm">
                                <div class="h-2 w-full rounded-lg bg-[#a8a29e]"></div>
                                <div class="h-2 w-4/5 rounded-lg bg-[#a8a29e]"></div>
                              </div>
                              <div class="flex items-center space-x-2 rounded-md bg-[#78716c] p-2 shadow-sm">
                                <div class="h-4 w-4 rounded-full bg-[#a8a29e]"></div>
                                <div class="h-2 flex-grow rounded-lg bg-[#a8a29e]"></div>
                              </div>
                            </div>
                            <span class="mt-2 block text-center text-sm font-medium">Warm Gray</span>
                          </a>
                      
                          <a class="theme-options flex flex-col justify-between cursor-pointer rounded-md  border-muted p-3 ring-ring hover:bg-muted transition-colors" data-theme="cool-blue">
                            <div class="space-y-2 rounded-sm bg-[#2c3e50] p-2">
                              <div class="space-y-2 rounded-md bg-[#34495e] p-2 shadow-sm">
                                <div class="h-2 w-full rounded-lg bg-[#46627f]"></div>
                                <div class="h-2 w-4/5 rounded-lg bg-[#46627f]"></div>
                              </div>
                              <div class="flex items-center space-x-2 rounded-md bg-[#34495e] p-2 shadow-sm">
                                <div class="h-4 w-4 rounded-full bg-[#46627f]"></div>
                                <div class="h-2 flex-grow rounded-lg bg-[#46627f]"></div>
                              </div>
                            </div>
                            <span class="mt-2 block text-center text-sm font-medium">Cool Blue</span>
                          </a>
                      
                          <a class="theme-options flex flex-col justify-between cursor-pointer rounded-md  border-muted p-3 ring-ring hover:bg-muted transition-colors" data-theme="olive-green">
                            <div class="space-y-2 rounded-sm bg-[#546854] p-2">
                              <div class="space-y-2 rounded-md bg-[#4e614e] p-2 shadow-sm">
                                <div class="h-2 w-full rounded-lg bg-[#607660]"></div>
                                <div class="h-2 w-4/5 rounded-lg bg-[#607660]"></div>
                              </div>
                              <div class="flex items-center space-x-2 rounded-md bg-[#4e614e] p-2 shadow-sm">
                                <div class="h-4 w-4 rounded-full bg-[#607660]"></div>
                                <div class="h-2 flex-grow rounded-lg bg-[#607660]"></div>
                              </div>
                            </div>
                            <span class="mt-2 block text-center text-sm font-medium">Olive Green</span>
                          </a>
                      
                          <a class="theme-options flex flex-col justify-between cursor-pointer rounded-md  border-muted p-3 ring-ring hover:bg-muted transition-colors" data-theme="deep-burgundy">
                            <div class="space-y-2 rounded-sm bg-[#4a1c2b] p-2">
                              <div class="space-y-2 rounded-md bg-[#5e2436] p-2 shadow-sm">
                                <div class="h-2 w-full rounded-lg bg-[#722c41]"></div>
                                <div class="h-2 w-4/5 rounded-lg bg-[#722c41]"></div>
                              </div>
                              <div class="flex items-center space-x-2 rounded-md bg-[#5e2436] p-2 shadow-sm">
                                <div class="h-4 w-4 rounded-full bg-[#722c41]"></div>
                                <div class="h-2 flex-grow rounded-lg bg-[#722c41]"></div>
                              </div>
                            </div>
                            <span class="mt-2 block text-center text-sm font-medium">Deep Burgundy</span>
                          </a>
                      
                          <a class="theme-options flex flex-col justify-between cursor-pointer rounded-md  border-muted p-3 ring-ring hover:bg-muted transition-colors" data-theme="charcoal">
                            <div class="space-y-2 rounded-sm bg-[#2f3640] p-2">
                              <div class="space-y-2 rounded-md bg-[#353b48] p-2 shadow-sm">
                                <div class="h-2 w-full rounded-lg bg-[#4b5563]"></div>
                                <div class="h-2 w-4/5 rounded-lg bg-[#4b5563]"></div>
                              </div>
                              <div class="flex items-center space-x-2 rounded-md bg-[#353b48] p-2 shadow-sm">
                                <div class="h-4 w-4 rounded-full bg-[#4b5563]"></div>
                                <div class="h-2 flex-grow rounded-lg bg-[#4b5563]"></div>
                              </div>
                            </div>
                            <span class="mt-2 block text-center text-sm font-medium">Charcoal</span>
                          </a>
                        </div>
                      </div>
                  </li>
                  
                </li>
                <li class="space-y-6 w-full">
                  <div>
                    <h3 class="text-lg font-medium">Contact Us</h3>
                    <p class="text-sm text-muted-foreground">
                      Get in touch with our development team or find answers to your questions.
                    </p>
                  </div>
                  <div class="border-t border-border mb-6"></div>
                
                  <!-- Main container -->
                  <div class="w-full space-y-8">
                    <!-- Developer Team Section -->
                    <!-- Developer Team Section -->
                    <div class="card rounded-lg border shadow-sm w-full" data-v0-t="card">
                      <div class="p-6">
                        <h3 class="text-xl font-semibold mb-4">Meet Our Development Team</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <!-- Developer 1 -->
                          <div class="flex flex-col items-center">
                            <img src="../static/images/profile.jpg" alt="John Doe" class="w-24 h-24 rounded-full object-cover mb-3">
                            <h4 class="font-medium">Dixith Mediga</h4>
                            <p class="text-sm text-muted-foreground">Lead Developer</p>
                          </div>
                          <!-- Developer 2 -->
                          <div class="flex flex-col items-center">
                            <img src="../static/images/profile.jpg" alt="Jane Smith" class="w-24 h-24 rounded-full object-cover mb-3">
                            <h4 class="font-medium">Harsh Jadhav</h4>
                            <p class="text-sm text-muted-foreground">Assistant Developer</p>
                          </div>
                          <!-- Developer 3 -->
                          <div class="flex flex-col items-center">
                            <img src="../static/images/profile.jpg" alt="Mike Johnson" class="w-24 h-24 rounded-full object-cover mb-3">
                            <h4 class="font-medium">George Garriga</h4>
                            <p class="text-sm text-muted-foreground">Sales Manager</p>
                          </div>
                        </div>
                      </div>
                    </div>
                
                    <!-- Contact Information -->
                    <div class="card rounded-lg border shadow-sm w-full" data-v0-t="card">
                      <div class="p-6">
                        <h3 class="text-xl font-semibold mb-4">Contact Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h4 class="font-medium mb-2">Office Address</h4>
                            <p class="text-sm text-muted-foreground">
                              123 Tech Park, Innovation Street<br>
                              Silicon Valley, CA 94000<br>
                              United States
                            </p>
                          </div>
                          <div>
                            <h4 class="font-medium mb-2">Contact Details</h4>
                            <p class="text-sm text-muted-foreground">
                              Email: <EMAIL><br>
                              Phone: +****************<br>
                              Hours: Mon-Fri, 9AM-6PM PST
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                
                    <!-- Contact Form -->
                    <div class="card rounded-lg border shadow-sm w-full" data-v0-t="card">
                      <div class="p-6">
                        <h3 class="text-xl font-semibold mb-4">Send Us a Message</h3>
                        <form class="space-y-4">
                          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label for="name" class="block text-sm font-medium mb-1">Name</label>
                              <input type="text" id="name" name="name" class="w-full p-2 border border-input rounded-md" required>
                            </div>
                            <div>
                              <label for="email" class="block text-sm font-medium mb-1">Email</label>
                              <input type="email" id="email" name="email" class="w-full p-2 border border-input rounded-md" required>
                            </div>
                          </div>
                          <div>
                            <label for="subject" class="block text-sm font-medium mb-1">Subject</label>
                            <input type="text" id="subject" name="subject" class="w-full p-2 border border-input rounded-md" required>
                          </div>
                          <div>
                            <label for="message" class="block text-sm font-medium mb-1">Message</label>
                            <textarea id="message" name="message" rows="4" class="w-full p-2 border border-input rounded-md" required></textarea>
                          </div>
                          <div>
                            <button type="submit" class="uk-button uk-button-primary">Send Message</button>
                          </div>
                        </form>
                      </div>
                    </div>
                
                    <!-- FAQ Section -->
                    <div class="card rounded-lg border shadow-sm w-full" data-v0-t="card">
                      <div class="p-6">
                        <h3 class="text-xl font-semibold mb-4">Frequently Asked Questions</h3>
                        <div class="space-y-4">
                          <details class="group">
                            <summary class="flex justify-between items-center font-medium cursor-pointer list-none">
                              <span>How do I get started with Hotel Manager?</span>
                              <span class="transition group-open:rotate-180">
                                <svg fill="none" height="24" shape-rendering="geometricPrecision" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" viewBox="0 0 24 24" width="24"><path d="M6 9l6 6 6-6"></path></svg>
                              </span>
                            </summary>
                            <p class="text-neutral-600 mt-3 group-open:animate-fadeIn">
                              To get started, simply sign up for an account on our website and follow the onboarding process. Our intuitive setup wizard will guide you through configuring your hotel's profile and settings.
                            </p>
                          </details>
                          <details class="group">
                            <summary class="flex justify-between items-center font-medium cursor-pointer list-none">
                              <span>Is there a free trial available?</span>
                              <span class="transition group-open:rotate-180">
                                <svg fill="none" height="24" shape-rendering="geometricPrecision" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" viewBox="0 0 24 24" width="24"><path d="M6 9l6 6 6-6"></path></svg>
                              </span>
                            </summary>
                            <p class="text-neutral-600 mt-3 group-open:animate-fadeIn">
                              Yes, we offer a 14-day free trial for all new users. This allows you to explore all features and functionalities of Hotel Manager before committing to a subscription.
                            </p>
                          </details>
                          <!-- Add more FAQ items as needed -->
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
               </ul>
            </div>
         </div>
      </div>

   </body>
</html>