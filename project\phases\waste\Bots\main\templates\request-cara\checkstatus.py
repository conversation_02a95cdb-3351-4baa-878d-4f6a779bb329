import requests

# Twilio account details (replace with actual Account SID and Auth Token)
TWILIO_ACCOUNT_SID = '**********************************'
TWILIO_AUTH_TOKEN = '005d910f85546392a91f58a3878c437c'

# Twilio API endpoint for the GET request
url = 'https://content.twilio.com/v1/Content/HXfb83695ec690fa2b77f40b21e920032e/ApprovalRequests'

# Making the GET request
response = requests.get(
    url,
    auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN),
    headers={'Content-Type': 'application/json'}
)

# Check the response status
if response.status_code == 200:
    # If the request was successful, print the response JSON
    print(response.json())
else:
    print(f"Request failed with status: {response.status_code}")
    print(response.text)