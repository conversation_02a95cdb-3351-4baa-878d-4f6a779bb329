import requests
import json

# Twilio credentials
TWILIO_ACCOUNT_SID = "**********************************"
TWILIO_AUTH_TOKEN = "005d910f85546392a91f58a3878c437c"

# API endpoint
url = "https://content.twilio.com/v1/Content"

# Catalog and product details
catalog_id = "****************"
spa_product = "tsm9cxtxrs"
massage_products_list = ["vx5zykpqwu", "7wiqotqnez", "ptea6c63r0"]

# Request payload
data = {
    "friendly_name": "v1_fixedproducts",
    "language": "en",
    "variables": {"1": "menu_ad", "2": "menu_name"},
    "types": {
        "twilio/catalog": {
            "id": catalog_id,
            "body": "Hi, check out this menu {{1}}",
            "subtitle": "Great deals",
            "title": "The Menu: {{2}}",
            "thumbnail_item_id": spa_product,
            "items": [
                {"id": spa_product, "section_title": "Spa"},
                *[{"id": product, "section_title": "Massage"} for product in massage_products_list]
            ]
        }
    }
}

# Make the POST request
response = requests.post(
    url,
    headers={"Content-Type": "application/json"},
    auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN),
    data=json.dumps(data)
)

# Print the response
if response.status_code == 201:
    print("Content created successfully:", response.json())
else:
    print("Failed to create content. Status code:", response.status_code)
    print("Response:", response.text)
