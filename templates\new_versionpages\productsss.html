<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Products</title>
  {% include 'imports.html' %}
</head>
<style>
  body {
    visibility: hidden;
  }

  #sidebar {
    position: sticky;
    top: 0;
    height: 100vh;
    /* Force full viewport height */
    overflow-y: auto;
    /* Allow scrolling within the sidebar */
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Carousel Styles */
  .carousel-container {
    position: relative;
    width: 70%;
    /* Reduced width by 30% */
    max-width: 315px;
    /* Reduced from 450px */
    height: 370px;
    /* Slightly reduced height */
    margin: 0 auto 40px;
  }

  .carousel-row {
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
    margin-bottom: 40px;
  }

  .category-section {
    width: 100%;
    margin-bottom: 20px;
  }

  .category-title {
    text-align: center;
    margin-bottom: 24px;
  }

  /* Rest of carousel styles remain the same */
  .carousel {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    /* border: 1px solid #e4e4e7; */
    border-radius: 8px;
    /* background-color: #ffffff; */
  }

  .carousel-inner {
    display: flex;
    width: 100%;
    height: 100%;
    transition: transform 0.5s ease;
  }

  .carousel-item {
    min-width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .carousel-item img {
    width: 100%;
    height: 85%;
    object-fit: cover;
  }

  .carousel-item .product-info {
    padding: 15px;
    width: 100%;
    text-align: center;
    background-color: var(--product-page-bg);
    color: var(--dropdown-text);
  }

  .carousel-control {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 33px;
    height: 33px;
    /* background-color: #ffffff; */
    border-radius: 50%;
    /* border: 1px solid #e4e4e7; */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 10;
    transition: background-color 0.1s ease;
  }

  .carousel-control:hover {
    background-color: #f4f4f5;
  }

  .carousel-control-prev {
    left: -18px;
  }

  .carousel-control-next {
    right: -18px;
  }

  .slide-indicator {
    position: absolute;
    bottom: -24px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    color: #71717a;
  }

  .carousel-control-prev {
    left: -50px;
    /* increased spacing compared to -18px */
  }

  .carousel-control-next {
    right: -50px;
    /* increased spacing compared to -18px */
  }

  .carousel-control-prev:hover {
    background-color: var(--theme-option-hover-bg);
  }

  .carousel-control-next:hover {
    background-color: var(--theme-option-hover-bg);
  }

  /* Grid layout for multiple slideshow containers */
  .carousel-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
  }
</style>

<body class="light">
  {% include 'components/loading.html' %}

  <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] ">
    {% include 'sidebar.html' %}

    <div class="flex flex-col">
      <header
        class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
        <div class="flex items-center gap-2 px-4 pl-0">
          <button id="toggle-btn" style="margin-left: 8px;"
            class="opacity-100 transition-opacity duration-300 focus:outline-none"
            style="background-color: transparent !important;">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-panel-left">
              <rect width="18" height="18" x="3" y="3" rx="2"></rect>
              <path d="M9 3v18"></path>
            </svg>
          </button>
          <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-2 h-4"
            style="background-color: var(--border-color);"></div>
          <nav aria-label="breadcrumb">
            <ol class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
              <li class="items-center gap-1.5 hidden md:block"><a class="transition-colors hover:text-foreground">Core
                  Pages</a></li>
              <li role="presentation" aria-hidden="true" class="[&amp;>svg]:w-3.5 [&amp;>svg]:h-3.5 hidden md:block">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="lucide lucide-chevron-right">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </li>
              <li class="inline-flex items-center gap-1.5"><span role="link" aria-disabled="true" aria-current="page"
                  class="font-normal text-foreground">Products</span></li>
            </ol>
          </nav>
        </div>
        {% include 'topright.html' %}
      </header>
      <main class="card flex flex-1 flex-col gap-2 p-4 md:gap-2 md:p-6">
        <div class="">
          <h2 class="text-xl font-medium leading-none tracking-tight category-title items-center "> Add Items <button
              id="addProductBtn"
              class="rounded-full bg-black text-white p-1 hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-600 ml-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg> </button> </h2>
          <div class="mx-auto max-w-2xl px-2 py-4 sm:px-3 sm:py-6 lg:max-w-7xl lg:px-2">

            <!-- Row 1: Food and Beverages -->
            <div class="carousel-grid">
              <!-- Food Menu Section -->
              <div class="category-section">
                <h2 class="text-xl font-medium leading-none tracking-tight category-title">
                  Food Menu
                </h2>

                <!-- Food Carousel -->
                <div class="carousel-container" data-carousel="food">
                  <div class="carousel">
                    <div class="carousel-inner">
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/foodmenu/burrata.png" alt="Burrata">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Burrata</h3>
                          <p class="mt-1 text-lg font-medium">$25</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/foodmenu/chicken.png" alt="Chicken">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Chicken</h3>
                          <p class="mt-1 text-lg font-medium">$32</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/foodmenu/filletsteak.png" alt="Fillet Steak">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Fillet Steak</h3>
                          <p class="mt-1 text-lg font-medium">$45</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/foodmenu/grilledsalmon.png" alt="Grilled Salmon">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Grilled Salmon</h3>
                          <p class="mt-1 text-lg font-medium">$38</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/foodmenu/lobsterroll.png" alt="Lobster Roll">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Lobster Roll</h3>
                          <p class="mt-1 text-lg font-medium">$42</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/foodmenu/pumpkinwithcoconut.png"
                          alt="Pumpkin with Coconut">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Pumpkin with Coconut</h3>
                          <p class="mt-1 text-lg font-medium">$28</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/foodmenu/steakandlobster.png" alt="Steak and Lobster">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Steak and Lobster</h3>
                          <p class="mt-1 text-lg font-medium">$65</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/foodmenu/tbone.png" alt="T-Bone Steak">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">T-Bone Steak</h3>
                          <p class="mt-1 text-lg font-medium">$55</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="slide-indicator">Slide 1 of 8</div>

                  <button class="carousel-control carousel-control-prev">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-arrow-left">
                      <path d="m12 19-7-7 7-7" />
                      <path d="M19 12H5" />
                    </svg>
                  </button>

                  <button class="carousel-control carousel-control-next">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-arrow-right">
                      <path d="M5 12h14" />
                      <path d="m12 5 7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Beverages Section -->
              <div class="category-section">
                <h2 class="text-xl font-medium leading-none tracking-tight category-title">Beverages</h2>

                <!-- Beverages Carousel -->
                <div class="carousel-container" data-carousel="beverages">
                  <div class="carousel">
                    <div class="carousel-inner">
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/beveragemenu/cocktail.jpg" alt="Cocktail">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Cocktail</h3>
                          <p class="mt-1 text-lg font-medium">$48</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/beveragemenu/drinks.jpeg" alt="Drinks">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Drinks</h3>
                          <p class="mt-1 text-lg font-medium">$89</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/beveragemenu/redwine.jpg" alt="Red Wine">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Red Wine</h3>
                          <p class="mt-1 text-lg font-medium">$40</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/beveragemenu/beers.jpg" alt="Beers">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Beers</h3>
                          <p class="mt-1 text-lg font-medium">$10</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/beveragemenu/soda.jpg" alt="Soda">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Soda</h3>
                          <p class="mt-1 text-lg font-medium">$3</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/beveragemenu/water.jpg" alt="Water">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Water</h3>
                          <p class="mt-1 text-lg font-medium">$5</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="slide-indicator">Slide 1 of 6</div>

                  <button class="carousel-control carousel-control-prev">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-arrow-left">
                      <path d="m12 19-7-7 7-7" />
                      <path d="M19 12H5" />
                    </svg>
                  </button>

                  <button class="carousel-control carousel-control-next">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-arrow-right">
                      <path d="M5 12h14" />
                      <path d="m12 5 7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- Row 2: Room Bookings and Spa & Massage -->
            <div class="carousel-grid">
              <!-- Room Bookings Section -->
              <div class="category-section">
                <h2 class="text-xl font-medium leading-none tracking-tight category-title">Room Bookings</h2>

                <!-- Room Bookings Carousel -->
                <div class="carousel-container" data-carousel="rooms">
                  <div class="carousel">
                    <div class="carousel-inner">
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/roombooking/executivesuite.jpeg" alt="Executive Suite">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Executive Suite</h3>
                          <p class="mt-1 text-lg font-medium">$150</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/roombooking/juniorsuite.jpeg" alt="Junior Suite">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Junior Suite</h3>
                          <p class="mt-1 text-lg font-medium">$55</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/roombooking/juniorsuitedeluxe.jpeg"
                          alt="Junior Suite Deluxe">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Junior Suite Deluxe</h3>
                          <p class="mt-1 text-lg font-medium">$65</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/roombooking/raid.jpeg" alt="Royal Suite">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Royal Suite</h3>
                          <p class="mt-1 text-lg font-medium">$75</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="slide-indicator">Slide 1 of 4</div>

                  <button class="carousel-control carousel-control-prev">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-arrow-left">
                      <path d="m12 19-7-7 7-7" />
                      <path d="M19 12H5" />
                    </svg>
                  </button>

                  <button class="carousel-control carousel-control-next">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-arrow-right">
                      <path d="M5 12h14" />
                      <path d="m12 5 7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Spa & Massage Section -->
              <div class="category-section">
                <h2 class="text-xl font-medium leading-none tracking-tight category-title">Spa & Massage</h2>

                <!-- Spa & Massage Carousel -->
                <div class="carousel-container" data-carousel="spa">
                  <div class="carousel">
                    <div class="carousel-inner">
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/starterimages/spa.png" alt="Spa Services">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Spa Services</h3>
                          <p class="mt-1 text-lg font-medium">$120</p>
                        </div>
                      </div>
                      <div class="carousel-item">
                        <img src="https://images-new.vercel.app/starterimages/massage.png" alt="Massage">
                        <div class="product-info">
                          <h3 class="mt-1 text-sm">Massage</h3>
                          <p class="mt-1 text-lg font-medium">$90</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="slide-indicator">Slide 1 of 2</div>

                  <button class="carousel-control carousel-control-prev">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-arrow-left">
                      <path d="m12 19-7-7 7-7" />
                      <path d="M19 12H5" />
                    </svg>
                  </button>

                  <button class="carousel-control carousel-control-next">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-arrow-right">
                      <path d="M5 12h14" />
                      <path d="m12 5 7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
    <!-- Product Add Modal -->
    <div id="addProductModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
      <div class="product-add-popup rounded-lg p-8 max-w-md w-full">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-medium">Add New Product</h3>
          <button id="closeModalBtn" class="text-gray-500 hover:text-gray-700" style="
                  background: transparent;
              ">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <form id="addProductForm" class="space-y-4">
          <div>
            <label class="block text-sm font-medium mb-1">Upload Image</label>
            <div class="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
              <input type="file" id="productImage" accept="image/*" class="hidden">
              <label for="productImage" class="cursor-pointer">
                <div id="imagePreview" class="flex flex-col items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <p class="mt-1 text-sm text-gray-500">Click to upload an image</p>
                </div>
              </label>
            </div>
          </div>
          <div>
            <label for="productTitle" class="block text-sm font-medium  mb-1">Title</label>
            <input type="text" id="productTitle" name="title" class="w-full border card rounded-md py-2 px-3">
          </div>
          <div>
            <label for="productPrice" class="block text-sm font-medium mb-1">Price ($)</label>
            <input type="number" id="productPrice" name="price" min="0" step="0.01"
              class="w-full border card rounded-md py-2 px-3">
          </div>
          <div>
            <label for="productTagCustom" class="block text-sm font-medium mb-1">Category</label>
            <div class="relative">
              <button type="button" id="productTagToggler"
                class="w-full flex items-center justify-between border border-gray-300 bg-background text-foreground card rounded-md py-2 px-3"
                style="background-color: transparent;" uk-toggle="target: #productTagDropdown">
                <span>Food</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div id="productTagDropdown" uk-dropdown="mode: click; pos: bottom-left; offset: 10" class="card w-full">
                <ul class="uk-nav uk-dropdown-nav card">
                  <li><a href="#" class="product-tag-option" data-tag="food">Food</a></li>
                  <li><a href="#" class="product-tag-option" data-tag="beverage">Beverage</a></li>
                  <li><a href="#" class="product-tag-option" data-tag="room">Room Booking</a></li>
                  <li><a href="#" class="product-tag-option" data-tag="spa">Massage or Spa</a></li>
                </ul>
              </div>
            </div>
            <input type="hidden" id="productTag" name="tag" value="food">
          </div>
          <div class="flex justify-end pt-4">
            <button type="button" id="cancelAddProduct" class="mr-2 px-4 py-2 border card rounded-md text-gray-700 ">
              Cancel
            </button>
            <button type="submit"
              class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300">
              Add Product
            </button>
          </div>
        </form>
      </div>
    </div>
    <script>
      document.addEventListener('DOMContentLoaded', function () {
        // Product tag selection code
        document.querySelectorAll('.product-tag-option').forEach(option => {
          option.addEventListener('click', function (e) {
            e.preventDefault();
            const tag = this.getAttribute('data-tag');
            const text = this.textContent;
            document.querySelector('#productTagToggler span').textContent = text;
            document.getElementById('productTag').value = tag;
            // Hide the dropdown using UIkit's API
            if (UIkit && UIkit.dropdown) {
              UIkit.dropdown(document.getElementById('productTagDropdown')).hide();
            }
          });
        });

        // Initialize all carousels
        const carousels = document.querySelectorAll('.carousel-container');

        carousels.forEach(function (carouselContainer) {
          const carouselInner = carouselContainer.querySelector('.carousel-inner');
          const items = carouselContainer.querySelectorAll('.carousel-item');
          const prevBtn = carouselContainer.querySelector('.carousel-control-prev');
          const nextBtn = carouselContainer.querySelector('.carousel-control-next');
          const slideIndicator = carouselContainer.querySelector('.slide-indicator');

          let currentIndex = 0;
          const totalItems = items.length;

          // Update carousel position
          function updateCarousel() {
            carouselInner.style.transform = `translateX(-${currentIndex * 100}%)`;
            slideIndicator.textContent = `Slide ${currentIndex + 1} of ${totalItems}`;
          }

          // Update slide indicator text initially
          slideIndicator.textContent = `Slide ${currentIndex + 1} of ${totalItems}`;

          // Event listeners
          prevBtn.addEventListener('click', () => {
            currentIndex = (currentIndex > 0) ? currentIndex - 1 : totalItems - 1;
            updateCarousel();
          });

          nextBtn.addEventListener('click', () => {
            currentIndex = (currentIndex < totalItems - 1) ? currentIndex + 1 : 0;
            updateCarousel();
          });
        });

        // Product modal code
        const addBtn = document.getElementById('addProductBtn');
        const modal = document.getElementById('addProductModal');
        const closeBtn = document.getElementById('closeModalBtn');
        const cancelBtn = document.getElementById('cancelAddProduct');
        const form = document.getElementById('addProductForm');
        const fileInput = document.getElementById('productImage');
        const imagePreview = document.getElementById('imagePreview');

        // Open modal
        addBtn.addEventListener('click', function () {
          modal.classList.remove('hidden');
        });

        // Close modal functions
        const closeModal = function () {
          modal.classList.add('hidden');
          form.reset();
          // Reset image preview
          imagePreview.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <p class="mt-1 text-sm text-gray-500">Click to upload an image</p>
                  `;
        };

        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);

        // Close when clicking outside modal
        modal.addEventListener('click', function (e) {
          if (e.target === modal) {
            closeModal();
          }
        });

        // Handle image preview
        fileInput.addEventListener('change', function () {
          if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function (e) {
              imagePreview.innerHTML = `<img src="${e.target.result}" class="max-h-40 object-contain" />`;
            };
            reader.readAsDataURL(this.files[0]);
          }
        });

        // Form submission
        form.addEventListener('submit', function (e) {
          e.preventDefault();

          // Get form values
          const title = document.getElementById('productTitle').value;
          const price = document.getElementById('productPrice').value;
          const tag = document.getElementById('productTag').value;
          const image = fileInput.files[0];

          // Validate
          if (!title || !price || !image) {
            alert('Please fill in all required fields');
            return;
          }

          // Here you would typically send the data to your server
          console.log('Product data:', { title, price, tag, image });

          // For demonstration purposes, show success and close
          alert('Product added successfully!');
          closeModal();
        });
      });
    </script>
</body>

</html>