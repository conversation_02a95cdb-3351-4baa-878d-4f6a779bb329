<!-- TESTING -->
<!--Dix testing-->
<!--(The UI is done and perfect, Backend is done and perfect.)-->
    <!--Just test out if there are any bugs and make sure its perfect-->
        <!--ESTIMATED TIME : 5-10 min-->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>History</title>
    {% include 'imports.html' %}

  </head>
  <body class="bg-background text-foreground">

    <!-- Franken UI -->

    <style>
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }
        .table-container {
            max-height: calc(100vh - 200px);
            overflow-y: visible;
        }
        
        .task-item {
            transition: transform 0.5s ease-out, opacity 0.5s ease-out;
        }
        .task-disappear {
            opacity: 0;
            transform: translateY(-20px);
        }
        .table-container thead {
            position: sticky;
            top: 0;
            z-index: 1;
            background-color: #fff;
        }
        .platform-tag {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: #d1fae5;
            color: #065f46;
        }
        .platform-tag-icon {
            width: 10px;
            height: 10px;
            margin-right: 4px;
            opacity: 0.7;
        }
        .task-disappear {
            animation: disappear 0.5s ease-out forwards;
        }
        @keyframes disappear {
            0% {
                opacity: 1;
                transform: translateY(0);
            }
            100% {
                opacity: 0;
                transform: translateY(-20px);
            }
        }
        .checked {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
        }
        .line-through {
            text-decoration: line-through;
        }
        .opacity-0 {
            opacity: 0;
            transition: opacity 0.5s ease-out;
        }
        .hidden {
            display: none;
        }
        .checked::after {
            content: '';
            display: block;
            position: absolute;
            width: 1.2em;
            height: 1.2em;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
        }
        #taskPopup {
            transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
            visibility: hidden;
            opacity: 0;
        }
        #taskPopup.show {
            visibility: visible;
            opacity: 1;
        }
        #taskPopup > div {
            transition: transform 0.3s ease-in-out;
            transform: scale(0.9);
        }
        #taskPopup.show > div {
            transform: scale(1);
        }
        
        .task-item:not(:last-child) {
            border-bottom: 1px solid #e4e4e7;
            padding-bottom: 16px;
            margin-bottom: 16px;
        }
        :root {
            --checkbox-border-color: #2a2a2d; /* or any other dark color you prefer */
        }
        .checkboxwomp.checked::after {
            content: '\2715'; /* Unicode for "×" */
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            color: white;
            font-size: 0.8em;
        }

        .checkboxwomp.disabled {
            pointer-events: none;
            opacity: 0.6;
        }

        .task-text.line-through {
            text-decoration: line-through;
            opacity: 0.7;
        }

        .checkboxwomp[data-state=unchecked] {
            border-color: var(--checkbox-border-color);
        }
        .task-text {
        margin-left: 10px; /* Add this line to create space between checkbox and text */
    }
    </style>
</head>
<body class="light">
    <div id="loading-overlay" class="loading-overlay">
        <div class="loader"></div>
    </div>

    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] overflow-hidden">
        {% include 'sidebar.html' %}

        <div class="flex flex-col">
            <header class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b px-6">
                <div class="flex items-center gap-4">
                    <a class="lg:hidden text-gray-600 hover:text-gray-800" href="#">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                            <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                            <path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1L21 9"></path>
                            <path d="M12 3v6"></path>
                        </svg>
                        <span class="sr-only">Home</span>
                    </a>
                    <h1 class="font-semibold text-lg ">History</h1>
                </div>

                {% include 'topright.html' %} <!-- Added the include here -->
            </header>
            <main class="card flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6">
                <main class="flex flex-1 flex-col">
                    <div class="border shadow-sm h-full rounded-lg overflow-hidden card">
                        <div class="relative w-full overflow-auto h-full">
                            <table class="w-full caption-bottom text-sm">
                            <thead class="[&amp;_tr]:border-b">
                                <tr class="card border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                                    <th class="h-11 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0">
                                        <div class="p-2 mr-auto">
                                            <div class="uk-inline w-1/2">
                                                <span class="uk-form-icon text-muted-foreground">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar">
                                                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                                        <line x1="16" y1="2" x2="16" y2="6"></line>
                                                        <line x1="8" y1="2" x2="8" y2="6"></line>
                                                        <line x1="3" y1="10" x2="21" y2="10"></line>
                                                    </svg>
                                                </span>
                                                <input id="searchInput" class="uk-input rounded-lg border border-gray-300 p-2 focus:border-blue-300" type="text" placeholder="Search.....">
                                            </div>
                                        </div>
                                    </th>
                                    <th class="h-12 px-1 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0">
                                        Platform
                                    </th>
                                    <th class="h-12 px-3 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0">
                                        Language
                                    </th>
                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0">
                                        Guest
                                    </th>
                                    <th class="h-12 px-7 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0">
                                        Phone
                                    </th>
                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0">
                                        Time
                                    </th>
                                    <th class="h-12 px-4 text-right align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="[&amp;_tr:last-child]:border-0" id="taskList">
                                <!-- Task elements will be dynamically generated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>

            <div id="taskPopup" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" max-width="400px">
                <div class="theme-card p-6 rounded-lg shadow-lg max-w-sm w-full">
                    <h2 class="theme-title text-xl font-semibold mb-4">Confirm Task Completion</h2>
                    <p id="popupTaskName" class="theme-text mb-2" style="max-width: 1000px;"></p>
                    <p id="popupTaskDescription" class="theme-text text-sm text-muted-foreground mb-6" style="max-width: 1000px;"></p>
                    <div class="flex justify-end">
                        <button id=confirmTask class="uk-button border card ">Confirm</button>
                    </div>
                </div>
            </div>
            
            

    <div id="notification" class="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-y-full opacity-0 flex items-center">
        <span id="notificationText" class="mr-4"></span>
        <button id="undoButton" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
            Undo
        </button>
    </div>
    
    <script>
        let currentTaskType = 'incomplete';
    
        // Function to fetch tasks from the Flask backend
        async function fetchTasks() {
            try {
                const endpoint = currentTaskType === 'incomplete' ? '/fetch-tasks' : '/fetch-completed-tasks';
                const response = await fetch(endpoint);
                const tasks = await response.json();
                return tasks;
            } catch (error) {
                console.error('Error fetching tasks:', error);
                return [];
            }
        }
    
        // Function to get the flag URL based on the language
        function getFlagUrl(language) {
            const flagUrls = {
                English: 'https://cdn-icons-png.flaticon.com/128/197/197374.png',
                Spanish: 'https://uxwing.com/wp-content/themes/uxwing/download/flags-landmarks/spain-country-flag-icon.png',
                France: 'https://cdn.countryflags.com/thumbs/france/flag-400.png',
                Italy: 'https://cdn-icons-png.flaticon.com/128/3373/3373278.png',
                Dutch: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQB3kPISjRFcQtqcQsUkpAi3GO4F7R528HflTkbojq7h_uT15o_omPAxGI-NQ&s',
                Swedish: 'https://uxwing.com/wp-content/themes/uxwing/download/flags-landmarks/sweden-flag-icon.png',
                German: 'https://uxwing.com/wp-content/themes/uxwing/download/flags-landmarks/germany-flag-icon.png',
                Indonesian: 'https://uxwing.com/wp-content/themes/uxwing/download/flags-landmarks/indonesia-flag-icon.png',
            };
            return flagUrls[language] || '';
        }
    
        function createTaskElement(task) {
            const taskElement = document.createElement('tr');
            taskElement.classList.add('card', 'border-b', 'transition-colors', 'hover:bg-muted/50', 'data-[state=selected]:bg-muted', 'task-item');
            taskElement.dataset.taskId = task.id;
    
            const platform = task.platform;
            const capitalizedPlatform = platform.charAt(0).toUpperCase() + platform.slice(1).toLowerCase();
    
            taskElement.innerHTML = `
                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">
                    <div class="flex items-center gap-2">
                        <div class="task-text">
                            <p class="text-base font-medium">${task.Task}</p>
                            <p class="text-sm text-muted-foreground">${task.description || ''}</p>
                        </div>
                    </div>
                </td>
                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">
                    ${platform.toLowerCase() === 'whatsapp' 
                        ? `<img src="https://cdn-icons-png.flaticon.com/128/1384/1384023.png" alt="WhatsApp" style="width: 24px; height: 24px;" title="WhatsApp" />`
                        : `<div class="platform-tag">${capitalizedPlatform}</div>`
                    }
                </td>
                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">
                    <img src="${getFlagUrl(task.language)}" alt="${task.language} Flag" style="width: 24px; height: 24px; margin-left: 10px;" title="${task.language}" />
                </td>
                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">
                    ${task.customer || 'N/A'}
                </td>
                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">
                    ${task.Phone}
                </td>
                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">
                    ${task.Time_stamp}
                </td>
                <td class="p-5 align-middle [&amp;:has([role=checkbox])]:pr-0 text-right">
                <button class="uk-icon-button uk-icon-button-small border-none " style="border: none !important; background-color: transparent !important; transition: none !important;">
                    <uk-icon icon="ellipsis-vertical"></uk-icon>
                </button>

                    <div class="uk-dropdown uk-drop border card" uk-dropdown="pos: bottom-right; mode: click">
                    <ul class="uk-dropdown-nav uk-nav">
                        <li><a href="#">Message User</a></li>
                        <li class="uk-nav-divider"></li>
                        <li><a href="#">Mark as invalid</a></li>
                    </ul>
                    </div>
                </td>
            `;
    
            // Add a data attribute with all searchable content
            const searchContent = `${task.Task} ${task.description || ''} ${task.customer || ''} ${task.Phone} ${task.Time_stamp}`.toLowerCase();
            taskElement.dataset.searchContent = searchContent;
    
            return taskElement;
        }
    
        async function updateTasks() {
            const tasks = await fetchTasks();
            const taskList = document.getElementById('taskList');
            taskList.innerHTML = ''; // Clear existing tasks
    
            tasks.reverse().forEach((task) => {
                const newTaskElement = createTaskElement(task);
                taskList.appendChild(newTaskElement);
            });
    
            // Apply the filter after updating the tasks
            filterTasks();
        }
    
        async function deleteTask(taskId) {
            try {
                const response = await fetch(`/delete-task/${taskId}`, {
                    method: 'DELETE',
                });
                if (!response.ok) {
                    throw new Error('Failed to delete task');
                }
                console.log('Task deleted successfully');
            } catch (error) {
                console.error('Error deleting task:', error);
            }
        }
    
        // Function for filtering tasks
        function filterTasks() {
            const searchInput = document.getElementById('searchInput');
            const filter = searchInput.value.toLowerCase();
            const taskItems = document.querySelectorAll('.task-item');
    
            taskItems.forEach(item => {
                const searchContent = item.dataset.searchContent;
                if (searchContent.includes(filter)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        }
    
        document.addEventListener('DOMContentLoaded', () => {
            const popup = document.getElementById('taskPopup');
            popup.addEventListener('click', (e) => {
                if (e.target === popup) {
                    popup.classList.remove('show');
                }
            });
    
            // Add event listener for the search input
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', filterTasks);
    
            // Add event listeners for task type options
            const taskTypeButton = document.getElementById('taskTypeButton');
            const incompleteTasksOption = document.getElementById('incompleteTasksOption');
            const completedTasksOption = document.getElementById('completedTasksOption');
    
            function closeDropdown() {
                UIkit.dropdown(taskTypeButton.nextElementSibling).hide(0);
            }
    
            incompleteTasksOption.addEventListener('click', (e) => {
                e.preventDefault();
                currentTaskType = 'incomplete';
                taskTypeButton.textContent = 'Incomplete Tasks';
                updateTasks();
                closeDropdown();
            });
    
            completedTasksOption.addEventListener('click', (e) => {
                e.preventDefault();
                currentTaskType = 'completed';
                taskTypeButton.textContent = 'Completed Tasks';
                updateTasks();
                closeDropdown();
            });
        });
    
        // Initial load and periodic update
        updateTasks();
        setInterval(updateTasks, 4000);
    </script>
</body>
</html>