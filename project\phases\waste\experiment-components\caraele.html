<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carousel Element</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #ffffff;
            font-family: 'Inter', sans-serif;
        }

        .carousel-container {
            position: relative;
            width: 318px;
            height: 338px;
        }

        .carousel {
            width: 100%;
            height: 100%;
            overflow: hidden;
            position: relative;
            border: 1px solid #e4e4e7;
            border-radius: 8px;
            background-color: #ffffff;
        }

        .carousel-inner {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.5s ease;
        }

        .carousel-item {
            min-width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .carousel-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .carousel-control {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 33px;
            height: 33px;
            background-color: #ffffff;
            border-radius: 50%;
            border: 1px solid #e4e4e7;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            z-index: 10;
            transition: background-color 0.1s ease;
        }

        .carousel-control:hover {
            background-color: #f4f4f5;
        }

        .carousel-control-prev {
            left: -52px; /* Changed from -18px to -52px (36px/2 + 16px + 18px) */
        }

        .carousel-control-next {
            right: -52px; /* Changed from -18px to -52px (36px/2 + 16px + 18px) */
        }

        .slide-indicator {
            position: absolute;
            bottom: -24px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: #71717a;
        }
    </style>
</head>
<body>
    <div class="carousel-container">
        <div class="carousel">
            <div class="carousel-inner">
                <div class="carousel-item">
                    <img src="https://images-new.vercel.app/roombooking/executivesuite.jpeg" alt="Executive Suite">
                </div>
                <div class="carousel-item">
                    <img src="https://images-new.vercel.app/roombooking/juniorsuite.jpeg" alt="Junior Suite">
                </div>
                <div class="carousel-item">
                    <img src="https://images-new.vercel.app/roombooking/juniorsuitedeluxe.jpeg" alt="Junior Suite Deluxe">
                </div>
                <div class="carousel-item">
                    <img src="https://images-new.vercel.app/roombooking/raid.jpeg" alt="Raid">
                </div>
            </div>
        </div>
        
        <div class="slide-indicator">Slide 1 of 4</div>
        
        <button class="carousel-control carousel-control-prev">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left">
                <path d="m12 19-7-7 7-7"/>
                <path d="M19 12H5"/>
            </svg>
        </button>
        
        <button class="carousel-control carousel-control-next">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right">
                <path d="M5 12h14"/>
                <path d="m12 5 7 7-7 7"/>
            </svg>
        </button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const carouselInner = document.querySelector('.carousel-inner');
            const items = document.querySelectorAll('.carousel-item');
            const prevBtn = document.querySelector('.carousel-control-prev');
            const nextBtn = document.querySelector('.carousel-control-next');
            const slideIndicator = document.querySelector('.slide-indicator');
            
            let currentIndex = 0;
            const totalItems = items.length;
            
            // Update carousel position
            function updateCarousel() {
                carouselInner.style.transform = `translateX(-${currentIndex * 100}%)`;
                slideIndicator.textContent = `Slide ${currentIndex + 1} of ${totalItems}`;
            }
            
            // Event listeners
            prevBtn.addEventListener('click', () => {
                currentIndex = (currentIndex > 0) ? currentIndex - 1 : totalItems - 1;
                updateCarousel();
            });
            
            nextBtn.addEventListener('click', () => {
                currentIndex = (currentIndex < totalItems - 1) ? currentIndex + 1 : 0;
                updateCarousel();
            });
        });
    </script>
</body>
</html>
