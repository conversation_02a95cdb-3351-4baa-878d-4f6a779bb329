import requests

# Hardcoded JSON data
beverage_data = {
    "action": "beverage_order",
    "Name": "<PERSON>",
    "Number": "+919398760681",
    "description": "<PERSON><PERSON><PERSON><PERSON>, Spain - Prieto <PERSON>",
    "Room_number": "101",
    "bev_order_msg": "Room '101' ordered Puro Rose",
    "Platform": "Whatsapp",
    "Language": "English",
    "Time": "10:30 PM",
    "outside_bizz_hours": False
}

spa_data = {
    "action": "spa_order",
    "Name": "<PERSON>",
    "Number": "+919398760681",
    "description": "Palacio <PERSON> Marqués Spa session",
    "Room_number": "101",
    "spa_order_msg": "Room '101' booked a spa session",
    "Platform": "Whatsapp",
    "Language": "English",
    "Time": "10:30 PM",
    "outside_bizz_hours": False
}

massage_data = {
    "action": "massage_order",
    "Name": "<PERSON>",
    "Number": "+919398760681",
    "description": "Masaje corporal <PERSON><PERSON><PERSON><PERSON> (90 min)",
    "Room_number": "101",
    "massage_order_msg": "Room '101' booked a massage session",
    "price": "195",
    "Platform": "Whatsapp",
    "Language": "English",
    "Time": "10:30 PM",
    "outside_bizz_hours": False
}


# Webhook URL
webhook_url = "https://951cnsfw-5678.inc1.devtunnels.ms/webhook-test/8f85be30-72cc-43aa-8225-fe5bf7465bbe"

# Send GET request with the hardcoded data as parameters
try:
    response = requests.get(webhook_url, params=massage_data)
    
    # Check if the request was successful
    if response.status_code == 200:
        print("Webhook notification sent successfully!")
        print("Response:", response.text)
    else:
        print(f"Failed to send webhook. Status code: {response.status_code}")
        print("Response:", response.text)
        
except requests.exceptions.RequestException as e:
    print(f"Error sending webhook: {e}")