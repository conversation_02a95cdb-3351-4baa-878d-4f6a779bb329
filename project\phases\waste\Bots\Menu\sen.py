import requests

# Disable SSL certificate verification (only for testing purposes)
# Note: In production, you should properly handle SSL certificates
requests.packages.urllib3.disable_warnings()

def send_test_request():
    url = "https://3.110.55.121/webhook-test/233ab3da-d8c4-4c64-96af-e6270f07c3f9"
    
    try:
        # Send GET request, disable SSL verification
        response = requests.get(url, verify=False)
        
        # Print response details
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
    except requests.exceptions.RequestException as e:
        print(f"Error occurred: {e}")

if __name__ == "__main__":
    send_test_request()