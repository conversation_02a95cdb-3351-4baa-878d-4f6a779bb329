import requests
import json
import os

# Twilio account details (replace with actual Account SID and Auth Token)
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')

# Twilio API endpoint - !!! IMPORTANT: UPDATE THE SID (HX...) AFTER RUNNING startercaravariable.py !!!
url = 'https://content.twilio.com/v1/Content/HX50460e90855bb0fd7417eccb26e4e63a/ApprovalRequests/whatsapp' # Replace HX... with the new SID

# Data payload to be sent
payload = {
    "name": "experiences_carousel",  # Match the friendly_name in the template script
    "category": "UTILITY"
}

# Making the POST request
response = requests.post(
    url,
    auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN),
    headers={'Content-Type': 'application/json'},
    data=json.dumps(payload)
)

# Check the response status
if response.status_code == 200:
    print("Request successful!")
    print(response.json())
else:
    print(f"Request failed with status: {response.status_code}")
    print(response.text)
    print(response.json())