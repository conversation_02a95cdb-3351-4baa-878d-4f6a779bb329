<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
  <title>Development Components</title>
  {% include 'imports.html' %}
  <style>
    .component-section {
      margin-bottom: 40px;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid var(--border-color);
    }
    
    .component-title {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }
    
    .component-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
    }

    .component-item {
      padding: 15px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
    }

    .component-item-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 10px;
      color: var(--muted-foreground);
    }
  </style>
</head>
<body>
  <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr]">
    {% include 'sidebar.html' %}
    <div class="flex flex-1 flex-col">
      <header class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
        <div class="flex items-center gap-2 px-4 pl-0">
          <button id="toggle-btn" style="margin-left: 8px;" class="opacity-100 transition-opacity duration-300 focus:outline-none" style="background-color: transparent !important;">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-panel-left">
              <rect width="18" height="18" x="3" y="3" rx="2"></rect>
              <path d="M9 3v18"></path>
            </svg>
          </button>
          <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-2 h-4" style="background-color: var(--border-color);"></div>
          <nav aria-label="breadcrumb">
            <ol class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
              <li class="inline-flex items-center gap-1.5"><a href="#" class="transition-colors hover:text-foreground">Components</a></li>
              <li role="presentation" aria-hidden="true" class="[&amp;>svg]:w-3.5 [&amp;>svg]:h-3.5">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </li>
              <li class="inline-flex items-center gap-1.5"><span role="link" aria-disabled="true" aria-current="page" class="font-normal text-foreground">Development</span></li>
            </ol>
          </nav>
        </div>
        {% include 'topright.html' %}
      </header>

      <main class="flex-1 p-4 md:p-6 overflow-auto">
        <h1 class="text-2xl font-bold mb-6">UI Component Library</h1>
        
        <!-- Buttons Section -->
        <section class="component-section card">
          <h2 class="component-title">Buttons</h2>
          <div class="component-grid">
            <div class="component-item">
              <h3 class="component-item-title">Primary Button</h3>
              <button class="uk-button uk-button-primary">Update profile</button>
            </div>
            <div class="component-item">
              <h3 class="component-item-title">Default Button</h3>
              <button class="uk-button uk-button-default mr-2 card universal-hover">Cancel</button>
            </div>
            <div class="component-item">
              <h3 class="component-item-title">Danger Button</h3>
              <button class="uk-button uk-button-danger">Delete Template</button>
            </div>
            <div class="component-item">
              <h3 class="component-item-title">Icon Button</h3>
              <button class="uk-button uk-button-default flex items-center justify-center card universal-hover"
                style="background-color: transparent; padding: 0 20px; height: 36px; line-height: 30px; width: 160px;">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="lucide lucide-user-plus mr-2">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <line x1="19" x2="19" y1="8" y2="14" />
                  <line x1="22" x2="16" y1="11" y2="11" />
                </svg>
                <span style="font-size: 14px; white-space: nowrap;">Assign Chat</span>
              </button>
            </div>
          </div>
        </section>

        <!-- Dropdowns Section -->
        <section class="component-section card">
          <h2 class="component-title">Dropdowns</h2>
          <div class="component-grid">
            <div class="component-item">
              <h3 class="component-item-title">Basic Dropdown</h3>
              <div class="relative">
                <button type="button" id="productTagToggler"
                  class="w-full flex items-center justify-between border border-gray-300 bg-background text-foreground card rounded-md py-2 px-3"
                  style="background-color: transparent;" uk-toggle="target: #productTagDropdown">
                  <span>Select Option</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                <div id="productTagDropdown" uk-dropdown="mode: click; pos: bottom-left; offset: 10" class="card w-full">
                  <ul class="uk-nav uk-dropdown-nav card">
                    <li><a href="#">Option 1</a></li>
                    <li><a href="#">Option 2</a></li>
                    <li><a href="#">Option 3</a></li>
                    <li><a href="#">Option 4</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="component-item">
              <h3 class="component-item-title">Dropdown with Details</h3>
              <div class="relative">
                <div class="theme-responsive-button card theme-dropdown flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer universal-hover">
                  <span>Select Permissions</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
                <div class="uk-drop uk-dropdown w-[252px] hidden themer-icon" uk-drop="mode: click; pos: bottom-right">
                  <ul class="uk-dropdown-nav">
                    <li class="uk-nav-divider"></li>
                    <li>
                      <a class="uk-drop-close" href="#demo" role="button">
                        <div>
                          <div class="dropdown-option-3">Viewer</div>
                          <div class="text-sm text-muted-foreground">Can view and comment.</div>
                        </div>
                      </a>
                    </li>
                    <li>
                      <a class="uk-drop-close" href="#demo" role="button">
                        <div>
                          <div class="dropdown-option-3">Developer</div>
                          <div class="text-sm text-muted-foreground">Can view, comment and edit.</div>
                        </div>
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Form Elements Section -->
        <section class="component-section card">
          <h2 class="component-title">Form Elements</h2>
          <div class="component-grid">
            <div class="component-item">
              <h3 class="component-item-title">Text Input</h3>
              <input class="uk-input card w-full rounded-md focus:outline-none focus:border-blue-500 message-input" type="text" placeholder="Enter text here">
            </div>
            <div class="component-item">
              <h3 class="component-item-title">Textarea</h3>
              <textarea class="uk-input card w-full rounded-md focus:outline-none focus:border-blue-500 message-input" style="min-height: 120px; resize: none;" placeholder="Enter your message here..."></textarea>
            </div>
            <div class="component-item">
              <h3 class="component-item-title">Checkbox</h3>
              <label class="block text-sm" for="display_0">
                <input class="uk-checkbox mr-2" type="checkbox"> Option Label
              </label>
            </div>
          </div>
        </section>

        <!-- Cards Section -->
        <section class="component-section card">
          <h2 class="component-title">Cards</h2>
          <div class="component-grid">
            <div class="component-item">
              <h3 class="component-item-title">Simple Card</h3>
              <div class="rounded-lg border p-4 w-full">
                <div class="flex justify-between items-center w-full">
                  <div class="text-lg font-semibold">Card Title</div>
                </div>
                <p class="mt-2 text-sm text-muted-foreground">This is a simple card with basic content.</p>
              </div>
            </div>
            <div class="component-item">
              <h3 class="component-item-title">Product Card</h3>
              <div class="carousel-item">
                <img src="https://images-new.vercel.app/foodmenu/burrata.png" alt="Burrata" style="max-width: 100%;">
                <div class="product-info">
                  <h3 class="mt-1 text-sm">Burrata</h3>
                  <p class="mt-1 text-lg font-medium">$25</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Carousel Section -->
        <section class="component-section card">
          <h2 class="component-title">Carousel</h2>
          <div>
            <div class="carousel-container" style="height: 200px;">
              <div class="carousel">
                <div class="carousel-inner">
                  <!-- Place items here -->
                </div>
              </div>

              <div class="slide-indicator">Slide 1 of 3</div>

              <button class="carousel-control carousel-control-prev card">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="lucide lucide-arrow-left">
                  <path d="m12 19-7-7 7-7" />
                  <path d="M19 12H5" />
                </svg>
              </button>

              <button class="carousel-control carousel-control-next card">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="lucide lucide-arrow-right">
                  <path d="M5 12h14" />
                  <path d="m12 5 7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
        </section>

        <!-- Avatar/User Components -->
        <section class="component-section card">
          <h2 class="component-title">User Components</h2>
          <div class="component-grid">
            <div class="component-item">
              <h3 class="component-item-title">User Card</h3>
              <div class="flex items-center space-x-4">
                <span class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full bg-accent">
                  <img class="aspect-square h-full w-full" src="https://ui.shadcn.com/avatars/01.png">
                </span>
                <div class="flex-1">
                  <p class="text-sm font-medium leading-none">Dixith Mediga</p>
                  <p class="text-sm"><EMAIL></p>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Navigation Components -->
        <section class="component-section card">
          <h2 class="component-title">Navigation</h2>
          <div class="component-grid">
            <div class="component-item">
              <h3 class="component-item-title">Menubar</h3>
              <div class="menubar" role="menubar">
                <div class="menubar-indicator"></div>
                <a href="#" role="menuitem">Option 1</a>
                <a href="#" role="menuitem" class="active">Option 2</a>
                <a href="#" role="menuitem">Option 3</a>
              </div>
            </div>
          </div>
        </section>

        <!-- Modal Example -->
        <section class="component-section card">
          <h2 class="component-title">Modal</h2>
          <div>
            <button id="showModalBtn" class="uk-button uk-button-primary">Show Modal</button>
            
            <div id="sampleModal" style="display: none;" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div class="card universal-background-color rounded-lg shadow-lg w-full max-w-md z-10 relative border border-gray-300">
                <div class="flex justify-between items-center p-4 border-b card">
                  <h3 class="text-lg font-medium">Sample Modal</h3>
                  <button id="closeModalBtn" style="background-color: transparent; height: 34px; border: none; cursor: pointer;" class="flex items-center justify-center card" aria-label="Close Modal">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>
                </div>
                <div class="p-4">
                  <p>This is a sample modal content.</p>
                </div>
                <div class="flex justify-end p-4 border-t card">
                  <button id="cancelModal" class="uk-button uk-button-default mr-2 card universal-hover" style="border-radius: 8px; height: 36px; line-height: 30px;">Cancel</button>
                  <button id="confirmModal" class="uk-button uk-button-primary" style="border-radius: 8px; height: 36px; line-height: 30px;">Confirm</button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Modal functionality
      const showModalBtn = document.getElementById('showModalBtn');
      const closeModalBtn = document.getElementById('closeModalBtn');
      const cancelModal = document.getElementById('cancelModal');
      const sampleModal = document.getElementById('sampleModal');

      showModalBtn.addEventListener('click', function() {
        sampleModal.style.display = 'flex';
      });

      function closeModal() {
        sampleModal.style.display = 'none';
      }

      closeModalBtn.addEventListener('click', closeModal);
      cancelModal.addEventListener('click', closeModal);
      
      // Close when clicking outside the modal
      sampleModal.addEventListener('click', function(e) {
        if (e.target === sampleModal) {
          closeModal();
        }
      });
    });
  </script>
</body>
</html>