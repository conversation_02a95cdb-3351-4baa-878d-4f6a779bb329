<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guest Genius</title>
    {% include 'imports.html' %}
    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://preline.co/assets/js/hs-apexcharts-helpers.js"></script>
    <link rel="stylesheet" href="../static/styles/totalsales.css">

    <style>
        body {
            visibility: hidden;
        }

        #chartdiv {
            width: 100%;
            height: 550px;
        }

        svg {
            display: block;
        }

        .chart-container {
            position: relative;
            margin-top: 20px;
            height: 400px;
            /* Increased height */
            width: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            /* Aligns content to bottom */
        }

        .table-container {
            max-height: 640px;
            overflow-y: auto;
        }

        .table-container thead {
            position: sticky;
            top: 0;
            z-index: 1;
        }

        .icon {
            margin-top: 1px;
        }

        .platform-icon {
            margin-top: 1px;
        }

        .table-container {
            max-height: 640px;
            overflow-y: auto;
            scrollbar-width: none;
            /* For Firefox */
            -ms-overflow-style: none;
            /* For Internet Explorer and Edge */
        }

        .table-container::-webkit-scrollbar {
            display: none;
            /* For Chrome, Safari and Opera */
        }

        .table-container thead {
            position: sticky;
            top: 0;
            z-index: 1;
        }

        /* Remove scrollbars */
        .table-container {
            max-height: 640px;
            overflow-y: hidden;
            /* Disable vertical scrolling */
        }

        .table-container::-webkit-scrollbar {
            display: none;
            /* Hide scrollbar for Chrome, Safari, and Opera */
        }

        .table-container {
            scrollbar-width: none;
            /* Hide scrollbar for Firefox */
            -ms-overflow-style: none;
            /* Hide scrollbar for IE and Edge */
        }
    </style>
    <style>
        .icon,
        .platform-icon {
            margin-top: 1px;
            border: 2px solid black;
            /* Increase border width */
            box-shadow: none;
            /* Remove any shadow that might blur the border */
        }
    </style>
    <style>
        .ggchart-dot {
            fill: #fff !important;
            stroke: #2662d9 !important;
            stroke-width: 3px !important;
        }
    </style>

    <style>
        .ggchart-dot {
            stroke: #2662d9 !important;
            stroke-width: 3px !important;
        }
    </style>
    <style>
        /* Force tooltip legend square to blue */
        .ggchart-tooltip .ggchart-tooltip-value span,
        .ggchart-tooltip .ggchart-tooltip-value div,
        .ggchart-tooltip .ggchart-tooltip-value::before {
            background: #2662d9 !important;
            border-radius: 2px !important;
            border: none !important;
        }
    </style>
    <style>
        .ggchart-line {
            stroke: #2662d9 !important;
        }

        .ggchart-dot {
            fill: #2662d9 !important;
        }
    </style>
    <style>
        .metric-card.dragging {
            opacity: 0.5;
            border: 2px dashed #a0aec0; /* A neutral dashed border */
        }
    </style>
    <style>
        .analytics-card.dragging {
            opacity: 0.5;
            border: 2px dashed #a0aec0; /* A neutral dashed border */
        }
    </style>

</head>

<body class="light">

    <!-- Franken UI -->
    {% include 'components/loading.html' %}
    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] ">
        {% include 'sidebar.html' %}
        <div class="flex flex-col">
            <header
                class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
                <div style="margin-left: 8px;" class="flex items-center gap-2 px-4 pl-0">
                    <button id="toggle-btn" class="opacity-100 transition-opacity duration-300 focus:outline-none"
                        style="background-color: transparent !important;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-panel-left">
                            <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                            <path d="M9 3v18"></path>
                        </svg>
                    </button>
                    <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-3 h-4"
                        style="background-color: var(--border-color);"></div>
                    <nav aria-label="breadcrumb">
                        <ol
                            class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
                            <div class="menubar" role="menubar">
                                <div class="menubar-indicator"></div>
                                <a href="/sales" role="menuitem">Sales Report</a>
                                <a href="/pmsanalytics" role="menuitem">PMS Analytics</a>
                                <a href="/googleana" role="menuitem" class="active">Google Analytics</a>
                                <a href="/status" role="menuitem">Status Report</a>
                            </div>
                        </ol>
                    </nav>
                </div>
                {% include 'topright.html' %}
            </header>
            <main class="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6">
                <div id="metric-cards-container" class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <div class="metric-card card relative overflow-hidden rounded-lg border ground shadow-sm" data-v0-t="card" draggable="true"
                        data-metric="revenue">
                        <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Direct Booking Revenue</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-badge-euro">
                                <path
                                    d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z" />
                                <path d="M7 12h5" />
                                <path d="M15 9.4a4 4 0 1 0 0 5.2" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div id="total-sales" class="text-2xl font-bold">€0.00</div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Revenue from Direct Bookings</p>
                            <div
                                class="absolute bottom-7 right-4 flex items-center text-xs text-green-600 border border-green-500 bg-transparent px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-arrow-up-narrow-wide mr-0.5">
                                    <path d="m3 8 4-4 4 4" />
                                    <path d="M7 4v16" />
                                    <path d="M11 12h4" />
                                    <path d="M11 16h7" />
                                    <path d="M11 20h10" />
                                </svg>
                                8.5%
                            </div>
                        </div>
                        <div class="absolute bottom-6 right-7 w-28 h-20">

                        </div>
                        <script>
                            // Store historical data for each metric
                            const metricHistory = {
                                revenue: [],
                                tips: [],
                                reviews: [],
                                sales: []
                            };

                            // Maximum number of historical data points to store
                            const MAX_HISTORY = 5;

                            // Function to generate SVG path from data array
                            function generateSVGPath(dataArray) {
                                if (!dataArray || dataArray.length === 0) {
                                    // Default path if no data
                                    return "M0 100 L20 70 L40 50 L60 40 L80 30 L100 20";
                                }

                                // Normalize the data to fit within the SVG viewBox (0-100)
                                const normalizedData = normalizeData(dataArray);

                                // Build the path data string
                                let pathData = "M0 " + (100 - normalizedData[0]);

                                // Calculate points evenly distributed across the x-axis
                                const xStep = 100 / (normalizedData.length - 1);
                                for (let i = 1; i < normalizedData.length; i++) {
                                    const x = xStep * i;
                                    const y = 100 - normalizedData[i]; // Invert y since SVG y=0 is at the top
                                    pathData += " L" + x + " " + y;
                                }

                                return pathData;
                            }

                            // Function to normalize data values to fit within 0-100 range
                            function normalizeData(dataArray) {
                                // Find min and max values
                                const min = Math.min(...dataArray);
                                const max = Math.max(...dataArray);

                                if (min === max) {
                                    // If all values are the same, create a flat line at 50%
                                    return dataArray.map(() => 50);
                                }

                                // Scale to 10-90 range to ensure visibility within the viewBox
                                return dataArray.map(value => {
                                    const normalized = ((value - min) / (max - min)) * 80 + 10;
                                    return normalized;
                                });
                            }

                            // Function to update the chart for a specific metric
                            function updateChart(metric, value) {
                                // Add new value to history
                                if (!metricHistory[metric]) {
                                    metricHistory[metric] = [];
                                }

                                metricHistory[metric].push(value);

                                // Keep only the most recent MAX_HISTORY entries
                                if (metricHistory[metric].length > MAX_HISTORY) {
                                    metricHistory[metric].shift();
                                }

                                // Generate path data from the history
                                const pathData = generateSVGPath(metricHistory[metric]);

                                // Update line and area paths
                                const linePath = document.getElementById(metric + 'Line');
                                const areaPath = document.getElementById(metric + 'Area');

                                if (linePath && areaPath) {
                                    linePath.setAttribute('d', pathData);
                                    areaPath.setAttribute('d', pathData + " L100 100 L0 100 Z");
                                }
                            }

                            // Predefined fixed patterns for each metric to ensure consistency
                            const fixedPatterns = {
                                revenue: [30, 50, 80, 65, 95],
                                tips: [40, 60, 45, 70, 85],
                                reviews: [20, 40, 60, 50, 75],
                                sales: [55, 65, 45, 70, 60]
                            };

                            // Function to initialize charts with fixed patterns
                            function generateInitialData() {
                                // Use fixed patterns instead of random data
                                Object.keys(fixedPatterns).forEach(metric => {
                                    // Use the predefined patterns for consistent visualization
                                    metricHistory[metric] = [...fixedPatterns[metric]];

                                    // Update chart with fixed data
                                    updateChart(metric, metricHistory[metric][metricHistory[metric].length - 1]);
                                });
                            }

                            // Function to update charts with real data
                            function updateChartsWithRealData() {
                                fetch('/firstrowsales')
                                    .then(response => {
                                        if (!response.ok) {
                                            throw new Error(`HTTP error! Status: ${response.status}`);
                                        }
                                        return response.json();
                                    })
                                    .then(data => {
                                        if (data && data.length > 0) {
                                            const totalSales = parseFloat(data[0].total_sales) || 0;
                                            const totalTips = parseFloat(data[0].total_tips) || 0;
                                            const totalCustomers = parseFloat(data[0].total_customers) || 0;
                                            // Use total_sales again for the fourth metric if no specific value exists
                                            const totalCount = parseFloat(data[0].total_count || data[0].total_sales) || 0;

                                            // Update charts with new data
                                            updateChart('revenue', totalSales);
                                            updateChart('tips', totalTips);
                                            updateChart('reviews', totalCustomers);
                                            updateChart('sales', totalCount);

                                            // Update the displayed values (assuming you have elements with these IDs)
                                            if (document.getElementById('total-sales')) {
                                                document.getElementById('total-sales').textContent = `€${totalSales.toFixed(2)}`;
                                            }
                                            if (document.getElementById('total-tips')) {
                                                document.getElementById('total-tips').textContent = `+${totalTips}`;
                                            }
                                            if (document.getElementById('total-customers')) {
                                                document.getElementById('total-customers').textContent = `+${totalCustomers}`;
                                            }
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error fetching data:', error);

                                        // On error, reset charts to fixed patterns
                                        Object.keys(fixedPatterns).forEach(metric => {
                                            updateChart(metric, fixedPatterns[metric][fixedPatterns[metric].length - 1]);
                                        });
                                    });
                            }

                            // Initialize charts when the page loads
                            document.addEventListener('DOMContentLoaded', function () {
                                // Generate initial visualization
                                generateInitialData();

                                // Update with real data immediately
                                updateChartsWithRealData();

                                // Set interval to update charts periodically
                                setInterval(updateChartsWithRealData, 60000); // Update every minute
                            });
                        </script>
                    </div>
                    <!-- Booking Conversion Rate Card -->
                    <div class="metric-card card relative overflow-hidden rounded-lg border ground shadow-sm" data-v0-t="card" draggable="true"
                        data-metric="sales">
                        <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Booking Conversion Rate
                            </h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-chart-spline">
                                <path d="M3 3v16a2 2 0 0 0 2 2h16" />
                                <path d="M7 16c.5-2 1.5-7 4-7 2 0 2 3 4 3 2.5 0 4.5-5 5-7" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div id="booking-conversion-rate-value" class="text-2xl font-bold">0%</div>
                            <!-- Changed ID and added % -->
                            <p class="text-xs text-gray-500 dark:text-gray-400">Conversion Rate for All Time</p>
                            <div
                                class="absolute bottom-7 right-4 flex items-center text-xs text-red-600 border border-red-500  px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-arrow-down-narrow-wide mr-0.5">
                                    <path d="m3 16 4 4 4-4" />
                                    <path d="M7 20V4" />
                                    <path d="M11 4h4" />
                                    <path d="M11 8h7" />
                                    <path d="M11 12h10" />
                                </svg>
                                3.2%
                            </div>
                        </div>
                        <!-- Gradient Chart Overlay with Smoother Curve -->
                        <div class="absolute bottom-6 right-7 w-28 h-20">
                        </div>
                    </div>
                    <!-- Total Tips Card -->
                    <div class="metric-card card relative overflow-hidden rounded-lg border ground shadow-sm" data-v0-t="card" draggable="true"
                        data-metric="tips">
                        <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Website Visitors</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-notepad-text">
                                <path d="M8 2v4" />
                                <path d="M12 2v4" />
                                <path d="M16 2v4" />
                                <rect width="16" height="18" x="4" y="4" rx="2" />
                                <path d="M8 10h6" />
                                <path d="M8 14h8" />
                                <path d="M8 18h5" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div id="website-visitors-value" class="text-2xl font-bold">+0</div> <!-- Changed ID -->
                            <p class="text-xs text-gray-500 dark:text-gray-400">Total Website Visitors (All Time)</p>
                            <div
                                class="absolute bottom-7 right-4 flex items-center text-xs text-red-600 border border-red-500  px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-arrow-down-narrow-wide mr-0.5">
                                    <path d="m3 16 4 4 4-4" />
                                    <path d="M7 20V4" />
                                    <path d="M11 4h4" />
                                    <path d="M11 8h7" />
                                    <path d="M11 12h10" />
                                </svg>
                                53.2%
                            </div>
                        </div>
                        <div class="absolute bottom-6 right-7 w-28 h-20">

                        </div>
                    </div>
                    <div class="metric-card card relative overflow-hidden rounded-lg border ground shadow-sm" data-v0-t="card" draggable="true"
                        data-metric="reviews">
                        <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Average Session Duration
                            </h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-scan-search">
                                <path d="M3 7V5a2 2 0 0 1 2-2h2" />
                                <path d="M17 3h2a2 2 0 0 1 2 2v2" />
                                <path d="M21 17v2a2 2 0 0 1-2 2h-2" />
                                <path d="M7 21H5a2 2 0 0 1-2-2v-2" />
                                <circle cx="12" cy="12" r="3" />
                                <path d="m16 16-1.9-1.9" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div id="avg-session-duration-value" class="text-2xl font-bold">0m 0s</div>
                            <!-- Changed ID and format -->
                            <p class="text-xs text-gray-500 dark:text-gray-400">Avg. Session Duration</p>
                            <div
                                class="absolute bottom-7 right-4 flex items-center text-xs text-green-600 border border-green-500 bg-transparent px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-arrow-up-narrow-wide mr-0.5">
                                    <path d="m3 8 4-4 4 4" />
                                    <path d="M7 4v16" />
                                    <path d="M11 12h4" />
                                    <path d="M11 16h7" />
                                    <path d="M11 20h10" />
                                </svg>
                                8.5%
                            </div>
                        </div>
                        <div class="absolute bottom-6 right-7 w-28 h-20">

                        </div>
                    </div>
                </div>
                <script>
                    document.addEventListener('DOMContentLoaded', function () {
                        const salesChartCanvas = document.getElementById('salesOverviewChart');
                        const worldMapDiv = document.getElementById('chartdiv');
                        const showBarChartLink = document.getElementById('show-bar-chart');
                        const showWorldMapLink = document.getElementById('show-world-map');

                        showBarChartLink.addEventListener('click', function (e) {
                            e.preventDefault();
                            salesChartCanvas.style.display = 'block';
                            worldMapDiv.style.display = 'none';
                        });

                        showWorldMapLink.addEventListener('click', function (e) {
                            e.preventDefault();
                            salesChartCanvas.style.display = 'none';
                            worldMapDiv.style.display = 'flex'; // Changed to flex for the layout
                        });

                        am5.ready(function () {
                            // Create root element
                            var root = am5.Root.new("mapdiv");

                            // Disable amCharts logo
                            root._logo.dispose();

                            // Set themes
                            root.setThemes([am5themes_Animated.new(root)]);

                            // Create the map chart
                            var chart = root.container.children.push(
                                am5map.MapChart.new(root, {
                                    panX: "none",
                                    panY: "none",
                                    projection: am5map.geoMercator(),
                                    wheelable: false,
                                    wheelX: "none",
                                    wheelY: "none",
                                    pinchZoom: false,
                                    maxZoomLevel: 1,
                                    minZoomLevel: 1,
                                    maxPanOut: 0,
                                    draggable: false,
                                    centerMapOnZoomOut: true
                                })
                            );

                            // Disable zoom control completely
                            chart.set("zoomControl", false);

                            var polygonSeries = chart.series.push(
                                am5map.MapPolygonSeries.new(root, {
                                    geoJSON: am5geodata_worldLow,
                                    exclude: ["AQ"]
                                })
                            );

                            polygonSeries.mapPolygons.template.setAll({
                                fill: am5.color(0xd0d1d5) // Lighter gray color for better visibility
                            });

                            var pointSeries = chart.series.push(am5map.ClusteredPointSeries.new(root, {}));
                            pointSeries.set("clusteredBullet", function (root) {
                                var container = am5.Container.new(root, {
                                    cursorOverStyle: "pointer"
                                });
                                var circle1 = container.children.push(am5.Circle.new(root, {
                                    radius: 8,
                                    tooltipY: 0,
                                    fill: am5.color(0x1e40af)  // Blue color
                                }));
                                var circle2 = container.children.push(am5.Circle.new(root, {
                                    radius: 12,
                                    fillOpacity: 0.3,
                                    tooltipY: 0,
                                    fill: am5.color(0x1e40af)  // Blue color
                                }));
                                var circle3 = container.children.push(am5.Circle.new(root, {
                                    radius: 16,
                                    fillOpacity: 0.3,
                                    tooltipY: 0,
                                    fill: am5.color(0x1e40af)  // Blue color
                                }));
                                var label = container.children.push(am5.Label.new(root, {
                                    centerX: am5.p50,
                                    centerY: am5.p50,
                                    fill: am5.color(0xffffff),
                                    populateText: true,
                                    fontSize: "8",
                                    text: "{value}"
                                }));
                                container.events.on("click", function (e) {
                                    pointSeries.zoomToCluster(e.target.dataItem);
                                });
                                return am5.Bullet.new(root, {
                                    sprite: container
                                });
                            });

                            pointSeries.bullets.push(function () {
                                var circle = am5.Circle.new(root, {
                                    radius: 6,
                                    tooltipY: 0,
                                    fill: am5.color(0x1e40af),  // Blue color
                                    tooltipText: "{title}: {value}"
                                });
                                return am5.Bullet.new(root, {
                                    sprite: circle
                                });
                            });

                            // Language regions data with strategic geographical placement
                            var languageRegions = [
                                { title: "German", code: "de", latitude: 51.1657, longitude: 10.4515, value: 4400 },
                                { title: "French", code: "fr", latitude: 46.2276, longitude: 2.2137, value: 3600 },
                                { title: "Italian", code: "it", latitude: 41.8719, longitude: 12.5674, value: 3100 },
                                { title: "English", code: "us", latitude: 40.7128, longitude: -95.0060, value: 2900 },
                                { title: "Swedish", code: "se", latitude: 62.1282, longitude: 15.6435, value: 2700 },
                                { title: "Spanish", code: "es", latitude: 40.4637, longitude: -3.7492, value: 1200 }
                            ];

                            languageRegions.forEach(region => {
                                pointSeries.data.push({
                                    geometry: { type: "Point", coordinates: [region.longitude, region.latitude] },
                                    title: region.title,
                                    value: region.value,
                                    tooltipHTML: `<div style="display: flex; align-items: center;">
                                                    <img src="https://flagcdn.com/24x18/${region.code}.png" alt="${region.title} flag" style="margin-right: 8px;">
                                                    <span>${region.title}: ${region.value}</span>
                                                  </div>`
                                });
                            });

                            chart.appear(1000, 100);
                        });
                    });
                </script>
                <div> <!-- Removed grid classes -->
                    <div class="rounded-lg shadow-sm w-full h-fit card" data-v0-t="card">
                        <!-- Removed col-span, order-last, min-w; added w-full -->
                        <div class="ggchart-container border card">
                            <div class="ggchart-header border-b card pr-0">
                                <div class="flex justify-between items-center w-full py-3">
                                    <div class="ggchart-title">
                                        <h2
                                            class="whitespace-nowrap text-2xl font-semibold leading-none tracking-tight">
                                            Direct Bookings Over Time</h2>
                                        <p class="text-sm text-muted-foreground mt-2">Showing direct bookings for the
                                            last
                                            <span id="time-range-text"> 30 days</span>
                                        </p>
                                    </div>
                                    <div class="relative pr-4">
                                        <button id="time-range-btn" class="card uk-button border default" type="button"
                                            uk-toggle="target: #time-range-dropdown">
                                            <svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 6px;"
                                                width="16" height="16" viewBox="0 0 24 24" fill="none"
                                                stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round" class="lucide lucide-calendar-days">
                                                <path d="M8 2v4" />
                                                <path d="M16 2v4" />
                                                <rect width="18" height="18" x="3" y="4" rx="2" />
                                                <path d="M3 10h18" />
                                                <path d="M8 14h.01" />
                                                <path d="M12 14h.01" />
                                                <path d="M16 14h.01" />
                                                <path d="M8 18h.01" />
                                                <path d="M12 18h.01" />
                                                <path d="M16 18h.01" />
                                            </svg>
                                            Time Range
                                        </button>
                                        <!-- Dropdown Menu -->
                                        <div class="card dropdown-content" id="time-range-dropdown"
                                            uk-dropdown="mode: click; pos: bottom-right; offset: 10">
                                            <ul class="uk-nav uk-dropdown-nav card">
                                                <li><a href="#" class="time-range-option" data-range="7">Last 7 days</a>
                                                </li>
                                                <li><a href="#" class="time-range-option" data-range="14">Last 14
                                                        days</a></li>
                                                <li><a href="#" class="time-range-option" data-range="30">Last 30
                                                        days</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ggchart-body" id="chart">
                                <div class="ggchart-tooltip">
                                    <div class="ggchart-tooltip-date"></div>
                                    <div class="ggchart-tooltip-value"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            const timeRangeBtn = document.getElementById('time-range-btn');
                            const timeRangeText = document.getElementById('time-range-text');
                            const timeRangeOptions = document.querySelectorAll('.time-range-option');
                            const timeRangeDropdown = document.getElementById('time-range-dropdown');

                            // Simplified animation support for dropdown
                            UIkit.util.on('#time-range-dropdown', 'beforeshow', function () {
                                this.style.transformOrigin = 'top right';
                                this.setAttribute('data-state', 'open');
                            });

                            // Fix the beforehide handler - REMOVE the problematic code
                            UIkit.util.off('#time-range-dropdown', 'beforehide'); // Remove existing handler

                            // Add a simpler beforehide handler if animation is still needed
                            UIkit.util.on('#time-range-dropdown', 'beforehide', function () {
                                this.setAttribute('data-state', '');
                                // Don't return false - let the dropdown close naturally
                            });

                            // Handle time range selection
                            timeRangeOptions.forEach(option => {
                                option.addEventListener('click', function (e) {
                                    e.preventDefault();
                                    const days = this.getAttribute('data-range');
                                    timeRangeText.textContent = ` ${days} days`;

                                    // Update chart data based on selected time range
                                    updateChartDataForRange(parseInt(days));

                                    // Close dropdown - use the proper UIkit method
                                    UIkit.dropdown(timeRangeDropdown).hide();
                                });
                            });

                            // Function to update chart data based on time range remains the same
                            function updateChartDataForRange(days) {
                                // Get current date
                                const endDate = new Date();
                                const startDate = new Date();
                                startDate.setDate(endDate.getDate() - days + 1);

                                // Generate new data for the selected time range
                                let newData;
                                if (days <= 7) {
                                    newData = generateData('desktop').slice(-7);
                                } else if (days <= 14) {
                                    newData = generateData('desktop').slice(-14);
                                } else {
                                    newData = generateData('desktop');
                                }

                                // Update the chart with new data
                                updateChart(newData, '#2662d9');

                                console.log(`Chart updated to show last ${days} days`);
                            }
                        });

                    </script>
                    <div id="analytics-cards-container" class="grid gap-4 md:grid-cols-3 lg:grid-cols-3 mt-8 w-full items-start">
                        <!-- Added items-start -->
                        <!-- Product Analytics -->
                        <div class="analytics-card card rounded-lg border shadow-sm chart-container chart-container-1 h-[370px] flex flex-col"
                            style="margin-top: 0px;" data-v0-t="card" draggable="true">
                            <div class="flex justify-between items-center p-4 border-b card">
                                <div class="flex flex-col">
                                    <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">
                                        Booking Sources</h3>
                                    <p class="text-sm text-muted-foreground mt-1">Distribution of booking channels</p>
                                </div>
                                <div class="relative">
                                    <button id="toggle-chart-btn" class="card uk-button border default" type="button"
                                        uk-toggle="target: #booking-sources-dropdown">
                                        <svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 6px;" width="16"
                                            height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-pie-chart">
                                            <path d="M21.21 15.89A10 10 0 1 1 8 2.83" />
                                            <path d="M22 12A10 10 0 0 0 12 2v10z" />
                                        </svg>
                                        Bookings
                                    </button>
                                    <!-- Dropdown Menu -->
                                    <div class="card dropdown-content" id="booking-sources-dropdown"
                                        uk-dropdown="mode: click; pos: bottom-right; offset: 10">
                                        <ul class="uk-nav uk-dropdown-nav card">
                                            <li><a href="#" class="chart-option" data-option="channel">By Channel</a>
                                            </li>
                                            <li><a href="#" class="chart-option" data-option="device">By Device</a></li>
                                            <li><a href="#" class="chart-option" data-option="campaign">By Campaign</a>
                                            </li>
                                            <li><a href="#" class="chart-option" data-option="region">By Region</a></li>
                                            <li><a href="#" class="chart-option" data-option="referrer">By Referrer</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="p-4 relative flex-grow flex flex-col"> <!-- Changed to flex-col -->
                                <div id="googleana-pieChartDiv" class="flex-grow" style="width: 100%; height: 100%;">
                                </div> <!-- Added flex-grow class -->
                            </div>
                        </div>
                        <!-- Total tips -->
                        <!-- Total tips -->
                        <div class="analytics-card card rounded-lg border shadow-sm chart-container chart-container-1 h-[370px] flex flex-col"
                            style="margin-top: 0px;" data-v0-t="card" draggable="true">
                            <div class="flex justify-between items-center p-4 border-b card">
                                <div class="flex flex-col">
                                    <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">
                                        Occupancy Rate</h3>
                                    <p class="text-sm text-muted-foreground mt-1">Daily occupancy
                                        for the last 7 days</p>
                                </div>
                                <div class="relative">
                                    <!-- Fixed target to correct dropdown ID -->
                                    <button id="toggle-occupancy-btn" class="card uk-button border default"
                                        type="button" uk-toggle="target: #occupancy-rate-dropdown">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-bar-chart-3" style="margin-right: 5px;">
                                            <path d="M3 3v18h18" />
                                            <path d="M18 17V9" />
                                            <path d="M13 17V5" />
                                            <path d="M8 17v-3" />
                                        </svg>
                                        Occupancy
                                    </button>
                                    <!-- Dropdown Menu -->
                                    <div class="card dropdown-content" id="occupancy-rate-dropdown"
                                        uk-dropdown="mode: click; pos: bottom-right; offset: 10">
                                        <ul class="uk-nav uk-dropdown-nav card">
                                            <li><a href="#" class="occupancy-option" data-range="7">Last 7 Days</a></li>
                                            <li><a href="#" class="occupancy-option" data-range="30">Last 30 Days</a>
                                            </li>
                                            <li><a href="#" class="occupancy-option" data-range="90">Last 90 Days</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class=" relative flex-grow flex flex-col p-0">
                                <!-- Reduced fixed height to prevent overflow -->
                                <div class="chart-inner-container flex-grow relative"
                                    style="height: 230px; padding-top: 10px;">
                                    <canvas id="googleana-TotalTipsChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <!-- Automated conversations -->
                        <div class="analytics-card rounded-lg border card shadow-sm h-[370px] overflow-hidden chart-container-3 flex flex-col"
                            data-v0-t="card" draggable="true">
                            <div class="flex justify-between items-center p-4 border-b card">
                                <div class="flex flex-col">
                                    <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">
                                        Guest Satisfaction</h3>
                                    <p class="text-sm text-muted-foreground mt-1">Average score from reviews</p>
                                </div>
                                <div class="relative">
                                    <button class="uk-button uk-button-default card universal-hover" type="button"
                                        style="background-color: transparent;"
                                        uk-toggle="target: #satisfaction-dropdown">
                                        <svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 5px;" width="18"
                                            height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-smile-plus">
                                            <path d="M22 11v1a10 10 0 1 1-9-10" />
                                            <path d="M8 14s1.5 2 4 2 4-2 4-2" />
                                            <line x1="9" x2="9.01" y1="9" y2="9" />
                                            <line x1="15" x2="15.01" y1="9" y2="9" />
                                            <path d="M16 5h6" />
                                            <path d="M19 2v6" />
                                        </svg>
                                        Reviews
                                    </button>
                                    <!-- Dropdown Menu -->
                                    <div class="card dropdown-content" id="satisfaction-dropdown"
                                        uk-dropdown="mode: click; pos: bottom-right; offset: 10">
                                        <ul class="uk-nav uk-dropdown-nav card">
                                            <li><a href="#" class="satisfaction-option" data-view="overall">Overall
                                                    Score</a></li>
                                            <li><a href="#" class="satisfaction-option" data-view="platform">By
                                                    Platform</a></li>
                                            <li><a href="#" class="satisfaction-option" data-view="sentiment">By
                                                    Sentiment</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="p-4 relative flex-grow flex flex-col">
                                <div class="relative w-full flex-grow flex items-center justify-center"
                                    style="transform: scale(0.9); transform-origin: center top;">
                                    <!-- Scaled down by 10% -->
                                    <div class="w-full h-full overflow-hidden">
                                        <svg viewBox="0 0 200 100" class="w-full h-full"
                                            preserveAspectRatio="xMidYMid meet">
                                            <path id="googleana-greyStylingCircle" d="M10 100 A 90 90 0 0 1 190 100"
                                                fill="none" stroke="#F3F4F6" stroke-width="6" />
                                            <path id="googleana-backgroundSemiCircle" d="M20 100 A 80 80 0 0 1 180 100"
                                                fill="none" stroke="#9ca3af" stroke-width="5"
                                                class="transition-all duration-300 hover:filter hover:brightness-105"
                                                filter="url(#shadow)" />
                                            <path id="googleana-foregroundSemiCircle" d="M20 100 A 80 80 0 0 1 180 100"
                                                fill="none" stroke="#151519" stroke-width="5" stroke-dasharray="226 25"
                                                class="transition-all duration-300 hover:filter hover:brightness-105"
                                                filter="url(#shadow)" />
                                            <defs>
                                                <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                                                    <feDropShadow dx="0" dy="1" stdDeviation="2" flood-opacity="0.1" />
                                                </filter>
                                            </defs>
                                        </svg>
                                    </div>
                                    <div class="absolute inset-0 flex flex-col items-center justify-center"
                                        style="padding-top: 63px;"> <!-- Adjusted padding for 90% scale -->
                                        <div
                                            class="flex items-center justify-center rounded-full p-1.5 mb-3 border border-gray-300 svg-icon-container">
                                            <!-- Replaced Blend icon with Smile -->
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-smile">
                                                <circle cx="12" cy="12" r="10" />
                                                <path d="M8 14s1.5 2 4 2 4-2 4-2" />
                                                <line x1="9" x2="9.01" y1="9" y2="9" />
                                                <line x1="15" x2="15.01" y1="9" y2="9" />
                                            </svg>
                                        </div>
                                        <span class="text-lg font-semibold " id="googleana-satisfactionScore">4.5 /
                                            5</span> <!-- Placeholder Score -->
                                        <p class="text-xs mt-1">Avg. Score</p>
                                    </div>
                                </div>
                                <div class="flex justify-between w-full">
                                    <div class="flex items-center">
                                        <div id="googleana-legendTipped" class="w-1 h-10 rounded-full mr-2"
                                            style="background-color: #16181d;"></div>
                                        <div>
                                            <span class="text-sm font-semibold "
                                                id="googleana-satisfiedCount">150</span> <!-- Placeholder Count -->
                                            <p class="text-xs ">Satisfied (4+)</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-end">
                                        <div class="text-right mr-2">
                                            <span class="text-sm font-semibold"
                                                id="googleana-neutralDissatisfiedCount">35</span>
                                            <!-- Placeholder Count -->
                                            <p class="text-xs ">Neutral/Dissatisfied</p>
                                        </div>
                                        <div id="googleana-legendNonTipped" class="w-1 h-10 rounded-full"
                                            style="background-color: #ABBDD3;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Scripts for the analytics containers -->
                    <link rel="stylesheet" href="https://cdn.amcharts.com/lib/5/index.css">
                    <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
                    <script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
                    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>
                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                    <script>
                        // PIE CHART (Booking Sources) - Updated Logic
                        // Function to fetch Booking Source Data (Placeholder - Needs Backend Update)
                        async function fetchBookingSourceData() {
                            try {
                                // **IMPORTANT:** Replace '/fetch-booking-sources' with your actual backend endpoint
                                // const response = await fetch('/fetch-booking-sources');
                                // if (!response.ok) {
                                //     throw new Error(`HTTP error! Status: ${response.status}`);
                                // }
                                // const data = await response.json();
                                // return data; // Assuming backend returns data like [{ source: 'Direct', bookings: 150 }, ...]

                                // Using placeholder data until backend is ready:
                                console.warn("Using placeholder data for Booking Sources Pie Chart. Update fetchBookingSourceData() with a real endpoint.");
                                return [
                                    { source: "Direct Website", bookings: 150, color: am4core.color("#264754") },
                                    { source: "Google Hotel Ads", bookings: 140, color: am4core.color("#e76e51") },
                                    { source: "Booking.com", bookings: 190, color: am4core.color("#2c9b91") },
                                    { source: "Expedia", bookings: 100, color: am4core.color("#f2a262") },
                                    { source: "Other Referrers", bookings: 110, color: am4core.color("#e7c468") }
                                ];
                            } catch (error) {
                                console.error('Error fetching Booking Source data:', error);
                                return null;
                            }
                        }

                        // Function to update the Pie Chart with Booking Source data
                        function updateBookingSourcePieChart(chart, data) {
                            if (data && Array.isArray(data)) {
                                chart.data = data;
                            } else {
                                console.error("Invalid data format for Booking Source Pie Chart:", data);
                                chart.data = []; // Clear chart if data is invalid
                            }
                        }

                        // Initialize the Chart once amCharts are ready
                        am4core.ready(async function () {
                            // Apply the animated theme
                            am4core.useTheme(am4themes_animated);
                            am4core.options.autoSetClassName = true; // Helps with CSS targeting if needed

                            // Create a Pie Chart instance in the chartdiv
                            var chart = am4core.create("googleana-pieChartDiv", am4charts.PieChart);
                            chart.radius = am4core.percent(60); // Adjust the size as needed

                            // Fetch data and update the chart
                            const bookingSourceData = await fetchBookingSourceData();
                            updateBookingSourcePieChart(chart, bookingSourceData);

                            // Add and configure the Pie Series
                            var pieSeries = chart.series.push(new am4charts.PieSeries());
                            pieSeries.dataFields.value = "bookings"; // Use 'bookings' field from data
                            pieSeries.dataFields.category = "source"; // Use 'source' field from data
                            pieSeries.slices.template.propertyFields.fill = "color";

                            // Disable the default amCharts logo
                            chart.logo.disabled = true;

                            // Create a Donut Chart by setting an inner radius
                            chart.innerRadius = am4core.percent(30);

                            // Customize slice appearance
                            pieSeries.slices.template.padding = 1;
                            pieSeries.slices.template.cornerRadius = 5;
                            pieSeries.slices.template.fillOpacity = 1;
                            pieSeries.slices.template.strokeWidth = 0;
                            pieSeries.slices.template.stroke = am4core.color("#ffffff");

                            // Configure Labels
                            pieSeries.labels.template.disabled = false;
                            pieSeries.labels.template.text = "{category}: {value.value}"; // Updated label text
                            pieSeries.labels.template.radius = 1;
                            pieSeries.labels.template.fontSize = 12;
                            pieSeries.labels.template.maxWidth = 80;
                            pieSeries.labels.template.wrap = true;

                            // Configure Ticks
                            pieSeries.ticks.template.disabled = false;
                            pieSeries.ticks.template.strokeOpacity = 0.7;
                            pieSeries.ticks.template.strokeWidth = 2;
                            pieSeries.ticks.template.length = 20; // Default length for all lines

                            // Adjust the length for specific categories if needed
                            pieSeries.ticks.template.adapter.add("length", function (length, target) {
                                if (target.dataItem && target.dataItem.category === "Spa") {
                                    return 5; // Adjusted length for the "Spa" line
                                }
                                return length;
                            });

                            // Add Tooltips
                            pieSeries.slices.template.tooltipText = "{category}: {value.value} bookings"; // Updated tooltip text

                            // Function to update chart colors based on the current theme
                            function updateChartColors() {
                                var body = document.body;
                                var isDarkTheme = body.classList.contains('pure-black') ||
                                    body.classList.contains('dark-gray') ||
                                    body.classList.contains('navy-blue') ||
                                    body.classList.contains('cool-blue') ||
                                    body.classList.contains('deep-burgundy') ||
                                    body.classList.contains('charcoal');

                                // Update text color for labels and ticks based on the theme
                                pieSeries.labels.template.fill = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
                                pieSeries.ticks.template.stroke = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
                            }

                            // Initial color update
                            updateChartColors();

                            // Observe theme changes to update chart colors dynamically
                            var observer = new MutationObserver(function (mutations) {
                                mutations.forEach(function (mutation) {
                                    if (mutation.type === "attributes" && mutation.attributeName === "class") {
                                        updateChartColors();
                                    }
                                });
                            });

                            observer.observe(document.body, {
                                attributes: true
                            });
                        });


                        // BAR CHART (Occupancy Rate) - Renamed
                        let googleanaOccupancyChart = null;

                        function googleanaInitializeOccupancyChart() {
                            const isPureBlack = document.body.classList.contains('pure-black');
                            const chartBarColor = isPureBlack ? '#fafafa' : '#18181b';
                            const gridColor = isPureBlack ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

                            const initialChartData = {
                                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                                datasets: [{
                                    label: 'Occupancy Rate',
                                    data: [], // Start with empty data
                                    backgroundColor: chartBarColor,
                                    borderColor: chartBarColor,
                                    borderWidth: 0,
                                    borderRadius: {
                                        topLeft: 5,
                                        topRight: 5,
                                        bottomLeft: 5,
                                        bottomRight: 5
                                    },
                                    borderSkipped: false
                                }]
                            };

                            const ctx = document.getElementById('googleana-TotalTipsChart').getContext('2d');
                            if (googleanaOccupancyChart) googleanaOccupancyChart.destroy();

                            googleanaOccupancyChart = new Chart(ctx, {
                                type: 'bar',
                                data: initialChartData,
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    layout: {
                                        padding: {
                                            left: 10,
                                            right: 10,
                                            top: 20,
                                            bottom: 20
                                        }
                                    },
                                    barPercentage: 0.9,
                                    categoryPercentage: 0.8,
                                    devicePixelRatio: 2,
                                    scales: {
                                        x: {
                                            grid: {
                                                display: false,
                                                color: 'transparent'
                                            },
                                            border: { display: false }
                                        },
                                        y: {
                                            display: true,
                                            beginAtZero: true,
                                            min: 0,
                                            max: 100,
                                            grid: {
                                                color: gridColor,
                                                borderDash: [5, 5],
                                                display: true
                                            },
                                            border: { display: false },
                                            ticks: {
                                                display: false,
                                                stepSize: 25
                                            }
                                        }
                                    },
                                    plugins: {
                                        tooltip: {
                                            enabled: false,
                                            external: createCustomTooltipForTips,
                                            mode: 'index',
                                            intersect: false
                                        },
                                        legend: { display: false }
                                    },
                                    hover: {
                                        mode: 'index',
                                        intersect: false
                                    }
                                }
                            });

                            // Use fixed data for occupancy rate (Mon-Sun)
                            const occupancyData = [75, 99, 67, 83, 59, 95, 88]; // Example fixed data
                            googleanaOccupancyChart.data.datasets[0].data = occupancyData;
                            googleanaOccupancyChart.update();
                        }
                        document.addEventListener('DOMContentLoaded', function () {
                            setTimeout(() => { googleanaInitializeOccupancyChart(); }, 100); // Use new function name
                            const observer = new MutationObserver(function (mutations) {
                                mutations.forEach(function (mutation) {
                                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                                        setTimeout(() => { googleanaInitializeOccupancyChart(); }, 100); // Use new function name
                                    }
                                });
                            });
                            observer.observe(document.body, { attributes: true });
                        });
                        // SEMI-CIRCLE GAUGE (Guest Satisfaction) - Renamed
                        function googleanaUpdateSatisfactionGauge() {
                            const isPureBlack = document.body.classList.contains('pure-black');
                            const greyStylingCircle = document.getElementById('googleana-greyStylingCircle');
                            const backgroundSemiCircle = document.getElementById('googleana-backgroundSemiCircle');
                            const foregroundSemiCircle = document.getElementById('googleana-foregroundSemiCircle');
                            if (!greyStylingCircle || !backgroundSemiCircle || !foregroundSemiCircle) return;
                            if (isPureBlack) {
                                greyStylingCircle.setAttribute('stroke', '#27272A');
                                backgroundSemiCircle.setAttribute('stroke', '#71717A');
                                foregroundSemiCircle.setAttribute('stroke', '#fafafa');
                            } else {
                                greyStylingCircle.setAttribute('stroke', '#F3F4F6');
                                backgroundSemiCircle.setAttribute('stroke', '#9ca3af');
                                foregroundSemiCircle.setAttribute('stroke', '#151519');
                            }
                            const legendTipped = document.getElementById('googleana-legendTipped');
                            const legendNonTipped = document.getElementById('googleana-legendNonTipped');
                            if (legendTipped && legendNonTipped) {
                                legendTipped.style.backgroundColor = isPureBlack ? '#fafafa' : '#151519';
                                legendNonTipped.style.backgroundColor = isPureBlack ? '#71717A' : '#9ca3af';
                            }
                        }
                        document.addEventListener('DOMContentLoaded', function () {
                            // --- Random Data for Satisfaction Gauge ---
                            const randomScore = (Math.random() * 2 + 3).toFixed(1); // Random score 3.0 to 5.0
                            const scorePercentage = ((randomScore - 1) / 4) * 100; // Map 1-5 score to 0-100%
                            const gaugeTotalLength = 251; // Approx. length of the semi-circle path d="M20 100 A 80 80 0 0 1 180 100"
                            const filledLength = (scorePercentage / 100) * gaugeTotalLength;
                            const emptyLength = gaugeTotalLength - filledLength;
                            const randomSatisfied = Math.floor(Math.random() * 200) + 100; // 100-300
                            const randomNeutral = Math.floor(Math.random() * 50) + 10;   // 10-60

                            // Update Gauge SVG
                            const foregroundCircle = document.getElementById('googleana-foregroundSemiCircle');
                            if (foregroundCircle) {
                                foregroundCircle.setAttribute('stroke-dasharray', `${filledLength} ${emptyLength}`);
                            }

                            // Update Text Elements
                            const scoreElement = document.getElementById('googleana-satisfactionScore');
                            if (scoreElement) {
                                scoreElement.textContent = `${randomScore} / 5`;
                            }
                            const satisfiedElement = document.getElementById('googleana-satisfiedCount');
                            if (satisfiedElement) {
                                satisfiedElement.textContent = randomSatisfied.toLocaleString();
                            }
                            const neutralElement = document.getElementById('googleana-neutralDissatisfiedCount');
                            if (neutralElement) {
                                neutralElement.textContent = randomNeutral.toLocaleString();
                            }
                            // --- End Random Data ---

                            googleanaUpdateSatisfactionGauge(); // Update colors based on theme
                            const observer = new MutationObserver(function (mutations) {
                                mutations.forEach(function (mutation) {
                                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                                        googleanaUpdateSatisfactionGauge(); // Use new function name
                                    }
                                });
                            });
                            observer.observe(document.body, { attributes: true });
                        });
                        // Removed dropdown logic as it's no longer used for the Occupancy chart

                        // Drag and drop for the three analytics cards (Booking Sources, Occupancy Rate, Guest Satisfaction)
                        document.addEventListener('DOMContentLoaded', function () {
                            const analyticsCardsContainer = document.getElementById('analytics-cards-container');
                            let draggedAnalyticsItem = null;

                            if (analyticsCardsContainer) {
                                analyticsCardsContainer.addEventListener('dragstart', function (e) {
                                    if (e.target.classList.contains('analytics-card')) {
                                        draggedAnalyticsItem = e.target;
                                        setTimeout(() => {
                                            if (draggedAnalyticsItem) draggedAnalyticsItem.classList.add('dragging');
                                        }, 0);
                                        e.dataTransfer.effectAllowed = 'move';
                                        e.dataTransfer.setData('text/plain', e.target.id || 'dragged-analytics-card');
                                    }
                                });

                                analyticsCardsContainer.addEventListener('dragend', function (e) {
                                    if (draggedAnalyticsItem && e.target === draggedAnalyticsItem) {
                                        draggedAnalyticsItem.classList.remove('dragging');
                                    } else if (draggedAnalyticsItem) {
                                        // Fallback if dragend target is not the item itself but item exists
                                        draggedAnalyticsItem.classList.remove('dragging');
                                    }
                                    draggedAnalyticsItem = null;
                                });

                                analyticsCardsContainer.addEventListener('dragover', function (e) {
                                    e.preventDefault();
                                });

                                analyticsCardsContainer.addEventListener('drop', function (e) {
                                    e.preventDefault();
                                    if (!draggedAnalyticsItem) {
                                        return;
                                    }

                                    const afterElement = getHorizontalDragAfterElementAnalytics(analyticsCardsContainer, e.clientX, 'analytics-card');
                                    if (afterElement) {
                                        if (draggedAnalyticsItem !== afterElement) { // Avoid inserting before itself
                                            analyticsCardsContainer.insertBefore(draggedAnalyticsItem, afterElement);
                                        }
                                    } else {
                                        analyticsCardsContainer.appendChild(draggedAnalyticsItem);
                                    }
                                    // The 'dragging' class is removed in dragend
                                });
                            }

                            function getHorizontalDragAfterElementAnalytics(container, x, draggableSelector) {
                                const draggableElements = [...container.querySelectorAll(`.${draggableSelector}:not(.dragging)`)];
                                return draggableElements.reduce((closest, child) => {
                                    const box = child.getBoundingClientRect();
                                    const offset = x - (box.left + box.width / 2);
                                    if (offset < 0 && offset > closest.offset) {
                                        return { offset: offset, element: child };
                                    } else {
                                        return closest;
                                    }
                                }, { offset: Number.NEGATIVE_INFINITY }).element;
                            }
                        });
                    </script>
                    <script>
                        // Add random data to specific cards on load
                        document.addEventListener('DOMContentLoaded', function () {
                            // Booking Conversion Rate (e.g., 1.0% to 5.0%)
                            const conversionRateEl = document.getElementById('booking-conversion-rate-value');
                            if (conversionRateEl) {
                                conversionRateEl.textContent = (Math.random() * 4 + 1).toFixed(1) + '%';
                            }

                            // Website Visitors (e.g., 500 to 5000)
                            const visitorsEl = document.getElementById('website-visitors-value');
                            if (visitorsEl) {
                                visitorsEl.textContent = '+' + (Math.floor(Math.random() * 4501) + 500).toLocaleString();
                            }

                            // Average Session Duration (e.g., 1m 0s to 4m 59s)
                            const sessionDurationEl = document.getElementById('avg-session-duration-value');
                            if (sessionDurationEl) {
                                const minutes = Math.floor(Math.random() * 4) + 1; // 1 to 4 minutes
                                const seconds = Math.floor(Math.random() * 60); // 0 to 59 seconds
                                sessionDurationEl.textContent = `${minutes}m ${seconds}s`;
                            }
                        });
                    </script>
                </div>
                <script>
                    document.addEventListener('DOMContentLoaded', function () {
                        const metricCardsContainer = document.getElementById('metric-cards-container');
                        let draggedItem = null;

                        if (metricCardsContainer) {
                            metricCardsContainer.addEventListener('dragstart', function (e) {
                                if (e.target.classList.contains('metric-card')) {
                                    draggedItem = e.target;
                                    setTimeout(() => {
                                        // Check if draggedItem is still valid before adding class
                                        if(draggedItem) draggedItem.classList.add('dragging');
                                    }, 0);
                                    e.dataTransfer.effectAllowed = 'move';
                                    // e.dataTransfer.setData('text/plain', e.target.id); // Optional
                                }
                            });

                            metricCardsContainer.addEventListener('dragend', function (e) {
                                if (draggedItem && e.target === draggedItem) {
                                    draggedItem.classList.remove('dragging');
                                } else if (draggedItem) {
                                     // Fallback if dragend target is not the item itself but item exists
                                    draggedItem.classList.remove('dragging');
                                }
                                draggedItem = null;
                            });

                            metricCardsContainer.addEventListener('dragover', function (e) {
                                e.preventDefault();
                            });

                            metricCardsContainer.addEventListener('drop', function (e) {
                                e.preventDefault();
                                if (!draggedItem) {
                                    return;
                                }

                                const afterElement = getHorizontalDragAfterElement(metricCardsContainer, e.clientX);
                                if (afterElement) {
                                    metricCardsContainer.insertBefore(draggedItem, afterElement);
                                } else {
                                    metricCardsContainer.appendChild(draggedItem);
                                }
                            });
                        }

                        function getHorizontalDragAfterElement(container, x) {
                            const draggableElements = [...container.querySelectorAll('.metric-card:not(.dragging)')];
                            return draggableElements.reduce((closest, child) => {
                                const box = child.getBoundingClientRect();
                                const offset = x - (box.left + box.width / 2);
                                if (offset < 0 && offset > closest.offset) {
                                    return { offset: offset, element: child };
                                } else {
                                    return closest;
                                }
                            }, { offset: Number.NEGATIVE_INFINITY }).element;
                        }
                    });
                </script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/7.8.5/d3.min.js"></script>
                <script>
                    // Data Generation and Chart Setup
                    function generateData(type) {
                        const data = [];
                        const endDate = new Date();
                        const startDate = new Date();
                        startDate.setDate(endDate.getDate() - 29);

                        for (let i = 0; i < 30; i++) {
                            const date = new Date(startDate);
                            date.setDate(startDate.getDate() + i);
                            let value = 200 + Math.random() * 150;
                            if (type === 'mobile') {
                                value = 220 + Math.random() * 180;
                            }
                            value += Math.sin(i / 7) * (type === 'mobile' ? 70 : 50);
                            value += Math.sin(i / 30) * (type === 'mobile' ? 100 : 80);
                            if (i % 12 === 0) {
                                value += Math.random() * (type === 'mobile' ? 120 : 100);
                            }
                            data.push({
                                date: date,
                                value: Math.round(value)
                            });
                        }
                        return data;
                    }

                    const desktopData = generateData('desktop');
                    const mobileData = generateData('mobile');
                    let currentData = desktopData;

                    const container = document.getElementById('chart');
                    const margin = { top: 20, right: 10, bottom: 80, left: 10 };
                    const width = container.clientWidth - margin.left - margin.right;
                    const height = container.clientHeight - margin.top - margin.bottom;

                    const svg = d3.select('#chart')
                        .append('svg')
                        .attr('width', '100%')
                        .attr('height', height + margin.top + margin.bottom)
                        .attr('viewBox', `0 0 ${width + margin.left + margin.right} ${height + margin.top + margin.bottom}`)
                        .attr('preserveAspectRatio', 'none')  // Updated here
                        .append('g')
                        .attr('transform', `translate(${margin.left},${margin.top})`);

                    // Create a fixed time domain for x-axis with evenly spaced days
                    const endDate = new Date();
                    const startDate = new Date();
                    startDate.setDate(endDate.getDate() - 29);

                    // Create fixed x-axis with consistent date intervals
                    const x = d3.scaleTime()
                        .domain([startDate, endDate])
                        .range([0, width]);

                    // Generate fixed tick values for more days to show all 30 days
                    const xTickValues = [];
                    for (let i = 0; i <= 29; i += 1) {
                        const date = new Date(startDate);
                        date.setDate(startDate.getDate() + i);
                        xTickValues.push(date);
                    }


                    const y = d3.scaleLinear()
                        .domain([0, d3.max(currentData, d => d.value) * 1.1])
                        .range([height, 0]);

                    const line = d3.line()
                        .x(d => x(d.date))
                        .y(d => y(d.value))
                        .curve(d3.curveMonotoneX);

                    // Create a fixed x-axis with all 30 days but formatted labels
                    svg.append('g')
                        .attr('class', 'axis')
                        .attr('transform', `translate(0,${height})`)
                        .call(d3.axisBottom(x)
                            .tickValues(xTickValues)
                            .tickFormat((d, i) => {
                                // Only show labels for every 3rd day to avoid crowding
                                if (i % 1 === 0) {
                                    return d3.timeFormat('%d')(d);
                                }
                                return '';
                            })
                            .tickSizeOuter(0)
                        )

                    svg.append('g')
                        .attr('class', 'axis')
                        .call(d3.axisLeft(y)
                            .ticks(5)
                            .tickSize(-width)
                            .tickFormat('')
                        )
                        .call(g => g.select('.domain').remove());

                    svg.append('path')
                        .datum(currentData)
                        .attr('class', 'ggchart-line')
                        .attr('stroke', '#2662d9') // Changed to blue
                        .attr('d', line);

                    const hoverLine = svg.append('line')
                        .attr('class', 'ggchart-hover-line')
                        .attr('y1', 0)
                        .attr('y2', height);

                    const dots = svg.selectAll('.ggchart-dot')
                        .data(currentData)
                        .enter()
                        .append('circle')
                        .attr('class', 'ggchart-dot')
                        .attr('cx', d => x(d.date))
                        .attr('cy', d => y(d.value))
                        .attr('r', 6)
                        .attr('fill', '#2662d9'); // Changed to blue

                    const tooltip = d3.select('.ggchart-tooltip');

                    svg.append('rect')
                        .attr('class', 'ggchart-overlay')
                        .attr('width', width)
                        .attr('height', height)
                        .on('mousemove', mousemove)
                        .on('mouseout', mouseout);

                    // Use requestAnimationFrame for ultra-fast, smooth hover updates
                    let rafId = null;
                    let lastHover = null;

                    function mousemove(event) {
                        const [mouseX, mouseY] = d3.pointer(event);
                        const bisectDate = d3.bisector(d => d.date).left;
                        const x0 = x.invert(mouseX);
                        const i = bisectDate(currentData, x0, 1);
                        const d0 = currentData[i - 1];
                        const d1 = currentData[i];
                        // Select the closer data point
                        const d = x0 - d0.date > d1.date - x0 ? d1 : d0;
                        const xPos = x(d.date);
                        const yPos = y(d.value);

                        // Only update if the hovered point changes
                        if (!lastHover || lastHover.date.getTime() !== d.date.getTime()) {
                            lastHover = d;
                            if (rafId) cancelAnimationFrame(rafId);
                            rafId = requestAnimationFrame(() => {
                                updateTooltipAndHighlights(d, xPos, yPos);
                            });
                        }
                    }

                    function updateTooltipAndHighlights(d, xPos, yPos) {
                        // Set position of the hover line to exactly match the data point
                        hoverLine
                            .attr('transform', `translate(${xPos},0)`)
                            .style('opacity', 1);

                        // Hide all dots first (only if not already hidden)
                        dots.style('opacity', 0);

                        // Show only the selected dot
                        svg.selectAll('.ggchart-dot')
                            .filter(dt => dt.date.getTime() === d.date.getTime())
                            .style('opacity', 1);

                        // Position the tooltip with clear offset from the point
                        const tooltipWidth = tooltip.node().offsetWidth;
                        const containerWidth = container.clientWidth;

                        // Calculate ideal position (right of the point by default)
                        let leftPos = xPos + margin.left + 15;

                        // If tooltip would go off the right edge, place it to the left of the point
                        if (leftPos + tooltipWidth > containerWidth - 10) {
                            leftPos = xPos + margin.left - tooltipWidth - 15;
                        }

                        // Ensure tooltip stays within container bounds
                        leftPos = Math.max(10, Math.min(containerWidth - tooltipWidth - 10, leftPos));

                        // Position tooltip and update content
                        tooltip
                            .style('opacity', 1)
                            .style('left', `${leftPos}px`)
                            .style('top', `${margin.top + 10}px`);

                        const formatDate = d3.timeFormat('%b %d, %Y');
                        tooltip.select('.ggchart-tooltip-date').text(formatDate(d.date));
                        tooltip.select('.ggchart-tooltip-value').text(d.value.toLocaleString());
                    }


                    function mouseout() {
                        hoverLine.style('opacity', 0);
                        dots.style('opacity', 0);
                        tooltip.style('opacity', 0);
                    }

                    function updateChart(newData, color) {
                        console.log("[DEBUG] updateChart called with color:", color, "and data:", newData);

                        currentData = newData;
                        y.domain([0, d3.max(newData, d => d.value) * 1.1]);

                        svg.select('.ggchart-line')
                            .datum(newData)
                            .attr('stroke', color)
                            .transition()
                            .duration(300)
                            .attr('d', line);

                        const dots = svg.selectAll('.ggchart-dot')
                            .data(newData);

                        dots.exit().remove();

                        dots.enter()
                            .append('circle')
                            .attr('class', 'ggchart-dot')
                            .attr('r', 6)
                            .merge(dots)
                            .attr('cx', d => x(d.date))
                            .attr('cy', d => y(d.value))
                            .attr('fill', color);

                        console.log("[DEBUG] Chart updated successfully.");
                    }

                    document.querySelectorAll('.ggchart-stat-box').forEach(box => {
                        box.addEventListener('click', function () {
                            console.log("[DEBUG] Stat box clicked:", this);

                            document.querySelectorAll('.ggchart-stat-box').forEach(b => b.classList.remove('active'));
                            this.classList.add('active');

                            const type = this.querySelector('h3').textContent.toLowerCase();
                            console.log("[DEBUG] Detected type:", type);

                            if (type.includes('pms')) {
                                console.log("[DEBUG] Switching to PMS data.");
                                updateChart(mobileData, '#2662d9'); // PMS data and blue color
                            } else {
                                console.log("[DEBUG] Switching to Guest Genius data.");
                                updateChart(desktopData, '#16a34a'); // Guest Genius data and green color
                            }
                        });
                    });

                    // Set default chart to Guest Genius
                    document.querySelector('.ggchart-stat-box').classList.add('active');
                    updateChart(desktopData, '#2662d9');

                    window.addEventListener('load', function () {
                        setTimeout(() => {
                            const firstPoint = currentData[0];
                            console.log("[DEBUG] First point on load:", firstPoint);
                            const xPosition = x(firstPoint.date);
                            const yPosition = y(firstPoint.value);
                            updateTooltipAndHighlights(firstPoint, xPosition, yPosition);
                            setTimeout(() => {
                                console.log("[DEBUG] Initial tooltip highlight shown");
                            }, 2000);
                        }, 500);
                    });
                    function debounce(func, wait) {
                        let timeout;
                        return function executedFunction(...args) {
                            const later = () => {
                                clearTimeout(timeout);
                                func(...args);
                            };
                            clearTimeout(timeout);
                            timeout = setTimeout(later, wait);
                        };
                    }
                </script>

                <script>
                    async function fetchSalesData() {
                        try {
                            const response = await fetch('/saleschart');
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            const data = await response.json();
                            return data;
                        } catch (error) {
                            console.error('Error fetching sales data:', error);
                            return null;
                        }
                    }

                    function calculateStepSize(maxValue) {
                        return Math.ceil(maxValue / 5);
                    }

                    async function updateChart(chart) {
                        const salesData = await fetchSalesData();
                        if (!salesData) return;

                        const salesChart = salesData.sales_chart[0];
                        const salesTips = salesData.sales_tips[0];

                        const salesChartData = [
                            salesChart.Mon, salesChart.Tue, salesChart.Wed, salesChart.Thu,
                            salesChart.Fri, salesChart.Sat, salesChart.Sun
                        ];

                        const salesTipsData = [
                            salesTips.Mon, salesTips.Tue, salesTips.Wed, salesTips.Thu,
                            salesTips.Fri, salesTips.Sat, salesTips.Sun
                        ];

                        const maxDataValue = Math.max(...salesChartData, ...salesTipsData);
                        const stepSize = calculateStepSize(maxDataValue);

                        chart.data.datasets[0].data = salesChartData;
                        chart.data.datasets[1].data = salesTipsData;
                        chart.options.scales.y.ticks.stepSize = stepSize;
                        chart.update();
                    }

                    async function initializeChart() {
                        const salesData = await fetchSalesData();
                        if (!salesData) return;

                        const salesChart = salesData.sales_chart[0];
                        const salesTips = salesData.sales_tips[0];

                        const salesChartData = [
                            salesChart.Mon, salesChart.Tue, salesChart.Wed, salesChart.Thu,
                            salesChart.Fri, salesChart.Sat, salesChart.Sun
                        ];

                        const salesTipsData = [
                            salesTips.Mon, salesTips.Tue, salesTips.Wed, salesTips.Thu,
                            salesTips.Fri, salesTips.Sat, salesTips.Sun
                        ];

                        const maxDataValue = Math.max(...salesChartData, ...salesTipsData);
                        const stepSize = calculateStepSize(maxDataValue);

                        const newSignupsData = {
                            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                            datasets: [
                                {
                                    label: 'Direct Bookings',
                                    data: salesChartData,
                                    backgroundColor: '#18181b'
                                },
                                {
                                    label: 'Website Visitors',
                                    data: salesTipsData,
                                    backgroundColor: '#aabdd1'
                                }
                            ]
                        };

                        const ctx = document.getElementById('salesOverviewChart').getContext('2d');
                        const newSignupsChart = new Chart(ctx, {
                            type: 'bar',
                            data: newSignupsData,
                            options: {
                                maintainAspectRatio: false,
                                interaction: {
                                    mode: 'index',
                                    intersect: false
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        grid: {
                                            color: 'rgba(0, 0, 0, 0.1)',
                                            borderDash: [5, 5]
                                        },
                                        ticks: {
                                            stepSize: stepSize
                                        }
                                    },
                                    x: {
                                        grid: {
                                            display: false
                                        }
                                    }
                                },
                                plugins: {
                                    tooltip: {
                                        backgroundColor: 'rgba(200, 200, 200, 0.3)',
                                        titleColor: '#000',
                                        bodyColor: '#000'
                                    }
                                },
                                elements: {
                                    bar: {
                                        borderRadius: 8,
                                        borderSkipped: false
                                    }
                                }
                            }
                        });

                        setInterval(() => {
                            updateChart(newSignupsChart);
                        }, 60000);
                    }

                    document.addEventListener('DOMContentLoaded', initializeChart);

                    async function fetchData(url) {
                        try {
                            const response = await fetch(url);
                            const data = await response.json();
                            return data;
                        } catch (error) {
                            console.error('Error fetching data:', error);
                            return [];
                        }
                    }

                    async function updateFirstRowSales() {
                        try {
                            const response = await fetch('/firstrowsales');
                            const data = await response.json();
                            document.getElementById('total-sales').textContent = `€${parseFloat(data[0].total_sales).toFixed(2)}`;
                            document.getElementById('total-tips').textContent = `+${data[0].total_tips}`;
                            document.getElementById('total-customers').textContent = `+${data[0].total_customers}`;
                        } catch (error) {
                            console.error('Error fetching first row sales data:', error);
                        }
                    }

                    async function init() {
                        await updateFirstRowSales();
                    }

                    // Initial call
                    init();

                    // Refresh data every 5 seconds
                    setInterval(init, 60000);
                </script>
</body>

</html>