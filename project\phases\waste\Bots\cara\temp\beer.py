import requests
import json

# Your Twilio account details (replace with your actual Account SID and Auth Token)
ACCOUNT_SID = '**********************************'
AUTH_TOKEN = '005d910f85546392a91f58a3878c437c'

# Twilio API endpoint for content creation
url = 'https://content.twilio.com/v1/Content'

# Payload data for the carousel with updated beer menu items and descriptions
payload = {
    "friendly_name": "v1_beer_menu_carousel",
    "language": "en",
    "types": {
        "twilio/carousel": {
            "body": "Explore our selection of refreshing beers!",
            "cards": [
                {
                    "title": "Coronita",
                    "body": "Refreshing lager with a crisp finish.",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/beers.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_coronita"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Go Back",
                            "id": "go_back"
                        }
                    ]
                },
                {
                    "title": "Estrella Galicia",
                    "body": "A smooth and balanced beer with a distinct character.",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/beers.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_estrella_galicia"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Go Back",
                            "id": "go_back"
                        }
                    ]
                },
                {
                    "title": "Estrella Galicia",
                    "body": "0.0% Non-alcoholic lager that doesn't compromise on taste.",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/beers.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_estrella_galicia_0_0"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Go Back",
                            "id": "go_back"
                        }
                    ]
                },
                {
                    "title": "Peroni",
                    "body": "Italian premium lager with a clean and light taste.",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/beers.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_peroni"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Go Back",
                            "id": "go_back"
                        }
                    ]
                }
            ]
        }
    }
}

# Make the POST request to the Twilio API
response = requests.post(
    url,
    auth=(ACCOUNT_SID, AUTH_TOKEN),
    headers={'Content-Type': 'application/json'},
    data=json.dumps(payload)
)

# Check the response status
if response.status_code == 201:
    print("Carousel created successfully!")
    print(response.json())
else:
    print(f"Failed to create carousel: {response.status_code}")
    print(response.text)