from flask import Flask, request, jsonify
from dotenv import load_dotenv
import os
import requests
from datetime import datetime

# Load the .env file
load_dotenv()

# Access the Supabase URL and Key
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_ANON_KEY')

app = Flask(__name__)

# Headers for Supabase API requests
headers = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Accept": "application/json",
    "Content-Type": "application/json"
}

# Fetch Users from Supabase
def fetch_users_from_supabase():
    print("[INFO] Fetching all users from Supabase.")
    url = f"{SUPABASE_URL}/rest/v1/Users"
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        print("[SUCCESS] Users fetched successfully.")
        return response.json()
    else:
        print(f"[ERROR] Failed to fetch users. Status code: {response.status_code}")
        return []

# Update User in Supabase
def update_user_in_supabase(user_id, updated_data):
    print(f"[INFO] Updating user (ID: {user_id}) in Supabase with data: {updated_data}")
    url = f"{SUPABASE_URL}/rest/v1/Users?id=eq.{user_id}"
    response = requests.patch(url, json=updated_data, headers=headers)
    if response.status_code in [200, 204]:
        print("[SUCCESS] User updated successfully.")
    else:
        print(f"[ERROR] Failed to update user. Status code: {response.status_code}")
        print(response.text)

def update_firstrowdash_revenue(price):
    print("[INFO] Updating firstrowdash total revenue.")
    url = f"{SUPABASE_URL}/rest/v1/firstrowdash?id=eq.0"

    # Fetch current data
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print(f"[ERROR] Failed to fetch firstrowdash data. Status code: {response.status_code}")
        return

    current_data = response.json()[0]

    # Extract numeric value from price, handling decimal points
    numeric_price = float(''.join(c for c in price if c.isdigit() or c == '.') or 0)

    # Update total revenue
    current_total_revenue = float(current_data['Total revenue'])
    new_total_revenue = current_total_revenue + numeric_price

    print(f"[DEBUG] Current total revenue: {current_total_revenue}")
    print(f"[DEBUG] Price to add: {numeric_price}")
    print(f"[DEBUG] New total revenue: {new_total_revenue}")

    # Prepare updated data
    updated_data = {
        "Total revenue": str(round(new_total_revenue, 2))
    }

    # Update the row
    update_response = requests.patch(url, json=updated_data, headers=headers)
    if update_response.status_code in [200, 204]:
        print("[SUCCESS] Firstrowdash total revenue updated successfully.")
    else:
        print(f"[ERROR] Failed to update firstrowdash total revenue. Status code: {update_response.status_code}")
        print(update_response.text)

# Get Current Date and Time
def get_current_datetime():
    now = datetime.now()
    formatted_date = now.strftime("%d/%m")
    formatted_time = now.strftime("%I:%M %p")
    print(f"[INFO] Current date and time: {formatted_date} | {formatted_time}")
    return f"{formatted_date} | {formatted_time}"

# Add Task to Supabase
def add_task_to_supabase(task_data):
    print(f"[INFO] Adding new task to Supabase: {task_data}")
    url = f"{SUPABASE_URL}/rest/v1/Tasks"
    response = requests.post(url, json=task_data, headers=headers)
    if response.status_code == 201:
        print("[SUCCESS] New task added successfully.")
    else:
        print(f"[ERROR] Failed to add task. Status code: {response.status_code}")
        print(response.text)

#Add sale to customer table
def add_task_to_customer_table(custask_data):
    print(f"[INFO] Adding new task to Supabase: {custask_data}")
    url = f"{SUPABASE_URL}/rest/v1/customers"
    response = requests.post(url, json=custask_data, headers=headers)
    if response.status_code == 201:
        print("[SUCCESS] New task added successfully.")
    else:
        print(f"[ERROR] Failed to add task. Status code: {response.status_code}")
        print(response.text)

# Update firstrowsales data with new food order
def update_firstrowsales_data(price):
    print("[INFO] Updating firstrowsales data with new food order.")
    url = f"{SUPABASE_URL}/rest/v1/firstrowsales?id=eq.1"

    # Fetch current data
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print(f"[ERROR] Failed to fetch firstrowsales data. Status code: {response.status_code}")
        return

    current_data = response.json()[0]

    # Extract numeric value from price, handling decimal points
    numeric_price = float(''.join(c for c in price if c.isdigit() or c == '.') or 0)

    # Update total sales
    current_total_sales = float(current_data['total_sales'])
    new_total_sales = current_total_sales + numeric_price

    print(f"[DEBUG] Current total sales: {current_total_sales}")
    print(f"[DEBUG] Price to add: {numeric_price}")
    print(f"[DEBUG] New total sales: {new_total_sales}")

    # Prepare updated data
    updated_data = {
        "total_sales": str(round(new_total_sales, 2))
    }

    update_firstrowdash_revenue(price)  # Add this line to update firstrowdash revenue

    # Update the row
    update_response = requests.patch(url, json=updated_data, headers=headers)
    if update_response.status_code in [200, 204]:
        print("[SUCCESS] Firstrowsales data updated successfully.")
    else:
        print(f"[ERROR] Failed to update firstrowsales data. Status code: {update_response.status_code}")
        print(update_response.text)

def update_tppd_mb_data(price):
    print("[INFO] Updating tppd data with new massage booking.")
    url = f"{SUPABASE_URL}/rest/v1/tppd?id=eq.1"

    # Fetch current data
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print(f"[ERROR] Failed to fetch tppd data. Status code: {response.status_code}")
        return

    current_data = response.json()[0]
    print(f"[DEBUG] Current tppd data: {current_data}")

    # Extract numeric value from price, handling decimal points
    numeric_price = float(''.join(c for c in price if c.isdigit() or c == '.') or 0)

    # Update massage booking sales and massage booking amount, using get() method with default values
    current_mb_sales = int(current_data.get('massage_amount', 0))
    current_mb_amount = float(current_data.get('massage_sales', 0))
    new_mb_sales = current_mb_sales + 1
    new_mb_amount = current_mb_amount + numeric_price

    print(f"[DEBUG] Current massage booking sales: {current_mb_sales}")
    print(f"[DEBUG] Current massage booking amount: {current_mb_amount}")
    print(f"[DEBUG] Price to add: {numeric_price}")
    print(f"[DEBUG] New massage booking sales: {new_mb_sales}")
    print(f"[DEBUG] New massage booking amount: {new_mb_amount}")

    update_firstrowdash_revenue(price)  # Add this line to update firstrowdash revenue

    # Prepare updated data
    updated_data = {
        "massage_amount": str(new_mb_sales),
        "massage_sales": str(round(new_mb_amount, 2))
    }

    # Update the row
    update_response = requests.patch(url, json=updated_data, headers=headers)
    if update_response.status_code in [200, 204]:
        print("[SUCCESS] TPPD massage booking data updated successfully.")
    else:
        print(f"[ERROR] Failed to update TPPD massage booking data. Status code: {update_response.status_code}")
        print(update_response.text)


# Update total customers in firstrowsales
def update_total_customers():
    print("[INFO] Updating total customers in firstrowsales.")
    url = f"{SUPABASE_URL}/rest/v1/firstrowsales?id=eq.1"

    # Fetch current data
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print(f"[ERROR] Failed to fetch firstrowsales data. Status code: {response.status_code}")
        return

    current_data = response.json()[0]

    # Increment total customers
    current_total_customers = int(current_data['total_customers'])
    new_total_customers = current_total_customers + 1

    print(f"[DEBUG] Current total customers: {current_total_customers}")
    print(f"[DEBUG] New total customers: {new_total_customers}")

    # Prepare updated data
    updated_data = {
        "total_customers": str(new_total_customers)
    }

    # Update the row
    update_response = requests.patch(url, json=updated_data, headers=headers)
    if update_response.status_code in [200, 204]:
        print("[SUCCESS] Firstrowsales total customers updated successfully.")
    else:
        print(f"[ERROR] Failed to update firstrowsales total customers. Status code: {update_response.status_code}")
        print(update_response.text)

# Add User to Supabase and update total customers
def add_user_and_update_customers(user_data):
    print(f"[INFO] Adding new user to Supabase: {user_data}")
    url = f"{SUPABASE_URL}/rest/v1/Users"
    response = requests.post(url, json=user_data, headers=headers)
    if response.status_code == 201:
        print("[SUCCESS] New user added successfully.")
        # Update total customers after successful user addition
        update_total_customers()
    else:
        print(f"[ERROR] Failed to add user. Status code: {response.status_code}")
        print(response.text)

# Fetch platforms data from Supabase
def fetch_platforms_from_supabase():
    print("[INFO] Fetching platforms data from Supabase.")
    url = f"{SUPABASE_URL}/rest/v1/platforms"
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        print("[SUCCESS] Platforms data fetched successfully.")
        return response.json()
    else:
        print(f"[ERROR] Failed to fetch platforms data. Status code: {response.status_code}")
        return []

# Update platforms data in Supabase
def update_platforms_in_supabase(platform, price):
    print(f"[INFO] Updating platforms data for {platform} with price: {price}")
    url = f"{SUPABASE_URL}/rest/v1/platforms?id=eq.1"

    # Fetch current data
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print(f"[ERROR] Failed to fetch platforms data. Status code: {response.status_code}")
        return

    current_data = response.json()[0]

    # Extract numeric value from price, handling decimal points
    numeric_price = float(''.join(c for c in price if c.isdigit() or c == '.') or 0)

    # Update sales and amount based on platform
    if platform == 'whatsapp':
        current_sales = int(current_data['whatsapp_sales'])
        current_amount = float(current_data['whatsapp_amount'])
        new_sales = current_sales + 1
        new_amount = current_amount + numeric_price
    elif platform == 'messenger':
        current_sales = int(current_data['messenger_sales'])
        current_amount = float(current_data['messenger_amount'])
        new_sales = current_sales + 1
        new_amount = current_amount + numeric_price
    elif platform == 'instagram':
        current_sales = int(current_data['instagram_sales'])
        current_amount = float(current_data['instagram_amount'])
        new_sales = current_sales + 1
        new_amount = current_amount + numeric_price
    elif platform == 'web':
        current_sales = int(current_data['web_sales'])
        current_amount = float(current_data['web_amount'])
        new_sales = current_sales + 1
        new_amount = current_amount + numeric_price
    elif platform == 'voice':
        current_sales = int(current_data['voice_sales'])
        current_amount = float(current_data['voice_amount'])
        new_sales = current_sales + 1
        new_amount = current_amount + numeric_price
    else:
        print(f"[ERROR] Unknown platform: {platform}")
        return

    print(f"[DEBUG] Current sales: {current_sales}")
    print(f"[DEBUG] Current amount: {current_amount}")
    print(f"[DEBUG] Price to add: {numeric_price}")
    print(f"[DEBUG] New sales: {new_sales}")
    print(f"[DEBUG] New amount: {new_amount}")

    # Prepare updated data
    updated_data = {
        f"{platform}_sales": str(new_sales),
        f"{platform}_amount": str(round(new_amount, 2))
    }

    # Update the row
    update_response = requests.patch(url, json=updated_data, headers=headers)
    if update_response.status_code in [200, 204]:
        print("[SUCCESS] Platforms data updated successfully.")
    else:
        print(f"[ERROR] Failed to update platforms data. Status code: {update_response.status_code}")
        print(update_response.text)

# Update tppd table (food)
def update_tppd_food_data(price):
    print("[INFO] Updating tppd data with new food order.")
    url = f"{SUPABASE_URL}/rest/v1/tppd?id=eq.1"

    # Fetch current data
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print(f"[ERROR] Failed to fetch tppd data. Status code: {response.status_code}")
        return

    current_data = response.json()[0]
    print(f"[DEBUG] Current tppd data: {current_data}")

    # Extract numeric value from price, handling decimal points
    numeric_price = float(''.join(c for c in price if c.isdigit() or c == '.') or 0)

    # Update food sales and food amount, using get() method with default values
    current_food_sales = int(current_data.get('food_amount', 0))
    current_food_amount = float(current_data.get('food_sales', 0))
    new_food_sales = current_food_sales + 1
    new_food_amount = current_food_amount + numeric_price

    print(f"[DEBUG] Current food sales: {current_food_sales}")
    print(f"[DEBUG] Current food amount: {current_food_amount}")
    print(f"[DEBUG] Price to add: {numeric_price}")
    print(f"[DEBUG] New food sales: {new_food_sales}")
    print(f"[DEBUG] New food amount: {new_food_amount}")

    # Prepare updated data
    updated_data = {
        "food_amount": str(new_food_sales),
        "food_sales": str(round(new_food_amount, 2))
    }

    # Update the row
    update_response = requests.patch(url, json=updated_data, headers=headers)
    if update_response.status_code in [200, 204]:
        print("[SUCCESS] TPPD food data updated successfully.")
    else:
        print(f"[ERROR] Failed to update TPPD food data. Status code: {update_response.status_code}")
        print(update_response.text)

# Update tppd table (beverage)
def update_tppd_bev_data(price):
    print("[INFO] Updating tppd data with new food order.")
    url = f"{SUPABASE_URL}/rest/v1/tppd?id=eq.1"

    # Fetch current data
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print(f"[ERROR] Failed to fetch tppd data. Status code: {response.status_code}")
        return

    current_data = response.json()[0]
    print(f"[DEBUG] Current tppd data: {current_data}")

    # Extract numeric value from price, handling decimal points
    numeric_price = float(''.join(c for c in price if c.isdigit() or c == '.') or 0)

    update_firstrowdash_revenue(price)  # Add this line to update firstrowdash revenue

    # Update food sales and food amount, using get() method with default values
    current_bev_sales = int(current_data.get('beverage_sales', 0))
    current_bev_amount = float(current_data.get('beverage_amount', 0))
    new_bev_sales = current_bev_sales + 1
    new_bev_amount = current_bev_amount + numeric_price

    print(f"[DEBUG] Current food sales: {current_bev_sales}")
    print(f"[DEBUG] Current food amount: {current_bev_amount}")
    print(f"[DEBUG] Price to add: {numeric_price}")
    print(f"[DEBUG] New food sales: {new_bev_sales}")
    print(f"[DEBUG] New food amount: {new_bev_amount}")

    # Prepare updated data
    updated_data = {
        "beverage_sales": str(new_bev_sales),
        "beverage_amount": str(round(new_bev_amount, 2))
    }

    # Update the row
    update_response = requests.patch(url, json=updated_data, headers=headers)
    if update_response.status_code in [200, 204]:
        print("[SUCCESS] TPPD food data updated successfully.")
    else:
        print(f"[ERROR] Failed to update TPPD food data. Status code: {update_response.status_code}")
        print(update_response.text)

# Update tppd table (room_bookings)
def update_tppd_rb_data(price):
    print("[INFO] Updating tppd data with new food order.")
    url = f"{SUPABASE_URL}/rest/v1/tppd?id=eq.1"

    # Fetch current data
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print(f"[ERROR] Failed to fetch tppd data. Status code: {response.status_code}")
        return

    current_data = response.json()[0]
    print(f"[DEBUG] Current tppd data: {current_data}")

    # Extract numeric value from price, handling decimal points
    numeric_price = float(''.join(c for c in price if c.isdigit() or c == '.') or 0)

    update_firstrowdash_revenue(price)  # Add this line to update firstrowdash revenue

    # Update room booking sales and room booking amount, using get() method with default values
    current_rb_sales = int(current_data.get('room_bookings', 0))
    current_rb_amount = float(current_data.get('room_sales', 0))
    new_rb_sales = current_rb_sales + 1
    new_rb_amount = current_rb_amount + numeric_price

    print(f"[DEBUG] Current room booking sales: {current_rb_sales}")
    print(f"[DEBUG] Current room booking amount: {current_rb_amount}")
    print(f"[DEBUG] Price to add: {numeric_price}")
    print(f"[DEBUG] New room booking sales: {new_rb_sales}")
    print(f"[DEBUG] New room booking amount: {new_rb_amount}")

    # Prepare updated data
    updated_data = {
        "room_bookings": str(new_rb_sales),
        "room_sales": str(round(new_rb_amount, 2))
    }

    # Update the row
    update_response = requests.patch(url, json=updated_data, headers=headers)
    if update_response.status_code in [200, 204]:
        print("[SUCCESS] TPPD food data updated successfully.")
    else:
        print(f"[ERROR] Failed to update TPPD food data. Status code: {update_response.status_code}")
        print(update_response.text)

def update_tppd_sb_data(price):
    print("[INFO] Updating tppd data with new food order.")
    url = f"{SUPABASE_URL}/rest/v1/tppd?id=eq.1"

    # Fetch current data
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print(f"[ERROR] Failed to fetch tppd data. Status code: {response.status_code}")
        return

    current_data = response.json()[0]
    print(f"[DEBUG] Current tppd data: {current_data}")

    # Extract numeric value from price, handling decimal points
    numeric_price = float(''.join(c for c in price if c.isdigit() or c == '.') or 0)

    update_firstrowdash_revenue(price)  # Add this line to update firstrowdash revenue

    # Update room booking sales and room booking amount, using get() method with default values
    current_sb_sales = int(current_data.get('spa_bookings', 0))
    current_sb_amount = float(current_data.get('spa_sales', 0))
    new_sb_sales = current_sb_sales + 1
    new_sb_amount = current_sb_amount + numeric_price

    print(f"[DEBUG] Current room booking sales: {current_sb_sales}")
    print(f"[DEBUG] Current room booking amount: {current_sb_amount}")
    print(f"[DEBUG] Price to add: {numeric_price}")
    print(f"[DEBUG] New room booking sales: {new_sb_sales}")
    print(f"[DEBUG] New room booking amount: {new_sb_amount}")

    # Prepare updated data
    updated_data = {
        "spa_bookings": str(new_sb_sales),
        "spa_sales": str(round(new_sb_amount, 2))
    }

    # Update the row
    update_response = requests.patch(url, json=updated_data, headers=headers)
    if update_response.status_code in [200, 204]:
        print("[SUCCESS] TPPD food data updated successfully. Spa bookings")
    else:
        print(f"[ERROR] Failed to update TPPD spa data. Status code: {update_response.status_code}")
        print(update_response.text)

# Update sales_chartts table
def update_sales_chartts():
    print("[INFO] Updating sales_chartts data.")
    url = f"{SUPABASE_URL}/rest/v1/sales_chartts?id=eq.1"

    # Fetch current data
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print(f"[ERROR] Failed to fetch sales_chartts data. Status code: {response.status_code}")
        return

    current_data = response.json()[0]
    print(f"[DEBUG] Current sales_chartts data: {current_data}")

    # Get current day of the week
    current_day = datetime.now().strftime("%a")

    # Update the count for the current day
    current_count = int(current_data.get(current_day, 0))
    new_count = current_count + 1

    print(f"[DEBUG] Current day: {current_day}")
    print(f"[DEBUG] Current count: {current_count}")
    print(f"[DEBUG] New count: {new_count}")

    # Prepare updated data
    updated_data = {
        current_day: str(new_count)
    }

    # Update the row
    update_response = requests.patch(url, json=updated_data, headers=headers)
    if update_response.status_code in [200, 204]:
        print("[SUCCESS] Sales_chartts data updated successfully.")
    else:
        print(f"[ERROR] Failed to update sales_chartts data. Status code: {update_response.status_code}")
        print(update_response.text)

@app.route('/data', methods=['POST'])
def receive_data():
    print("[INFO] Received new data request.")
    if request.is_json:
        data = request.get_json()
        print(f"[INFO] JSON data received: {data}")

        # Handle new_contact action
        if data.get("action") == "new_contact":
            print("[INFO] Processing 'new_contact' action.")
            number = data.get("number")
            room_no = data.get("room_no")
            name = data.get("Name")
            platform = data.get("Platform")

            # Fetch existing users
            existing_users = fetch_users_from_supabase()

            # Check if the number already exists
            existing_user = next((user for user in existing_users if user['phone'] == number), None)

            if existing_user:
                print(f"[INFO] User with phone {number} already exists.")
                # If the room number is different, update the user's data
                if existing_user['room_no'] != room_no:
                    print(f"[INFO] Room number has changed. Updating user data.")
                    updated_user_data = {
                        "Name": name,
                        "phone": number,
                        "room_no": room_no,
                        "platform": platform,
                        "language": "English",  # Default value, adjust as needed
                        "time": get_current_datetime()
                    }
                    update_user_in_supabase(existing_user['id'], updated_user_data)
                    return jsonify({"message": "User data updated successfully"}), 200
                else:
                    print(f"[INFO] User with phone {number} and same room number exists. No update needed.")
                    return jsonify({"message": "Contact already exists"}), 200
            else:
                # Prepare new user data with current date and time
                print(f"[INFO] No existing user found with phone {number}. Adding new user.")
                new_user = {
                    "Name": name,
                    "phone": number,
                    "room_no": room_no,
                    "platform": platform,
                    "language": "English",  # Default value, adjust as needed
                    "time": get_current_datetime()
                }

                # Add new user to Supabase and update total customers
                add_user_and_update_customers(new_user)

                return jsonify({"message": "New contact added successfully and total customers updated"}), 201

        # Handle Food_order_sales action
        elif data.get("action") == "Food_order_sales":
            print("[INFO] Processing 'Food_order_sales' action.")
            task_description = data.get("Action")
            phone = data.get("user_data")
            platform = data.get("platform")
            language = data.get("language")
            room_number = data.get("room_number")
            name = data.get("user_name")
            price = str(data.get("price", "0"))  # Ensure price is a string
            order = data.get("food_name")
            extra = data.get("extra")
            cus_name = data.get("customer")

            # Ensure phone number starts with a '+'
            formatted_phone = f"+{phone}" if not phone.startswith('+') else phone
            print(f"[INFO] Formatted phone number: {formatted_phone}")

            # Prepare new task data with current date and time
            new_task = {
                "Task": task_description,
                "Phone": formatted_phone,
                "Time_stamp": datetime.now().strftime("%I:%M %p"),  # Time in HH:MM format
                "platform": platform,
                "description": extra,
                "customer": cus_name,
                "language": language
            }

            # Add new task to Supabase
            add_task_to_supabase(new_task)

            # Prepare new user data for Food_order_sales
            print(f"[INFO] Adding user for 'Food_order_sales'.")
            new_user = {
                "Name": name,
                "phone": formatted_phone,
                "room_no": room_number,
                "platform": platform,
                "language": language,
                "time": get_current_datetime()
            }

            cusfoodtask_data = {
                "Name": name,
                "room_number": room_number,
                "phone_number": formatted_phone, 
                "Platform": platform,
                "language": language,
                "spend" : price,
                "order" : order
            }

            #add custask to customer table
            add_task_to_customer_table(cusfoodtask_data)

            # Update firstrowsales data with the new food order price
            update_firstrowsales_data(price)

            # Update platforms data with the new food order price and platform
            update_platforms_in_supabase(platform, price)

            # Update tppd table with the new food order data
            update_tppd_food_data(price)

            # Update sales_chartts table
            update_sales_chartts()

            return jsonify({"message": "Food order and user data added successfully, and sales data updated"}), 201
        
        #------------------------------------------------Beverage Order---------------------------------------------------------------------

        # Handle Beverage_order action
        elif data.get("action") == "Beverage_order":
            print("[INFO] Processing 'Beverage_order' action.")
            bev_task_description = data.get("task")
            bev_phone = data.get("user_data")
            bev_platform = data.get("platform")
            bev_language = data.get("language")
            bev_room_number = data.get("room_number")
            bev_name = data.get("Name")
            bev_sale = data.get("sale")
            bev_cus_name = data.get("customer")
            #price = str(data.get("price", "0"))  # Ensure price is a string
            bev_price = '30'

            # Prepare new task data with current date and time
            new_task = {
                "Task": bev_task_description,
                "Phone": bev_phone,
                "Time_stamp": datetime.now().strftime("%I:%M %p"),  # Time in HH:MM format
                "platform": bev_platform,
                "language": bev_language,
                "customer" : bev_cus_name
            }

            # Add new task to Supabase
            add_task_to_supabase(new_task)

            # Prepare new user data for Food_order_sales
            print(f"[INFO] Adding user for 'Beverage_sales'.")

            custask_data = {
                "Name": bev_name,
                "room_number": bev_room_number,
                "phone_number": bev_phone, 
                "Platform": bev_platform,
                "language": bev_language,
                "spend" : bev_price,
                "customer" : bev_cus_name,
                "order" : bev_sale
            }

            #add custask to customer table
            add_task_to_customer_table(custask_data)

            # Prepare new user data for Food_order_sales
            print(f"[INFO] Adding task to customer table for 'Beverage_sales'.")

            # Update firstrowsales data with the new food order price
            update_firstrowsales_data(bev_price)

            # Update platforms data with the new food order price and platform
            update_platforms_in_supabase(bev_platform, bev_price)

            # Update tppd table with the new food order data
            update_tppd_bev_data(bev_price)

            # Update sales_chartts table
            update_sales_chartts()

            return jsonify({"message": "Food order and user data added successfully, and sales data updated"}), 201
        
        elif data.get("action") == "spa_booking":
            print("[INFO] Processing 'spa_booking' action.")
            spa_service = data.get("Service")
            spa_phone = data.get("Phone")
            spa_platform = data.get("Platform")
            spa_language = data.get("Language")
            spa_room_number = data.get("Room_number")
            spa_name = data.get("Name")
            spa_price = data.get("Spend")

            print(f"[INFO] Adding spa booking data for user: {spa_name}")

            custask_data = {
                "Name": spa_name,
                "room_number": spa_room_number,
                "phone_number": spa_phone, 
                "Platform": spa_platform,
                "language": spa_language,
                "spend": spa_price,
                "order": spa_service
            }

            # Add spa booking data to customer table
            add_task_to_customer_table(custask_data)

            # Update firstrowsales data with the new spa booking price
            update_firstrowsales_data(spa_price)

            # Update platforms data with the new spa booking price and platform
            update_platforms_in_supabase(spa_platform, spa_price)

            # Update tppd table with the new spa booking data
            # Note: You might want to create a new function for spa bookings if needed
            update_tppd_sb_data(spa_price)

            # Update sales_chartts table
            update_sales_chartts()

            return jsonify({"message": "Spa booking data added successfully, and sales data updated"}), 201
        
        elif data.get("action") == "massage_booking":
            print("[INFO] Processing 'massage_booking' action.")
            mb_service = data.get("Service")
            mb_phone = data.get("Phone")
            mb_platform = data.get("Platform")
            mb_language = data.get("Language")
            mb_room_number = data.get("Room_number")
            mb_name = data.get("Name")
            mb_price = data.get("Spend")

            print(f"[INFO] Adding massage booking data for user: {mb_name}")

            custask_data = {
                "Name": mb_name,
                "room_number": mb_room_number,
                "phone_number": mb_phone,
                "Platform": mb_platform,
                "language": mb_language,
                "spend": mb_price,
                "order": mb_service
            }

            # Add massage booking data to customer table
            add_task_to_customer_table(custask_data)

            # Update firstrowsales data with the new massage booking price
            update_firstrowsales_data(mb_price)

            # Update platforms data with the new massage booking price and platform
            update_platforms_in_supabase(mb_platform, mb_price)

            # Update tppd table with the new massage booking data
            update_tppd_mb_data(mb_price)

            # Update sales_chartts table
            update_sales_chartts()

            return jsonify({"message": "Massage booking data added successfully, and sales data updated"}), 201
        
        #--------------------------------------room booking------------------------------------------------------------------------
            #handle room booking data 

        elif data.get("action") == "room_booking":
            print("[INFO] Processing 'room_booking' action.")
            rb_sale = data.get("Service")
            rb_phone = data.get("Phone")
            rb_platform = data.get("Platform")
            rb_language = data.get("Language")
            rb_room_number = data.get("Room_number")
            rb_name = data.get("User_name")
            #price = str(data.get("price", "0"))  # Ensure price is a string
            rb_price = data.get("Spend")

            # Prepare new user data for Food_order_sales
            print(f"[INFO] Adding user for 'Beverage_sales'.")

            custask_data = {
                "Name": rb_name,
                "room_number": rb_room_number,
                "phone_number": rb_phone, 
                "Platform": rb_platform,
                "language": rb_language,
                "spend" : rb_price,
                "order" : rb_sale
            }

            #add custask to customer table
            add_task_to_customer_table(custask_data)

            # Prepare new user data for Food_order_sales
            print(f"[INFO] Adding task to customer table for 'Beverage_sales'.")

            # Update firstrowsales data with the new food order price
            update_firstrowsales_data(rb_price)

            # Update platforms data with the new food order price and platform
            update_platforms_in_supabase(rb_platform, rb_price)

            # Update tppd table with the new food order data
            update_tppd_rb_data(rb_price)

            # Update sales_chartts table
            update_sales_chartts()

            return jsonify({"message": "Food order and user data added successfully, and sales data updated"}), 201

        return jsonify({"error": "Invalid action"}), 400
    else:
        print("[ERROR] Request is not JSON.")
        return jsonify({"error": "Request must be JSON"}), 400
    
    #--------------------------------------spa booking------------------------------------------------------------------------
        


if __name__ == '__main__':
    print("[INFO] Starting Flask server.")
    print(f"Using supabase url : {SUPABASE_URL}")
    print(f"Using supabase key : {SUPABASE_KEY}")
    app.run(port=80)