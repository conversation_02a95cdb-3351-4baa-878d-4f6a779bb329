import os
import sys
import glob
import json
from Bots.Voice.English.upload_to_supabase import process_single_transcript, process_all_transcripts

def test_upload_latest_transcript():
    """Test uploading the latest transcript file to Supabase"""
    print("Testing upload of latest transcript file...")
    
    # Get the transcripts directory
    transcripts_dir = os.path.join(os.path.dirname(__file__), 'transcripts')
    
    # Ensure the directory exists
    if not os.path.exists(transcripts_dir):
        print(f"Transcripts directory not found: {transcripts_dir}")
        return False
    
    # Get all JSON files
    json_files = glob.glob(os.path.join(transcripts_dir, '*.json'))
    
    if not json_files:
        print("No transcript files found")
        return False
    
    # Find the most recent file
    latest_file = max(json_files, key=os.path.getmtime)
    
    print(f"Found latest transcript file: {os.path.basename(latest_file)}")
    
    # Process the file
    success = process_single_transcript(latest_file)
    
    if success:
        print(f"Successfully uploaded {os.path.basename(latest_file)} to Supabase")
        return True
    else:
        print(f"Failed to upload {os.path.basename(latest_file)} to Supabase")
        return False

def test_upload_specific_transcript(file_path):
    """Test uploading a specific transcript file to Supabase"""
    print(f"Testing upload of specific transcript file: {os.path.basename(file_path)}...")
    
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return False
    
    # Process the file
    success = process_single_transcript(file_path)
    
    if success:
        print(f"Successfully uploaded {os.path.basename(file_path)} to Supabase")
        return True
    else:
        print(f"Failed to upload {os.path.basename(file_path)} to Supabase")
        return False

def test_upload_all_transcripts():
    """Test uploading all transcript files to Supabase"""
    print("Testing upload of all transcript files...")
    
    # Process all files
    success = process_all_transcripts()
    
    if success:
        print("Successfully uploaded all transcript files to Supabase")
        return True
    else:
        print("Failed to upload all transcript files to Supabase")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test uploading voice call transcripts to Supabase')
    parser.add_argument('--file', type=str, help='Path to a specific transcript file to test')
    parser.add_argument('--latest', action='store_true', help='Test uploading the most recent transcript file')
    parser.add_argument('--all', action='store_true', help='Test uploading all transcript files')
    
    args = parser.parse_args()
    
    if args.file:
        test_upload_specific_transcript(args.file)
    elif args.latest:
        test_upload_latest_transcript()
    elif args.all:
        test_upload_all_transcripts()
    else:
        # Default to testing the latest transcript
        test_upload_latest_transcript()
