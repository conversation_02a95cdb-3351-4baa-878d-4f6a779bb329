import requests
import random
import json
import time
import argparse
import sys
import os
from dotenv import load_dotenv
import requests as supabase_requests
from datetime import datetime

# Load environment variables from .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), ".env"))

# VAPI API Keys
PUBLIC_VAPI_KEY  = os.environ.get("PUBLIC_VAPI_KEY")
PRIVATE_VAPI_KEY = os.environ.get("PRIVATE_VAPI_KEY")

# Supabase API credentials
SUPABASE_URL = os.environ.get("SUPABASE_URL")
SUPABASE_ANON_KEY = os.environ.get("SUPABASE_ANON_KEY")

missing_env = [k for k, v in {
    "PUBLIC_VAPI_KEY": PUBLIC_VAPI_KEY,
    "PRIVATE_VAPI_KEY": PRIVATE_VAPI_KEY,
    "SUPABASE_URL": SUPABASE_URL,
    "SUPABASE_ANON_KEY": SUPABASE_ANON_KEY
}.items() if not v]
if missing_env:  #  early, explicit failure
    raise RuntimeError(
        f"Missing required environment variable(s): {', '.join(missing_env)}. "
        "Check your .env file or deployment secrets."
    )

# Default phone number to call
DEFAULT_TO_NUMBER = "+919398760681"
# VAPI API base URL
VAPI_API_BASE_URL = "https://api.vapi.ai"

# Headers for API requests
headers = {
    "Authorization": f"Bearer {PRIVATE_VAPI_KEY}",
    "Content-Type": "application/json"
}
# Supabase headers
supabase_headers = {
    "apikey": SUPABASE_ANON_KEY,
    "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=minimal"
}

def format_time(timestamp_str):
    """Convert ISO timestamp to HH:MM AM/PM format"""
    if not timestamp_str:
        return None
    
    try:
        # Parse the ISO timestamp
        dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        # Format to HH:MM AM/PM
        return dt.strftime('%I:%M %p')
    except Exception as e:
        print(f"Error formatting time: {e}")
        return None

def calculate_call_ratio(call_data):
    """Calculate the ratio of AI to user talk time"""
    if not call_data or 'messages' not in call_data:
        return "0:0"
    
    ai_time = 0
    user_time = 0
    
    for msg in call_data['messages']:
        if msg.get('role') == 'bot' and msg.get('duration'):
            ai_time += msg.get('duration', 0)
        elif msg.get('role') == 'user' and msg.get('duration'):
            user_time += msg.get('duration', 0)
    
    # Convert to seconds and format as ratio
    ai_seconds = int(ai_time / 1000) if ai_time else 0
    user_seconds = int(user_time / 1000) if user_time else 0
    
    return f"{ai_seconds}:{user_seconds}"

def analyze_sentiment(transcript):
    """Simple sentiment analysis based on keywords"""
    if not transcript:
        return "5/10 (Neutral)"
    
    positive_words = ['thank', 'thanks', 'good', 'great', 'excellent', 'happy', 'appreciate', 'helpful', 'perfect', 'wonderful']
    negative_words = ['bad', 'poor', 'terrible', 'unhappy', 'disappointed', 'issue', 'problem', 'wrong', 'not working', 'complaint']
    
    # Count occurrences of positive and negative words
    positive_count = sum(1 for word in positive_words if word.lower() in transcript.lower())
    negative_count = sum(1 for word in negative_words if word.lower() in transcript.lower())
    
    # Calculate sentiment score (1-10 scale)
    base_score = 5  # Neutral starting point
    sentiment_score = base_score + positive_count - negative_count
    
    # Ensure score is within 1-10 range
    sentiment_score = max(1, min(10, sentiment_score))
    
    # Determine sentiment label
    if sentiment_score >= 8:
        sentiment_label = "Positive"
    elif sentiment_score <= 3:
        sentiment_label = "Negative"
    elif sentiment_score < 5:
        sentiment_label = "Slightly Negative"
    elif sentiment_score > 5:
        sentiment_label = "Slightly Positive"
    else:
        sentiment_label = "Neutral"
    
    return f"{sentiment_score}/10 ({sentiment_label})"

def calculate_call_time(call_data):
    """Calculate the total call time in minutes"""
    if not call_data:
        return "0 minutes"
    
    start_time = call_data.get('startedAt')
    end_time = call_data.get('endedAt')
    
    if not start_time or not end_time:
        return "Unknown"
    
    try:
        # Parse ISO timestamps
        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        
        # Calculate duration in minutes
        duration_seconds = (end_dt - start_dt).total_seconds()
        duration_minutes = int(duration_seconds / 60)
        
        if duration_minutes == 0:
            return f"{int(duration_seconds)} seconds"
        elif duration_minutes == 1:
            return "1 minute"
        else:
            return f"{duration_minutes} minutes"
    except Exception as e:
        print(f"Error calculating call time: {e}")
        return "Unknown"

def extract_phone_number(call_data):
    """Extract the phone number from call data"""
    if not call_data or 'customer' not in call_data:
        return None
    
    # Get the phone number from the customer field
    phone_number = call_data.get('customer', {}).get('number', '')
    
    # Remove the '+' prefix if present
    if phone_number.startswith('+'):
        phone_number = phone_number[1:]
    
    return phone_number

def upload_call_data_to_supabase(call_data):
    """Upload call data directly to Supabase without creating local files"""
    try:
        # Extract necessary data
        call_id = call_data.get('id')
        
        # Generate a numeric call_id for the database (using last 8 chars of UUID)
        numeric_call_id = int(call_id.replace('-', '')[-8:], 16) % 100000000
        
        # Extract phone number
        guest_phone = extract_phone_number(call_data)
        
        # Format timestamps
        call_start_time = format_time(call_data.get('startedAt'))
        call_end_time = format_time(call_data.get('endedAt'))
        
        # Get transcript from different possible locations
        call_transcript = ""
        if call_data.get('artifact') and call_data['artifact'].get('transcript'):
            call_transcript = call_data['artifact']['transcript']
        elif call_data.get('transcript'):
            call_transcript = call_data.get('transcript', '')
        
        # Calculate call ratio (AI:User talk time)
        call_ratio = calculate_call_ratio(call_data)
        
        # Analyze sentiment
        sentiment_analysis = analyze_sentiment(call_transcript)
        
        # Determine if call is outgoing or incoming
        call_type = call_data.get('type', '')
        out_or_in = 'outgoing' if call_type == 'outboundPhoneCall' else 'incoming'
        
        # Generate call summary
        call_summary = call_data.get('summary', 'No summary available')
        
        # Calculate total call time
        call_time = calculate_call_time(call_data)
        
        # Determine call status
        call_status = 'Completed' if call_data.get('status') == 'ended' else call_data.get('status', 'Unknown')
        
        # Prepare data for Supabase
        supabase_data = {
            "call_id": numeric_call_id,
            "guest_phone": guest_phone,
            "call_start_time": call_start_time,
            "call_end_time": call_end_time,
            "call_transcript": call_transcript,
            "call_ratio": call_ratio,
            "sentiment_analysis": sentiment_analysis,
            "out_or_in": out_or_in,
            "call_summary": call_summary,
            "call_time": call_time,
            "call_status": call_status,
            "room_no": None  # This could be updated later if room information is available
        }
        
        # Upload to Supabase
        response = supabase_requests.post(
            f"{SUPABASE_URL}/rest/v1/voicebot_data",
            headers=supabase_headers,
            json=supabase_data
        )
        
        if response.status_code == 201:
            print(f"Successfully uploaded data for call {call_id} to Supabase")
            return True
        else:
            print(f"Failed to upload data: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"Error uploading to Supabase: {e}")
        return False

# Set up argument parser
def parse_arguments():
    parser = argparse.ArgumentParser(description='Place a call using VAPI with a random assistant')
    parser.add_argument('--to', dest='to_number', type=str, default=DEFAULT_TO_NUMBER,
                        help=f'Phone number to call (default: {DEFAULT_TO_NUMBER})')
    parser.add_argument('--debug', dest='debug', action='store_true',
                        help='Enable debug mode with verbose output')
    parser.add_argument('--assistant-id', dest='assistant_id', type=str, default="711e3821-8040-496c-a760-23676f0db2d6",
                        help='Specific assistant ID to use')
    parser.add_argument('--phone-id', dest='phone_id', type=str, default="3c14ca41-bd73-49d9-9bce-6e4956834a3d",
                        help='Specific phone number ID to use')
    parser.add_argument('--monitor', dest='monitor', action='store_true', default=True,
                        help='Monitor the call status until it completes')
    parser.add_argument('--get-transcript', dest='get_transcript', type=str,
                        help='Get transcript for a specific call ID')

    return parser.parse_args()

def get_all_assistants(debug=False):
    """Retrieve all assistants from VAPI"""
    url = f"{VAPI_API_BASE_URL}/assistant"

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise an exception for HTTP errors

        assistants = response.json()
        print(f"Successfully retrieved {len(assistants)} assistants")

        # Debug: Print full assistant data
        if debug and assistants and len(assistants) > 0:
            print("Assistant data sample:")
            print(json.dumps(assistants[0], indent=2))

        return assistants
    except requests.exceptions.RequestException as e:
        print(f"Error retrieving assistants: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return []

def get_phone_numbers(debug=False):
    """Retrieve all phone numbers from VAPI"""
    url = f"{VAPI_API_BASE_URL}/phone-number"

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()

        phone_numbers = response.json()
        print(f"Successfully retrieved {len(phone_numbers)} phone numbers")

        # Debug: Print full phone number data
        if debug and phone_numbers and len(phone_numbers) > 0:
            print("Phone number data sample:")
            print(json.dumps(phone_numbers[0], indent=2))

        return phone_numbers
    except requests.exceptions.RequestException as e:
        print(f"Error retrieving phone numbers: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return []

def place_call(assistant_id, from_number_id, to_number, debug=False):
    """Place a call using a specific assistant and phone number"""
    url = f"{VAPI_API_BASE_URL}/call"

    # Check if assistant_id is valid
    if not assistant_id:
        print("Error: Assistant ID is missing or invalid")
        return None

    payload = {
        "type": "outboundPhoneCall",
        "assistantId": assistant_id,
        "phoneNumberId": from_number_id,
        "customer": {
            "number": to_number
        }
    }

    # Debug: Print the payload
    if debug:
        print(f"Request payload: {json.dumps(payload, indent=2)}")

    try:
        response = requests.post(url, headers=headers, json=payload)

        # Try to get JSON response even if status code is not 200
        try:
            response_json = response.json()
            if debug:
                print(f"Response status: {response.status_code}")
                print(f"Response body: {json.dumps(response_json, indent=2)}")
        except:
            if debug:
                print(f"Response status: {response.status_code}")
                print(f"Response text: {response.text}")

        response.raise_for_status()

        call_data = response.json()
        print(f"Successfully placed call with ID: {call_data.get('id')}")
        return call_data
    except requests.exceptions.RequestException as e:
        print(f"Error placing call: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return None

def get_call_status(call_id):
    """Get the current status of a call"""
    call_status_url = f"{VAPI_API_BASE_URL}/call/{call_id}"

    try:
        status_response = requests.get(call_status_url, headers=headers)
        status_response.raise_for_status()

        call_data = status_response.json()
        return call_data
    except requests.exceptions.RequestException as e:
        print(f"Error checking call status: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return None

def get_call_transcript(call_id):
    """Get the transcript for a completed call"""
    # First get the full call data
    call_data = get_call_status(call_id)

    if not call_data:
        print(f"Error: Could not retrieve call data for call ID: {call_id}")
        return None

    # Check if the call has a transcript in the artifact
    if call_data.get('artifact') and call_data['artifact'].get('transcript'):
        return call_data

    # If no transcript is found but the call is completed, try to fetch it again
    # Sometimes the transcript takes a moment to be processed after the call ends
    if call_data.get('status') in ['completed', 'ended']:
        print("Call is completed but no transcript found. Waiting 5 seconds and trying again...")
        time.sleep(5)

        # Try again
        call_data = get_call_status(call_id)
        if call_data and call_data.get('artifact') and call_data['artifact'].get('transcript'):
            print("Successfully retrieved transcript on second attempt.")
            return call_data

    # Return whatever we have, even if transcript is missing
    return call_data

def upload_transcript_to_supabase(call_data, call_id):
    """Upload the call transcript directly to Supabase without saving locally"""
    try:
        print(f"\nProcessing transcript for call {call_id}...")
        
        # Upload transcript to Supabase directly using call data
        try:
            print("Uploading transcript to Supabase...")
            if upload_call_data_to_supabase(call_data):
                print("Successfully uploaded transcript to Supabase voicebot_data table")
                return True
            else:
                print("Failed to upload transcript to Supabase")
                return False
        except Exception as e:
            print(f"Error uploading transcript to Supabase: {e}")
            return False
            
    except Exception as e:
        print(f"Error processing transcript: {e}")
        return False

def monitor_call(call_id, interval=5, max_time=300, debug=False):
    """Monitor a call until it completes or max_time is reached"""
    print(f"\nMonitoring call {call_id}...")
    start_time = time.time()
    last_status = None

    while time.time() - start_time < max_time:
        call_data = get_call_status(call_id)

        if not call_data:
            print("Failed to get call status. Stopping monitoring.")
            return

        current_status = call_data.get('status')

        # Only print if status has changed
        if current_status != last_status:
            print(f"Call status: {current_status} (at {time.strftime('%H:%M:%S')})")
            last_status = current_status

        # Check if call has ended
        if current_status in ['completed', 'ended', 'failed', 'busy', 'no-answer', 'canceled']:
            print(f"\nCall has ended with status: {current_status}")
            if call_data.get('endedReason'):
                print(f"End reason: {call_data.get('endedReason')}")
            if call_data.get('cost'):
                print(f"Call cost: ${call_data.get('cost')}")

            # Debug: Print call data to see what we're working with
            if debug:
                print("\nCall data received:")
                print(json.dumps(call_data, indent=2))

            # Save the transcript when call ends (trying for both 'completed' and 'ended' statuses)
            transcript_saved = False
            if current_status in ['completed', 'ended']:
                print("\nAttempting to save transcript...")

                # Try to get the most complete transcript data
                print("Fetching complete transcript data...")
                transcript_data = get_call_transcript(call_id)

                # Use the transcript data if available, otherwise use the current call data
                data_to_save = transcript_data if transcript_data else call_data

                transcript_success = upload_transcript_to_supabase(data_to_save, call_id)
                if transcript_success:
                    print("Call transcript uploaded successfully")
                    transcript_saved = True
                else:
                    print("Failed to upload call transcript")

            # If we couldn't save transcript but have transcript data, try to save it anyway
            if not transcript_saved and call_data.get('transcript'):
                print("\nTrying to upload transcript from transcript data...")
                transcript_success = upload_transcript_to_supabase(call_data, call_id)
                if transcript_success:
                    print("Call transcript uploaded successfully")

            return call_data  # Return call data for further processing if needed

        # Wait before checking again
        time.sleep(interval)

    print(f"\nReached maximum monitoring time of {max_time} seconds. Call may still be in progress.")
    return None

def main():
    # Parse command line arguments
    args = parse_arguments()
    to_number = args.to_number
    debug_mode = args.debug

    # Check if we're just retrieving a transcript for a past call
    if args.get_transcript:
        call_id = args.get_transcript
        print(f"\nRetrieving transcript for call ID: {call_id}")

        # Get the call data with transcript
        call_data = get_call_transcript(call_id)

        if call_data:
            # Upload the transcript
            transcript_success = upload_transcript_to_supabase(call_data, call_id)
            if transcript_success:
                print(f"\nTranscript for call {call_id} uploaded successfully.")
            else:
                print(f"\nFailed to upload transcript for call {call_id}.")
        else:
            print(f"\nFailed to retrieve call data for call ID: {call_id}")

        return  # Exit after retrieving transcript

    # Use the assistant ID from arguments (which has a new default)
    assistant_id = args.assistant_id
    # We'll fetch assistant details later if needed for name, or just use ID
    assistant_name = f"Assistant ID {assistant_id}" 

    print(f"\nUsing assistant: {assistant_name}")

    # Get phone numbers
    phone_numbers = get_phone_numbers(debug=debug_mode)
    if not phone_numbers:
        print("No phone numbers found. Please add a phone number in the VAPI dashboard.")
        return

    # Print all phone numbers for reference
    print("\nAvailable Phone Numbers:")
    for i, phone in enumerate(phone_numbers):
        phone_id = phone.get('id')
        phone_number = phone.get('twilioPhoneNumber', 'Unknown')
        print(f"{i+1}. {phone_number} (ID: {phone_id})")

    # Use specified phone ID or use the first available (now args.phone_id has a default)
    if args.phone_id:
        # Find the phone with the specified ID
        selected_phone = next((p for p in phone_numbers if p.get('id') == args.phone_id), None)

        if not selected_phone:
            print(f"Phone number with ID '{args.phone_id}' not found. Defaulting to first available or please check the ID and try again.")
            # Fallback to first phone if specified default ID is not found
            if phone_numbers:
                phone_number_id = phone_numbers[0].get('id')
                phone_number = phone_numbers[0].get('twilioPhoneNumber', 'Unknown')
                print(f"\nUsing first available phone number: {phone_number} (ID: {phone_number_id})")
            else:
                print("No phone numbers available.")
                return
        else:
            phone_number_id = args.phone_id
            phone_number = selected_phone.get('twilioPhoneNumber', 'Unknown')
            print(f"\nUsing specified phone number: {phone_number} (ID: {phone_number_id})")
    else: # Should not be reached if phone_id has a default, but kept for safety
        # Use the first phone number
        phone_number_id = phone_numbers[0].get('id')
        phone_number = phone_numbers[0].get('twilioPhoneNumber', 'Unknown')
        print(f"\nUsing first available phone number: {phone_number} (ID: {phone_number_id})")

    # Place the call
    print(f"\nPlacing call to {to_number} using assistant '{assistant_name}'...")
    call_data = place_call(assistant_id, phone_number_id, to_number, debug=debug_mode)

    if call_data:
        print("\nCall placed successfully!")
        print(f"Call ID: {call_data.get('id')}")
        print(f"Call Status: {call_data.get('status')}")

        call_id = call_data.get('id')

        if args.monitor:
            # Monitor the call until it completes
            monitor_call(call_id, debug=debug_mode)
        else:
            # Just check status once after a short delay
            print("\nWaiting for 5 seconds to check call status...")
            time.sleep(5)

            updated_call_data = get_call_status(call_id)
            if updated_call_data:
                print(f"\nUpdated Call Status: {updated_call_data.get('status')}")
                print("\nTo monitor this call until completion, run:")
                print(f"python app.py --assistant-id {assistant_id} --phone-id {phone_number_id} --to {to_number} --monitor")
    else:
        print("\nFailed to place call. Please check the error messages above.")

if __name__ == "__main__":
    main()