from flask import Blueprint, render_template, request, redirect, url_for, session, jsonify, make_response
from werkzeug.security import check_password_hash, generate_password_hash
from functools import wraps
import os
import jwt
import datetime
from datetime import timedelta
from dotenv import load_dotenv

auth = Blueprint('auth', __name__)

# Load environment variables once at startup
load_dotenv()

# Get admin credentials from environment variables
ADMIN_USERNAME = os.getenv('ADMIN_USERNAME', 'admin')
ADMIN_PASSWORD = os.getenv('ADMIN_PASSWORD', 'admin12345')
JWT_SECRET = os.getenv('JWT_SECRET', 'default_secret_key')
JWT_EXPIRATION = int(os.getenv('JWT_EXPIRATION', '3600'))  # Default 1 hour

# Initialize user database with admin credentials
users = {
    ADMIN_USERNAME: generate_password_hash(ADMIN_PASSWORD),
}


def generate_token(username):
    """Generate a JWT token for the user"""
    print("GENERATING A TOKEN")
    payload = {
        'username': username,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(seconds=JWT_EXPIRATION)
    }
    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # Get token from Authorization header
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
        
        # Check for token in cookies (for web browser flows)
        if not token and request.cookies.get('token'):
            token = request.cookies.get('token')
        
        # Alternatively, check if token is in request parameters
        if not token:
            token = request.args.get('token')
            
        if not token:
            return jsonify({'message': 'Token is missing!'}), 401
            
        try:
            data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            # You could fetch user details here if needed
            current_user = data['username']
        except:
            return jsonify({'message': 'Token is invalid!'}), 401
            
        return f(current_user=current_user, *args, **kwargs)
    
    return decorated

@auth.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        # Check if request wants JSON response (API call)
        wants_json = request.headers.get('Accept') == 'application/json'
        
        if username in users and check_password_hash(users[username], password):
            # Generate JWT token
            token = generate_token(username)
            
            # Return appropriate response based on client preference
            if wants_json:
                return jsonify({
                    'message': 'Login successful',
                    'token': token,
                    'username': username
                })
            else:
                # For web flow, set token as cookie and redirect
                response = make_response(redirect(url_for('index')))
                # Set cookie to expire when the token expires
                response.set_cookie('token', token, max_age=JWT_EXPIRATION, httponly=True)
                return response
                
        if wants_json:
            return jsonify({'message': 'Invalid credentials'}), 401
        return "Invalid credentials", 401
        
    return render_template('login.html')

@auth.route('/logout')
def logout():
    # Create response and clear the token cookie
    response = make_response(redirect(url_for('auth.login')))
    response.delete_cookie('token')
    return response

def login_required(view):
    @wraps(view)
    def wrapped_view(*args, **kwargs):
        token = request.cookies.get('token')
        
        if not token:
            return redirect(url_for('auth.login'))
            
        try:
            data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            # Token is valid, continue to the view
            return view(*args, **kwargs)
        except:
            # Token is invalid or expired, redirect to login
            return redirect(url_for('auth.login'))
    
    return wrapped_view