.chart-dropdown-container {
    position: relative;
    z-index: 10;
}

.chart-dropdown-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: var(--theme-selector-bg, #ffffff);
    color: var(--theme-selector-color, #212529);
    border: 1px solid var(--theme-selector-border, #dee2e6);
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    min-width: 70px;
}

.chart-dropdown-btn:hover {
    background-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.1));
}

.chart-dropdown-btn svg {
    transition: transform 0.3s ease;
}

.chart-dropdown-btn[aria-expanded="true"] svg {
    transform: rotate(180deg);
}

.chart-dropdown-menu {
    position: absolute;
    right: 0;
    top: calc(100% + 0.25rem);
    background-color: var(--theme-selector-menu-bg, #ffffff);
    border: 1px solid var(--theme-selector-menu-border, #dee2e6);
    border-radius: 0.375rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-width: 10rem;
}

.chart-dropdown-item {
    display: block;
    padding: 0.5rem 1rem;
    color: var(--theme-option-color, #212529);
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.chart-dropdown-item:hover {
    background-color: var(--theme-option-hover-bg, #f1f5f9);
}

/* Add this to your existing CSS */
.chart-dropdown-btn i,
.chart-dropdown-item i {
    display: inline-block;
    width: 16px;
    text-align: center;
}

.chart-dropdown-item {
    display: flex;
    align-items: center;
}