import requests
import os
import json

# Authentication credentials
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')

# Payload data
payload = {
    "friendly_name": "massage_menu_cara", # Changed friendly name
    "language": "en",
    "types": {
        "twilio/carousel": {
            "body": "Schedule Your Relaxing Massage!", # Changed body text
            "cards": [
                {
                    "title": "Alquimist Massage (90 min)", # Changed title
                    "body": "Masaje corporal <PERSON><PERSON> (90 min)\n€225.00", # Changed body
                    "media": "https://images-new.vercel.app/starterimages/massage.png", # Changed media URL
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Schedule Massage", # Changed button title
                            "id": "schedule_massage_90" # Changed button ID
                        }
                    ]
                },
                {
                    "title": "Alquimist Massage (60 min)", # Changed title
                    "body": "Masaje corporal <PERSON><PERSON><PERSON> (60 min)\n€195.00", # Changed body
                    "media": "https://images-new.vercel.app/starterimages/massage.png", # Changed media URL
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Schedule Massage", # Changed button title
                            "id": "schedule_massage_60_body" # Changed button ID
                        }
                    ]
                },
                {
                    "title": "Alquimist Massage (60 min)", # Changed title
                    "body": "Masaje Facial Absoluto (60 min)\n€195.00", # Changed body
                    "media": "https://images-new.vercel.app/starterimages/massage.png", # Changed media URL
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Schedule Massage", # Changed button title
                            "id": "schedule_massage_60_facial" # Changed button ID
                        }
                    ]
                }
            ]
        }
    }
}

# Make the POST request
response = requests.post(
    'https://content.twilio.com/v1/Content',
    auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN),
    json=payload
)

# Enhanced response handling
print("\n=== Response Details ===")
print(f"Status Code: {response.status_code}")
print("\n=== Headers ===")
for header, value in response.headers.items():
    print(f"{header}: {value}")

print("\n=== Response Content ===")
try:
    # Try to print formatted JSON
    print(json.dumps(response.json(), indent=2))
except json.JSONDecodeError:
    # If not JSON, print raw text
    print(response.text)

print("\n=== Request Details ===")
print(f"Request URL: {response.request.url}")
print(f"Request Method: {response.request.method}")
print("Request Headers:")
for header, value in response.request.headers.items():
    print(f"{header}: {value}")

print("\n=== Timing ===")
print(f"Elapsed Time: {response.elapsed.total_seconds()} seconds")

if response.status_code != 200:
    print("\n=== Error Details ===")
    print(f"Error Status Code: {response.status_code}")
    print("Error Response:", response.text)
