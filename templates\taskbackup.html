<!-- TESTING -->
<!--Dix testing-->
<!--(The UI is done and perfect, Backend is done and perfect.)-->
<!--Just test out if there are any bugs and make sure its perfect-->
<!--ESTIMATED TIME : 5-10 min-->

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Tasks</title>
    {% include 'imports.html' %}

</head>
<style>
    body {
        visibility: hidden;
    }
</style>

<body class="bg-background text-foreground">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />



    <!-- Franken UI -->

    <style>
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }

        .table-container {
            max-height: calc(100vh - 200px);
            overflow-y: visible;
        }

        .task-item {
            transition: transform 0.5s ease-out, opacity 0.5s ease-out;
        }

        .task-disappear {
            opacity: 0;
            transform: translateY(-20px);
        }

        .table-container thead {
            position: sticky;
            top: 0;
            z-index: 1;
            background-color: #fff;
        }

        .platform-tag {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: #d1fae5;
            color: #065f46;
        }

        .platform-tag-icon {
            width: 10px;
            height: 10px;
            margin-right: 4px;
            opacity: 0.7;
        }

        .task-disappear {
            animation: disappear 0.5s ease-out forwards;
        }

        @keyframes disappear {
            0% {
                opacity: 1;
                transform: translateY(0);
            }

            100% {
                opacity: 0;
                transform: translateY(-20px);
            }
        }

        .checked {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
        }

        .line-through {
            text-decoration: line-through;
        }

        .opacity-0 {
            opacity: 0;
            transition: opacity 0.5s ease-out;
        }

        .hidden {
            display: none;
        }

        .checked::after {
            content: '';
            display: block;
            position: absolute;
            width: 1.2em;
            height: 1.2em;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
        }

        #taskPopup {
            transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
            visibility: hidden;
            opacity: 0;
        }

        #taskPopup.show {
            visibility: visible;
            opacity: 1;
        }

        #taskPopup>div {
            transition: transform 0.3s ease-in-out;
            transform: scale(0.9);
        }

        #taskPopup.show>div {
            transform: scale(1);
        }

        .task-item:not(:last-child) {
            border-bottom: 1px solid #e4e4e7;
            padding-bottom: 16px;
            margin-bottom: 16px;
        }

        :root {
            --checkbox-border-color: #2a2a2d;
            /* or any other dark color you prefer */
        }

        .checkboxwomp.checked::after {
            content: '\2715';
            /* Unicode for "×" */
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            color: white;
            font-size: 0.8em;
        }

        .checkboxwomp.disabled {
            pointer-events: none;
            opacity: 0.6;
        }

        .task-text.line-through {
            text-decoration: line-through;
            opacity: 0.7;
        }

        .checkboxwomp[data-state=unchecked] {
            border-color: var(--checkbox-border-color);
        }

        .task-text {
            margin-left: 10px;
            /* Add this line to create space between checkbox and text */
        }

        .checkboxwomp {
            background-color: transparent !important;
            /* Make background transparent */
        }

        .checkboxwomp.checked {
            background-color: black !important;
            /* Keep the background black when checked */
        }

        .checkboxwomp[data-state=unchecked] {
            border-color: var(--checkbox-border-color);
            background-color: transparent !important;
            /* Ensure unchecked state is transparent */
        }

        .checkboxwomp:not(.checked) {
            background-color: transparent !important;
            /* Additional rule to ensure transparency */
        }

        /* Add tooltip styles */
        .description-text {
            position: relative;
            cursor: default;
        }

        /* For browsers that don't support the uk-tooltip attribute */
        .description-text[title]:hover::after {
            content: attr(title);
            position: absolute;
            left: 0;
            top: 100%;
            z-index: 1000;
            background: #ffffff;
            color: #333333;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            white-space: normal;
            max-width: 300px;
            word-wrap: break-word;
            font-size: 0.875rem;
            line-height: 1.4;
            pointer-events: none;
            margin-top: 5px;
        }

        /* Add a pseudo-element for the arrow */
        .description-text[title]:hover::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 100%;
            border-width: 5px;
            border-style: solid;
            border-color: transparent transparent #e2e8f0 transparent;
            transform: translateY(-10px);
            z-index: 1001;
        }

        /* Dark mode support */
        .dark .description-text[title]:hover::after {
            background: #1f2937;
            color: #ffffff;
            border-color: #374151;
        }

        .dark .description-text[title]:hover::before {
            border-color: transparent transparent #374151 transparent;
        }

        /* Complete tooltip overhaul - more reliable implementation */
        .task-tooltip-container {
            position: relative;
            display: inline-block;
        }

        .task-tooltip {
            visibility: hidden;
            position: absolute;
            z-index: 9999;
            left: 0;
            top: 100%;
            width: 300px;
            margin-top: 10px;
            padding: 10px;
            background-color: #ffffff;
            color: #333333;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            text-align: left;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s, visibility 0.3s;
            word-wrap: break-word;
            pointer-events: none;
        }

        .task-tooltip-container:hover .task-tooltip {
            visibility: visible;
            opacity: 1;
        }

        /* Triangle pointer */
        .task-tooltip::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 20px;
            border-width: 8px;
            border-style: solid;
            border-color: transparent transparent #e2e8f0 transparent;
        }

        .task-tooltip::after {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 20px;
            border-width: 7px;
            border-style: solid;
            border-color: transparent transparent #ffffff transparent;
            margin-left: 1px;
        }

        /* Dark mode support */
        .dark .task-tooltip {
            background-color: #1f2937;
            color: #ffffff;
            border-color: #374151;
        }

        .dark .task-tooltip::before {
            border-color: transparent transparent #374151 transparent;
        }

        .dark .task-tooltip::after {
            border-color: transparent transparent #1f2937 transparent;
        }

        /* Override any UIkit styles that might interfere */
        [uk-tooltip] {
            position: static !important;
        }
    </style>
    </head>

    <body class="light">
        {% include 'components/loading.html' %}

        <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] ">
            {% include 'sidebar.html' %}

            <div class="flex flex-col">
                <header class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
                    <div class="flex items-center gap-1">
                        <button id="toggle-btn" class="opacity-100 transition-opacity duration-300 focus:outline-none"
                            style="background-color: transparent !important;">
                            <svg xmlns="http://www.w3.org/2000/svg" id="toggle-icon" width="20" height="20"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-panel-left-close">
                                <rect width="18" height="18" x="3" y="3" rx="2" />
                                <path d="M9 3v18" />
                                <path d="m16 15-3-3 3-3" />
                            </svg>
                        </button>
                        <a class="lg:hidden text-gray-600 hover:text-gray-800" href="#">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="h-6 w-6">
                                <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                                <path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1L21 9"></path>
                                <path d="M12 3v6"></path>
                            </svg>
                            <span class="sr-only">Home</span>
                        </a>
                        <h1 class="font-semibold text-lg">Tasks</h1>
                    </div>
                    {% include 'topright.html' %}
                </header>
                <main class="card flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6">
                    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                        <div class=" card metric-container rounded-lg border shadow-sm relative overflow-hidden"
                            data-v0-t="card">
                            <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Total Lifetime tasks</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="text-gray-500 dark:text-gray-400 lucide lucide-calendar-check-icon lucide-calendar-check">
                                    <path d="M8 2v4"/>
                                    <path d="M16 2v4"/>
                                    <rect width="18" height="18" x="3" y="4" rx="2"/>
                                    <path d="M3 10h18"/>
                                    <path d="m9 16 2 2 4-4"/>
                                </svg>
                            </div>
                            <div class="p-6">
                                <div class="text-2xl font-bold" id="total-revenue">3424</div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">All tasks ever created</p>
                            </div>

                            <div class="absolute bottom-7 right-4 flex items-center text-xs text-red-600 border border-red-500 px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-down-narrow-wide mr-0.5">
                                    <path d="m3 16 4 4 4-4" />
                                    <path d="M7 20V4" />
                                    <path d="M11 4h4" />
                                    <path d="M11 8h7" />
                                    <path d="M11 12h10" />
                                </svg>
                                3.2%
                            </div>
                            <!-- Gradient Chart Overlay -->
                            <div class="absolute bottom-0 right-0 w-36 h-32">
                                
                            </div>
                        </div>

                        <style>
                            .metric-container {
                                position: relative;
                                overflow: hidden;
                            }
                        </style>
                        <div class=" card metric-container rounded-lg border shadow-sm relative overflow-hidden"
                            data-v0-t="card">
                            <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Todays Tasks</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="text-gray-500 dark:text-gray-400 lucide lucide-calendar-check2-icon lucide-calendar-check-2">
                                    <path d="M8 2v4"/>
                                    <path d="M16 2v4"/>
                                    <path d="M21 14V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8"/>
                                    <path d="M3 10h18"/>
                                    <path d="m16 20 2 2 4-4"/>
                                </svg>
                            </div>
                            <div class="p-6">
                                <div class="text-2xl font-bold" id="total-reservations">2312</div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Tasks created today</p>
                            </div>

                            <div class="absolute bottom-7 right-4 flex items-center text-xs text-green-600 border border-green-500 bg-transparent px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-narrow-wide mr-0.5">
                                    <path d="m3 8 4-4 4 4" />
                                    <path d="M7 4v16" />
                                    <path d="M11 12h4" />
                                    <path d="M11 16h7" />
                                    <path d="M11 20h10" />
                                </svg>
                                8.5%
                            </div>
                            <!-- Gradient Chart Overlay with Different Curve -->
                            <div class="absolute bottom-0 right-0 w-36 h-32">
                                
                            </div>
                        </div>
                        <div class="card metric-container rounded-lg border shadow-sm relative overflow-hidden"
                            data-v0-t="card">
                            <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Pending tasks</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="text-gray-500 dark:text-gray-400 lucide lucide-clock-arrow-up-icon lucide-clock-arrow-up">
                                    <path d="M13.228 21.925A10 10 0 1 1 21.994 12.338"/>
                                    <path d="M12 6v6l1.562.781"/>
                                    <path d="m14 18 4-4 4 4"/>
                                    <path d="M18 22v-8"/>
                                </svg>
                            </div>
                            <div class="p-6">
                                <div class="text-2xl font-bold" id="pending-tasks">4256</div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Tasks not yet completed</p>
                            </div>

                            <div class="absolute bottom-7 right-4 flex items-center text-xs text-red-600 border border-red-500 px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-down-narrow-wide mr-0.5">
                                    <path d="m3 16 4 4 4-4" />
                                    <path d="M7 20V4" />
                                    <path d="M11 4h4" />
                                    <path d="M11 8h7" />
                                    <path d="M11 12h10" />
                                </svg>
                                3.2%
                            </div>
                            <!-- Gradient Chart Overlay with Different Curve -->
                            <div class="absolute bottom-0 right-0 w-36 h-32">
                                
                            </div>
                        </div>
                        <div class=" card metric-container rounded-lg border shadow-sm relative overflow-hidden"
                            data-v0-t="card">
                            <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Finished tasks</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="text-gray-500 dark:text-gray-400 lucide lucide-clipboard-check-icon lucide-clipboard-check">
                                    <rect width="8" height="4" x="8" y="2" rx="1" ry="1"/>
                                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                                    <path d="m9 14 2 2 4-4"/>
                                </svg>
                            </div>
                            <div class="p-6">
                                <div class="text-2xl font-bold" id="finished-tasks">3565</div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Completed tasks</p>
                            </div>

                            <div class="absolute bottom-7 right-4 flex items-center text-xs text-green-600 border border-green-500 bg-transparent px-1.5 py-0.5 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-narrow-wide mr-0.5">
                                    <path d="m3 8 4-4 4 4" />
                                    <path d="M7 4v16" />
                                    <path d="M11 12h4" />
                                    <path d="M11 16h7" />
                                    <path d="M11 20h10" />
                                </svg>
                                8.5%
                            </div>
                            <!-- Gradient Chart Overlay with Smoother Curve -->
                            <div class="absolute bottom-0 right-0 w-36 h-32">
                                
                                </div>
                            </div>
                            <div class="card metric-container rounded-lg border shadow-sm relative overflow-hidden"
                                data-v0-t="card">
                                <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                    <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Avg Completion Time</h3>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="text-gray-500 dark:text-gray-400 lucide lucide-calendar-clock-icon lucide-calendar-clock">
                                        <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5"/>
                                        <path d="M16 2v4"/>
                                        <path d="M8 2v4"/>
                                        <path d="M3 10h5"/>
                                        <path d="M17.5 17.5 16 16.3V14"/>
                                        <circle cx="16" cy="16" r="6"/>
                                    </svg>
                                </div>
                                <div class="p-6">
                                    <div class="text-2xl font-bold" id="avg-completion-time">47m</div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Average task completion time</p>
                                </div>
                                <div class="absolute bottom-7 right-4 flex items-center text-xs text-green-600 border border-green-500 px-1.5 py-0.5 rounded-md">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-narrow-wide mr-0.5">
                                        <path d="m3 8 4-4 4 4" />
                                        <path d="M7 4v16" />
                                        <path d="M11 12h4" />
                                        <path d="M11 16h7" />
                                        <path d="M11 20h10" />
                                    </svg>
                                    5.1%
                                </div>
                                <!-- Gradient Chart Overlay -->
                                <div class="absolute bottom-0 right-0 w-36 h-32">
                            </div>
                        </div>
                    </div>
                    <main class="flex flex-1 flex-col">
                        <div class="border shadow-sm h-full rounded-lg overflow-hidden card">
                            <div class="relative w-full overflow-auto h-full">
                                <table class="w-full caption-bottom text-sm">
                                    <thead class="[&amp;_tr]:border-b">
                                        <tr
                                            class="card border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                                            <th
                                                class="h-11 px-2 text-left align-middle font-medium text-muted-foreground [&amp;:has([role=checkbox])]:pr-0">
                                                <div class="p-2 mr-auto">
                                                    <div class="uk-inline w-1/2">
                                                        <span class="uk-form-icon text-muted-foreground">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16"
                                                                height="16" viewBox="0 0 24 24" fill="none"
                                                                stroke="currentColor" stroke-width="2"
                                                                stroke-linecap="round" stroke-linejoin="round"
                                                                class="lucide lucide-calendar-search">
                                                                <path d="M16 2v4" />
                                                                <path
                                                                    d="M21 11.75V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7.25" />
                                                                <path d="m22 22-1.875-1.875" />
                                                                <path d="M3 10h18" />
                                                                <path d="M8 2v4" />
                                                                <circle cx="18" cy="18" r="3" />
                                                            </svg>
                                                        </span>

                                                        <input id="searchInput"
                                                            class="uk-input rounded-md border border-gray-300 p-2 focus:border-blue-300 focus:outline-none"
                                                            type="text" placeholder="Search.....">
                                                    </div>
                                                </div>
                                            </th>
                                            <th class="h-12 text-center align-middle font-medium text-muted-foreground">
                                                Platform
                                            </th>
                                            <th class="h-12 text-center align-middle font-medium text-muted-foreground">
                                                Language
                                            </th>
                                            <th class="h-12 text-center align-middle font-medium text-muted-foreground">
                                                Guest
                                            </th>
                                            <th class="h-12 text-center align-middle font-medium text-muted-foreground">
                                                Phone
                                            </th>
                                            <th class="h-12 text-center align-middle font-medium text-muted-foreground">
                                                Time
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="[&amp;_tr:last-child]:border-0" id="taskList">
                                        <!-- Task elements will be dynamically generated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </main>

                    <div id="taskPopup" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
                        max-width="400px">
                        <div class="theme-card p-6 rounded-lg shadow-lg max-w-sm w-full">
                            <h2 class="theme-title text-xl font-semibold mb-4">Confirm Task Completion</h2>
                            <p id="popupTaskName" class="theme-text mb-2" style="max-width: 1000px;"></p>
                            <p id="popupTaskDescription" class="theme-text text-sm text-muted-foreground mb-6"
                                style="max-width: 1000px;"></p>
                            <div class="flex justify-end">
                                <button id=confirmTask class="uk-button border card ">Confirm</button>
                            </div>
                        </div>
                    </div>

                    <div id="notification"
                        class="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-y-full opacity-0 flex items-center">
                        <span id="notificationText" class="mr-4"></span>
                        <button id="undoButton"
                            class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
                            Undo
                        </button>
                    </div>

                    <script>
                        let currentTaskType = 'incomplete';

                        // Function to fetch tasks from the Flask backend
                        async function fetchTasks() {
                            try {
                                const endpoint = currentTaskType === 'incomplete' ? '/fetch-tasks' : '/fetch-completed-tasks';
                                const response = await fetch(endpoint);
                                const tasks = await response.json();
                                return tasks;
                            } catch (error) {
                                console.error('Error fetching tasks:', error);
                                return [];
                            }
                        }

                        // Function to get the flag URL based on the language
                        function getFlagUrl(language) {
                            const flagUrls = {
                                English: 'https://cdn-icons-png.flaticon.com/128/197/197374.png',
                                Spanish: 'https://uxwing.com/wp-content/themes/uxwing/download/flags-landmarks/spain-country-flag-icon.png',
                                France: 'https://cdn.countryflags.com/thumbs/france/flag-400.png',
                                Italy: 'https://cdn-icons-png.flaticon.com/128/3373/3373278.png',
                                Dutch: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQB3kPISjRFcQtqcQsUkpAi3GO4F7R528HflTkbojq7h_uT15o_omPAxGI-NQ&s',
                                Swedish: 'https://uxwing.com/wp-content/themes/uxwing/download/flags-landmarks/sweden-flag-icon.png',
                                German: 'https://uxwing.com/wp-content/themes/uxwing/download/flags-landmarks/germany-flag-icon.png',
                                Indonesian: 'https://uxwing.com/wp-content/themes/uxwing/download/flags-landmarks/indonesia-flag-icon.png',
                            };
                            return flagUrls[language] || '';
                        }

                        function createTaskElement(task) {
                            const taskElement = document.createElement('tr');
                            taskElement.classList.add('card', 'border-b', 'transition-colors', 'hover:bg-muted/50', 'data-[state=selected]:bg-muted', 'task-item');
                            taskElement.dataset.taskId = task.id;

                            const platform = task.platform;
                            const capitalizedPlatform = platform.charAt(0).toUpperCase() + platform.slice(1).toLowerCase();

                            const isCompleted = currentTaskType === 'completed';
                            const checkboxClass = isCompleted ? 'checked disabled' : '';
                            const taskTextClass = isCompleted ? 'line-through' : '';

                            // Handle description truncation
                            const fullDescription = task.description || '';
                            const truncatedDescription = fullDescription.length > 32 ?
                                `${fullDescription.substring(0, 32)}...` :
                                fullDescription;

                            taskElement.innerHTML = `
                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">
                    <div class="flex items-center gap-2">
                        <button
                            type="button"
                            role="checkbox"
                            aria-checked="${isCompleted ? 'true' : 'false'}"
                            data-state="${isCompleted ? 'checked' : 'unchecked'}"
                            value="on"
                            class="peer h-4 w-4 shrink-0 rounded-sm checkboxwomp border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-black data-[state=checked]:border-black ${checkboxClass}"
                            id="task-${task.id}"
                            ${isCompleted ? 'disabled' : `onclick="toggleTask(this, ${task.id}, '${task.Task.replace(/'/g, "\\'")}', '${fullDescription.replace(/'/g, "\\'")}')"`}
                        ></button>
                        <div class="task-text ${taskTextClass}">
                            <p class="text-base font-medium">${task.Task}</p>
                            <div class="task-tooltip-container">
                                <p class="text-sm text-muted-foreground">${truncatedDescription}</p>
                                ${fullDescription.length > 32 ?
                                    `<div class="task-tooltip">${fullDescription}</div>` : ''}
                            </div>
                        </div>
                    </div>
                </td>
                <td class="align-middle text-center">
                    ${platform.toLowerCase() === 'whatsapp' ?
                                    `<img src="../static/icons/black-whatsapp-icon.png" 
                            alt="WhatsApp" 
                            title="WhatsApp"
                            class="whatsapp-theme-icon mx-auto"
                            style="width: 24px; height: 24px;"
                            loading="lazy">`
                                    : sanitizeHTML(platform)}
                </td>
                <td class="align-middle text-center">
                    <img src="${getFlagUrl(task.language)}" alt="${task.language} Flag" style="width: 24px; height: 24px;" class="mx-auto" title="${task.language}" />
                </td>
                <td class="align-middle text-center">
                    ${task.customer || 'N/A'}
                </td>
                <td class="align-middle text-center">
                    ${task.Phone}
                </td>
                <td class="align-middle text-center">
                    ${task.Time_stamp}
                </td>
            `;

                            // Add a data attribute with all searchable content
                            const searchContent = `${task.Task} ${fullDescription} ${task.customer || ''} ${task.Phone} ${task.Time_stamp}`.toLowerCase();
                            taskElement.dataset.searchContent = searchContent;

                            return taskElement;
                        }

                        async function updateTasks() {
                            const tasks = await fetchTasks();
                            const taskList = document.getElementById('taskList');
                            taskList.innerHTML = ''; // Clear existing tasks

                            tasks.reverse().forEach((task) => {
                                const newTaskElement = createTaskElement(task);
                                taskList.appendChild(newTaskElement);
                            });

                            // Apply the filter after updating the tasks
                            filterTasks();

                            // Initialize tooltips if using UIkit (backup approach)
                            if (typeof UIkit !== 'undefined' && UIkit.tooltip) {
                                document.querySelectorAll('[uk-tooltip]').forEach(el => {
                                    UIkit.tooltip(el);
                                });
                            }
                        }

                        async function toggleTask(button, taskId, taskName, taskDescription) {
                            if (currentTaskType === 'completed') {
                                return; // Do nothing if the task is already completed
                            }

                            const isChecked = button.getAttribute('data-state') === 'checked';
                            if (!isChecked) {
                                const popup = document.getElementById('taskPopup');
                                const popupTaskName = document.getElementById('popupTaskName');
                                const popupTaskDescription = document.getElementById('popupTaskDescription');
                                popupTaskName.textContent = taskName;
                                popupTaskDescription.textContent = taskDescription || '';
                                popup.classList.add('show');

                                const confirmButton = document.getElementById('confirmTask');
                                confirmButton.onclick = async () => {
                                    popup.classList.remove('show');

                                    const taskElement = button.closest('.task-item');
                                    taskElement.classList.add('task-disappear');

                                    setTimeout(async () => {
                                        await deleteTask(taskId);
                                        await updateTasks();
                                    }, 500);
                                };
                            } else {
                                button.setAttribute('data-state', 'unchecked');
                                const taskElement = button.parentElement;
                                taskElement.classList.remove('line-through', 'opacity-0');
                                button.classList.remove('checked');
                                button.innerHTML = '';
                            }
                        }

                        async function deleteTask(taskId) {
                            try {
                                const response = await fetch(`/delete-task/${taskId}`, {
                                    method: 'DELETE',
                                });
                                if (!response.ok) {
                                    throw new Error('Failed to delete task');
                                }
                                console.log('Task deleted successfully');
                            } catch (error) {
                                console.error('Error deleting task:', error);
                            }
                        }

                        // Function for filtering tasks
                        function filterTasks() {
                            const searchInput = document.getElementById('searchInput');
                            const filter = searchInput.value.toLowerCase();
                            const taskItems = document.querySelectorAll('.task-item');

                            taskItems.forEach(item => {
                                const searchContent = item.dataset.searchContent;
                                if (searchContent.includes(filter)) {
                                    item.style.display = '';
                                } else {
                                    item.style.display = 'none';
                                }
                            });
                        }

                        document.addEventListener('DOMContentLoaded', () => {
                            const popup = document.getElementById('taskPopup');
                            popup.addEventListener('click', (e) => {
                                if (e.target === popup) {
                                    popup.classList.remove('show');
                                }
                            });

                            // Add event listener for the search input
                            const searchInput = document.getElementById('searchInput');
                            searchInput.addEventListener('input', filterTasks);

                            // Add event listeners for task type options
                            const taskTypeButton = document.getElementById('taskTypeButton');
                            const incompleteTasksOption = document.getElementById('incompleteTasksOption');
                            const completedTasksOption = document.getElementById('completedTasksOption');

                            function closeDropdown() {
                                UIkit.dropdown(taskTypeButton.nextElementSibling).hide(0);
                            }

                            incompleteTasksOption.addEventListener('click', (e) => {
                                e.preventDefault();
                                currentTaskType = 'incomplete';
                                taskTypeButton.textContent = 'Incomplete Tasks';
                                updateTasks();
                                closeDropdown();
                            });

                            completedTasksOption.addEventListener('click', (e) => {
                                e.preventDefault();
                                currentTaskType = 'completed';
                                taskTypeButton.textContent = 'Completed Tasks';
                                updateTasks();
                                closeDropdown();
                            });
                        });

                        // Initial load and periodic update
                        updateTasks();
                        setInterval(updateTasks, 4000);

                        // Add this to your existing script section
                        const updateWhatsAppIcons = () => {
                            document.querySelectorAll('.whatsapp-icon').forEach(icon => {
                                icon.src = `../static/icons/${document.body.classList.contains('dark') ? 'white' : 'black'}-whatsapp-icon.png`;
                            });
                        };

                        // Add observer for theme changes
                        const observer = new MutationObserver((mutations) => {
                            mutations.forEach((mutation) => {
                                if (mutation.attributeName === 'class') {
                                    updateWhatsAppIcons();
                                }
                            });
                        });

                        observer.observe(document.body, {
                            attributes: true
                        });
                    </script>

                    <!-- Add this right before </body> to ensure tooltips work -->
                    <script>
                        // Ensure tooltips are properly initialized after page load
                        document.addEventListener('DOMContentLoaded', function () {
                            // For UIkit tooltips (if using UIkit)
                            if (typeof UIkit !== 'undefined' && UIkit.tooltip) {
                                setTimeout(function () {
                                    document.querySelectorAll('[uk-tooltip]').forEach(el => {
                                        UIkit.tooltip(el).init();
                                    });
                                }, 500);
                            }
                        });
                    </script>

    </body>
</html>