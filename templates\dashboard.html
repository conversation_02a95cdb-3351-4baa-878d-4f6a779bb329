<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard</title>
    <!-- Add Geist font -->
    <link href="https://api.fontshare.com/v2/css?f[]=geist@400&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/franken-ui@1.1.0/dist/css/core.min.css" />
    <link rel="stylesheet" href="/static/styles/custom.css">
    <style>
        html,
        body,
        input,
        button,
        select,
        option,
        textarea {
            font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif !important;
        }
    </style>
    {% include 'imports.html' %}


<body class="light">
    <style>
        .sidebar {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 280px;
            overflow-y: auto;
            height: 100%;
        }

        .done {
            opacity: 0.5;
        }

        .custom-checkbox {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            width: 16px;
            height: 16px;
            border: 1px solid #A0AEC0;
            border-radius: 3px;
            background-color: transparent;
            cursor: pointer;
            position: relative;
        }

        .custom-checkbox:checked::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 10px;
            height: 10px;
            background-color: #3182CE;
            border-radius: 2px;
        }

        #sidebar {
            position: sticky;
            top: 0;
            height: 100vh;
            /* Force full viewport height */
            overflow-y: auto;
            /* Allow scrolling within the sidebar */
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .padding-adjuster {
            padding-left: 24px;
        }

        /* Custom dashboard grid layout with enhanced Conversation Analytics */
        .dashboard-metrics-grid {
            display: grid;
            gap: 1rem;
            grid-template-columns: repeat(2, 1fr);
            transition: all 0.3s ease-in-out;
        }

        @media (min-width: 1024px) {
            .dashboard-metrics-grid {
                grid-template-columns: 2fr 2fr 3fr 1fr;
                /* Total: 6.5fr - First half: 4fr (2+2), Second half: 2.5fr (0.5+2) */
            }
        }

        /* Smooth transitions for all metric cards */
        .metric-card {
            transition: all 0.3s ease-in-out, transform 0.2s ease-in-out;
        }

        /* Enhanced Conversation Analytics specific styling */
        .conversation-analytics-card {
            min-width: 0; /* Prevent flex shrinking issues */
        }

        /* Responsive adjustments for smaller screens */
        @media (max-width: 768px) {
            .dashboard-metrics-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (min-width: 768px) and (max-width: 1023px) {
            .dashboard-metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Ensure content within cards remains properly formatted */
        .metric-card .card-content {
            display: flex;
            flex-direction: column;
            height: 100%;
            min-height: 150px;
        }

        .metric-card .card-header {
            flex-shrink: 0;
        }

        .metric-card .card-body {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        /* Hover effect for Bookings and Occupancy title */
        .conversation-analytics-card #bookings-dropdown-btn:hover {
            background-color: rgba(0, 0, 0, 0.05) !important;
        }

        /* Dark mode hover effect */
        .pure-black .conversation-analytics-card #bookings-dropdown-btn:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }

        /* Dropdown options hover effect - match the selected state (but not on already selected) */
        #bookings-dropdown li:not(.uk-active) .bookings-option:hover {
            background-color: #f8f9fa !important;
        }

        /* Dark mode dropdown options hover effect - match the selected state (but not on already selected) */
        .pure-black #bookings-dropdown li:not(.uk-active) .bookings-option:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }

        /* Ensure selected state styling is consistent */
        #bookings-dropdown .uk-active > .bookings-option {
            background-color: #f8f9fa !important;
        }

        .pure-black #bookings-dropdown .uk-active > .bookings-option {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }

        /* Remove default UIkit hover effects except our custom ones */
        #bookings-dropdown .uk-dropdown-nav > li > a:not(.bookings-option):hover,
        #bookings-dropdown .uk-dropdown-nav > li:hover:not(.uk-active) {
            background-color: transparent !important;
            background: transparent !important;
        }

        /* Add the hoverable-card styles from analytics */
        .hoverable-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            perspective: 800px;
            box-shadow: 0 0 10px rgba(255, 110, 110, 0.15);
        }

        .hoverable-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 16px rgba(255, 110, 110, 0.25);
        }

        /* Ensure the button styles work properly */
        .uk-button {
            background-color: transparent !important;
            border: 1px solid var(--theme-selector-border) !important;
            color: inherit !important;
        }

        .uk-button:hover {
            background-color: rgba(0, 0, 0, 0.05) !important;
        }

        button.uk-button-default {
            background-color: transparent !important;
            border: 1px solid var(--theme-selector-border) !important;
            color: inherit !important;
        }

        button.uk-button-default:hover {
            background-color: rgba(0, 0, 0, 0.05) !important;
        }

        /* Theme-specific border colors */
        .pure-black .uk-button,
        .pure-black button.uk-button-default {
            border-color: #27272a !important;
        }

        .light .uk-button,
        .light button.uk-button-default {
            border-color: #e5e7eb !important;
        }

        /* Chart container styling for MVP implementation */
        .chart-container {
            height: 367px;
            display: flex;
            flex-direction: column;
        }

        .chart-canvas-container {
            flex: 1;
            display: flex;
            align-items: stretch;
            min-height: 0;
        }

        .chart-canvas {
            width: 100% !important;
            height: 100% !important;
            max-height: 260px;
        }

        /* Staff Progress Bar Styles */
        :root {
            --staff-progress-bg: #9ca3af;
            --staff-progress-color: #151519;
        }

        .pure-black {
            --staff-progress-bg: #71717a;
            --staff-progress-color: #fafafa;
        }

        .light {
            --staff-progress-bg: #9ca3af;
            --staff-progress-color: #151519;
        }

        /* Animation for progress bar */
        @keyframes staffProgressAnimation {
            from {
                stroke-dashoffset: 157.08;
            }
            to {
                stroke-dashoffset: 39.27; /* 75% progress: 157.08 - (157.08 * 0.75) */
            }
        }

        .staff-progress-animated {
            animation: staffProgressAnimation 2s ease-in-out forwards;
        }
    </style>
    </head>

    <body class="light">
        <!-- {% include 'components/loading.html' %} -->

        <div class="grid min-h-screen w-full  lg:grid-cols-[280px_1fr]">
            {% include 'sidebar.html' %}
            <div class="flex flex-col">
                <header
                    class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header padding-adjuster">
                    <div class="flex items-center gap-2 px-4 pl-0">
                        <button id="toggle-btn" class="opacity-100 transition-opacity duration-300 focus:outline-none"
                            style="background-color: transparent !important;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-panel-left">
                                <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                                <path d="M9 3v18"></path>
                            </svg>
                        </button>
                        <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-3 h-4"
                            style="background-color: var(--border-color);"></div>
                        <nav aria-label="breadcrumb">
                            <ol
                                class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
                                <!-- Dashboard link (active) -->
                                <div class="menubar" role="menubar">
                                    <div class="menubar-indicator"></div>
                                    <a href="/" role="menuitem" class="active">Dashboard</a>
                                    <a href="/users" role="menuitem">Users</a>
                                    <a href="/products" role="menuitem">Products</a>
                                    <a href="/analytics" role="menuitem">Overview</a>
                                </div>
                            </ol>
                        </nav>
                    </div>
                    {% include 'topright.html' %}
                </header>
                <div class="relative">
                    <div id="calendarDropdown"
                        class="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-xl p-6 transition-all duration-300 ease-in-out transform scale-95 opacity-0 invisible"
                        role="dialog" aria-modal="true" aria-label="Date picker">
                        <div class="flex flex-col space-y-4">
                            <div class="flex items-center justify-between">
                                <div class="w-1/2 pr-2">
                                    <label for="startDate" class="block text-sm font-medium  mb-1">Start Date</label>
                                    <input type="text" id="startDate"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="Select start date" readonly aria-label="Start date">
                                </div>
                                <div class="w-1/2 pl-2">
                                    <label for="endDate" class="block text-sm font-medium  mb-1">End Date</label>
                                    <input type="text" id="endDate"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="Select end date" readonly aria-label="End date">
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <button id="prevMonth"
                                    class="text-gray-600 hover:text-gray-800 p-1 rounded-full hover:bg-gray-100"
                                    aria-label="Previous month">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </button>
                                <span id="currentMonth" class="text-lg font-semibold "></span>
                                <button id="nextMonth"
                                    class="text-gray-600 hover:text-gray-800 p-1 rounded-full hover:bg-gray-100"
                                    aria-label="Next month">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                            <div id="calendar" class="grid grid-cols-7 gap-1 text-center"></div>
                            <div class="flex flex-wrap gap-2 mt-2">
                                <button
                                    class="preset-range bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-sm hover:bg-gray-200 transition-colors duration-300"
                                    data-days="7">Last 7 days</button>
                                <button
                                    class="preset-range bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-sm hover:bg-gray-200 transition-colors duration-300"
                                    data-days="30">Last 30 days</button>
                                <button
                                    class="preset-range bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-sm hover:bg-gray-200 transition-colors duration-300"
                                    data-range="this-month">This month</button>
                                <button
                                    class="preset-range bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-sm hover:bg-gray-200 transition-colors duration-300"
                                    data-range="last-month">Last month</button>
                            </div>
                            <div class="flex justify-between">
                                <button id="clearDates"
                                    class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors duration-300">Clear</button>
                                <button id="applyDates"
                                    class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors duration-300">Apply</button>
                            </div>
                        </div>
                    </div>
                </div>
                <main class="card flex flex-col gap-4 p-4 px-6 pb-0">
                    <div class="dashboard-metrics-grid">
                        <div class="card relative rounded-lg border shadow-sm metric-card" data-v0-t="card" data-metric="revenue">
                            <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                <div class="flex items-center gap-2">

                                    <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Total revenue</h3>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="text-2xl font-bold" id="total-revenue">€{{ total_revenue|safe }}</div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Total lifetime revenue</p>
                            </div>
                            <!-- Gradient Chart Overlay -->
                            <div class="absolute bottom-5 right-7 w-28 h-24">
                                <svg viewBox="0 0 100 100" class="w-full h-full" preserveAspectRatio="none">
                                    <defs>
                                        <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                                            <stop offset="0%" stop-color="rgb(249, 115, 22)" stop-opacity="0.3" />
                                            <stop offset="100%" stop-color="rgb(249, 115, 22)" stop-opacity="0" />
                                        </linearGradient>
                                    </defs>

                                    <!-- Line -->
                                    <path id="revenueLine" d="M0 100 L20 70 L40 50 L60 40 L80 30 L100 20" fill="none"
                                        stroke="rgb(249, 115, 22)" stroke-width="2" />

                                    <!-- Gradient Area -->
                                    <path id="revenueArea"
                                        d="M0 100 L20 70 L40 50 L60 40 L80 30 L100 20 L100 100 L0 100 Z"
                                        fill="url(#revenueGradient)" />
                                </svg>
                            </div>
                        </div>
                        <div class="card relative overflow-hidden rounded-lg border shadow-sm metric-card" data-v0-t="card"
                            data-metric="reservations">
                            <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                <div class="flex items-center gap-2">

                                    <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Total reservations
                                    </h3>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="text-2xl font-bold" id="total-reservations">{{ total_reservations|safe }}
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Lifetime reservations</p>
                            </div>
                            <!-- Gradient Chart Overlay with Different Curve -->
                            <div class="absolute bottom-5 right-7 w-28 h-24">
                                <svg viewBox="0 0 100 100" class="w-full h-full" preserveAspectRatio="none">
                                    <defs>
                                        <linearGradient id="reservationsGradient" x1="0" y1="0" x2="0" y2="1">
                                            <stop offset="0%" stop-color="rgb(249, 115, 22)" stop-opacity="0.3" />
                                            <stop offset="100%" stop-color="rgb(249, 115, 22)" stop-opacity="0" />
                                        </linearGradient>
                                    </defs>

                                    <!-- Line -->
                                    <path id="reservationsLine" d="M0 100 L20 70 L40 50 L60 40 L80 30 L100 20"
                                        fill="none" stroke="rgb(249, 115, 22)" stroke-width="2" />

                                    <!-- Gradient Area -->
                                    <path id="reservationsArea"
                                        d="M0 100 L20 70 L40 50 L60 40 L80 30 L100 20 L100 100 L0 100 Z"
                                        fill="url(#reservationsGradient)" />
                                </svg>
                            </div>
                        </div>
                        <div class="card rounded-lg border relative overflow-hidden animated-on-load animate-conversation-analytics metric-card conversation-analytics-card" data-v0-t="card"
                            style="height: 150px;">
                            <div class="p-4 pb-1 flex flex-row items-center justify-between space-y-0">
                                <div class="uk-position-relative">
                                    <button type="button" id="bookings-dropdown-btn"
                                        class="whitespace-nowrap tracking-tight text-sm font-large flex items-center gap-2 bg-transparent border-none outline-none cursor-pointer"
                                        aria-haspopup="true"
                                        style="background-color: transparent; color: inherit; padding: 6px 8px; margin: -6px -8px; border-radius: 4px; transition: all 0.3s ease;">
                                        Bookings and Occupancy
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevrons-up-down-icon lucide-chevrons-up-down"><path d="m7 15 5 5 5-5"/><path d="m7 9 5-5 5 5"/></svg>
                                    </button>
                                    <div id="bookings-dropdown"
                                        uk-dropdown="mode: click; pos: bottom-left"
                                        class="uk-dropdown dropdown-content"
                                        style="min-width: 250px;">
                                        <ul class="uk-nav uk-dropdown-nav uk-overflow-auto uk-cs-options no-scrollbar">
                                            <li class="uk-active">
                                                <a class="bookings-option" data-value="bookings-occupancy">
                                                    <div class="uk-cs-item-wrapper">
                                                        <span class="uk-cs-item-text">Bookings and Occupancy</span>
                                                    </div>
                                                    <uk-icon class="uk-cs-check" icon="check"></uk-icon>
                                                </a>
                                            </li>
                                            <li>
                                                <a class="bookings-option" data-value="checkins-checkouts">
                                                    <div class="uk-cs-item-wrapper">
                                                        <span class="uk-cs-item-text">Check-ins and Check-outs</span>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-copy-icon lucide-book-copy"><path d="M2 16V4a2 2 0 0 1 2-2h11"/><path d="M22 18H11a2 2 0 1 0 0 4h10.5a.5.5 0 0 0 .5-.5v-15a.5.5 0 0 0-.5-.5H11a2 2 0 0 0-2 2v12"/><path d="M5 14H4a2 2 0 1 0 0 4h1"/></svg>
                            </div>
                            <div class="px-4 pb-0 relative z-10">
                                <div class="flex items-baseline">
                                    <div id="total-tips" style="margin-top: 0px" class="text-2xl font-bold">150</div>
                                </div>
                            </div>

                            <!-- Static Progress Bar - No Animation -->
                            <div class="px-4 absolute bottom-3 left-0 right-0 w-full">
                                <div class="overflow-hidden w-full">
                                    <!-- Static Progress Bar Container -->
                                    <div class="flex w-full rounded-full bg-gray-100 h-[10px] overflow-hidden">
                                        <!-- Pre-rendered Progress Bars with static widths -->
                                        <div class="h-full bg-cyan-500" style="width: 70%;"></div>
                                        <div class="h-full bg-rose-500" style="width: 30%;"></div>
                                    </div>
                                    <!-- Static Legend -->
                                    <div class="mt-2 flex flex-wrap justify-center text-[12px]">
                                        <div class="flex items-center mr-3 mb-1">
                                            <span class="inline-block w-2.5 h-2.5 rounded-sm bg-cyan-500 mr-1"></span>
                                            <span>Total Bookings: 105</span>
                                        </div>
                                        <div class="flex items-center mr-3 mb-1">
                                            <span class="inline-block w-2.5 h-2.5 rounded-sm bg-rose-500 mr-1"></span>
                                            <span>Total Occupancy: 45</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Staff chats -->
                        <div class="card hoverable-card rounded-lg border relative overflow-hidden metric-card" data-v0-t="card"
                            style="height: 150px;">
                            <div class="p-3 flex flex-col h-full">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium text-center mb-3">Staff chats</h3>

                                <!-- Spacer to push progress bar to bottom -->
                                <div class="flex-1"></div>

                                <!-- Half Circle Progress Bar Container - Full Width at Bottom -->
                                <div class="w-full flex justify-center items-end px-1">
                                    <div class="relative w-full max-w-40 h-20">
                                        <!-- Background Half Circle -->
                                        <svg class="w-full h-20" viewBox="0 0 100 50" style="overflow: visible;">
                                            <path d="M 0 48 A 50 50 0 0 1 100 48"
                                                  fill="none"
                                                  stroke="var(--staff-progress-bg)"
                                                  stroke-width="5"
                                                  stroke-linecap="round"/>
                                            <!-- Progress Half Circle -->
                                            <path id="staff-progress-path"
                                                  d="M 0 48 A 50 50 0 0 1 100 48"
                                                  fill="none"
                                                  stroke="var(--staff-progress-color)"
                                                  stroke-width="5"
                                                  stroke-linecap="round"
                                                  stroke-dasharray="157.08"
                                                  stroke-dashoffset="157.08"
                                                  style="transition: stroke-dashoffset 2s ease-in-out;">
                                        </svg>
                                        <!-- Percentage Text -->
                                        <div class="absolute inset-0 flex items-center justify-center pt-2">
                                            <span id="staff-progress-text" class="text-xl font-bold">75%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
                <main class="card flex flex-col gap-8 p-4 px-6 pt-0 mt-8">
                    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
                        <div class=" card rounded-lg border  shadow-sm animate-container chart-container" data-v0-t="card">
                            <div class="flex flex-col space-y-1.5 p-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex flex-col space-y-1 -mt-[0px]">
                                        <h3
                                            class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">
                                            Booking comparision</h3>
                                    </div>
                                    <!-- Color legend labels - side by side with data -->
                                    <div class="flex flex-row gap-4 animate-item">
                                        <div class="flex items-center gap-2">
                                            <span class="inline-block w-2.5 h-2.5 rounded-sm bg-[#DC3912]"></span>
                                            <span class="text-xs">Guest Genius: 121</span>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="inline-block w-2.5 h-2.5 rounded-sm bg-[#3366CC]"></span>
                                            <span class="text-xs">PMS System: 921</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6 pt-0 chart-canvas-container" style="padding-top: 20px;">
                                <canvas id="revenueGrowthChart" class="chart-canvas"></canvas>
                            </div>
                        </div>
                        <div class="card rounded-lg border animate-container shadow-sm chart-container" data-v0-t="card">
                            <div class="flex flex-col space-y-1.5 p-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex flex-col space-y-1 mt-[3.5px]">
                                        <h3
                                            class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">
                                            Total Users</h3>
                                    </div>
                                    <!-- Color legend labels - side by side with data -->
                                    <div class="flex flex-row animate-item gap-4">
                                        <div class="flex items-center gap-2">
                                            <span class="inline-block w-2.5 h-2.5 rounded-sm bg-[#5ea5f6]"></span>
                                            <span class="text-xs">New Signups: 840</span>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="inline-block w-2.5 h-2.5 rounded-sm bg-[#e76e50]"></span>
                                            <span class="text-xs">Online Users: 946</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
<style>
    #newSignupsChart {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: -moz-crisp-edges;
        image-rendering: crisp-edges;
        image-rendering: optimizeQuality;
    }
</style>
                            <div style="margin-top: 19px;" class="p-6 pt-0 chart-canvas-container">
                                <canvas id="newSignupsChart" class="chart-canvas"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
                        <div class="card rounded-lg border shadow-sm h-fit" data-v0-t="card">
                            <div class="flex flex-col space-y-1 p-5">
                                <div class="relative">
                                    <h3 class="whitespace-nowrap text-2xl font-semibold leading-none tracking-tight">
                                        Recent Chats</h3>
                                    <button style="margin-top: 8px;" onclick="window.location.href='/livechat'"
                                        class="uk-button border card flex items-center gap-2 absolute right-0 top-1/2 transform -translate-y-1/2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-arrow-up-left">
                                            <path d="M7 17V7h10" />
                                            <path d="M17 17 7 7" />
                                        </svg>
                                        View All Chats
                                    </button>
                                </div>
                                <p class="text-sm text-muted-foreground">Recent messages from guests</p>
                            </div>
                            <hr class="card border-t border-gray-200 -mt-2 card">
                            <div class="p-6">
                                <style>
                                    #recent-chats-list::-webkit-scrollbar {
                                        display: none;
                                    }

                                    /* Ensure the ul container has no extra padding/margin */
                                    #recent-chats-list {
                                        margin: 0 !important;
                                        padding: 0 !important;
                                    }

                                    /* Ensure each chat item takes the full width with no extra spacing */
                                    #recent-chats-list .chat-item {
                                        display: flex;
                                        width: 100% !important;
                                        box-sizing: border-box;
                                        height: 68px;
                                        margin: 0 !important;
                                        padding: 12px !important;
                                        /* Adjust if needed */
                                        border-radius: 0.375rem;
                                        transition: background-color 0.2s;
                                    }

                                    /* Remove extra spacing from dividers if present */
                                    #recent-chats-list hr[data-divider-for-chat] {
                                        margin: 0 !important;
                                        padding: 0 !important;
                                    }
                                </style>
                                <ul id="recent-chats-list" class="max-h-[290px] overflow-y-auto"
                                    style="min-height: 290px; scrollbar-width: none; -ms-overflow-style: none;">
                                    <!-- Chat items will be inserted here -->
                                </ul>
                            </div>
                        </div>
                        <div class="card rounded-lg border shadow-sm animate-container h-fit" data-v0-t="card">
                            <div class="flex flex-col space-y-1 p-5">
                                <div class="relative">
                                    <h3
                                        class="whitespace-nowrap text-2xl font-semibold leading-none tracking-tight animate-title">
                                        Recent Users</h3>
                                    <button style="margin-top: 8px;" onclick="window.location.href='/users'"
                                        class="uk-button border card flex items-center gap-2 absolute right-0 top-1/2 transform -translate-y-1/2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-arrow-up-left">
                                            <path d="M7 17V7h10" />
                                            <path d="M17 17 7 7" />
                                        </svg>
                                        View All Users
                                    </button>
                                </div>
                                <p class="text-sm text-muted-foreground">A list of the total guests that used Guest
                                    genius.</p>
                            </div>
                            <hr class="border-t border-gray-200 -mt-2 card">
                            <div class="p-6" id="users-container">
                                <style>
                                    #users-list::-webkit-scrollbar {
                                        display: none;
                                    }

                                    .user-item {
                                        padding: 12px;
                                        margin-bottom: 0;
                                        border-radius: 0.375rem;
                                        transition: background-color 0.2s;
                                        height: 68px;
                                    }

                                    /* Remove extra spacing from hr dividers in the user list */
                                    #users-list hr[data-divider-for-user] {
                                        margin: 0;
                                        padding: 0;
                                    }

                                    #recent-chats-list hr[data-divider-for-chat] {
                                        margin: 0;
                                        padding: 0;
                                    }
                                </style>
                                <div id="users-list"
                                    style="max-height: 290px; min-height: 290px; overflow-y: auto; position: relative; scrollbar-width: none; -ms-overflow-style: none;">
                                </div>
                            </div>
                        </div>
                    </div>

                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                    <script src="https://kit.fontawesome.com/c5d6c4974.js" crossorigin="anonymous"></script>
                    <link rel="stylesheet"
                        href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/css/flag-icon.min.css"
                        integrity="sha512-rRkkH/qCvU95u1uPId/ Schw9iwRqF5d85" crossorigin="anonymous" />
                    <script>
                        // User list functionality

                        function fetchUsers() {
                            // Show loading state
                            const usersList = document.getElementById('users-list');
                            if (usersList) {
                                usersList.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
                            }

                            fetch('/updateduserslist')
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                                    }
                                    return response.json();
                                })
                                .then(users => {
                                    if (!usersList) {
                                        console.error('Could not find users-list element');
                                        return;
                                    }

                                    usersList.innerHTML = '';

                                    // Display all users instead of limiting to 5
                                    users.reverse().forEach((user, index) => {
                                        const userElement = document.createElement('div');
                                        userElement.classList.add('flex', 'items-center', 'gap-4', 'user-item');
                                        userElement.setAttribute('data-user-id', user.user_id);

                                        // Determine language code for flag display
                                        let languageCode = 'us'; // Default
                                        if (user.guest_language === 'English') {
                                            languageCode = 'gb';
                                        } else if (user.guest_language === 'Spanish') {
                                            languageCode = 'es';
                                        } else if (user.guest_language === 'French') {
                                            languageCode = 'fr';
                                        }

                                        // Get display values from new data structure
                                        const displayName = user.guest_name || 'Guest';
                                        const displayPhone = user.guest_phone_or_id || '';
                                        const displayRoom = user.guest_room_number || '';
                                        const displayLanguage = user.guest_language || 'English';

                                        userElement.innerHTML = `
                                            <div class="flex items-center gap-4 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-gray-500 shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <circle cx="12" cy="7" r="4"></circle>
                                                    <path d="M20 21v-2a4 4 0 0 0 -4 -4H8a4 4 0 0 0 -4 4v2"></path>
                                                </svg>
                                                <div class="flex-grow flex gap-4 items-center justify-between min-w-0">
                                                    <div class="flex-1">
                                                        <p class="font-medium text-sm">${displayName}</p>
                                                        <p class="text-sm text-foreground truncate">${displayPhone}</p>
                                                    </div>
                                                    <div class="flex-1">
                                                        <p class="uk-label uk-label-primary ml-[-0px]" style="font-size: 0.7rem; padding: 0.15rem 0.5rem;">${displayRoom}</p>
                                                    </div>
                                                    <div class="flex-1 flex items-center gap-2">
                                                        ${displayLanguage.toLowerCase() === "english"
                                                ? `<img src="https://cdn-icons-png.flaticon.com/128/197/197374.png" alt="English Flag" class="w-6 h-6 ml-[-40px]">`
                                                : `<span class="flag-icon flag-icon-${languageCode} ml-[-80px]"></span>`}
                                                        <img src="/static/icons/whatsapp2.png" alt="WhatsApp" class="whatsapp-icon w-6 h-6 ml-[50%]">
                                                    </div>
                                                    <ul class="uk-iconnav">
                                                        <li>
                                                            <a href="#" class="admin-required">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis-vertical">
                                                                    <circle cx="12" cy="12" r="1"></circle>
                                                                    <circle cx="12" cy="5" r="1"></circle>
                                                                    <circle cx="12" cy="19" r="1"></circle>
                                                                </svg>
                                                            </a>
                                                            <div uk-dropdown="mode: click; pos: bottom-right">
                                                                <ul class="uk-nav uk-dropdown-nav">
                                                                    <li><a href="#" class="admin-required">Restrict user</a></li>
                                                                    <li><a href="#" class="admin-required">Block number</a></li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        `;

                                        usersList.appendChild(userElement);

                                        // Add divider except after the last item
                                        if (index < users.length - 1) {
                                            const divider = document.createElement('hr');
                                            divider.classList.add('border-t', 'border-gray-200', 'card');
                                            divider.setAttribute('data-divider-for-user', user.user_id);
                                            usersList.appendChild(divider);
                                        }
                                    });

                                    // Handle empty state
                                    if (users.length === 0) {
                                        usersList.innerHTML = '<div class="text-center py-4 text-gray-500">No users found</div>';
                                    }

                                    // Update WhatsApp icons for dark/light mode
                                    updateWhatsAppIcons();
                                })
                                .catch(error => {
                                    console.error('Error fetching users:', error);

                                    if (usersList) {
                                        usersList.innerHTML = '<div class="text-center py-4 text-gray-500">Failed to load users. Please refresh.</div>';
                                    }
                                });
                        }

                        // Make sure this function exists
                        function updateWhatsAppIcons() {
                            document.querySelectorAll('.whatsapp-icon').forEach(icon => {
                                // Check if body has the pure-black class (dark mode)
                                if (document.body.classList.contains('pure-black')) {
                                    // In dark mode, use the white WhatsApp icon
                                    icon.src = "../static/icons/white-whatsapp-icon.png";
                                } else {
                                    // In light mode, use the standard WhatsApp icon (green)
                                    icon.src = "/static/icons/whatsapp2.png";
                                }
                            });
                        }

                        // Immediately fetch users when the page loads
                        document.addEventListener('DOMContentLoaded', function () {
                            fetchUsers();
                        });

                        // No auto-refresh

                        document.addEventListener('click', function (event) {
                            if (currentlyOpenDropdown && !event.target.closest('.dropdown')) {
                                currentlyOpenDropdown.classList.add('hidden');
                                currentlyOpenDropdown = null;
                            }
                        });

                        // Chart functionality
                        const revenueGrowthData = {
                            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                            datasets: [
                                {
                                    data: [8, 90, 130, 78, 170, 120, 180],
                                    backgroundColor: 'rgba(220, 57, 18 , 0.2)',
                                    borderColor: '#DC3912',
                                    borderWidth: 2,
                                    fill: false,
                                    tension: 0,
                                    pointRadius: 4,
                                    pointHoverRadius: 6,
                                    label: ''
                                },
                                {
                                    data: [80, 110, 95, 70, 135, 185, 200],
                                    backgroundColor: 'rgba(51, 102, 204,    0.2)',
                                    borderColor: '#3366CC',
                                    borderWidth: 2,
                                    fill: false,
                                    tension: 0,
                                    pointRadius: 4,
                                    pointHoverRadius: 8,
                                    label: ''
                                }
                            ]
                        };
                        document.addEventListener('DOMContentLoaded', () => {
                            function isLightMode() {
                                const body = document.querySelector('body');
                                return body.classList.contains('light');
                            }

                            let gridColor = isLightMode() ? 'rgba(0, 0, 0, 0.1)' : 'rgba(255,255,255,0.1)';

                            const revenueGrowthChart = new Chart(document.getElementById('revenueGrowthChart'), {
                                type: 'line',
                                data: revenueGrowthData,
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    interaction: {
                                        mode: 'index',
                                        intersect: false,
                                    },
                                    hover: {
                                        mode: 'index',
                                        intersect: false,
                                    },
                                    plugins: {
                                        tooltip: {
                                            enabled: false,
                                            external: externalCustomTooltip
                                        },
                                        legend: {
                                            display: false
                                        },
                                        // legend: {
                                        //     labels: {
                                        //         usePointStyle: true,
                                        //         pointStyle: 'rectRounded',
                                        //         borderRadius: 0.5,
                                        //         boxWidth: 15,
                                        //         boxHeight: 15,
                                        //         padding: 20,
                                        //         font: {
                                        //             size: 12
                                        //         }
                                        //     },
                                        //     align: 'center',
                                        //     position: 'top'
                                        // },
                                        annotation: {
                                            annotations: {
                                                line1: {
                                                    type: 'line',
                                                    yMin: 200,
                                                    yMax: 200,
                                                    borderColor: 'rgba(169, 169, 169, 0.5)',
                                                    borderWidth: 1,
                                                    borderDash: [5, 5]
                                                }
                                            }
                                        }
                                    },

                                    hover: {
                                        mode: 'nearest',
                                        intersect: false
                                    },
                                    scales: {
                                        y: {
                                            beginAtZero: true,
                                            grid: {
                                                display: false,
                                            },
                                            ticks: {
                                                stepSize: 50
                                            }
                                        },
                                        x: {
                                            grid: {
                                                color: gridColor
                                            },
                                            ticks: {
                                                stepSize: 50
                                            }
                                        }
                                    }
                                }
                            });

                            // Setup MutationObserver
                            const observer = new MutationObserver(() => {
                                const newGridColor = isLightMode() ? 'rgba(0, 0, 0, 0.1)' : 'rgba(255,255,255,0.1)';

                                // Update revenueGrowthChart grid colors
                                revenueGrowthChart.options.scales.x.grid.color = newGridColor;
                                revenueGrowthChart.options.scales.y.grid.color = newGridColor;

                                // Update newSignupsChart Y axis grid color
                                newSignupsChart.options.scales.y.grid.color = newGridColor;

                                // Update charts
                                revenueGrowthChart.update();
                                newSignupsChart.update();

                                // Update WhatsApp icons for theme changes
                                updateWhatsAppIcons();
                            });

                            // Start observing changes to the body class
                            observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });

                            // Initialize newSignupsChart
                            const ctx = document.getElementById('newSignupsChart').getContext('2d');

                            // Create gradient
                            const gradient = ctx.createLinearGradient(0, 0, 0, 300);
                            gradient.addColorStop(0, '#abbbd3'); // Top color
                            gradient.addColorStop(1, 'white');    // Bottom color

                            // Locate the newSignupsData block within your script and update it as follows:
                            const newSignupsData = {
                                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                                datasets: [
                                    {
                                        label: 'Online Users',
                                        data: [120, 140, 135, 125, 115, 90, 111],
                                        backgroundColor: isLightMode() ? '#e76e50' : '#e76e50',
                                        borderColor: isLightMode() ? '#e76e50' : '#e76e50',
                                        borderWidth: 0,
                                        borderRadius: {
                                            topLeft: 6,
                                            topRight: 6,
                                            bottomLeft: 6,
                                            bottomRight: 6
                                        },
                                        borderSkipped: false, // This ensures all borders are shown
                                        hoverBackgroundColor: isLightMode() ? '#e76e50' : '#e76e50'
                                    },
                                    {
                                        label: 'New Signups',
                                        data: [80, 120, 110, 130, 105, 95, 100],
                                        backgroundColor: isLightMode() ? '#5ea5f6' : '#5ea5f6',
                                        borderColor: isLightMode() ? '#5ea5f6' : '#5ea5f6',
                                        borderWidth: 1,
                                        borderRadius: {
                                            topLeft: 6,
                                            topRight: 6,
                                            bottomLeft: 6,
                                            bottomRight: 6
                                        },
                                        borderSkipped: false, // This ensures all borders are shown
                                        hoverBackgroundColor: isLightMode() ? '#5ea5f6' : '#abbbd3'
                                    }
                                ]
                            };

                            const newSignupsChart = new Chart(document.getElementById('newSignupsChart'), {
                                type: 'bar',
                                data: newSignupsData,
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    devicePixelRatio: window.devicePixelRatio || 2,
                                    scales: {
                                        y: {
                                            beginAtZero: true,
                                            grid: {
                                                color: gridColor,
                                                borderDash: [5, 5]
                                            },
                                            ticks: {
                                                stepSize: 50
                                            }
                                        },
                                        x: {
                                            grid: {
                                                color: 'transparent'
                                            },
                                            ticks: {
                                                stepSize: 50
                                            }
                                        }
                                    },
                                    plugins: {
                                        tooltip: {
                                            enabled: false,
                                            external: createCustomTooltipForTips   // Use the custom tooltip function
                                        },
                                        legend: {
                                            display: false  // This will remove the labels on top of the chart
                                        }
                                    },
                                    hover: {
                                        mode: null
                                    }
                                }
                            });

                            // Update chart colors when theme changes
                            observer.observe(document.body, {
                                attributes: true,
                                attributeFilter: ['class']
                            });

                            document.body.addEventListener('classChange', () => {
                                newSignupsChart.data.datasets[0].backgroundColor = isLightMode() ? '#18181b' : '#fafafa';
                                newSignupsChart.data.datasets[0].borderColor = isLightMode() ? '#18181b' : '#fafafa';
                                newSignupsChart.data.datasets[0].hoverBackgroundColor = isLightMode() ? '#18181b' : '#fafafa';
                                newSignupsChart.update();
                            });
                        });


                        let fetchInterval;
                        let currentRequest;

                        function fetchData() {
                            if (currentRequest && currentRequest.readyState !== 4) {
                                currentRequest.abort();
                            }

                            currentRequest = $.ajax({
                                url: '/fetch-data',
                                method: 'GET',
                                success: function (data) {
                                    if (data.length > 0) {
                                        const totalReservations = data[0]['Total reservations'];
                                        const totalRevenue = data[0]['Total revenue'];
                                        const conversationOutsideBusinessHours = data[0]['Conversation Outside Business Hours'];
                                        const queriesLeftToAnswer = data[0]['Queries Left to Answer'];

                                        document.querySelector('#total-revenue').textContent = '£' + totalRevenue;
                                        document.querySelector('#total-reservations').textContent = totalReservations;
                                    }
                                },
                                error: function (error) {
                                    console.error('Error fetching data:', error);
                                }
                            });
                        }

                        document.addEventListener('DOMContentLoaded', fetchData);

                        function handleVisibilityChange() {
                            if (document.visibilityState === 'visible') {
                                clearInterval(fetchInterval);
                                fetchInterval = setInterval(fetchData, 60000);
                            } else {
                                clearInterval(fetchInterval);
                            }
                        }

                        document.addEventListener('visibilitychange', handleVisibilityChange);

                        window.addEventListener('beforeunload', function () {
                            clearInterval(fetchInterval);
                            if (currentRequest && currentRequest.readyState !== 4) {
                                currentRequest.abort();
                            }
                        });

                        // Recent Chats functionality
                        function fetchRecentChats() {
                            console.log('Fetching recent chats from Supabase...');

                            // Display loading state
                            const chatsList = document.getElementById('recent-chats-list');
                            if (chatsList) {
                                chatsList.innerHTML = '<li class="text-center text-gray-500">Loading chats...</li>';
                            }

                            // Use the Supabase endpoint to fetch chat data
                            fetch('/supabase_chats')
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                                    }
                                    return response.json();
                                })
                                .then(data => {
                                    console.log('Chats loaded successfully from Supabase');
                                    displaySupabaseChats(data);
                                })
                                .catch(error => {
                                    console.error('Error fetching chats from Supabase:', error);
                                    if (chatsList) {
                                        chatsList.innerHTML = `<li class="text-center text-gray-500">
                                            Failed to load chats. Please refresh the page or try again later.
                                            <br><small class="text-red-500">${error.message}</small>
                                        </li>`;
                                    }
                                });
                        }

                        // Helper function to format timestamp to "HH:MM AM/PM" format
                        function formatTimeAgo(timestamp) {
                            if (!timestamp || typeof timestamp !== 'string') {
                                return 'unknown time';
                            }

                            try {
                                // If timestamp already contains ":" and AM/PM, return as is
                                if (timestamp.includes(':') && (timestamp.includes('AM') || timestamp.includes('PM'))) {
                                    return timestamp;
                                }

                                // Parse the timestamp
                                const date = new Date(timestamp);
                                if (!isNaN(date.getTime())) {
                                    // Format as HH:MM AM/PM
                                    let hours = date.getHours();
                                    const minutes = date.getMinutes().toString().padStart(2, '0');
                                    const ampm = hours >= 12 ? 'PM' : 'AM';

                                    // Convert to 12-hour format
                                    hours = hours % 12;
                                    hours = hours ? hours : 12; // the hour '0' should be '12'

                                    return hours + ':' + minutes + ' ' + ampm;
                                }

                                // If we can't parse it, return a simplified version of the original
                                if (timestamp.includes('T')) {
                                    // Try to extract time from ISO format
                                    const timePart = timestamp.split('T')[1];
                                    if (timePart) {
                                        const simpleTime = timePart.substring(0, 5); // Get HH:MM
                                        const hour = parseInt(simpleTime.split(':')[0]);
                                        const minute = simpleTime.split(':')[1];
                                        const ampm = hour >= 12 ? 'PM' : 'AM';
                                        const hour12 = hour % 12 || 12;
                                        return hour12 + ':' + minute + ' ' + ampm;
                                    }
                                }

                                return timestamp;
                            } catch (e) {
                                console.error('Error formatting timestamp:', e);
                                return timestamp; // Return original as fallback
                            }
                        }


                        // Keep the original displayChats function for backward compatibility
                        // Helper function to truncate text to a specific length
                        function truncateText(text, maxLength) {
                            if (!text) return '';
                            if (text.length <= maxLength) return text;
                            return text.substring(0, maxLength) + '...';
                        }

                        // New function to display chats from Supabase
                        function displaySupabaseChats(chats) {
                            const chatsList = document.getElementById('recent-chats-list');
                            if (!chatsList) {
                                console.error('Could not find recent-chats-list element');
                                return;
                            }

                            chatsList.innerHTML = '';

                            if (!Array.isArray(chats) || chats.length === 0) {
                                chatsList.innerHTML = '<li class="text-center text-gray-500 py-4">No recent chats found</li>';
                                return;
                            }

                            console.log('Processing Supabase chats:', chats.length);

                            // Group chats by user_id
                            const chatsByUser = {};
                            const userInfo = {};

                            // First, organize chats by user_id and collect user info
                            chats.forEach(chat => {
                                // Convert user_id to string to use as object key
                                const userId = String(chat.user_id);

                                if (!chatsByUser[userId]) {
                                    chatsByUser[userId] = [];
                                    userInfo[userId] = {
                                        name: chat.sender,
                                        id: userId,
                                        phone_number: chat.phone_number || '+1234567890'
                                    };
                                }

                                // Parse the customer field correctly
                                let isCustomer = false;
                                if (typeof chat.customer === 'string') {
                                    isCustomer = chat.customer.toLowerCase() === 'true';
                                } else if (typeof chat.customer === 'boolean') {
                                    isCustomer = chat.customer;
                                }

                                // Add the chat with properly parsed customer field
                                chatsByUser[userId].push({
                                    ...chat,
                                    customer: isCustomer
                                });
                            });

                            // Process each user's chats
                            let chatsAdded = 0;
                            const userIds = Object.keys(chatsByUser);

                            userIds.forEach(userId => {
                                const userChats = chatsByUser[userId];
                                const user = userInfo[userId];

                                // Find customer messages
                                const customerMessages = userChats.filter(chat => chat.customer === true);

                                if (customerMessages.length > 0) {
                                    // Sort by timestamp (newest first) and get the most recent customer message
                                    customerMessages.sort((a, b) => {
                                        return new Date(b.timestamp) - new Date(a.timestamp);
                                    });

                                    const latestMessage = customerMessages[0];

                                    // Create chat item element
                                    const chatItem = document.createElement('li');
                                    chatItem.className = 'flex items-center gap-4 chat-item';

                                    // Format timestamp
                                    const timeAgo = formatTimeAgo(latestMessage.timestamp);

                                    // Display phone number from the data
                                    const phoneNumber = user.phone_number || latestMessage.phone_number || '+1234567890';

                                    chatItem.innerHTML = `
                                        <a href="/livechat?user=${userId}" class="flex items-center gap-4 w-full">
                                            <div class="flex-shrink-0">
                                                <img src="" alt="${latestMessage.sender}" class="w-10 h-10 rounded-full" id="avatar-${userId}">
                                            </div>
                                            <div class="flex-grow min-w-0">
                                                <div class="flex items-center justify-between">
                                                    <h4 class="font-medium text-sm">${latestMessage.sender}</h4>
                                                    <span class="text-xs text-foreground translate-y-[10px]">${timeAgo}</span>
                                                </div>
                                                <p class="text-sm text-foreground truncate">${truncateText(latestMessage.message, 39)}</p>
                                            </div>
                                            <div class="flex-shrink-0">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                                     fill="none" stroke="currentColor" stroke-width="2"
                                                     stroke-linecap="round" stroke-linejoin="round"
                                                     class="text-gray-400">
                                                    <path d="m9 18 6-6-6-6"></path>
                                                </svg>
                                            </div>
                                        </a>
                                    `;

                                    // Fetch gender and set appropriate avatar
                                    fetch(`/guess_gender?name=${encodeURIComponent(latestMessage.sender)}`)
                                        .then(response => response.json())
                                        .then(data => {
                                            const avatarImg = document.getElementById(`avatar-${userId}`);
                                            if (avatarImg) {
                                                if (data.gender === 'male') {
                                                    avatarImg.src = "https://ui.shadcn.com/avatars/02.png";
                                                } else if (data.gender === 'female') {
                                                    avatarImg.src = "https://ui.shadcn.com/avatars/01.png";
                                                } else {
                                                    avatarImg.src = "https://ui.shadcn.com/avatars/03.png"; // Default for unknown gender
                                                }
                                            }
                                        });

                                    chatsList.appendChild(chatItem);
                                    chatsAdded++;

                                    // Add divider except after the last item
                                    if (chatsAdded < userIds.length) {
                                        const divider = document.createElement('hr');
                                        divider.className = 'border-t border-gray-200 card';
                                        divider.setAttribute('data-divider-for-chat', userId);
                                        chatsList.appendChild(divider);
                                    }
                                }
                            });

                            // If no chats found
                            if (chatsAdded === 0) {
                                chatsList.innerHTML = '<li class="text-center text-gray-500 py-4">No recent chats found</li>';
                            }
                        }

                        // Make sure we call the fetch function when the page loads
                        document.addEventListener('DOMContentLoaded', function () {
                            console.log('DOM loaded, fetching recent chats...');
                            fetchRecentChats();
                        });

                        // Immediate invocation as a backup
                        if (document.readyState === 'complete' || document.readyState === 'interactive') {
                            console.log('Document already ready, fetching recent chats immediately...');
                            fetchRecentChats();
                        }

                    </script>
                    <script>
                        function externalCustomTooltip(context) {
                            let tooltipEl = document.getElementById('chartjs-tooltip');
                            if (!tooltipEl) {
                                tooltipEl = document.createElement('div');
                                tooltipEl.id = 'chartjs-tooltip';
                                tooltipEl.innerHTML = '<table></table>';
                                tooltipEl.style.transition = 'left 0.3s ease, top 0.3s ease, opacity 0.3s ease';
                                tooltipEl.style.position = 'absolute';
                                tooltipEl.style.pointerEvents = 'none';
                                tooltipEl.style.whiteSpace = 'nowrap';
                                document.body.appendChild(tooltipEl);
                            }
                            const tooltipModel = context.tooltip;
                            if (tooltipModel.opacity === 0) {
                                tooltipEl.style.opacity = 0;
                                return;
                            }

                            // Build simplest possible tooltip with just values
                            let innerHtml = '<div style="padding:4px;font-size:13px;">';
                            if (tooltipModel.title) {
                                innerHtml += `<div style="font-weight:600;margin-bottom:4px;">${tooltipModel.title[0]}</div>`;
                            }
                            if (tooltipModel.body) {
                                tooltipModel.body.forEach((body) => {
                                    const value = body.lines[0].split(':')[1] || body.lines[0];
                                    innerHtml += `<div style="margin:2px 0;">${value.trim()}</div>`;
                                });
                            }
                            innerHtml += '</div>';
                            innerHtml += '</table>';
                            tooltipEl.querySelector('table').innerHTML = innerHtml;

                            // Position the tooltip
                            const position = context.chart.canvas.getBoundingClientRect();
                            let left = position.left + window.pageXOffset + tooltipModel.caretX;
                            let top = position.top + window.pageYOffset + tooltipModel.caretY;

                            tooltipEl.style.opacity = 1;
                            tooltipEl.style.left = left + 'px';
                            tooltipEl.style.top = top + 'px';
                            tooltipEl.style.padding = tooltipModel.options.padding + 'px';

                            // Adjust position if needed
                            const tooltipRect = tooltipEl.getBoundingClientRect();
                            if (tooltipRect.left < 0) {
                                left = position.left + window.pageXOffset + tooltipModel.caretX + 10;
                            }
                            if (tooltipRect.right > window.innerWidth) {
                                left = position.left + window.pageXOffset + tooltipModel.caretX - tooltipRect.width - 10;
                            }
                            tooltipEl.style.left = left + 'px';
                            tooltipEl.style.top = top + 'px';
                        }
                    </script>
                    <script>
                        // Add this to your dashboard.html file (replacing or modifying the existing animation script)
                        document.addEventListener('DOMContentLoaded', function () {
                            // Create the intersection observer for static content
                            const observer = new IntersectionObserver((entries) => {
                                entries.forEach(entry => {
                                    if (entry.isIntersecting) {
                                        const element = entry.target;
                                        triggerAnimation(element);
                                        observer.unobserve(element);
                                    }
                                });
                            }, {
                                root: null,
                                threshold: 0.1,
                                rootMargin: '50px'
                            });

                            // Apply CSS for initial invisible state
                            const style = document.createElement('style');
                            style.textContent = `
                                    .animate-container:not(.animate-container-visible) {
                                    opacity: 0;
                                    transform: translateY(20px);
                                    transition: opacity 0.5s, transform 0.5s;
                                    }
                                    .animate-title:not(.animate-title-visible) {
                                    opacity: 0;
                                    transform: translateY(-8px);
                                    transition: opacity 0.4s, transform 0.4s;
                                    }
                                    .animate-text:not(.animate-text-visible) {
                                    opacity: 0;
                                    transition: opacity 0.4s;
                                    }
                                    .animate-item:not(.animate-item-visible) {
                                    opacity: 0;
                                    transform: translateX(-10px);
                                    transition: opacity 0.3s, transform 0.3s;
                                    }
                                    .animate-container-visible,
function createCustomTooltipForTips(context) {
                            // Create tooltip element if it doesn't exist
                            let tooltipEl = document.getElementById('tips-chart-tooltip');

                            if (!tooltipEl) {
                                tooltipEl = document.createElement('div');
                                tooltipEl.id = 'tips-chart-tooltip';
                                tooltipEl.style.position = 'absolute';
                                tooltipEl.style.pointerEvents = 'none';
                                tooltipEl.style.transition = 'left 0.3s ease, top 0.3s ease, opacity 0.3s ease'; // Smoother animation
                                tooltipEl.style.zIndex = 1000;
                                document.body.appendChild(tooltipEl);
                            }

                            // Hide tooltip when not active
                            const tooltipModel = context.tooltip;
                            if (tooltipModel.opacity === 0) {
                                tooltipEl.style.opacity = 0;
                                return;
                            }

                            // Set tooltip content
                            if (tooltipModel.body) {
                                const dataPoint = tooltipModel.dataPoints[0];
                                const value = dataPoint.raw;
                                const label = tooltipModel.title[0]; // Day label (Mon, Tue, etc.)

                                // Get the correct color from the specific dataset being hovered
                                const datasetIndex = dataPoint.datasetIndex;
                                const chartBarColor = context.chart.config.data.datasets[datasetIndex].backgroundColor;
                                const datasetLabel = context.chart.config.data.datasets[datasetIndex].label || '';

                                // Using the same color scheme as externalCustomTooltip function
                                tooltipEl.innerHTML = '<div class="flex items-center rounded-md px-1.5 py-1 shadow-md" style="background-color: var(--dropdown-bg); border: 1px solid var(--dropdown-border, #dee2e6); font-weight:300; color: var(--dropdown-text, #111827);"><span class="w-3 h-3 rounded-sm mr-2 flex-shrink-0" style="background-color: ' + chartBarColor + '"></span><span class="text-sm">' + label + '</span><span class="ml-4 text-sm font-medium">' + value + '</span></div>';
                            }

                            // Position the tooltip with improved positioning
                            const position = context.chart.canvas.getBoundingClientRect();
                            const tooltipWidth = tooltipEl.offsetWidth;

                            // Calculate position (center aligned with bar)
                            const positionX = position.left + window.pageXOffset + tooltipModel.caretX - (tooltipWidth / 2);
                            const positionY = position.top + window.pageYOffset + tooltipModel.caretY - 40; // Position above the bar

                            // Apply position with smooth transition
                            tooltipEl.style.opacity = 1;
                            tooltipEl.style.left = positionX + 'px';
                            tooltipEl.style.top = positionY + 'px';
                        }
                                    .animate-title-visible,
                                    .animate-text-visible,
                                    .animate-item-visible {
                                    opacity: 1 !important;
                                    transform: translate(0, 0) !important;
                                    }
                                `;
                            document.head.appendChild(style);

                            // Observe all animated elements that are not in async containers
                            document.querySelectorAll('.animate-container, .animate-title, .animate-text, .animate-item').forEach(element => {
                                // Check if this element is within one of our special async containers
                                const isInUsersList = element.closest('#users-container');
                                const isInChatsList = element.closest('#recent-chats-list');

                                if (!isInUsersList && !isInChatsList) {
                                    observer.observe(element);
                                }
                            });

                            // Create a function to trigger animations for elements
                            window.triggerAnimation = function (element) {
                                if (element.classList.contains('animate-container')) {
                                    element.classList.add('animate-container-visible');
                                } else if (element.classList.contains('animate-title')) {
                                    element.classList.add('animate-title-visible');
                                } else if (element.classList.contains('animate-text')) {
                                    element.classList.add('animate-text-visible');
                                } else if (element.classList.contains('animate-item')) {
                                    element.classList.add('animate-item-visible');
                                }

                                // Also trigger animations for children with animation classes
                                element.querySelectorAll('.animate-title, .animate-text, .animate-item').forEach(child => {
                                    setTimeout(() => triggerAnimation(child), 100);
                                });
                            };

                            // Hook into your API fetch callbacks
                            const originalFetchUsers = window.fetchUsers;
                            window.fetchUsers = function () {
                                originalFetchUsers.apply(this, arguments);

                                // After data is loaded, trigger animations
                                const usersList = document.getElementById('users-list');
                                if (usersList) {
                                    usersList.addEventListener('DOMNodeInserted', function handler() {
                                        // Remove the event listener after first insertion to prevent multiple triggers
                                        usersList.removeEventListener('DOMNodeInserted', handler);

                                        // Delay slightly to ensure content is rendered
                                        setTimeout(() => {
                                            const container = document.querySelector('#users-container').closest('.animate-container');
                                            if (container) triggerAnimation(container);
                                        }, 300);
                                    });
                                }
                            };

                            // Do the same for recent chats
                            const originalFetchRecentChats = window.fetchRecentChats;
                            window.fetchRecentChats = function () {
                                originalFetchRecentChats.apply(this, arguments);

                                // After data is loaded, trigger animations
                                const chatsList = document.getElementById('recent-chats-list');
                                if (chatsList) {
                                    chatsList.addEventListener('DOMNodeInserted', function handler() {
                                        chatsList.removeEventListener('DOMNodeInserted', handler);

                                        setTimeout(() => {
                                            const container = chatsList.closest('.animate-container');
                                            if (container) triggerAnimation(container);
                                        }, 300);
                                    });
                                }
                            };
                        });
                        </script>
                                       <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            // Bookings dropdown functionality
                            const bookingsDropdownBtn = document.getElementById('bookings-dropdown-btn');
                            const bookingsDropdown = document.getElementById('bookings-dropdown');
                            const bookingsOptions = document.querySelectorAll('#bookings-dropdown .bookings-option');

                            // Initialize UIkit dropdown if not already done
                            let dropdownComponent = null;
                            if (typeof UIkit !== 'undefined' && bookingsDropdown) {
                                // Check if dropdown is already initialized
                                dropdownComponent = UIkit.dropdown(bookingsDropdown);

                                if (!dropdownComponent) {
                                    dropdownComponent = UIkit.dropdown(bookingsDropdown, {
                                        mode: 'click',
                                        pos: 'bottom-left'
                                    });
                                }

                                // Add animation classes
                                bookingsDropdown.classList.add('dropdown-content');

                                // Store component reference for later use
                                bookingsDropdown._dropdownComponent = dropdownComponent;
                            }

                            // Handle bookings dropdown selection
                            bookingsOptions.forEach(option => {
                                option.addEventListener('click', function (e) {
                                    e.preventDefault();

                                    // Get the display text and value
                                    const selectedValue = this.getAttribute('data-value');
                                    const selectedText = this.querySelector('.uk-cs-item-text').textContent;

                                    // Update button text (keeping the SVG)
                                    const svgIcon = bookingsDropdownBtn.querySelector('svg');
                                    bookingsDropdownBtn.innerHTML = selectedText + ' ';
                                    bookingsDropdownBtn.appendChild(svgIcon);

                                    // Store the selected value
                                    bookingsDropdownBtn.setAttribute('data-selected', selectedValue);

                                    // Update active state
                                    bookingsOptions.forEach(opt => {
                                        const parentLi = opt.closest('li');
                                        if (parentLi) {
                                            parentLi.classList.remove('uk-active');

                                            // Remove check icon if exists
                                            const checkIcon = opt.querySelector('.uk-cs-check');
                                            if (checkIcon) {
                                                checkIcon.remove();
                                            }
                                        }
                                    });

                                    // Add active state and check icon to selected item
                                    const parentLi = this.closest('li');
                                    if (parentLi) {
                                        parentLi.classList.add('uk-active');

                                        // Add check icon if not exists
                                        if (!this.querySelector('.uk-cs-check')) {
                                            const checkIcon = document.createElement('uk-icon');
                                            checkIcon.className = 'uk-cs-check';
                                            checkIcon.setAttribute('icon', 'check');
                                            this.appendChild(checkIcon);
                                        }
                                    }

                                    // Close the dropdown immediately
                                    const storedComponent = bookingsDropdown._dropdownComponent;
                                    if (storedComponent) {
                                        // Simply hide the dropdown immediately
                                        storedComponent.hide(false);
                                    }
                                });
                            });

                            // Staff Progress Bar Animation
                            // Trigger animation after a short delay
                            setTimeout(function() {
                                const progressPath = document.getElementById('staff-progress-path');
                                if (progressPath) {
                                    progressPath.classList.add('staff-progress-animated');
                                }
                            }, 500);
                        });
                    </script>
     </body>
</html>