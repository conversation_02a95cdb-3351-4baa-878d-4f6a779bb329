import requests
import os
import json

# Authentication credentials
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')

# Payload data
payload = {
    "friendly_name": "starter_cara_mod",
    "language": "en",
    "types": {
        "twilio/carousel": {
            "body": "Explore our premium services!",
            "cards": [
                {
                    "title": "Room Service",
                    "body": "Explore our exceptional food and beverage options!",
                    "media": "https://images-new.vercel.app/starterimages/roomservice.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Order Now",
                            "id": "order_now"
                        }
                    ]
                },
                {
                    "title": "Book a Spa Session",
                    "body": "Relax and rejuvenate with our spa services.",
                    "media": "https://images-new.vercel.app/starterimages/spa.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Book a Spa Session",
                            "id": "book_spa"
                        }
                    ]
                },
                {
                    "title": "Schedule a Massage",
                    "body": "Unwind with our professional massage services.",
                    "media": "https://images-new.vercel.app/starterimages/massage.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Book a Massage",
                            "id": "book_massage"
                        }
                    ]
                },
                {
                    "title": "Book a Room",
                    "body": "Reserve your luxurious accommodations for an unforgettable experience at Can Marques.",
                    "media": "https://images-new.vercel.app/starterimages/roombooking.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Book a Room",
                            "id": "book_room"
                        }
                    ]
                },
                {
                    "title": "Speak with our Receptionist",
                    "body": "Contact our receptionist for assistance with bookings, queries, or concerns.",
                    "media": "https://images-new.vercel.app/starterimages/reception.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Chat with Staff",
                            "id": "chat_staff"
                        }
                    ]
                },
                {
                    "title": "Experiences",
                    "body": "Explore unique experiences, including wine tastings, luxury yacht bookings, a personal chef, and more.",
                    "media": "https://images-new.vercel.app/starterimages/experiences.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Explore Experiences",
                            "id": "explore_experiences"
                        }
                    ]
                }
            ]
        }
    }
}

# Make the POST request
response = requests.post(
    'https://content.twilio.com/v1/Content',
    auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN),
    json=payload
)

# Enhanced response handling
print("\n=== Response Details ===")
print(f"Status Code: {response.status_code}")
print("\n=== Headers ===")
for header, value in response.headers.items():
    print(f"{header}: {value}")

print("\n=== Response Content ===")
try:
    # Try to print formatted JSON
    print(json.dumps(response.json(), indent=2))
except json.JSONDecodeError:
    # If not JSON, print raw text
    print(response.text)

print("\n=== Request Details ===")
print(f"Request URL: {response.request.url}")
print(f"Request Method: {response.request.method}")
print("Request Headers:")
for header, value in response.request.headers.items():
    print(f"{header}: {value}")

print("\n=== Timing ===")
print(f"Elapsed Time: {response.elapsed.total_seconds()} seconds")

if response.status_code != 200:
    print("\n=== Error Details ===")
    print(f"Error Status Code: {response.status_code}")
    print("Error Response:", response.text)
