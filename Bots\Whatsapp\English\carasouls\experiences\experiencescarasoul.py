import requests
import os
import json

# Authentication credentials
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')

# Payload data
payload = {
    "friendly_name": "experiences_carousel",
    "language": "en",
    "types": {
        "twilio/carousel": {
            "body": "Explore unique experiences, including wine tastings, luxury yacht bookings, a personal chef, and more.",
            "cards": [
                {
                    "title": "Wine tasting",
                    "body": "Discover exquisite wines and expand your palate.",
                    "media": "https://images-new.vercel.app/experiences/winetaste.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Reserve Wine Tasting",
                            "id": "book_wine_tasting"
                        }
                    ]
                },
                {
                    "title": "Yacht Charters",
                    "body": "Experience luxury and freedom on the open water.",
                    "media": "https://images-new.vercel.app/experiences/yacht.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Charter a Yacht",
                            "id": "book_yacht_charters"
                        }
                    ]
                },
                {
                    "title": "Private Chef",
                    "body": "Enjoy a personalized culinary experience in the comfort of your home.",
                    "media": "https://images-new.vercel.app/experiences/chef.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Hire Personal Chef",
                            "id": "book_private_chef"
                        }
                    ]
                },
                {
                    "title": "Helicopter Tour",
                    "body": "See the sights from a breathtaking new perspective.",
                    "media": "https://res.klook.com/images/fl_lossy.progressive,q_65/c_fill,w_1200,h_630/w_80,x_15,y_15,g_south_west,l_Klook_water_br_trans_yhcmh3/activities/dfec86ikamnqg7tuhtnf/New%20York%20Helicopter%20Tour.jpg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Book Aerial Tour",
                            "id": "book_helicopter_tour"
                        }
                    ]
                }
            ]
        }
    }
}

# Make the POST request
response = requests.post(
    'https://content.twilio.com/v1/Content',
    auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN),
    json=payload
)

# Enhanced response handling
print("\n=== Response Details ===")
print(f"Status Code: {response.status_code}")
print("\n=== Headers ===")
for header, value in response.headers.items():
    print(f"{header}: {value}")

print("\n=== Response Content ===")
try:
    # Try to print formatted JSON
    print(json.dumps(response.json(), indent=2))
except json.JSONDecodeError:
    # If not JSON, print raw text
    print(response.text)

print("\n=== Request Details ===")
print(f"Request URL: {response.request.url}")
print(f"Request Method: {response.request.method}")
print("Request Headers:")
for header, value in response.request.headers.items():
    print(f"{header}: {value}")

print("\n=== Timing ===")
print(f"Elapsed Time: {response.elapsed.total_seconds()} seconds")

if response.status_code != 200:
    print("\n=== Error Details ===")
    print(f"Error Status Code: {response.status_code}")
    print("Error Response:", response.text)
