import requests
import json
import os

# Twilio account details (replace with actual Account SID and Auth Token)
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')

# Twilio API endpoint (as per the given URL)
url = 'https://content.twilio.com/v1/Content/HX90086324ff4757c28a22446152eab6e6/ApprovalRequests/whatsapp'

# Data payload to be sent
payload = {
    "name": "room_booking_menu",  # Update this as needed
    "category": "UTILITY"
}

# Making the POST request
response = requests.post(
    url,
    auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN),
    headers={'Content-Type': 'application/json'},
    data=json.dumps(payload)
)

# Check the response status
if response.status_code == 200:
    print("Request successful!")
    print(response.json())
else:
    print(f"Request failed with status: {response.status_code}")
    print(response.text)
    print(response.json())