.url-type-selector {
    position: relative;
    z-index: 2000;
    margin-right: 1rem;
    margin-bottom: 1rem;
}

.url-type-selector-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: var(--theme-selector-bg, #ffffff);
    color: var(--theme-selector-color, #212529);
    border: 1px solid var(--theme-selector-border, #dee2e6);
    border-radius: 9px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.url-type-selector-btn:hover {
    background-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.1));
}

.url-type-selector-btn i {
    margin-right: 0.5rem;
}

.url-type-selector-menu {
    display: none;
    position: absolute;
    left: 0;
    top: calc(100% + 5px);
    background-color: var(--theme-selector-menu-bg, #ffffff);
    border: 1px solid var(--theme-selector-menu-border, #dee2e6);
    border-radius: 0.375rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    transition: opacity 0.3s, visibility 0.3s;
    width: 150px;
}

.url-type-option {
    padding: 0.5rem 1rem;
    cursor: pointer;
    color: var(--theme-option-color, #212529);
    display: flex;
    align-items: center;
    transition: background-color 0.3s ease;
}

.url-type-option:hover {
    background-color: var(--theme-option-hover-bg, #f1f5f9);
}

.uk-input {
    flex: 1;
    min-width: 200px;
    transition: border-color 0.3s ease;
}

.uk-input:hover, .uk-input:focus {
    border-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.3));
}

@media (max-width: 640px) {
    .uk-flex-wrap {
        flex-direction: column;
    }
    
    .url-type-selector,
    .uk-input {
        width: 100%;
        margin-right: 0;
        margin-left: 0;
        margin-bottom: 1rem;
    }
}

