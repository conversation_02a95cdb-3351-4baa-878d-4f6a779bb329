# Download the helper library from https://www.twilio.com/docs/python/install
import os
from twilio.rest import Client
import json

# Find your Account SID and Auth Token at twilio.com/console
# and set the environment variables. See http://twil.io/secure
TWILIO_ACCOUNT_SID="**********************************"
TWILIO_AUTH_TOKEN="005d910f85546392a91f58a3878c437c"

temp = "HX519c98eb1018a84d1ae1e74f3199050d"

try:
    # Initialize client with correct credentials
    client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

    message = client.messages.create(
        content_sid=temp, # SID of the starter template
        to="whatsapp:+************",
        from_="whatsapp:+***********"
    )

    print(message.sid)
    print(message.body)

except Exception as e:
    print(f"Error: {str(e)}")