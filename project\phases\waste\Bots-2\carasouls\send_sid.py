# Download the helper library from https://www.twilio.com/docs/python/install
import os
from twilio.rest import Client
import json

# Find your Account SID and Auth Token at twilio.com/console
# and set the environment variables. See http://twil.io/secure
TWILIO_ACCOUNT_SID="**********************************"
TWILIO_AUTH_TOKEN="005d910f85546392a91f58a3878c437c"

STARTER_TEMPLATE = "HX5e3392d6f03fa0b742eeb75cf50f4048"

SPA_TEMPLATE = "HX0ea1611c7a662becc730c2b9dc788554"

NEW_SPA_TEMPLATE = "HX883e0100f73a407477e2283b11bbab04"

try:
    # Initialize client with correct credentials
    client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

    message = client.messages.create(
        content_sid=NEW_SPA_TEMPLATE, # SID of the starter template
        to="whatsapp:+************",
        from_="whatsapp:+***********"
    )

    print(message.sid)
    print(message.body)

except Exception as e:
    print(f"Error: {str(e)}")