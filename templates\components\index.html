<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Interactive Line Chart</title>
  <style>
    /* Base reset and common styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    
    /* Main layout styles */
    body {
      background-color: #ffffff;
      padding: 20px;
    }
    
    /* Chart container with border and rounded corners */
    .ggchart-container {
      background-color: white;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      overflow: hidden;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    /* Header section with title and stats */
    .ggchart-header {
      padding: 20px;
      height: 95px;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      justify-content: space-between;
      align-items: stretch;
    }
    
    .ggchart-title {
      flex-grow: 1;
    }
    
    .ggchart-title h2 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 6px;
    }
    
    .ggchart-title p {
      font-size: 14px;
      color: #666;
    }
    
    /* Statistics boxes styling */
    .ggchart-stats-container {
      display: flex;
      padding: 0;
      margin: -20px 0 -20px 20px;
      position: relative;
      border-left: 1px solid #e5e7eb;
    }
    
    .ggchart-stat-box {
      text-align: center;
      padding: 20px;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      justify-content: center;
      transition: background-color 0.2s;
      min-width: 120px;
    }
    
    /* Interactive elements and hover effects */
    .ggchart-stat-box:hover {
      background-color: #ffffff;
    }
    
    .ggchart-stat-box.active {
      background-color: #ffffff;
    }
    
    .ggchart-stat-box.active h3 {
      color: #2a9d90;
    }
    
    .ggchart-stat-box h3 {
      font-size: 13px;
      color: #666;
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .ggchart-stat-box p {
      font-size: 16px;
      color: #333;
      font-weight: 600;
    }
    
    .ggchart-stat-box:first-child {
      border-right: 1px solid #e5e7eb;
    }
    
    /* Chart visualization styles */
    .ggchart-body {
      padding: 20px;
      position: relative;
      height: 300px;
    }
    
    svg {
      width: 100%;
      height: 100%;
      overflow: visible;
    }
    
    /* D3.js specific styling */
    .axis path,
    .axis line {
      stroke: #e0e0e0;
      stroke-width: 1.5px;
    }
    
    .axis text {
      fill: #666;
      font-size: 12px;
      font-weight: 500;
    }
    
    .ggchart-line {
      /* Path for the main line chart */
      fill: none;
      stroke: #2a9d90;
      stroke-width: 2px;
      stroke-linejoin: round;
      stroke-linecap: round;
      transition: 0.3s;
    }
    
    /* Tooltip and interaction elements */
    .ggchart-tooltip {
      position: absolute;
      background-color: #fff;
      border-radius: 6px;
      border: 1px solid #e5e7eb;
      padding: 10px 12px;
      font-size: 14px;
      pointer-events: none;
      opacity: 0;
      transition: all 0.2s ease-out;
      margin: 0;
      z-index: 100;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .ggchart-tooltip-date {
      font-weight: 600;
      margin-bottom: 5px;
      color: #333;
    }
    
    .ggchart-tooltip-value {
      display: flex;
      align-items: center;
      color: #666;
    }
    
    .ggchart-tooltip-value::before {
      content: '';
      display: inline-block;
      width: 10px;
      height: 10px;
      background-color: #2a9d90;
      border-radius: 2px;
      margin-right: 6px;
    }
    
    .ggchart-dot {
      fill: white;
      stroke: #2a9d90;
      stroke-width: 3;
      opacity: 0;
      transition: opacity 0.1s ease-out;
    }
    
    .ggchart-hover-line {
      stroke: #ddd;
      stroke-width: 1;
      opacity: 0;
      transition: transform 0.1s ease-out;
    }
    
    .ggchart-overlay {
      fill: none;
      pointer-events: all;
    }
    
    /* Rename colliding icon/option classes */
    .ggchart-line.mobile {
      stroke: #2662d9;
    }
    
    .ggchart-dot.mobile {
      stroke: #2662d9;
    }
    
    .ggchart-tooltip-value.mobile::before {
      background-color: #2662d9;
    }
    
    .ggchart-stat-box.active.mobile h3 {
      color: #2662d9;
    }
  </style>
</head>
<body>
  <div class="ggchart-container">
    <div class="ggchart-header">
      <div class="ggchart-title">
        <h2>Total sales</h2>
        <p>Showing total sales for the last 30 days</p>
      </div>
      <div class="ggchart-stats-container">
        <div class="ggchart-stat-box" role="button" tabindex="0">
          <h3>Desktop</h3>
          <p>24,828</p>
        </div>
        <div class="ggchart-stat-box" role="button" tabindex="0">
          <h3>Mobile</h3>
          <p>25,010</p>
        </div>
      </div>
    </div>
    <div class="ggchart-body" id="chart">
      <div class="ggchart-tooltip">
        <div class="ggchart-tooltip-date"></div>
        <div class="ggchart-tooltip-value"></div>
      </div>
    </div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/7.8.5/d3.min.js"></script>
  <script>
    // Data Generation and Chart Setup
    function generateData(type) {
      const data = [];
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 29);
      
      for (let i = 0; i < 30; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);
        let value = 200 + Math.random() * 150;
        if (type === 'mobile') {
          value = 220 + Math.random() * 180;
        }
        value += Math.sin(i / 7) * (type === 'mobile' ? 70 : 50);
        value += Math.sin(i / 30) * (type === 'mobile' ? 100 : 80);
        if (i % 12 === 0) {
          value += Math.random() * (type === 'mobile' ? 120 : 100);
        }
        data.push({
          date: date,
          value: Math.round(value)
        });
      }
      return data;
    }
    
    const desktopData = generateData('desktop');
    const mobileData = generateData('mobile');
    let currentData = desktopData;
    
    const container = document.getElementById('chart');
    const margin = { top: 20, right: 50, bottom: 80, left: 10 };
    const width = container.clientWidth - margin.left - margin.right;
    const height = container.clientHeight - margin.top - margin.bottom;
    
    const svg = d3.select('#chart')
      .append('svg')
      .attr('width', width + margin.left + margin.right)
      .attr('height', height + margin.top + margin.bottom)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);
    
    const x = d3.scaleTime()
      .domain(d3.extent(currentData, d => d.date))
      .range([0, width]);
    
    const y = d3.scaleLinear()
      .domain([0, d3.max(currentData, d => d.value) * 1.1])
      .range([height, 0]);
    
    const line = d3.line()
      .x(d => x(d.date))
      .y(d => y(d.value))
      .curve(d3.curveMonotoneX);
    
    svg.append('g')
      .attr('class', 'axis')
      .attr('transform', `translate(0,${height})`)
      .call(d3.axisBottom(x)
        .ticks(d3.timeDay.every(1))
        .tickFormat(d3.timeFormat('%d'))
        .tickSizeOuter(0)
      )
      .selectAll("text")
        .style("text-anchor", "middle")
        .attr("dx", "0")
        .attr("dy", "2em")
        .attr("transform", "rotate(0)");
    
    svg.append('g')
      .attr('class', 'axis')
      .call(d3.axisLeft(y)
        .ticks(5)
        .tickSize(-width)
        .tickFormat('')
      )
      .call(g => g.select('.domain').remove());
    
    svg.append('path')
      .datum(currentData)
      .attr('class', 'ggchart-line')
      .attr('d', line);
    
    const hoverLine = svg.append('line')
      .attr('class', 'ggchart-hover-line')
      .attr('y1', 0)
      .attr('y2', height);
    
    const dots = svg.selectAll('.ggchart-dot')
      .data(currentData)
      .enter()
      .append('circle')
      .attr('class', 'ggchart-dot')
      .attr('cx', d => x(d.date))
      .attr('cy', d => y(d.value))
      .attr('r', 6);
    
    const tooltip = d3.select('.ggchart-tooltip');
    
    svg.append('rect')
      .attr('class', 'ggchart-overlay')
      .attr('width', width)
      .attr('height', height)
      .on('mousemove', mousemove)
      .on('mouseout', mouseout);
    
    function updateTooltipAndHighlights(d, xPos, yPos) {
      hoverLine.attr('transform', `translate(${xPos},0)`).style('opacity', 1);
      dots.style('opacity', 0);
      svg.selectAll('.ggchart-dot')
        .filter(dt => dt.date.getTime() === d.date.getTime())
        .style('opacity', 1);
      
      const tooltipWidth = tooltip.node().offsetWidth;
      const containerWidth = container.clientWidth;
      let leftPos;
      if (d === currentData[0]) {
        leftPos = xPos + margin.left + 15;
      } else if (xPos + margin.left < containerWidth / 2) {
        leftPos = xPos + margin.left + 15;
      } else {
        leftPos = xPos + margin.left - tooltipWidth - 15;
      }
      
      leftPos = Math.max(10, Math.min(containerWidth - tooltipWidth - 10, leftPos));
      let topPos = margin.top + 10;
      
      tooltip
        .style('opacity', 1)
        .style('left', `${leftPos}px`)
        .style('top', `${topPos}px`);
      
      const formatDate = d3.timeFormat('%b %d, %Y');
      tooltip.select('.ggchart-tooltip-date').text(formatDate(d.date));
      tooltip.select('.ggchart-tooltip-value').text(d.value.toLocaleString());
    }
    
    function mousemove(event) {
      const [mouseX] = d3.pointer(event);
      const xDate = x.invert(mouseX);
      const bisect = d3.bisector(d => d.date).left;
      const i = bisect(currentData, xDate, 1);
      let d;
      if (i === 0) {
        d = currentData[0];
      } else {
        const d0 = currentData[i - 1];
        const d1 = currentData[i] || d0;
        d = xDate - d0.date > d1.date - xDate ? d1 : d0;
      }
      const xPosition = x(d.date);
      const yPosition = y(d.value);
      updateTooltipAndHighlights(d, xPosition, yPosition);
    }
    
    function mouseout() {
      hoverLine.style('opacity', 0);
      dots.style('opacity', 0);
      tooltip.style('opacity', 0);
    }
    
    window.addEventListener('resize', debounce(() => {
      if (container.clientWidth !== width + margin.left + margin.right) {
        d3.select('#chart svg').remove();
        setTimeout(() => {
          const width = container.clientWidth - margin.left - margin.right;
          x.range([0, width]);
          x.domain(d3.extent(currentData, d => d.date));
          const svg = d3.select('#chart')
            .append('svg')
            .attr('width', width + margin.left + margin.right)
            .attr('height', height + margin.top + margin.bottom)
            .append('g')
            .attr('transform', `translate(${margin.left},${margin.top})`);
          
          svg.append('g')
            .attr('class', 'axis')
            .attr('transform', `translate(0,${height})`)
            .call(d3.axisBottom(x)
              .ticks(d3.timeDay.every(1))
              .tickFormat(d3.timeFormat('%d'))
              .tickSizeOuter(0)
            )
            .selectAll("text")
              .style("text-anchor", "middle")
              .attr("dx", "0")
              .attr("dy", "2em")
              .attr("transform", "rotate(0)");
          
          svg.append('g')
            .attr('class', 'axis')
            .call(d3.axisLeft(y)
              .ticks(5)
              .tickSize(-width)
              .tickFormat('')
            )
            .call(g => g.select('.domain').remove());
          
          svg.append('path')
            .datum(currentData)
            .attr('class', 'ggchart-line')
            .attr('d', line);
          
          const hoverLine = svg.append('line')
            .attr('class', 'ggchart-hover-line')
            .attr('y1', 0)
            .attr('y2', height);
          
          const dots = svg.selectAll('.ggchart-dot')
            .data(currentData)
            .enter()
            .append('circle')
            .attr('class', 'ggchart-dot')
            .attr('cx', d => x(d.date))
            .attr('cy', d => y(d.value))
            .attr('r', 6);
          
          svg.append('rect')
            .attr('class', 'ggchart-overlay')
            .attr('width', width)
            .attr('height', height)
            .on('mousemove', mousemove)
            .on('mouseout', mouseout);
        }, 100);
      }
    }, 100));
    
    function updateChart(newData, type) {
      currentData = newData;
      y.domain([0, d3.max(newData, d => d.value) * 1.1]);
      
      if (type === 'mobile') {
        svg.selectAll('.ggchart-line, .ggchart-dot').classed('mobile', true);
        tooltip.select('.ggchart-tooltip-value').classed('mobile', true);
      } else {
        svg.selectAll('.ggchart-line, .ggchart-dot').classed('mobile', false);
        tooltip.select('.ggchart-tooltip-value').classed('mobile', false);
      }
      
      svg.select('.ggchart-line')
        .datum(newData)
        .transition()
        .duration(300)
        .attr('d', line);
      
      const dots = svg.selectAll('.ggchart-dot')
        .data(newData);
      
      dots.exit().remove();
      
      dots.enter()
        .append('circle')
        .attr('class', 'ggchart-dot')
        .attr('r', 6)
        .merge(dots)
        .attr('cx', d => x(d.date))
        .attr('cy', d => y(d.value));
    }
    
    document.querySelectorAll('.ggchart-stat-box').forEach(box => {
      box.addEventListener('click', function() {
        document.querySelectorAll('.ggchart-stat-box').forEach(b => {
          b.classList.remove('active', 'mobile');
        });
        const type = this.querySelector('h3').textContent.toLowerCase();
        this.classList.add('active');
        if (type === 'mobile') {
          this.classList.add('mobile');
        }
        updateChart(type === 'desktop' ? desktopData : mobileData, type);
      });
      
      box.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.click();
        }
      });
    });
    
    document.querySelector('.ggchart-stat-box').classList.add('active');
    updateChart(desktopData, 'desktop');
    
    window.addEventListener('load', function() {
      setTimeout(() => {
        const firstPoint = currentData[0];
        const xPosition = x(firstPoint.date);
        const yPosition = y(firstPoint.value);
        updateTooltipAndHighlights(firstPoint, xPosition, yPosition);
        setTimeout(() => {
          if (!document.querySelector('.ggchart-overlay:hover')) {
            mouseout();
          }
        }, 2000);
      }, 500);
    });
    
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }
  </script>
</body>
</html>