.model-selector {
    z-index: 2000;
    background-color: inherit;
    position: relative;
    width: 100%;
}
.model-selector-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: var(--theme-selector-bg, #e2e8f0);
    color: var(--theme-selector-color, #212529);
    border: 1px solid var(--theme-selector-border, #dee2e6);
    border-radius: 9px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}
.model-selector-btn:hover {
    background-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.1));
}
.dropdown-icon {
    width: 20px;
    height: 20px;
}
.model-selector-menu {
    display: none;
    position: absolute;
    left: 0;
    right: 0;
    top: calc(100% + 5px);
    background-color: var(--theme-selector-menu-bg, #ffffff);
    border: 1px solid var(--theme-selector-menu-border, #dee2e6);
    border-radius: 0.375rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
}
.model-option {
    padding: 0.5rem 1rem;
    cursor: pointer;
    color: var(--theme-option-color, #212529);
}
.model-option:hover {
    background-color: var(--theme-option-hover-bg, #f1f5f9);
}

.temperature-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
}
.temperature-selector input[type="range"] {
    flex-grow: 1;
    -webkit-appearance: none;
    width: 100%;
    height: 8px;
    border-radius: 5px;  
    background: #d3d3d3;
    outline: none;
}
.temperature-selector input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%; 
    background: #4CAF50;
    cursor: pointer;
}
.temperature-selector input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
}
.temperature-selector output {
    font-weight: bold;
    min-width: 40px;
    text-align: center;
}