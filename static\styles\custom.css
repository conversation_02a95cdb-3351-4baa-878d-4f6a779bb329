* {
    font-family: 'Onest', sans-serif !important;
}

.language-selector {
    z-index: 2000;
    background-color: inherit;
    position: relative;
    /* Add this line */
}

/* Chart colors for different themes */
.light {
    --chart-bar-color: #18181b;
}

.pure-black {
    --chart-bar-color: #fafafa;
}

/* Chart specific styles */
#TotalTipsChart {
    transition: all 0.3s ease;
}

.language-selector-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    background-color: var(--theme-selector-bg, #e2e8f0);
    color: var(--theme-selector-color, #212529);
    border: 1px solid var(--theme-selector-border, #dee2e6);
    border-radius: 9px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.language-selector-btn:hover {
    background-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.1));
}

.language-selector-btn i {
    margin-right: 0.25rem;
}

.language-selector-menu {
    display: none;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: calc(100% + 5px);
    background-color: var(--theme-selector-menu-bg, #ffffff);
    border: 1px solid var(--theme-selector-menu-border, #dee2e6);
    border-radius: 0.375rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    width: 150px;
}

.language-option {
    padding: 0.5rem 1rem;
    cursor: pointer;
    color: var(--theme-option-color, #212529);
}

.language-option:hover {
    background-color: var(--theme-option-hover-bg, #f1f5f9);
}


.main-content {
    margin-left: 280px;
    width: calc(100% - 280px);
    min-height: 100vh;
}

@media (max-width: 1024px) {

    .main-content {
        margin-left: 0;
        width: 100%;
    }
}

.goog-te-banner-frame.skiptranslate {
    display: none !important;
}

.light {
    background-color: #ffffff;
    color: #212529;
}

.light #form-container {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
}

/* Pure Black */
.pure-black {
    background-color: #000000;
    color: #ffffff;
}

.pure-black #form-container {
    background-color: #121212;
    border: 1px solid #333333;
}

.pure-black input,
.pure-black textarea {
    background-color: #09090b;
    border: 1px solid #333333;
    color: #ffffff;
}

.pure-black button {
    background-color: transparent;
    color: #ffffff;
}

.pure-black .special-button {
    background-color: #055cff;
    color: #ffffff;
    border-radius: 9px;
    border: 1px solid #055cff;
}

.pure-black .special-button2 {
    border-radius: 9px;
    border: 1px solid var(--theme-selector-border);
}

.light .special-button2 {
    border-radius: 9px;
    border: 1px solid var(--theme-selector-border);
}

.light .special-button {
    background-color: #055cff;
    color: #ffffff;
    border-radius: 9px;
    border: 1px solid #055cff;
}

.light .special-button-3 {
    background-color: #dee2e6;
    color: #000000;
    border: 1px solid var(--theme-selector-border);
    transition: all 0.3s ease;
}

.light .special-button-3:hover {
    background-color: transparent;
    border: 1px solid var(--theme-selector-border);
}

.pure-black .special-button-3 {
    background-color: #141414;
    color: #ffffff;
    border: 1px solid var(--theme-selector-border);
    transition: all 0.3s ease;
}

.pure-black .special-button-3:hover {
    background-color: transparent;
    border: 1px solid var(--theme-selector-border);
}



.light {
    background-color: #ffffff;
    color: #212529;
    --theme-selector-bg: #ffffff;
    --theme-selector-color: #212529;
    --theme-selector-border: #dee2e6;
    --theme-selector-menu-bg: #ffffff;
    --theme-selector-menu-border: #dee2e6;
    --theme-option-color: #212529;
    --theme-option-hover-bg: #f1f5f9;
    --border-color: #e5e7eb;
    --navbar-background-color: #ffffff;
    --product-page-bg: #e2e4e6;
    --tabs-background-color: #e4e4e7;
    /* Menubar specific */
    --menubar-bg: transparent;
    --menubar-border: #e5e5e5;
    --menubar-indicator-bg: #f4f4f5;
    --menubar-link-color: var(--theme-selector-color);
}
/* Carousel control button background */
    .light .carousel-control {
        background-color: #ffffff;
    }

    .light .carousel-control.card:hover { /* Increased specificity */
        background-color: #f4f4f5 !important;
    }

/* Pure Black */
.pure-black {
    background-color: #09090b;
    color: #ffffff;
    --theme-selector-bg: #333333;
    --theme-selector-color: #ffffff;
    --theme-selector-border: #444444;
    --theme-selector-menu-bg: #121212;
    --theme-selector-menu-border: #333333;
    --theme-option-color: #ffffff;
    --theme-option-hover-bg: #1e1e1e;
    --checkbox-border: #27272a;
    --border-color: #27272a;
    --navbar-background-color: #09090b;
    --product-page-bg: #121212;
    --tabs-background-color: #27272a;
    /* Menubar specific */
    --menubar-bg: #09090b;
    --menubar-border: #27272a;
    --menubar-indicator-bg: #27272a;
    --menubar-link-color: #cccccc;
    --popup-shadow: rgba(0, 0, 0, 0.5);
/* Carousel control button background */
    .pure-black .carousel-control.card { /* Increased specificity */
        background-color: #09090b !important; /* Added !important */
    }

    .pure-black .carousel-control.card:hover { /* Increased specificity */
        background-color: #27272a !important;
    }
    --popup-overlay: rgba(0, 0, 0, 0.5);
    --success-bg: rgba(9, 9, 11, 0.95);
    --popup-divider: #27272a;
}

.theme-selector {
    position: relative;
    z-index: 2000;
}

.theme-selector-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    background-color: var(--theme-selector-bg, #ffffff);
    color: var(--theme-selector-color, #212529);
    border: 1px solid var(--theme-selector-border, #dee2e6);
    border-radius: 9px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.theme-selector-btn:hover {
    background-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.1));
}

.theme-selector-btn i {
    margin-right: 0.25rem;
}

.theme-selector-menu {
    display: none;
    position: absolute;
    right: 0;
    top: calc(100% + 20px);
    background-color: var(--theme-selector-menu-bg, #ffffff);
    border: 1px solid var(--theme-selector-menu-border, #dee2e6);
    border-radius: 0.375rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    transition: opacity 0.3s, visibility 0.3s;
    width: 200px;
    /* Fixed width */
}

/* .theme-selector:hover .theme-selector-menu {
        display: block;
    } */
.theme-option {
    padding: 0.5rem 1rem;
    cursor: pointer;
    color: var(--theme-option-color, #212529);
    display: flex;
    align-items: center;
}

.theme-option:hover {
    background-color: var(--theme-option-hover-bg, #f1f5f9);
}

.theme-color-preview {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #000;
}

.theme-selector,
.language-selector {
    margin-left: 0.5rem;
    /* Adds a small gap between the buttons */
}

.theme-selector-btn,
.language-selector-btn {
    padding: 0.5rem 1rem;
    /* Adjust padding as needed */
}

/* Add similar variable sets for other themes */


/* Light Theme */
.light .card,
.light .gg-card {
    border-color: #e5e7eb;
    /* Example border color for light theme (it was #d3d3d3, changed it because it was too harsh)*/
}

/* Pure Black Theme */
.pure-black .card {
    border-color: #27272a;
    /* overflow: hidden; */
    /* No border for pure black theme */
}


.pure-black .shadow-fixer {
    box-shadow: none;
}

.light .shadow-fixer {
    box-shadow: none;
}

.pure-black .lines {
    background-color: #27272a;
}

.light .lines {
    border-color: #e5e7eb;
    background-color: #e5e7eb;
}

.light .chat-input-card {
    background-color: white;
}

.pure-black .chat-input-card {
    background-color: #09090b;
}


.pure-black .main-card {
    border: 1px solid #ffffff;
}

.light .main-card {
    border: 1px solid #000000;
}



.options-selector {
    z-index: 2000;
    background-color: inherit;
    position: relative;
}

.options-selector-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    background-color: var(--theme-selector-bg, #e2e8f0);
    color: var(--theme-selector-color, #212529);
    border: 1px solid var(--theme-selector-border, #dee2e6);
    border-radius: 9px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.options-selector-btn:hover {
    background-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.1));
}

.options-selector-btn i {
    margin-right: 0.25rem;
}

.options-selector-menu {
    display: none;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: calc(100% + 5px);
    background-color: var(--theme-selector-menu-bg, #ffffff);
    border: 1px solid var(--theme-selector-menu-border, #dee2e6);
    border-radius: 0.375rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    width: 150px;
}

/* .options-selector:hover .options-selector-menu,
    .options-selector.active .options-selector-menu {
        opacity: 1;
        visibility: visible;
    }

    .options-selector:hover .options-selector-menu {
        display: block;
    } */
.options-option {
    padding: 0.5rem 1rem;
    cursor: pointer;
    color: var(--theme-option-color, #212529);
}

.options-option:hover {
    background-color: var(--theme-option-hover-bg, #f1f5f9);
}

.theme-selector,
.language-selector,
.options-selector {
    margin-left: 0.2rem;
    /* Increase the gap between selectors */
}

.theme-selector-btn,
.language-selector-btn,
.options-selector-btn,
.link-selector-btn {
    padding: 0.5rem 1rem;
    /* Ensure consistent padding */
    height: 38px;
    /* Set a fixed height for all buttons */
    display: flex;
    width: 150px;
    align-items: center;
    justify-content: center;
}

/* Ensure consistent width for all selector menus */
.theme-selector-menu,
.language-selector-menu,
.options-selector-menu {
    width: 150px;
    /* Set a consistent width */
}

/* Adjust the flex container for the selectors */
.flex-1.flex.items-center.gap-4.justify-end {
    gap: 1rem;
    /* Use gap instead of margin for more consistent spacing */
}

/* Base styles for the buttons */
.uk-tab-alt {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.uk-tab-alt li {
    margin-right: 10px;
}

.uk-tab-alt li a {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

/* Active state */
.uk-tab-alt li.uk-active a {
    font-weight: bold;
}

/* Light Theme */
.light .uk-tab-alt li a {
    color: #212529;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.light .uk-tab-alt li.uk-active a,
.light .uk-tab-alt li a:hover {
    background-color: #e9ecef;
    border-color: #ced4da;
}

/* Pure Black Theme */
.pure-black .uk-tab-alt li a {
    color: #ffffff;
    background-color: #333333;
    border: 1px solid #444444;
}

.pure-black .uk-tab-alt li.uk-active a,
.pure-black .uk-tab-alt li a:hover {
    background-color: #1e1e1e;
    border-color: #555555;
}


/* Ensure transparency in all themes */
.light .uk-tab-alt.ml-auto.max-w-40,
.pure-black .uk-tab-alt.ml-auto.max-w-40 {
    background-color: transparent !important;
    box-shadow: none !important;
}

/* Base styles for labels */
.uk-label {
    padding: 0.25em 0.6em;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    text-transform: uppercase;
    font-weight: 600;
}

/* Customer Language Label */
.customer-language {
    background-color: var(--language-label-bg, #f0f0f0);
    color: var(--language-label-color, #333);
}

/* Platform Label */
.platform {
    background-color: var(--platform-label-bg, #3498db);
    color: var(--platform-label-color, #fff);
}

/* Theme-specific styles */
.light {
    --language-label-bg: #f0f0f0;
    --language-label-color: #333;
    --platform-label-bg: #3498db;
    --platform-label-color: #fff;
}

.pure-black {
    --language-label-bg: #333;
    --language-label-color: #fff;
    --platform-label-bg: #0a84ff;
    --platform-label-color: #fff;
}




#urgency-options {
    background-color: var(--dropdown-bg);
    color: var(--dropdown-text);
    border-color: var(--dropdown-border);
}

#urgency-options li {
    color: var(--dropdown-text);
}

#urgency-options li:hover {
    background-color: var(--dropdown-hover-bg);
    color: var(--dropdown-hover-text);
}

/* ...existing code... */

#area-options,
#priority-options {
    background-color: var(--dropdown-bg);
    color: var(--dropdown-text);
    border-color: var(--dropdown-border);
}

#area-options li,
#priority-options li {
    color: var(--dropdown-text);
}

#area-options li:hover,
#priority-options li:hover {
    background-color: var(--dropdown-hover-bg);
    color: var(--dropdown-hover-text);
}

/* ...existing code... */

/* Light */
.light {
    /* ... existing variables ... */
    --dropdown-bg: #ffffff;
    --dropdown-text: #212529;
    --dropdown-hover-bg: #f4f4f5;
    --dropdown-hover-text: #212529;
    --dropdown-border: #dee2e6;
    --active-state-color: #ffffff;
}

/* Pure Black */
.pure-black {
    /* ... existing variables ... */
    --dropdown-bg: #121212;
    --dropdown-text: #ffffff;
    --dropdown-hover-bg: #27272a;
    --dropdown-hover-text: #ffffff;
    --dropdown-border: #333333;
    --active-state-color: #09090b;
    --active-state-text-color: #ffffff;
}

.theme-card {
    background-color: var(--theme-selector-bg);
    color: var(--theme-selector-color);
    border: 1px solid var(--theme-selector-border);
}

.theme-title {
    color: var(--theme-selector-color);
}

.theme-text {
    color: var(--theme-option-color);
}

.theme-button {
    background-color: var(--button-bg);
    color: var(--button-text);
    border: 1px solid var(--button-border);
}

.theme-button:hover {
    background-color: var(--button-hover-bg);
}


/* Light Theme */
.light .card,
.light .gg-card {
    border-color: #e5e7eb;
    /* Example border color for light theme (it was #d3d3d3, changed it because it was too harsh)*/
}

.pure-black .no-overflow-card {
    border-color: #27272a;
}

.light .no-overflow-card {
    border-color: #e5e7eb;
}

.uk-tab-alt {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.uk-tab-alt li {
    margin-right: 5px;
    /* Reduced margin between buttons */
}

.uk-tab-alt li a {
    display: inline-block;
    padding: 4px 8px;
    /* Reduced padding */
    text-decoration: none;
    background-color: transparent !important;
    border: none !important;
    transition: none;
    font-size: 0.875rem;
    /* Slightly smaller font size for compactness */
}

/* Active state - only change font weight */
.uk-tab-alt li.uk-active a {
    font-weight: bold;
}

/* Remove hover effects */
.uk-tab-alt li a:hover {
    background-color: transparent !important;
    opacity: 1 !important;
}

/* Ensure no background or effects for all themes */
.light .uk-tab-alt li a,
.pure-black .uk-tab-alt li a {
    background-color: transparent !important;
    border: none !important;
}

/* Remove any remaining background or border from the parent container */
.uk-tab-alt.ml-auto.max-w-40 {
    background-color: transparent !important;
    box-shadow: none !important;
}

.uk-tab-alt {
    display: flex;
    height: 2.25rem;
    width: 100%;
    align-items: center;
    justify-content: center;
    border-radius: .5rem;
    background-color: transparent;
    padding: .25rem;
    color: hsl(var(--muted-foreground));
}

#send-button {
    border: 1px solid transparent;
    background-color: var(--theme-option-color);
    color: var(--theme-selector-menu-border);
}

.uk-iconnav a.hidden {
    display: inline-block !important;
}


.border-left {
    border-left: 1px solid var(--theme-selector-menu-border);
    /* You can adjust the color to match your design */
}

.theme-options {
    transition: all 0.3s ease;
}

.theme-options.selected-theme {
    border-color: var(--theme-selector-color);
    box-shadow: 0 0 0 2px var(--theme-selector-color);
}

.theme-options:hover {
    transform: translateY(-2px);
}


.font-option {
    padding: 0.5rem 1rem;
    cursor: pointer;
    color: var(--theme-option-color, #212529);
}

.font-option:hover {
    background-color: var(--theme-option-hover-bg, #f1f5f9);
}


.greyed-out-input {
    color: #666 !important;
    cursor: not-allowed !important;
}

.editable-input {
    transition: background-color 0.3s ease;
}

.editable-input:not([readonly]) {

    cursor: text;
}


.link-container {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    background-color: #f9fafb;
}

.link-label {
    width: 30%;
    font-weight: 600;
}

.custom-link-btn {
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #374151;
    transition: all 0.3s ease;
}

.custom-link-btn:hover {
    background-color: #e5e7eb;
}

.link-selector-btn {
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0.5rem;
    background-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.1));
    color: var(--theme-selector-color, #212529);
    border: 1px solid var(--theme-selector-border, #dee2e6);
    border-radius: 8px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.link-selector-btn:hover {
    background-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.1));
}


.file-upload-area {
    transition: border-color 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--platform-label-bg);
}

.file-upload-area {
    border: 2px dashed var(--platform-label-bg);
    transition: border-color 0.3s ease;
}

.light {
    /* ... existing styles ... */
    --theme-overlay-bg: rgb(200, 200, 200);
}

.pure-black {
    /* ... existing styles ... */
    --theme-overlay-bg: rgb(40, 40, 40);
}



.task-filter-dropdown {
    position: relative;
    z-index: 2000;
    margin-left: auto;
    /* This will push the button to the right */
    margin-bottom: 1rem;
    width: fit-content;
    /* This ensures the dropdown container only takes up as much width as needed */
}


.task-filter-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: var(--theme-selector-bg, #ffffff);
    color: var(--theme-selector-color, #212529);
    border: 1px solid var(--theme-selector-border, #dee2e6);
    border-radius: 9px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.task-filter-btn:hover {
    background-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.1));
}

.task-filter-btn i {
    margin-right: 0.5rem;
}

.task-filter-btn .fa-chevron-down {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.task-filter-btn[aria-expanded="true"] .fa-chevron-down {
    transform: rotate(180deg);
}

.task-filter-menu {
    display: none;
    position: absolute;
    right: 0;
    /* Change this from 'left: 0' to 'right: 0' */
    top: calc(100% + 5px);
    background-color: var(--theme-selector-menu-bg, #ffffff);
    border: 1px solid var(--theme-selector-menu-border, #dee2e6);
    border-radius: 0.375rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    transition: opacity 0.3s, visibility 0.3s;
    width: 200px;
}

.task-filter-option {
    padding: 0.5rem 1rem;
    cursor: pointer;
    color: var(--theme-option-color, #212529);
    display: flex;
    align-items: center;
    transition: background-color 0.3s ease;
}

.task-filter-option:hover {
    background-color: var(--theme-option-hover-bg, #f1f5f9);
}

.task-filter-option i {
    width: 20px;
    text-align: center;
    margin-right: 0.5rem;
}

@media (max-width: 640px) {
    .task-filter-dropdown {
        width: 100%;
        margin-right: 0;
        margin-left: 0;
        margin-bottom: 1rem;
    }
}

.user-options-container {
    position: relative;
}

.user-options-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: var(--theme-selector-bg, #ffffff);
    color: var(--theme-selector-color, #212529);
    border: 1px solid var(--theme-selector-border, #dee2e6);
    border-radius: 9px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.user-options-btn:hover {
    background-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.1));
}

.user-options-btn svg {
    transition: transform 0.3s ease;
}

.user-options-btn[aria-expanded="true"] svg {
    transform: rotate(180deg);
}

.user-options-menu {
    position: absolute;
    right: 0;
    top: calc(100% + 5px);
    background-color: var(--theme-selector-menu-bg, #ffffff);
    border: 1px solid var(--theme-selector-menu-border, #dee2e6);
    border-radius: 0.375rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    transition: opacity 0.3s, visibility 0.3s;
    /* width: 200px; */
}

.user-options-menu a {
    padding: 0.5rem 1rem;
    cursor: pointer;
    color: var(--theme-option-color, #212529);
    display: flex;
    align-items: center;
    transition: background-color 0.3s ease;
}

.user-options-menu a:hover {
    background-color: var(--theme-option-hover-bg, #f1f5f9);
}

.calendar-day {
    color: var(--theme-option-color);
    background-color: var(--theme-selector-bg);
}

.calendar-day:hover {
    background-color: var(--theme-option-hover-bg);
}

.calendar-day.selected {
    background-color: var(--theme-selector-color);
    color: var(--theme-selector-bg);
}

.datepicker {
    background-color: var(--theme-selector-bg);
    color: var(--theme-selector-color);
}

.calendar-day.in-range {
    background-color: var(--theme-option-hover-bg);
}

#calendarDropdown {
    background-color: var(--theme-selector-menu-bg);
    border-color: var(--theme-selector-menu-border);
}

#calendarDropdown input {
    background-color: var(--theme-selector-bg);
    color: var(--theme-option-color);
    border-color: var(--theme-selector-border);
}

.uk-drop.uk-dropdown {
    background-color: var(--dropdown-bg, #ffffff) !important;
    border: 1px solid var(--dropdown-border, #dee2e6) !important;
}

.uk-dropdown-nav.uk-nav li {
    color: var(--dropdown-text, #212529) !important;
}

.uk-dropdown-nav.uk-nav li:hover {
    background-color: var(--dropdown-hover-bg, #f8f9fa) !important;
    color: var(--dropdown-hover-text, #212529) !important;
}

.uk-nav-divider {
    border-top: 1px solid var(--dropdown-border, #dee2e6) !important;
}

#pms-options {
    background-color: var(--dropdown-bg);
    color: var(--dropdown-text);
    border-color: var(--dropdown-border);
}

#pms-options li {
    color: var(--dropdown-text);
}

#pms-options li:hover {
    background-color: var(--dropdown-hover-bg);
    color: var(--dropdown-hover-text);
}

#permission-selector-options {
    background-color: var(--dropdown-bg);
    color: var(--dropdown-text);
    border-color: var(--dropdown-border);
}

#permission-selector-options li {
    color: var(--dropdown-text);
}

#permission-selector-options li:hover {
    background-color: var(--dropdown-hover-bg);
    color: var(--dropdown-hover-text);
}

#permission-options {
    background-color: var(--dropdown-bg);
    color: var(--dropdown-text);
    border-color: var(--dropdown-border);
}

#permission-options li {
    color: var(--dropdown-text);
}

#permission-options li:hover {
    background-color: var(--dropdown-hover-bg);
    color: var(--dropdown-hover-text);
}

.dropdown-options {
    background-color: var(--dropdown-bg);
    color: var(--dropdown-text);
    border-color: var(--dropdown-border);
}

.dropdown-option {
    color: var(--dropdown-text);
}

.dropdown-option:hover {
    background-color: var(--dropdown-hover-bg);
    color: var(--dropdown-hover-text);
}

.dropdown-options-2 {
    background-color: var(--dropdown-bg);
    color: var(--dropdown-text);
    border-color: var(--dropdown-border);
}

.dropdown-option-2 {
    color: var(--dropdown-text);
}

.dropdown-option-2:hover {
    background-color: var(--dropdown-hover-bg);
    color: var(--dropdown-hover-bg);
}

.dropdown-option-3 {
    color: var(--theme-selector-color);
}

.overlay-card {
    background-color: var(--theme-selector-bg);
    color: var(--theme-selector-color);
}

.uk-dropdown-nav>li>a:hover,
.uk-dropdown-nav>li.uk-active>a {
    background-color: var(--dropdown-hover-bg);
    color: var(--theme-selector-color);
}

/* Update Dropdown Menu Styles */
#dropdownDefaultRadio {
    background-color: var(--dropdown-bg, #ffffff);
    border-color: var(--dropdown-border, #dee2e6);
    color: var(--dropdown-text, #212529);
}

#dropdownDefaultRadio ul {
    background-color: var(--dropdown-bg, #ffffff);
    color: var(--dropdown-text, #212529);
}

#dropdownDefaultRadio input[type="radio"] {
    background-color: var(--radio-bg, #f9fafb);
    border-color: var(--radio-border, #d1d5db);
    color: var(--radio-checked, #3b82f6);
}

#dropdownDefaultRadio label {
    color: var(--label-color, #212529);
}

.dark #dropdownDefaultRadio {
    background-color: var(--dropdown-bg-dark, #1f2937);
    border-color: var(--dropdown-border-dark, #374151);
    color: var(--dropdown-text-dark, #f9fafb);
}

.dark #dropdownDefaultRadio ul {
    background-color: var(--dropdown-bg-dark, #1f2937);
    color: var(--dropdown-text-dark, #f9fafb);
}

.dark #dropdownDefaultRadio input[type="radio"] {
    background-color: var(--radio-bg-dark, #374151);
    border-color: var(--radio-border-dark, #4b5563);
    color: var(--radio-checked-dark, #3b82f6);
}

.dark #dropdownDefaultRadio label {
    color: var(--label-color-dark, #f9fafb);
}

.pure-black .search-bar {
    /* ...existing code... */
    background-color: transparent;
    border: 1px solid #dee2e6;
    /* ...existing code... */
}

.pure-black .checkboxwomp[data-state="checked"] {
    background-color: #09090b;
}

.pure-black .checkbox {
    border-color: var(--checkbox-border);
}

/* Popup Theme Variables */
.light {
    /* ...existing variables... */
    --popup-bg: #ffffff;
    --popup-text: #000000;
    --popup-border: #e5e7eb;
    --popup-shadow: rgba(0, 0, 0, 0.12);
    --popup-overlay: rgba(0, 0, 0, 0.2);
    --success-bg: rgba(255, 255, 255, 0.95);
    --popup-divider: #e5e7eb;
}

.pure-black {
    /* ...existing variables... */
    --popup-bg: #09090b;
    --popup-text: #ffffff;
    --popup-border: #27272a;
    --popup-shadow: rgba(0, 0, 0, 0.5);
    --popup-overlay: rgba(0, 0, 0, 0.5);
    --success-bg: rgba(9, 9, 11, 0.95);
    --popup-divider: #27272a;
}

/* Menubar Styles */
.menubar {
    background-color: var(--menubar-bg);
    padding: 2px;
    display: inline-flex;
    align-items: center;
    border-radius: 6px;
    border: 1px solid var(--menubar-border);
    height: 34px;
    box-sizing: border-box;
    position: relative;
    font-family: 'Onest', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.menubar-indicator {
    position: absolute;
    top: 2px;
    left: 0;
    height: calc(100% - 4px);
    background: var(--menubar-indicator-bg);
    border-radius: 4px;
    transition: left 0.25s cubic-bezier(.4,1,.7,1), width 0.25s cubic-bezier(.4,1,.7,1);
    z-index: 1;
    pointer-events: none;
}

.menubar a {
    background-color: transparent;
    color: var(--menubar-link-color);
    border: none;
    padding: 0 12px;
    margin: 0;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    border-radius: 4px;
    transition: color 0.2s ease;
    font-family: inherit;
    position: relative;
    z-index: 2;
    text-decoration: none;
    white-space: nowrap;
    display: flex;
    align-items: center;
    height: 100%;
    box-sizing: border-box;
}

.menubar a:hover,
.menubar a:focus {
    color: var(--theme-selector-color);
    background-color: var(--menubar-indicator-bg);
}

.menubar a.active {
    color: var(--theme-selector-color);
}

.menubar a:active {
    opacity: 0.85;
}

/* Update popup styles to use theme variables */
.popup-overlay {
    background: var(--popup-overlay);
    backdrop-filter: blur(4px);
}

.popup {
    background: var(--popup-bg);
    color: var(--popup-text);
    border: 1px solid var(--popup-border);
    box-shadow: 0 4px 24px var(--popup-shadow);
}

.popup h2 {
    color: var(--popup-text);
}

.popup-divider {
    background: var(--popup-divider);
}

.popup-content strong {
    color: var(--popup-text);
}

.popup-content p {
    color: var(--popup-text);
}

.success-message {
    background: var(--success-bg);
    color: var(--popup-text);
    border: 1px solid var(--popup-border);
}

.estimated-time {
    color: var(--popup-text);
    opacity: 0.8;
}

/* Dark mode button styles */
.pure-black .intro-prompt-button {
    background-color: transparent;
    border: 1px solid #27272a;
    color: #ffffff;
}

.pure-black .intro-prompt-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}


/* Light theme button styles */
.light .intro-prompt-button {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    color: #374151;
}

/* PMS Analytics button styles */
.uk-button.border.card {
    background-color: transparent !important;
    transition: all 0.3s ease;
}

/* Light theme */
.light .uk-button.border.card {
    border: 1px solid var(--border-color);
    color: var(--theme-selector-color);
}

/* Class for adding hovers to the buttons */
.light .uk-button.border.card:hover {
    background-color: #f4f4f5 !important;
}

/* Dark theme */
.pure-black .uk-button.border.card {
    border: 1px solid var(--border-color);
    background-color: transparent !important;
    color: var(--theme-selector-color);
}

/* Class for adding hovers to the buttons */
.pure-black .uk-button.border.card:hover {
    background-color: #1d1c1c !important;
}

.uk-button.border.card:hover {
    background-color: var(--theme-selector-hover-bg) !important;
}

/* Hide scrollbar for Top Tasks container */
#task-list::-webkit-scrollbar {
    display: none;
}

#task-list {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}

/* WhatsApp icon theme switching */
.whatsapp-theme-icon {
    content: url('../icons/whatsapp2.png');
}

.pure-black .whatsapp-theme-icon,
.dark-gray .whatsapp-theme-icon {
    content: url('../icons/whatsapp2.png');
}

/* WhatsApp icon class used in Recent Users section */
.whatsapp-icon {
    transition: content 0.3s ease;
}

.pure-black .whatsapp-icon {
    content: url('../icons/whatsapp2.png');
}

/* Label styles for different themes */
.light .uk-label.uk-label-primary {
    background: #18181b;
    color: #f3f4f6;
}

.pure-black .uk-label.uk-label-primary {
    background: #f3f4f6;
    color: #18181b;
}

/* Base label styles */
.uk-label.uk-label-primary {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    text-transform: none;
}

/* Popup confirm button styles for different themes */
.pure-black .uk-button.border.card {
    border-color: #27272a !important;
}

/* Icons visibility fix for all themes */
.uk-icon svg {
    color: var(--theme-selector-color);
}

/* Specific color adjustments for dark themes */
.pure-black .uk-icon svg {
    color: #ffffff;
}

/* Light theme specific */
.light .uk-icon svg {
    color: #000000;
}

.dark .uk-icon svg {
    color: #ffffff;
}

/* Hover effect for icons */
.uk-icon:hover svg {
    opacity: 6;
}

/* ...existing code... */

/* Updated icon visibility styles for dark themes */
.pure-black .uk-icon svg path {
    fill: #ffffff !important;
    stroke: #ffffff !important;
}

/* Light theme specific */
.light .uk-icon svg path {
    fill: #000000 !important;
    stroke: #000000 !important;
}

/* Ensure icons in dropdowns and buttons are also visible */
.pure-black .uk-dropdown .uk-icon svg path,
.pure-black .uk-button .uk-icon svg path {
    fill: #ffffff !important;
    stroke: #ffffff !important;
}

/* Update dropdown hover styles */
.uk-dropdown-nav.uk-nav li:hover {
    background-color: #f4f4f5 !important;
    color: var(--dropdown-hover-text, #212529) !important;
}

.uk-drop.uk-dropdown .uk-dropdown-nav li:hover {
    background-color: #f4f4f5 !important;
}

/* Ensure this hover color is consistent across all themes */
.pure-black .uk-dropdown-nav.uk-nav li:hover {
    background-color: #f4f4f5 !important;
}

/* Light theme dropdown hover */
.light .uk-dropdown-nav.uk-nav li:hover {
    background-color: #f4f4f5 !important;
    color: var(--dropdown-hover-text, #212529) !important;
}

/* Dark themes dropdown hover */
.pure-black .uk-dropdown-nav.uk-nav li:hover {
    background-color: #27272a !important;
    color: #ffffff !important;
}

/* Ensure this applies to all dropdown types in dark themes */
.pure-black .uk-drop.uk-dropdown .uk-dropdown-nav li:hover {
    background-color: #27272a !important;
}

/* Global override for hover effects on buttons, icons, and dropdown buttons */
/* button:hover, */
/* .uk-button:hover, */
.uk-icon:hover,
.language-selector-btn:hover,
.theme-selector-btn:hover,
.options-selector-btn:hover,
.uk-tab-alt li a:hover,
.uk-dropdown-nav.uk-nav li:hover,
.intro-prompt-button:hover {
    background-color: #f4f4f5 !important;
}

/* Add new class for custom hover colors */
.custom-hover:hover {
    background-color: #f4f4f5;
}

.dark .custom-hover:hover,
.pure-black .custom-hover:hover {
    background-color: #27272a;
}

.pure-black .dark-button {
    background-color: #ffffff;
    color: #ffffff;
}

/* ...existing code... */
.universal-hover:hover {
    background-color: #f4f4f5 !important;
    transition: all 0.4s ease;
}


.pure-black .universal-hover:hover {
    background-color: #27272a !important;
}

.light .universal-hover:hover {
    background-color: #f4f4f5 !important;
}

.light .uk-navbar-container:not(.uk-navbar-transparent) {
    background-color: var(--navbar-background-color);
}

.pure-black .uk-navbar-container:not(.uk-navbar-transparent) {
    background-color: var(--navbar-background-color);
    ;
}

.light .uk-navbar-container .uk-navbar-nav a {
    color: black;
    /* Replace with desired color */
}

.light .uk-navbar-container .uk-navbar-nav a:hover {
    /* Replace with desired hover color */
    opacity: 50;
}

.pure-black .uk-navbar-container .uk-navbar-nav a {
    color: white;
    /* Replace with desired color */
}

.pure-black .uk-navbar-container .uk-navbar-nav a:hover {
    /* Replace with desired hover color */
    opacity: 10;
}

/* ...existing code... */
html {
    scroll-behavior: smooth;
}

.light {
    --chart-container-bg: #ffffff;
    /* or your preferred light color */
}

.pure-black {
    --chart-container-bg: #121212;
    /* or your preferred dark color */
}

/* ...existing code... */

/* Override UIkit notification background with theme variable */
.uk-notification-message {
    background-color: var(--chart-container-bg) !important;
    color: var(--theme-selector-color) !important;

}

.uk-notification-message .uk-notification-close {
    background-color: transparent !important;
    color: inherit !important;
}


/* Interactive elements and hover effects */
.ggchart-stat-box:hover {
    background-color: var(--theme-selector-hover-bg);
}

.ggchart-stat-box.active {
    background-color: var(--theme-selector-bg);
}


/* D3.js specific styling */
.axis path,
.axis line {
    stroke: var(--border-color);
    stroke-width: 1.5px;
}

.axis text {
    fill: #666;
    font-size: 12px;
    font-weight: 500;
}

#chartjs-tooltip {
    background: var(--dropdown-bg, #f9fafb);
    border: 1px solid var(--dropdown-border, #dee2e7eb);
    border-radius: 0.25rem;
    padding: 0.125rem 0.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    font-size: 0.75rem;
    color: var(--dropdown-text, #111827);
    text-align: left;
    opacity: 0;
    transition: opacity 0.3s ease, left 0.3s ease, top 0.3s ease, transform 0.3s ease;
    pointer-events: none;
    z-index: 1000;
    transform: translate(-50%, 0);
    will-change: opacity, left, top, transform;
}

#chartjs-tooltip table {
    margin: 0;
}

.custom-hover-effect {
    transition: all 0.2s ease;
}

.custom-hover-effect:hover {
    background-color: var(--theme-option-hover-bg);
    /* Light hover background color */
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.user-item:hover {
    background-color: var(--theme-option-hover-bg);
}

.chat-item:hover {
    background-color: var(--theme-option-hover-bg);
}

.main-custom-hover-effect:hover {
    background-color: var(--theme-option-hover-bg);
}

.product-add-popup {
    background-color: var(--popup-bg);
    color: var(--theme-selector-color);
    border: 1px solid var(--theme-selector-border);
}

.table-container thead th {
    position: sticky;
    top: 0;
    background-color: var(--navbar-background-color);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    z-index: 20;
}

.pure-black .border-below-color {
    border-color: #055cff;
}

.light .with-overflow-card {
    border-color: #e5e7eb;
    overflow: hidden !important;
}

.pure-black .with-overflow-card {
    border-color: #27272a;
    overflow: hidden !important;
}

.with-overflow-card {
    overflow: hidden;
    border-radius: 8px;
}

.table-container thead tr.with-overflow-card {
    background-color: var(--card);
}

.universal-background-color {
    background-color: var(--navbar-background-color);
}

/* Make the header sticky - add this to all pages */
.sticky-page-header {
    position: sticky;
    top: 0;
    z-index: 5;
    /* Ensure it appears above content when scrolling */
    background-color: var(--navbar-background-color);
    /* Match the background color */
}


.dropdown-content {
    transform-origin: top right;
    animation-duration: 200ms;
    /* Slightly faster for a snappier feel */
    animation-fill-mode: forwards;
    animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
    will-change: transform, opacity;
}

.dropdown-content[data-state="open"] {
    animation-name: dropdownFadeIn;
}

.dropdown-content:not([data-state="open"]) {
    animation-name: dropdownFadeOut;
}

@keyframes dropdownFadeIn {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(-10px);
    }

    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes dropdownFadeOut {
    0% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }

    100% {
        opacity: 0;
        transform: scale(0.8) translateY(-10px);
    }
}

/* From Uiverse.io by vishnupprajapat */
.checkbox-wrapper-46 input[type="checkbox"],
.checkbox-wrapper-46 input[type="radio"] {
    display: none;
    visibility: hidden;
}

.checkbox-wrapper-46 .cbx {
    margin: auto;
    -webkit-user-select: none;
    user-select: none;
    cursor: pointer;
}

.checkbox-wrapper-46 .cbx span {
    display: inline-block;
    vertical-align: middle;
    transform: translate3d(0, 0, 0);
}

.checkbox-wrapper-46 .cbx span:first-child {
    position: relative;
    width: 18px;
    height: 18px;
    border-radius: 3px;
    transform: scale(1);
    vertical-align: middle;
    border: 1px solid #9098a9;
    transition: all 0.2s ease;
}

.checkbox-wrapper-46 .cbx span:first-child svg {
    position: absolute;
    top: 3px;
    left: 2px;
    fill: none;
    stroke: #ffffff;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-dasharray: 16px;
    stroke-dashoffset: 16px;
    transition: all 0.3s ease;
    transition-delay: 0.1s;
    transform: translate3d(0, 0, 0);
}

.checkbox-wrapper-46 .cbx span:first-child:before {
    content: "";
    width: 100%;
    height: 100%;
    background: #506eec;
    display: block;
    transform: scale(0);
    opacity: 1;
    border-radius: 50%;
}

.checkbox-wrapper-46 .cbx span:last-child {
    padding-left: 8px;
}

.checkbox-wrapper-46 .cbx:hover span:first-child {
    border-color: #506eec;
}

.checkbox-wrapper-46 .inp-cbx:checked+.cbx span:first-child {
    background: #506eec;
    border-color: #506eec;
    animation: wave-46 0.4s ease;
}

.checkbox-wrapper-46 .inp-cbx:checked+.cbx span:first-child svg {
    stroke-dashoffset: 0;
}

.checkbox-wrapper-46 .inp-cbx:checked+.cbx span:first-child:before {
    transform: scale(3.5);
    opacity: 0;
    transition: all 0.6s ease;
}

@keyframes wave-46 {
    50% {
        transform: scale(0.9);
    }
}

/* From Uiverse.io by lenin55 */
.cl-toggle-switch {
    position: relative;
}

.cl-switch {
    position: relative;
    display: inline-block;
}

/* Input */
.cl-switch>input {
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    z-index: -1;
    position: absolute;
    right: 6px;
    top: -8px;
    display: block;
    margin: 0;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    background-color: rgb(0, 0, 0, 0.38);
    outline: none;
    opacity: 0;
    transform: scale(1);
    pointer-events: none;
    transition: opacity 0.3s 0.1s, transform 0.2s 0.1s;
}

/* Track */
.cl-switch>span::before {
    content: "";
    float: right;
    display: inline-block;
    margin: 5px 0 5px 10px;
    border-radius: 7px;
    width: 36px;
    height: 14px;
    background-color: rgb(0, 0, 0, 0.38);
    vertical-align: top;
    transition: background-color 0.2s, opacity 0.2s;
}

/* Thumb */
.cl-switch>span::after {
    content: "";
    position: absolute;
    top: 2px;
    right: 16px;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    background-color: #fff;
    box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
    transition: background-color 0.2s, transform 0.2s;
}

/* Checked */
.cl-switch>input:checked {
    right: -10px;
    background-color: #85b8b7;
}

.cl-switch>input:checked+span::before {
    background-color: #85b8b7;
}

.cl-switch>input:checked+span::after {
    background-color: #018786;
    transform: translateX(16px);
}

/* Hover, Focus */
.cl-switch:hover>input {
    opacity: 0.04;
}

.cl-switch>input:focus {
    opacity: 0.12;
}

.cl-switch:hover>input:focus {
    opacity: 0.16;
}

/* Active */
.cl-switch>input:active {
    opacity: 1;
    transform: scale(0);
    transition: transform 0s, opacity 0s;
}

.cl-switch>input:active+span::before {
    background-color: #8f8f8f;
}

.cl-switch>input:checked:active+span::before {
    background-color: #85b8b7;
}

/* Disabled */
.cl-switch>input:disabled {
    opacity: 0;
}

.cl-switch>input:disabled+span::before {
    background-color: #ddd;
}

.cl-switch>input:checked:disabled+span::before {
    background-color: #bfdbda;
}

.cl-switch>input:checked:disabled+span::after {
    background-color: #61b5b4;
}

/* ...existing code... */

/* Fix for checkbox animation centering */
.checkbox-wrapper-46 .cbx span:first-child:before {
    content: "";
    width: 100%;
    height: 100%;
    background: var(--accent-color, #506EEC);
    display: block;
    transform: scale(0);
    opacity: 1;
    border-radius: 50%;
    /* Center the animation properly */
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    transition: all 0.3s ease;
}

/* Ensure the parent element has proper positioning for the animation */
.checkbox-wrapper-46 .cbx span:first-child {
    position: relative;
    width: 18px;
    height: 18px;
    border-radius: 3px;
    transform: scale(1);
    vertical-align: middle;
    border: 1px solid #9098A9;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Adjust the animation to expand from center */
@keyframes wave-46 {
    50% {
        transform: scale(0.9);
    }
}

.checkbox-wrapper-46 .inp-cbx:checked+.cbx span:first-child:before {
    transform: scale(3);
    opacity: 0;
    transition: all 0.4s ease;
}

/* Add this at the end of your custom.css file */

/* Fix for checkbox and label alignment */
.checkbox-wrapper-46 .cbx {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-wrapper-46 .cbx span:first-child {
    margin-right: 6px;
}

.checkbox-wrapper-46 .cbx span:first-child svg {
    width: 12px;
    height: 10px;
}

/* Fix for the guest-facing languages section */
.checkbox-wrapper-46 .cbx span {
    display: flex;
    align-items: center;
}

.uk-dropdown-nav>li>a:hover,
.uk-dropdown-nav>li.uk-active>a {
    background-color: var(--dropdown-hover-bg);
    color: var(--theme-selector-color);
}


/* Light Mode Toggle Overrides */
.light .cl-toggle-switch .cl-switch>input {
    background-color: rgba(0, 0, 0, 0.38);
}

.light .cl-toggle-switch .cl-switch>input:checked {
    background-color: #85b8b7;
}

.light .cl-toggle-switch .cl-switch>span::before {
    background-color: rgba(0, 0, 0, 0.38);
}

.light .cl-toggle-switch .cl-switch>input:checked+span::before {
    background-color: #85b8b7;
}

.light .cl-toggle-switch .cl-switch>span::after {
    background-color: #fff;
}

.light .cl-toggle-switch .cl-switch>input:checked+span::after {
    background-color: #018786;
}

/* Pure Black Mode Toggle Overrides */
.pure-black .cl-toggle-switch .cl-switch>input {
    background-color: rgba(255, 255, 255, 0.2);
}

.pure-black .cl-toggle-switch .cl-switch>input:checked {
    background-color: #506eec;
}

.pure-black .cl-toggle-switch .cl-switch>span::before {
    background-color: rgba(255, 255, 255, 0.2);
}

.pure-black .cl-toggle-switch .cl-switch>input:checked+span::before {
    background-color: #506eec;
}

/* Pure Black Mode Toggle Overrides - Revised for brighter circle */
.pure-black .cl-toggle-switch .cl-switch>span::after {
    background-color: #a7a7a7;
    /* Brighter background for the circle */
}

.pure-black .cl-toggle-switch .cl-switch>input:checked+span::after {
    background-color: #66aaff;
    /* Bright variant when toggled on */
}

.pure-black .tab-toggle-switch {
    background-color: #27272a;
    color: #ffffff;
}

.light .tab-toggle-switch {
    background-color: #e6d2d2;
    color: #333333;
}

.light .opp-color-button {
    background-color: #000000;
    color: #ffffff;
}

.pure-black .opp-color-button {
    background-color: #ffffff;
    color: #000000;
}

.activity-line-colors {
    border: 1px solid var(--theme-selector-color);
    background-color: var(--theme-selector-color); /* Changed from transparent to solid fill */
}

.activity-icon {
    color: var(--theme-selector-bg); /* Changed to use background color for contrast */
}

/* Add or modify this style */

.activity-timeline .absolute.left-\[15px\] {
    left: 18px; /* Adjust to match the larger circle */
    background-color: var(--theme-selector-color);
}

.activity-icon {
    color: var(--theme-selector-bg);
    /* Remove the scale transformation as we've increased the SVG size directly */
    display: flex;
    align-items: center;
    justify-content: center;
}


/* Add these styles inside the <style> tag in the export modal */
#export-calendar {
    transform: scale(0.95);
    opacity: 0;
    transition: transform 0.2s ease, opacity 0.2s ease;
    transform-origin: top left;
  }
  
  #export-calendar.visible {
    transform: scale(1);
    opacity: 1;
  }
  
  /* Animation classes for entry and exit */
  .calendar-enter {
    animation: calendarEnter 0.2s ease forwards;
  }
  
  .calendar-exit {
    animation: calendarExit 0.2s ease forwards;
  }
  
  @keyframes calendarEnter {
    from {
      transform: scale(0.95);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  @keyframes calendarExit {
    from {
      transform: scale(1);
      opacity: 1;
    }
    to {
      transform: scale(0.95);
      opacity: 0;
    }
  }

