<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Upload Popup</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Inter', sans-serif;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 20px;
            background-color: #ffffff;
        }

        .trigger-button {
            padding: 10px 20px;
            background-color: #000000;
            color: #ffffff;
            border: 1px solid #e4e4e7;
            cursor: pointer;
            font-size: 16px;
            border-radius: 6px;
        }

        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
        }

        .popup-container {
            background-color: #ffffff;
            border: 1px solid #e4e4e7;
            padding: 20px;
            width: 800px;
            display: flex;
            gap: 20px;
            border-radius: 8px;
            position: relative;
        }

        .image-upload {
            flex: 1;
            border: 1px dashed #e4e4e7;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            border-radius: 6px;
        }

        .image-upload p {
            text-align: center;
            color: grey;
        }

        .details-form {
            flex: 1;
            position: relative;
            padding-left: 20px;
        }

        .details-form::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            border-left: 1px dotted #e4e4e7;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: #000000;
        }

        input[type="text"],
        input[type="number"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #e4e4e7;
            color: #000000;
            background-color: #ffffff;
            border-radius: 6px;
        }

        .tags {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .tag {
            padding: 5px 10px;
            border: 1px solid #e4e4e7;
            cursor: pointer;
            border-radius: 6px;
        }

        .tag.selected {
            background-color: #000000;
            color: #ffffff;
        }

        .save-button {
            width: 100%;
            padding: 10px;
            background-color: #18181b;
            color: #ffffff;
            border: none;
            cursor: pointer;
            margin-top: 20px;
            border-radius: 12px;
        }

        #preview-image {
            max-width: 100%;
            max-height: 200px;
            display: none;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <button class="trigger-button">Open Upload Popup</button>

    <div class="popup-overlay">
        <div class="popup-container">
            <div class="image-upload" onclick="document.getElementById('file-input').click()">
                <input type="file" id="file-input" hidden accept="image/*">
                <p>Click to upload image</p>
                <img id="preview-image" src="" alt="Preview">
            </div>
            <div class="details-form">
                <div class="form-group">
                    <label>Title</label>
                    <input type="text" id="title">
                </div>
                <div class="form-group">
                    <label>Price</label>
                    <input type="number" id="price">
                </div>
                <div class="form-group">
                    <label>Select Tag (Choose 1)</label>
                    <div class="tags">
                        <div class="tag" data-tag="tag1">Tag 1</div>
                        <div class="tag" data-tag="tag2">Tag 2</div>
                        <div class="tag" data-tag="tag3">Tag 3</div>
                        <div class="tag" data-tag="tag4">Tag 4</div>
                    </div>
                </div>
                <button class="save-button">Save</button>
            </div>
        </div>
    </div>

    <script>
        const triggerButton = document.querySelector('.trigger-button');
        const popupOverlay = document.querySelector('.popup-overlay');
        const fileInput = document.getElementById('file-input');
        const previewImage = document.getElementById('preview-image');
        const tags = document.querySelectorAll('.tag');
        const saveButton = document.querySelector('.save-button');

        triggerButton.addEventListener('click', () => {
            popupOverlay.style.display = 'flex';
        });

        popupOverlay.addEventListener('click', (e) => {
            if (e.target === popupOverlay) {
                popupOverlay.style.display = 'none';
            }
        });

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    previewImage.src = e.target.result;
                    previewImage.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });

        tags.forEach(tag => {
            tag.addEventListener('click', () => {
                tags.forEach(t => t.classList.remove('selected'));
                tag.classList.add('selected');
            });
        });

        saveButton.addEventListener('click', () => {
            const title = document.getElementById('title').value;
            const price = document.getElementById('price').value;
            const selectedTag = document.querySelector('.tag.selected');
            
            if (!fileInput.files[0] || !title || !price || !selectedTag) {
                alert('Please fill in all fields and select a tag');
                return;
            }

            // Handle your save logic here
            console.log({
                title,
                price,
                tag: selectedTag.dataset.tag,
                image: fileInput.files[0]
            });
            
            popupOverlay.style.display = 'none';
        });
    </script>
</body>
</html>
