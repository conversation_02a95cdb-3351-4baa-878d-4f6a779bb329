document.addEventListener('DOMContentLoaded', function() {
    const optionsSelector = document.querySelector('.options-selector');
    const optionsOptions = document.querySelectorAll('.options-option');
    const optionsSelectorBtn = optionsSelector.querySelector('.options-selector-btn');
    const optionsSelectorMenu = optionsSelector.querySelector('.options-selector-menu');

    // Toggle menu on button click
    optionsSelectorBtn.addEventListener('click', function(event) {
        event.stopPropagation();
        if (optionsSelectorMenu.style.display === 'block') {
            optionsSelectorMenu.style.display = 'none';
        } else {
            optionsSelectorMenu.style.display = 'block';
        }
    });

    // Close menu when clicking outside
    document.addEventListener('click', function() {
        optionsSelectorMenu.style.display = 'none';
    });

    // Prevent menu from closing when clicking inside it
    optionsSelectorMenu.addEventListener('click', function(event) {
        event.stopPropagation();
    });

    optionsOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Add your logic here for what should happen when an option is selected
            console.log('Option selected:', this.textContent);
            optionsSelectorMenu.style.display = 'none';
        });
    });
});