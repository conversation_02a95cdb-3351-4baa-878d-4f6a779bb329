import os
import json
import re
from time import sleep
from datetime import datetime, time
from flask import Flask, request, jsonify
from twilio.twiml.messaging_response import MessagingResponse
from twilio.rest import Client
from dotenv import load_dotenv
import requests
from prompt import system_prompt  # Import the system prompt
from groq import Groq  # Import Groq for AI responses

# ============================== Imports and Constants ==============================

# Load environment variables from the .env file
load_dotenv()

# Webhook URL for new users and orders
WEBHOOK_URL = 'https://951cnsfw-5678.inc1.devtunnels.ms/webhook-test/8f85be30-72cc-43aa-8225-fe5bf7465bbe'

# Twilio account credentials from the .env file
account_sid = os.getenv('ACCOUNT_SID')
auth_token = os.getenv('AUTH_TOKEN')

# Initialize Twilio Client
client = Client(account_sid, auth_token)

# WhatsApp numbers from the .env file
from_whatsapp_number = os.getenv('FROM_WHATSAPP_NUMBER')

# Groq API key from the .env file
GROQ_API_KEY = os.getenv('GROQ_API_KEY')
groq_client = Groq(api_key=GROQ_API_KEY)  # Initialize Groq client

# Paths to JSON files
CHATS_FILE_PATH = os.path.join('Bots', 'chats.json')
USERS_FILE_PATH = os.path.join('Bots', 'users.json')
USER_DATA_FILE_PATH = os.path.join('Bots', 'userdata.json')
PROCESSED_MESSAGES_FILE_PATH = os.path.join('Bots', 'processed_messages.json')
MESSAGES_FILE_PATH = os.path.join('Bots', 'messages.json')  # New messages.json file

# ============================== Welcome and Other Messages ==============================

REQUEST_ROOM_MESSAGE = (
    "Hello, welcome to Can Marques!\nPlease enter your room number before proceeding."
)
ROOM_NUMBER_SAVED_MESSAGE = "Your room number has been saved. How may I assist you today?"

# Content SIDs for various selectors
STARTER_OPTION_SELECTOR_SID = "HX2d5c9f3921da4d2f92035a2ad73eacf2"

# Review Messages
one_star_message = "We're sorry your experience wasn't up to standard. Your feedback is valuable, and we'll work to improve."
two_star_message = "Thank you for your feedback. We appreciate your honesty and will strive to address the areas that need improvement."
three_star_message = "Thank you for your review! We're glad to have met your expectations and will work to enhance our service."
four_star_message = "Thank you for your positive feedback! We're pleased you had a good experience and will continue to improve."
five_star_message = "Thank you for your excellent review! We're thrilled you had a great experience and hope to serve you again soon!"

# Confirmation Messages
FOOD_ORDER_CONFIRMATION = "Good choice. Your order has been registered.\nPlease make the payment here: https://guestgenius.es/foodorder.\nYour dish will be delivered shortly once you complete the payment."
BEVERAGE_ORDER_CONFIRMATION = "Good choice. Your order has been confirmed. Estimated time is 15-20 minutes."
MASSAGE_ORDER_CONFIRMATION = (
    "Thank you for your booking! Your request has been successfully registered.\n"
    "To proceed with the booking, please make the payment here: https://guestgenius.es/massageorder.\n"
    "Once your payment is confirmed, we will schedule your massage session at the earliest opportunity."
)
SPA_ORDER_CONFIRMATION = (
    "Good choice. Your order has been registered.\n"
    "Please make the payment here: https://guestgenius.es/spaorder.\n"
    "Your spa will be scheduled shortly once you complete the payment."
)
ROOM_BOOKING_CONFIRMATION = (
    "Good choice. Your order has been registered.\n"
    "Please make the payment here: https://guestgenius.es/roombooking.\n"
    "Your room will be reserved shortly once you complete the payment."
)

# Food Allergies Messages
BURRATA_ALLERGY_MESSAGE = os.getenv('Burrata_allergie_message')
PUMPKIN_ALLERGY_MESSAGE = os.getenv('Roasted_Pumpkin_allergie_message')
LOBSTER_ROLL_ALLERGY_MESSAGE = os.getenv('Lobster_Roll_allergie_message')
SIRLOIN_ALLERGY_MESSAGE = os.getenv('Sirloin_allergie_message')
CHICKEN_ALLERGY_MESSAGE = os.getenv('Cornfed_Chicken_allergie_message')
TBONE_ALLERGY_MESSAGE = os.getenv('Tbone_allergie_message')
STEAK_LOBSTER_ALLERGY_MESSAGE = os.getenv('Steak_Lobster_allergie_message')
SALMON_ALLERGY_MESSAGE = os.getenv('Grilled_Salmon_allergie_message')

# Order Confirmation Messages
BURRATA_ORDER_MESSAGE = os.getenv('Burrata_order_message')
PUMPKIN_ORDER_MESSAGE = os.getenv('Roasted_Pumpkin_order_message')
LOBSTER_ROLL_ORDER_MESSAGE = os.getenv('Lobster_Roll_order_message')
SIRLOIN_ORDER_MESSAGE = os.getenv('Sirloin_order_message')
CHICKEN_ORDER_MESSAGE = os.getenv('Cornfed_Chicken_order_message')
TBONE_ORDER_MESSAGE = os.getenv('Tbone_order_message')
STEAK_LOBSTER_ORDER_MESSAGE = os.getenv('Steak_Lobster_order_message')
SALMON_ORDER_MESSAGE = os.getenv('Salmon_order_message')

# ============================== Flask App Setup ==============================

# Create Flask app
app = Flask(__name__)

# ============================== Helper Functions ==============================

def load_json(file_path, default_data):
    """
    Load data from a JSON file. If the file doesn't exist, create it with default data.
    If the file exists but is empty or contains invalid JSON, reset it to default data.
    """
    # Ensure the directory exists
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
        with open(file_path, 'w') as file:
            json.dump(default_data, file, indent=4)
        return default_data
    try:
        with open(file_path, 'r') as file:
            return json.load(file)
    except json.JSONDecodeError:
        print(f"Warning: {file_path} is invalid or corrupted. Resetting to default data.")
        with open(file_path, 'w') as file:
            json.dump(default_data, file, indent=4)
        return default_data

def save_json(file_path, data):
    """
    Save data to a JSON file.
    """
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w') as file:
            json.dump(data, file, indent=4)
    except Exception as e:
        raise Exception(f"Error saving data to {file_path}: {e}")

def load_users():
    """Load users from the JSON file."""
    default_data = {"guests": []}
    return load_json(USERS_FILE_PATH, default_data)

def save_users(data):
    """Save users to the JSON file."""
    save_json(USERS_FILE_PATH, data)

def load_chats():
    """Load chats from the JSON file."""
    default_data = {"users": [], "chats": {}}
    return load_json(CHATS_FILE_PATH, default_data)

def save_chats(data):
    """Save chats to the JSON file."""
    save_json(CHATS_FILE_PATH, data)

def load_user_data():
    """Load user data from the JSON file."""
    default_data = {"users": []}
    return load_json(USER_DATA_FILE_PATH, default_data)

def save_user_data(data):
    """Save user data to the JSON file."""
    save_json(USER_DATA_FILE_PATH, data)

def load_processed_messages():
    """Load processed message SIDs from the JSON file."""
    default_data = {"processed_sids": []}
    return load_json(PROCESSED_MESSAGES_FILE_PATH, default_data)

def save_processed_messages(data):
    """Save processed message SIDs to the JSON file."""
    save_json(PROCESSED_MESSAGES_FILE_PATH, data)

def load_messages():
    """Load messages from the JSON file."""
    default_data = {"messages": []}
    return load_json(MESSAGES_FILE_PATH, default_data)

def save_messages(data):
    """Save messages to the JSON file."""
    save_json(MESSAGES_FILE_PATH, data)

def is_message_processed(sid):
    """Check if a message SID has already been processed."""
    processed_data = load_processed_messages()
    return sid in processed_data.get('processed_sids', [])

def mark_message_processed(sid):
    """Mark a message SID as processed."""
    processed_data = load_processed_messages()
    processed_data['processed_sids'].append(sid)
    save_processed_messages(processed_data)

def find_guest(phone_number, users_data):
    """Find a guest by phone number."""
    for guest in users_data.get('guests', []):
        if guest.get('phone_number') == phone_number:
            return guest
    return None

def is_business_hours():
    """
    Check if the current time is within business hours (e.g., 9 AM to 6 PM).
    Returns:
        formatted_time (str): Current time in 'HH:MM AM/PM' format.
        is_outside_hours (bool): True if current time is outside business hours, else False.
    """
    now = datetime.now()
    formatted_time = now.strftime('%I:%M %p')
    current_time = now.time()
    
    # Define business hours
    start_business = time(9, 0)   # 9:00 AM
    end_business = time(18, 0)    # 6:00 PM

    if current_time < start_business or current_time > end_business:
        return formatted_time, True
    else:
        return formatted_time, False

def is_valid_room_number(room_number):
    """Validate if the room number is a valid integer."""
    return re.fullmatch(r'\d+', room_number) is not None

def starter_option_selector(to_number):
    """Send the starter option selector message."""
    try:
        message = client.messages.create(
            from_=from_whatsapp_number,
            to=to_number,
            content_sid=STARTER_OPTION_SELECTOR_SID
        )
        print(f"Starter option selector message sent! SID: {message.sid}")
    except Exception as e:
        print(f"Error sending starter option selector message: {str(e)}")

def get_ai_response(user_input, retries=3, backoff=5):
    """
    Generate AI response with retry logic for handling rate limits.
    """
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_input}
    ]
    for attempt in range(retries):
        try:
            response = groq_client.chat.completions.create(
                model="llama3-70b-8192",
                messages=messages,
                max_tokens=1024,  # Reduced tokens to limit usage
                temperature=0.7
            )
            assistant_response = response.choices[0].message.content
            return assistant_response
        except Exception as e:
            error_message = str(e).lower()
            if 'rate_limit_exceeded' in error_message or 'rate limit' in error_message:
                print(f"Rate limit exceeded. Attempt {attempt +1}/{retries}. Waiting for {backoff} seconds.")
                sleep(backoff)
            else:
                print(f"Error generating AI response: {e}")
                break
    return "Sorry, I'm currently experiencing high traffic. Please try again later."

def handle_new_user(sender, users_data, profile_name, wa_id):
    """
    Handle a new user by adding them to the users.json, sending welcome messages,
    and sending user details to the specified webhook.
    """
    new_guest = {
        "phone_number": sender,
        "room_number": None,
        "helpline": False  # Initialize helpline status
    }
    users_data['guests'].append(new_guest)
    save_users(users_data)
    try:
        # Send Starter Option Selector
        client.messages.create(
            from_=from_whatsapp_number,
            to=sender,
            content_sid=STARTER_OPTION_SELECTOR_SID
        )
        print(f"Starter option selector message sent to {sender} with SID: {STARTER_OPTION_SELECTOR_SID}")

        # Prepare user details for the webhook
        user_details = {
            "guestgenius": {
                "action": "new_user",
                "details": {
                    "Name": profile_name,
                    "Number": f"+{wa_id}",
                    "Room number": 101,  # Update as needed
                    "Platform": "Whatsapp",
                    "Language": "English",
                    "Time": datetime.now().strftime('%d/%m | %I:%M %p')
                }
            }
        }

        # Send user details to the webhook
        response = requests.post(WEBHOOK_URL, json=user_details)
        if response.status_code == 200:
            print("Successfully sent new user data to webhook.")
        else:
            print(f"Failed to send new user data to webhook. Status Code: {response.status_code}")

    except Exception as e:
        print(f"Error sending welcome and starter messages to {sender}: {str(e)}")

def handle_room_service_option(sender):
    """Handle the room service option selection."""
    try:
        client.messages.create(
            from_=from_whatsapp_number,
            to=sender,
            content_sid="HXb9b169a221a86232e0491f9e190710bc"
        )
        print(f"Room service option selector message sent to {sender} with SID: HXb9b169a221a86232e0491f9e190710bc")
    except Exception as e:
        print(f"Error sending room service option selector message to {sender}: {str(e)}")
    return

def handle_food_allergies(sender, payload):
    """Handle food allergy selections."""
    allergy_messages = {
        "burrata_allergies": BURRATA_ALLERGY_MESSAGE,
        "pumpkin_allergies": PUMPKIN_ALLERGY_MESSAGE,
        "lobsterroll_allergies": LOBSTER_ROLL_ALLERGY_MESSAGE,
        "sirloin_allergies": SIRLOIN_ALLERGY_MESSAGE,
        "chicken_allergies": CHICKEN_ALLERGY_MESSAGE,
        "tbone_allergies": TBONE_ALLERGY_MESSAGE,
        "steaklobster_allergies": STEAK_LOBSTER_ALLERGY_MESSAGE,
        "salmon_allergies": SALMON_ALLERGY_MESSAGE
    }

    message_to_send = allergy_messages.get(payload)
    if message_to_send:
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=message_to_send
            )
            print(f"Food allergy message for {payload} sent to {sender}.")
        except Exception as e:
            print(f"Error sending food allergy message to {sender}: {str(e)}")
    else:
        print(f"No matching allergy message found for payload: {payload}")

def handle_order_confirmation(sender, payload):
    """Handle order confirmations based on payload."""
    order_messages = {
        "order_burrata": BURRATA_ORDER_MESSAGE,
        "order_pumpkin": PUMPKIN_ORDER_MESSAGE,
        "order_lobsterroll": LOBSTER_ROLL_ORDER_MESSAGE,
        "order_sirloin": SIRLOIN_ORDER_MESSAGE,
        "order_chicken": CHICKEN_ORDER_MESSAGE,
        "order_tbone": TBONE_ORDER_MESSAGE,
        "order_steaklobster": STEAK_LOBSTER_ORDER_MESSAGE,
        "order_salmon": SALMON_ORDER_MESSAGE
    }

    # Map dish names to their descriptions
    dish_descriptions = {
        "burrata": "Roasted figs, jamon ibérico, watercress, truffle & toasted almonds",
        "pumpkin": "Roasted pumpkin with coconut soup",
        "lobsterroll": "Buttered brioche, lime mayo, leek, lemon",
        "sirloin": "300g - Tender yet succulent with a strip of juicy crackling",
        "chicken": "Cornfed chicken breast with truffled potato, spring onion, and morels",
        "tbone": "900g - Ideal to share, all the sauces & fries",
        "steaklobster": "Ribeye marbled for flavour, 1/2 Lobster with fries, garlic butter, lemon",
        "salmon": "Miso, honey, lemon, watercress, spinach, salmon roe"
    }

    message_to_send = order_messages.get(payload)
    if message_to_send:
        try:
            # Send the order confirmation message
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=message_to_send
            )
            print(f"Order confirmation for {payload} sent to {sender}.")

            # Send the additional message using the provided Content SID
            additional_content_sid = "HXf61ee1cab7b7b36b7403be0f79f34bcc"
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid=additional_content_sid
            )
            print(f"Additional confirmation message sent to {sender} with SID: {additional_content_sid}.")

            # Get user details
            wa_id = sender.replace("whatsapp:+", "")
            user_data = load_user_data()
            user = next((u for u in user_data.get('users', []) if u.get('Number') == f"+{wa_id}"), None)

            if not user:
                print(f"User data not found for wa_id: {wa_id}. Cannot send webhook.")
                return False

            # Get dish name and its description
            dish_name = payload.replace("order_", "")
            description = dish_descriptions.get(dish_name, "No description available")
            food_order_msg = f"Room '{user.get('Room number')}' ordered {dish_name.replace('_', ' ').title()}."

            # Get current time and business hours status
            formatted_time, is_outside_hours = is_business_hours()

            # Prepare webhook parameters
            params = {
                "action": "food_order",
                "Name": user.get("Name", "Unknown"),
                "Number": f"+{wa_id}",
                "description": description,
                "Room_number": str(user.get("Room number", "Unknown")),
                "food_order_msg": food_order_msg,
                "Platform": user.get("Platform", "Unknown"),
                "Language": user.get("Language", "Unknown"),
                "Time": formatted_time,
                "outside_bizz_hours": is_outside_hours
            }

            # Send webhook as GET request
            print("Sending food order details to webhook...")
            response = requests.get(WEBHOOK_URL, params=params)
            if response.status_code == 200:
                print("Successfully sent food order details to webhook.")
                print("Webhook payload:", params)
                return True
            else:
                print(f"Failed to send food order details to webhook. Status Code: {response.status_code}")
                return False

        except Exception as e:
            print(f"Error handling order confirmation for {payload} and {sender}: {str(e)}")
            return False
    else:
        print(f"No matching order message found for payload: {payload}")
        return False

def handle_beverage_orders(sender, payload):
    """Handle beverage order confirmations."""
    beverages = [
        "order_coronita", "order_mr_ginger", "order_berries_mojito",
        "order_summer_cooler", "order_estrella_galicia", "order_estrella_galicia_0_0",
        "order_peroni", "order_creative_tonic_water", "order_exotic_yuzu_sensation",
        "order_fever_tree_tonic", "order_zero_azucar_tonic"
    ]
    if payload in beverages:
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=BEVERAGE_ORDER_CONFIRMATION
            )
            print(f"Beverage order confirmation for {payload} sent to {sender}.")
        except Exception as e:
            print(f"Error sending beverage order confirmation to {sender}: {str(e)}")

def handle_room_booking(sender):
    """Handle room booking selections."""
    try:
        client.messages.create(
            from_=from_whatsapp_number,
            to=sender,
            body=ROOM_BOOKING_CONFIRMATION
        )
        print(f"Room booking confirmation sent to {sender}.")

        # Send review prompt message
        client.messages.create(
            from_=from_whatsapp_number,
            to=sender,
            content_sid="HXf61ee1cab7b7b36b7403be0f79f34bcc"
        )
        print(f"Review prompt sent to {sender} with SID: HXf61ee1cab7b7b36b7403be0f79f34bcc")

    except Exception as e:
        print(f"Error handling room booking for {sender}: {str(e)}")

def handle_button_payload(sender, payload):
    """
    Handle different button payloads for various services like food, beverages, spa, etc.
    Returns True if a payload was handled, False otherwise.
    """
    try:
        if payload == "order_food_option":
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HX7159fe9e75094a6ac1beb620ab5ac636"
            )
            print(f"Order food option selector message sent to {sender} with SID: HX7159fe9e75094a6ac1beb620ab5ac636")
            return True
        
        # ...existing code...
        elif payload == "order_room_service":
            try:
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    content_sid="HXcf94750f4839b7ab136b3da913ae60f7"
                )
                print(f"Room service order message sent to {sender} with SID: HXcf94750f4839b7ab136b3da913ae60f7")
            except Exception as e:
                print(f"Error sending room service order message to {sender}: {str(e)}")
            return True

        elif payload == "order_beverage_option":
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HX2c677e28821bc1a644f994b406724995"
            )
            print(f"Order beverage option selector message sent to {sender} with SID: HX2c677e28821bc1a644f994b406724995")
            return True
        
        elif payload == "book_spa_option":
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HX291b469365c8cbeac916dcd50261a0ea"
            )
            print(f"Spa date and time selector message sent to {sender} with SID: HX291b469365c8cbeac916dcd50261a0ea")
            return True

        elif payload == "massage_service_option":
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HX1cd65c8409a60ae217d719c486818ce6"
            )
            print(f"Massage service option selector message sent to {sender} with SID: HX1cd65c8409a60ae217d719c486818ce6")
            return True

        elif payload == "spa_booking_option":
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HXef191c1dd51a6557a129e9896551f7a8"
            )
            print(f"Spa image and description sent to {sender} with SID: HXef191c1dd51a6557a129e9896551f7a8")
            return True

        elif payload == "room_booking_option":
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                content_sid="HXc2efe1a6a0752aa05ff1281eedda4fff"
            )
            print(f"Room booking option selector message sent to {sender} with SID: HXc2efe1a6a0752aa05ff1281eedda4fff")
            return True

        elif payload == "chat_with_staff_option":
            try:
                # Activate helpline for the user
                users_data = load_users()
                guest = find_guest(sender, users_data)
                if guest:
                    guest['helpline'] = True
                    save_users(users_data)
                    print(f"Activated LiveChat-Helpline for {sender}.")

                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    body="You have entered LiveChat-Helpline mode. Your messages will be saved and reviewed by our staff."
                )
                print(f"Sent helpline activation message to {sender}.")
            except Exception as e:
                print(f"Error sending dashboard activation message to {sender}: {str(e)}")
            return True

        elif payload == "exit_helpline_option":
            try:
                # Deactivate helpline for the user
                users_data = load_users()
                guest = find_guest(sender, users_data)
                if guest:
                    guest['helpline'] = False
                    save_users(users_data)
                    print(f"Deactivated LiveChat-Helpline for {sender}.")

                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    body="You have exited LiveChat-Helpline mode. You can now interact with the AI as usual."
                )
                print(f"Sent helpline deactivation message to {sender}.")
            except Exception as e:
                print(f"Error deactivating helpline for {sender}: {str(e)}")
            return True

        elif payload in [
            "book_alquimist_90",
            "book_facial_60",
            "book_alquimist_60",
            "book_junior_suite",
            "book_the_raid",
            "book_junior_suite_deluxe",
            "book_executive_suite"
        ]:
            try:
                content_sid = 'HX4ade4714876d858178ab241d5bd6a34e' if payload in [
                    "book_junior_suite",
                    "book_the_raid",
                    "book_junior_suite_deluxe",
                    "book_executive_suite"
                ] else "HXd842a9216b1b3aad23dccfe555ba87d6"
                
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    content_sid=content_sid
                )
                
                if payload in [
                    "book_junior_suite",
                    "book_the_raid",
                    "book_junior_suite_deluxe",
                    "book_executive_suite"
                ]:
                    print(f"Room booking confirmation sent to {sender}.")
                else:
                    print(f"Date and time selector message sent to {sender} with SID: {content_sid}")
            except Exception as e:
                message_type = "room booking confirmation" if payload in [
                    "book_junior_suite",
                    "book_the_raid",
                    "book_junior_suite_deluxe",
                    "book_executive_suite"
                ] else "date and time selector"
                print(f"Error sending {message_type} message to {sender}: {str(e)}")
            return True

        elif payload.startswith("order_"):
            handled = handle_order_confirmation(sender, payload)
            return handled

        else:
            print(f"No handler for button payload: {payload}")
            return False

    except Exception as e:
        print(f"Error handling button payload '{payload}' for {sender}: {str(e)}")
        return False

# ============================== Flask Route ==============================

@app.route("/", methods=['POST'])
def handle_incoming_message():
    # Get the incoming data from the request
    incoming_data = request.get_json() if request.is_json else request.form.to_dict()

    # Extract the SmsMessageSid to track processed messages
    sms_message_sid = incoming_data.get('SmsMessageSid', '')
    if is_message_processed(sms_message_sid):
        print(f"Message SID {sms_message_sid} has already been processed. Skipping.")
        return '', 204
    else:
        mark_message_processed(sms_message_sid)

    # **Add this block to ignore status updates**
    if 'MessageStatus' in incoming_data:
        print("Received status update. Ignoring.")
        return '', 204

    # Log the entire received API request
    print("Received API Request:")
    print(json.dumps(incoming_data, indent=4))

    # Extract relevant fields from the incoming data
    sender = incoming_data.get('From', '')
    message_body = incoming_data.get('Body', '').strip()
    message_type = incoming_data.get('MessageType', '').lower()
    button_payload = incoming_data.get('ButtonPayload', '')
    list_id = incoming_data.get('ListId', '')  # For interactive messages
    list_title = incoming_data.get('ListTitle', '')
    profile_name = incoming_data.get('ProfileName', '')
    wa_id = incoming_data.get('WaId', '')

    # Prevent responding to messages sent from the bot's own number
    if sender == from_whatsapp_number:
        print("Received message from the bot itself. Ignoring to prevent loop.")
        return '', 204  # No Content

    # Load users data
    users_data = load_users()

    # Find the guest by phone number
    guest = find_guest(sender, users_data)

    if not guest:
        # New User: Add to guests and send Welcome Message and starter SID
        handle_new_user(sender, users_data, profile_name, wa_id)  # Passed additional parameters
        return '', 204

    # Load chats data
    chats_data = load_chats()

    # Extract phone number
    phone_number = incoming_data.get('WaId', '').replace('whatsapp:+', '')

    # Ensure user exists in chats.json users
    chat_user = next((u for u in chats_data['users'] if u['phone_number'] == phone_number), None)
    if not chat_user:
        # Add user to chats.json users
        chat_user = {
            "id": len(chats_data['users']) + 1,  # Simple ID assignment
            "name": "Guest",  # Default name
            "status": "active",
            "avatar": "",
            "phone_number": phone_number,
            "room_number": guest.get('room_number'),
            "platform": "whatsapp"
        }
        chats_data['users'].append(chat_user)
        save_chats(chats_data)
    else:
        # Optionally, handle existing chat users
        pass

    # ============================== Interactive Message Handling ==============================

    # Check if the user is in helpline mode
    is_helpline = False

    if is_helpline:
        # Save the message to messages.json
        messages_data = load_messages()
        message_entry = {
            "timestamp": datetime.now().isoformat(),
            "sender": sender,
            "message": message_body
        }
        messages_data['messages'].append(message_entry)
        save_messages(messages_data)
        print(f"Saved message from {sender} to messages.json during helpline period.")

        # Optionally, acknowledge receipt
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body="Your message has been received by our staff. They will get back to you shortly."
            )
        except Exception as e:
            print(f"Error sending acknowledgment to {sender}: {str(e)}")

        return '', 204

    # ============================== Interactive Message Handling ==============================

    if message_type == "interactive":
        print(f"Handling interactive message with ListId: {list_id}")

        # Handle Review Messages First
        if list_id.endswith("_star_review"):
            review_messages = {
                "1_star_review": one_star_message,
                "2_star_review": two_star_message,
                "3_star_review": three_star_message,
                "4_star_review": four_star_message,
                "5_star_review": five_star_message
            }

            message_to_send = review_messages.get(list_id)
            if message_to_send:
                try:
                    client.messages.create(
                        from_=from_whatsapp_number,
                        to=sender,
                        body=message_to_send
                    )
                    print(f"Sent {list_id} response to {sender}")
                    return '', 204
                except Exception as e:
                    print(f"Error sending review response to {sender}: {str(e)}")
                    return '', 204
            else:
                print(f"No matching review message found for ListId: {list_id}")
                return '', 204

        # Handle Date and Time Selections
        if list_id.startswith("date_"):
            selected_date_key = list_id  # e.g., "date_21"

            # Define your spa and massage titles (ensure these keys match your actual titles)
            spa_titles = [
                "21-10-24 (2-3 PM)",
                "21-10-24 (3-4 PM)",
                "22-10-24 (2-3 PM)",
                "22-10-24 (3-4 PM)"
                # Add more as needed
            ]

            massage_titles = [
                "21-10-24 (10-12 AM)",
                "21-10-24 (4-6 PM)",
                "22-10-24 (10-12 AM)",
                "22-10-24 (4-6 PM)"
                # Add more as needed
            ]

            nights_date_mapping = {
                "date_21": "21-10-24",
                "date_22": "22-10-24",
                "date_23": "23-10-24",
                "date_24": "24-10-24",
                "date_25": "25-10-24",
                # Add more mappings as needed
            }

            if selected_date_key in nights_date_mapping:
                # Detect room booking selection
                handle_room_booking(sender)
                return '', 204  # Exit after handling

            elif selected_date_key in massage_titles:
                service_type = "massage"
                confirmation_message = MASSAGE_ORDER_CONFIRMATION
                try:
                    client.messages.create(
                        from_=from_whatsapp_number,
                        to=sender,
                        body=confirmation_message
                    )
                    print(f"Massage order confirmation for {selected_date_key} sent to {sender}.")
                except Exception as e:
                    print(f"Error sending massage order confirmation to {sender}: {str(e)}")

            elif selected_date_key in spa_titles:
                service_type = "spa"
                confirmation_message = SPA_ORDER_CONFIRMATION
                try:
                    client.messages.create(
                        from_=from_whatsapp_number,
                        to=sender,
                        body=confirmation_message
                    )
                    print(f"Spa order confirmation for {selected_date_key} sent to {sender}.")

                    # ============================== Webhook Integration ==============================

                    # Prepare spa data for the webhook as query parameters
                    spa_data = {
                        "action": "spa_order",
                        "Name": profile_name,
                        "Number": f"+{wa_id}",
                        "description": "Palacio Can Marqués Spa session",
                        "Room_number": "101",  # Assuming room number is fixed as 101
                        "spa_order_msg": "Room '101' booked a spa session",
                        "Platform": "Whatsapp",
                        "Language": "English",
                        "Time": datetime.now().strftime('%I:%M %p'),  # Current time
                        "outside_bizz_hours": is_business_hours()[1]  # True if outside business hours
                    }

                    # Log spa_data for debugging
                    print("Prepared spa data for webhook:", spa_data)

                    # Send spa data to the webhook as a GET request
                    print("Sending spa order details to webhook...")
                    response = requests.get(WEBHOOK_URL, params=spa_data)
                    if response.status_code == 200:
                        print("Successfully sent spa order details to webhook.")
                    else:
                        print(f"Failed to send spa order details to webhook. Status Code: {response.status_code}")

                except Exception as e:
                    print(f"Error sending spa order confirmation to {sender}: {str(e)}")
            else:
                # Fallback: Determine based on ListTitle
                if list_title in spa_titles:
                    service_type = "spa"
                    confirmation_message = SPA_ORDER_CONFIRMATION
                    try:
                        client.messages.create(
                            from_=from_whatsapp_number,
                            to=sender,
                            body=confirmation_message
                        )
                        print(f"Spa order confirmation for {list_title} sent to {sender}.")

                        # ============================== Webhook Integration ==============================

                        # Prepare spa data for the webhook as query parameters
                        spa_data = {
                            "action": "spa_order",
                            "Name": profile_name,
                            "Number": f"+{wa_id}",
                            "description": "Palacio Can Marqués Spa session",
                            "Room_number": "101",  # Assuming room number is fixed as 101
                            "spa_order_msg": "Room '101' booked a spa session",
                            "Platform": "Whatsapp",
                            "Language": "English",
                            "Time": datetime.now().strftime('%I:%M %p'),  # Current time
                            "outside_bizz_hours": is_business_hours()[1]  # True if outside business hours
                        }

                        # Log spa_data for debugging
                        print("Prepared spa data for webhook:", spa_data)

                        # Send spa data to the webhook as a GET request
                        print("Sending spa order details to webhook...")
                        response = requests.get(WEBHOOK_URL, params=spa_data)
                        if response.status_code == 200:
                            print("Successfully sent spa order details to webhook.")
                        else:
                            print(f"Failed to send spa order details to webhook. Status Code: {response.status_code}")

                    except Exception as e:
                        print(f"Error sending spa order confirmation to {sender}: {str(e)}")

                elif list_title in massage_titles:
                    service_type = "massage"
                    confirmation_message = MASSAGE_ORDER_CONFIRMATION
                    try:
                        client.messages.create(
                            from_=from_whatsapp_number,
                            to=sender,
                            body=confirmation_message
                        )
                        print(f"Massage order confirmation for {list_title} sent to {sender}.")
                    except Exception as e:
                        print(f"Error sending massage order confirmation to {sender}: {str(e)}")
                else:
                    print(f"Unable to determine service type for ListId: {list_id} and ListTitle: {list_title}")
                    return '', 204  # Exit after handling

            return '', 204  # Exit after handling

        # Handle Room Booking Selections (1_nights to 6_nights)
        if message_body in ["1_nights", "2_nights", "3_nights", "4_nights", "5_nights", "6_nights"]:
            handle_room_booking(sender)
            return '', 204  # Exit after handling

        # Unhandled Interactive Messages
        print(f"Unhandled ListId: {list_id}")
        return '', 204  # Exit after handling interactive messages

    # ============================== Button Message Handling ==============================

    if message_type == "button":
        print(f"Handling button message with payload: {button_payload}")
        if button_payload in ["book_alquimist_90", "book_facial_60", "book_alquimist_60"]:
            try:
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    content_sid="HXd842a9216b1b3aad23dccfe555ba87d6"
                )
                print(f"Date and time selector message sent to {sender} with SID: HXd842a9216b1b3aad23dccfe555ba87d6")
                return '', 204
            except Exception as e:
                print(f"Error sending date and time selector message to {sender}: {str(e)}")
                return '', 204

        handled = handle_button_payload(sender, button_payload)
        if handled:
            return '', 204  # Exit after handling button messages

    # ============================== Spa Booking Handling ==============================

    # Handle Interactive Messages for Spa Order Confirmation
    if message_type == "interactive" and list_title in spa_titles:
        service_type = "spa"
        confirmation_message = SPA_ORDER_CONFIRMATION
        try:
            client.messages.create(
                from_=from_whatsapp_number,
                to=sender,
                body=confirmation_message
            )
            print(f"Spa order confirmation for {list_title} sent to {sender}.")

            # Prepare spa data for the webhook as query parameters
            spa_data = {
                "action": "spa_order",
                "Name": profile_name,
                "Number": f"+{wa_id}",
                "description": "Palacio Can Marqués Spa session",
                "Room_number": "101",  # Assuming room number is fixed as 101
                "spa_order_msg": "Room '101' booked a spa session",
                "Platform": "Whatsapp",
                "Language": "English",
                "Time": datetime.now().strftime('%I:%M %p'),  # Current time
                "outside_bizz_hours": is_business_hours()[1]  # True if outside business hours
            }

            # Log spa_data for debugging
            print("Prepared spa data for webhook:", spa_data)

            # Send spa data to the webhook as a GET request
            print("Sending spa order details to webhook...")
            response = requests.get(WEBHOOK_URL, params=spa_data)
            if response.status_code == 200:
                print("Successfully sent spa order details to webhook.")
            else:
                print(f"Failed to send spa order details to webhook. Status Code: {response.status_code}")

        except Exception as e:
            print(f"Error sending spa order confirmation to {sender}: {str(e)}")
        return '', 204

    # ============================== AI Chat Handling ==============================

    # Check if the message is "XX" regardless of room_number
    if message_body.upper() == "XX":
        try:
            # Assign room number 101 to the guest
            guest['room_number'] = 101
            save_users(users_data)
            print(f'Assigned room number 101 to {sender}.')

            # Send Starter Option Selector
            starter_option_selector(sender)

            # Load user data
            user_data = load_user_data()

            # Check if user data already exists
            if not any(user['Number'] == '+' + wa_id for user in user_data['users']):
                # Create new user entry
                new_user = {
                    "Name": profile_name,
                    "Number": '+' + wa_id,
                    "Room number": 101,  # Fixed as per requirement
                    "Platform": "Whatsapp",
                    "Language": "English",
                    "Time": datetime.now().strftime('%d/%m | %I:%M %p')  # Format: DD/MM | HH:MM AM/PM
                }
                user_data['users'].append(new_user)
                save_user_data(user_data)
                print(f"Saved new user data for {new_user['Number']}.")
            else:
                print(f"User data for {wa_id} already exists.")
        except Exception as e:
            print(f"Error handling GGTEST for {sender}: {str(e)}")
        return '', 204

    # Proceed only if the user has a room number
    room_number = guest.get('room_number')
    if room_number:
        # ============================== Service Handling ==============================

        # Handle Interactive Messages for Room Service Option
        if message_type == "interactive" and message_body == "room_service_option":
            handle_room_service_option(sender)
            return '', 204

        # Handle Chat with Staff Option
        if list_id == "chat_with_staff_option" and message_body == "chat_with_staff_option":
            try:
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    body="Please tell us your request."
                )
                print(f"Prompted {sender} to tell their request.")

                # Initialize chat history for the user if not present
                user_id = str(chat_user.get('id', ''))
                if user_id not in chats_data['chats']:
                    chats_data['chats'][user_id] = []
                save_chats(chats_data)
            except Exception as e:
                print(f"Error handling chat with staff option for {sender}: {str(e)}")
            return '', 204

        # Respond with AI-generated answer
        assistant_response = get_ai_response(message_body)
        if assistant_response:
            try:
                # Send the AI-generated response to the user
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    body=assistant_response
                )
                print(f'Sent AI response to {sender}.')
            except Exception as e:
                print(f"Error sending AI response to {sender}: {str(e)}")
        return '', 204

    else:
        # User Does Not Have a Room Number
        if is_valid_room_number(message_body):
            # User is sending their room number
            guest['room_number'] = int(message_body)
            save_users(users_data)
            try:
                # Send ROOM_NUMBER_SAVED_MESSAGE
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    body=ROOM_NUMBER_SAVED_MESSAGE
                )
                print(f'Saved room number "{message_body}" for {sender} and sent confirmation.')

                # Send the starter option selector after saving room number
                starter_option_selector(sender)
            except Exception as e:
                print(f"Error sending messages to {sender}: {str(e)}")
            return '', 204
        else:
            # Prompt user to enter their room number
            try:
                client.messages.create(
                    from_=from_whatsapp_number,
                    to=sender,
                    body=REQUEST_ROOM_MESSAGE
                )
                print(f'Prompted {sender} to enter room number.')
            except Exception as e:
                print(f"Error sending room number request to {sender}: {str(e)}")
            return '', 204

# ============================== Run the App ==============================

if __name__ == '__main__':
    app.run(debug=False, port=5000)