<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bar Chart - Interactive</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #ffffff;
            margin: 0;
            padding: 20px;
        }
        .chart-container {
            border: 1px solid #e4e4e7;
            border-radius: 8px;
            padding: 15px;
            max-width: 1300px;
            margin: auto;
            background: #ffffff;
        }
        #chartdiv {
            width: 100%;
            height: 300px;
            margin-top: 15px;
        }
        .chart-header {
            border-bottom: 1px solid #e4e4e7;
            padding-bottom: 15px;
        }
        .chart-header h3 {
            margin: 0 0 5px 0;
        }
        .chart-header p {
            margin: 0;
            color: #71717a;
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <div class="chart-header">
            <h3>Bar Chart - Interactive</h3>
            <p>Showing total visitors for the last 3 months</p>
        </div>
        <div id="chartdiv"></div>
    </div>

    <script>
        am5.ready(function() {
            var root = am5.Root.new("chartdiv");
            root.setThemes([am5themes_Animated.new(root)]);

            var chart = root.container.children.push(
                am5xy.XYChart.new(root, {
                    panX: false,
                    panY: false,
                    wheelX: "none",
                    wheelY: "none",
                    layout: root.verticalLayout,
                    paddingLeft: 38,
                    paddingRight: 38
                })
            );

            var xRenderer = am5xy.AxisRendererX.new(root, { 
                minGridDistance: 15,
                cellStartLocation: 0,
                cellEndLocation: 1,
                minorGridEnabled: false
            });
            xRenderer.grid.template.setAll({ 
                strokeOpacity: 0,
                location: 0.5
            });
            
            var xAxis = chart.xAxes.push(
                am5xy.CategoryAxis.new(root, {
                    renderer: xRenderer,
                    categoryField: "date",
                    start: 0,
                    end: 1,
                    paddingLeft: 0,
                    paddingRight: 0
                })
            );

            // Add this label adapter to show only every 5th label
            xAxis.get("renderer").labels.template.setAll({
                oversizedBehavior: "truncate",
                maxWidth: 50,  // Reduce max width to prevent overlap
                fontSize: 10   // Reduce font size for better fit
            });
            xAxis.get("renderer").labels.template.adapters.add("text", function(text, target) {
                var index = xAxis.dataItems.indexOf(target.dataItem);
                return (index % 5 === 0) ? text : "";
            });

            var yAxis = chart.yAxes.push(
                am5xy.ValueAxis.new(root, {
                    renderer: am5xy.AxisRendererY.new(root, {
                        opposite: true,  // Move to opposite side
                        visible: false   // Hide the axis
                    }),
                    width: 0  // Set axis width to 0
                })
            );

            // Hide y-axis labels and line
            yAxis.get("renderer").labels.template.setAll({
                opacity: 0
            });
            yAxis.get("renderer").grid.template.setAll({
                opacity: 0
            });

            var series = chart.series.push(
                am5xy.ColumnSeries.new(root, {
                    name: "Visitors",
                    xAxis: xAxis,
                    yAxis: yAxis,
                    valueYField: "visitors",
                    categoryXField: "date"
                })
            );
            series.columns.template.setAll({ fill: am5.color("#2a9d90"), strokeOpacity: 0 });

            var data = [];
            for (var i = 1; i <= 85; i++) {  // Changed from 30 to 85
                data.push({ date: i.toString(), visitors: Math.floor(Math.random() * 200) + 50 });
            }

            xAxis.data.setAll(data);
            series.data.setAll(data);
            series.appear(1000);
            chart.appear(1000, 100);
        });
    </script>
</body>
</html>
