<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Dashboard</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Inter Font (used by shadcn/ui) -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Remix Icon CDN -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- ApexCharts CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/apexcharts@latest/dist/apexcharts.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50 p-6">
    <div class="container mx-auto">
        <!-- Top KPI Containers -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <!-- Container 1 -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <p class="text-sm text-gray-500">Total Users</p>
                <p class="text-2xl font-semibold text-gray-900">1,234</p>
                <p class="text-sm text-gray-500 mt-2">+12.5% from last month</p>
            </div>
            <!-- Container 2 -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <p class="text-sm text-gray-500">Page Views</p>
                <p class="text-2xl font-semibold text-gray-900">45,678</p>
                <p class="text-sm text-gray-500 mt-2">+8.3% from last month</p>
            </div>
            <!-- Container 3 -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <p class="text-sm text-gray-500">Bounce Rate</p>
                <p class="text-2xl font-semibold text-gray-900">32.1%</p>
                <p class="text-sm text-gray-500 mt-2">-2.4% from last month</p>
            </div>
            <!-- Container 4 -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <p class="text-sm text-gray-500">Session Duration</p>
                <p class="text-2xl font-semibold text-gray-900">4m 32s</p>
                <p class="text-sm text-gray-500 mt-2">+1.2% from last month</p>
            </div>
        </div>

        <!-- KPI Card Section -->
        <div class="sm:mx-auto sm:max-w-2xl mb-8">
            <!-- Header Section -->
            <div class="mb-4">
                <h3 class="text-sm text-gray-500">Total sales</h3>
                <p class="text-3xl font-semibold text-gray-900">$292,400</p>
            </div>

            <!-- Distribution Section -->
            <div class="mt-6">
                <h4 class="text-sm text-gray-500">Sales channel distribution</h4>
                <!-- Progress Bar -->
                <div class="mt-4 flex h-2 w-full rounded-full bg-gray-200 overflow-hidden">
                    <div class="w-[34.4%] bg-blue-500"></div>
                    <div class="w-[30.6%] bg-orange-500"></div>
                    <div class="w-[20.9%] bg-sky-500"></div>
                    <div class="w-[14.1%] bg-purple-500"></div>
                </div>
            </div>

            <!-- Channel Cards Grid -->
            <dl class="mt-8 grid grid-cols-1 gap-4 sm:grid-cols-2">
                <!-- Direct Sales Card -->
                <div class="group relative bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex items-center space-x-2">
                        <span class="w-2.5 h-2.5 rounded-sm bg-blue-500"></span>
                        <dt class="text-sm text-gray-900">
                            <a href="#" class="focus:outline-none">
                                <span class="absolute inset-0" aria-hidden="true"></span>
                                Direct sales
                            </a>
                        </dt>
                    </div>
                    <dd class="mt-1 text-sm text-gray-700">
                        <span class="font-semibold">34.4%</span> · $100.5K
                    </dd>
                    <i class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-gray-600 transition-colors"></i>
                </div>

                <!-- Retail Stores Card -->
                <div class="group relative bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex items-center space-x-2">
                        <span class="w-2.5 h-2.5 rounded-sm bg-orange-500"></span>
                        <dt class="text-sm text-gray-900">
                            <a href="#" class="focus:outline-none">
                                <span class="absolute inset-0" aria-hidden="true"></span>
                                Retail stores
                            </a>
                        </dt>
                    </div>
                    <dd class="mt-1 text-sm text-gray-700">
                        <span class="font-semibold">30.6%</span> · $89.5K
                    </dd>
                    <i class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-gray-600 transition-colors"></i>
                </div>

                <!-- E-commerce Card -->
                <div class="group relative bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex items-center space-x-2">
                        <span class="w-2.5 h-2.5 rounded-sm bg-sky-500"></span>
                        <dt class="text-sm text-gray-900">
                            <a href="#" class="focus:outline-none">
                                <span class="absolute inset-0" aria-hidden="true"></span>
                                E-commerce
                            </a>
                        </dt>
                    </div>
                    <dd class="mt-1 text-sm text-gray-700">
                        <span class="font-semibold">20.9%</span> · $61.2K
                    </dd>
                    <i class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-gray-600 transition-colors"></i>
                </div>

                <!-- Wholesale Card -->
                <div class="group relative bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex items-center space-x-2">
                        <span class="w-2.5 h-2.5 rounded-sm bg-purple-500"></span>
                        <dt class="text-sm text-gray-900">
                            <a href="#" class="focus:outline-none">
                                <span class="absolute inset-0" aria-hidden="true"></span>
                                Wholesale
                            </a>
                        </dt>
                    </div>
                    <dd class="mt-1 text-sm text-gray-700">
                        <span class="font-semibold">14.1%</span> · $41.2K
                    </dd>
                    <i class="ri-arrow-right-up-line absolute right-2 top-2 text-gray-400 group-hover:text-gray-600 transition-colors"></i>
                </div>
            </dl>
        </div>

        <!-- Legend Indicator -->
        <div class="flex justify-center sm:justify-end items-center gap-x-4 mb-3 sm:mb-6">
            <div class="inline-flex items-center">
                <span class="size-2.5 inline-block bg-blue-600 rounded-sm me-2"></span>
                <span class="text-[13px] text-gray-600">Income</span>
            </div>
            <div class="inline-flex items-center">
                <span class="size-2.5 inline-block bg-purple-600 rounded-sm me-2"></span>
                <span class="text-[13px] text-gray-600">Outcome</span>
            </div>
        </div>

        <!-- Chart Container -->
        <div id="hs-curved-area-charts" class="bg-white p-6 rounded-lg shadow-sm"></div>
    </div>

    

    <!-- ApexCharts and Lodash Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@latest/dist/apexcharts.min.js"></script>
    <!-- Preline ApexCharts Helpers -->
    <script src="https://preline.co/assets/js/hs-apexcharts-helpers.js"></script>
    <script>
        window.addEventListener('load', () => {
            // Apex Line Chart
            (function () {
                buildChart('#hs-curved-area-charts', (mode) => ({
                    chart: {
                        height: 300,
                        type: 'area',
                        toolbar: {
                            show: false
                        },
                        zoom: {
                            enabled: false
                        }
                    },
                    series: [
                        {
                            name: 'Income',
                            data: [18000, 51000, 60000, 38000, 88000, 50000, 40000, 52000, 88000, 80000, 60000, 70000]
                        },
                        {
                            name: 'Outcome',
                            data: [27000, 38000, 60000, 77000, 40000, 50000, 49000, 29000, 42000, 27000, 42000, 50000]
                        }
                    ],
                    legend: {
                        show: false
                    },
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        curve: 'smooth',
                        width: 2
                    },
                    grid: {
                        strokeDashArray: 2
                    },
                    fill: {
                        type: 'gradient',
                        gradient: {
                            type: 'vertical',
                            shadeIntensity: 1,
                            opacityFrom: 0.1,
                            opacityTo: 0.8
                        }
                    },
                    xaxis: {
                        type: 'category',
                        tickPlacement: 'on',
                        categories: [
                            '25 January 2023',
                            '26 January 2023',
                            '27 January 2023',
                            '28 January 2023',
                            '29 January 2023',
                            '30 January 2023',
                            '31 January 2023',
                            '1 February 2023',
                            '2 February 2023',
                            '3 February 2023',
                            '4 February 2023',
                            '5 February 2023'
                        ],
                        axisBorder: {
                            show: false
                        },
                        axisTicks: {
                            show: false
                        },
                        crosshairs: {
                            stroke: {
                                dashArray: 0
                            },
                            dropShadow: {
                                show: false
                            }
                        },
                        tooltip: {
                            enabled: false
                        },
                        labels: {
                            style: {
                                colors: '#9ca3af',
                                fontSize: '13px',
                                fontFamily: 'Inter, ui-sans-serif',
                                fontWeight: 400
                            },
                            formatter: (title) => {
                                let t = title;

                                if (t) {
                                    const newT = t.split(' ');
                                    t = `${newT[0]} ${newT[1].slice(0, 3)}`;
                                }

                                return t;
                            }
                        }
                    },
                    yaxis: {
                        labels: {
                            align: 'left',
                            minWidth: 0,
                            maxWidth: 140,
                            style: {
                                colors: '#9ca3af',
                                fontSize: '13px',
                                fontFamily: 'Inter, ui-sans-serif',
                                fontWeight: 400
                            },
                            formatter: (value) => value >= 1000 ? `${value / 1000}k` : value
                        }
                    },
                    tooltip: {
                        theme: 'light', // Set tooltip theme to light mode
                        x: {
                            format: 'MMMM yyyy'
                        },
                        y: {
                            formatter: (value) => `$${value >= 1000 ? `${value / 1000}k` : value}`
                        },
                        custom: function (props) {
                            const { categories } = props.ctx.opts.xaxis;
                            const { dataPointIndex } = props;
                            const title = categories[dataPointIndex].split(' ');
                            const newTitle = `${title[0]} ${title[1]}`;

                            return buildTooltip(props, {
                                title: newTitle,
                                mode,
                                hasTextLabel: true,
                                wrapperExtClasses: 'min-w-28',
                                labelDivider: ':',
                                labelExtClasses: 'ms-2'
                            });
                        }
                    },
                    responsive: [{
                        breakpoint: 568,
                        options: {
                            chart: {
                                height: 300
                            },
                            labels: {
                                style: {
                                    colors: '#9ca3af',
                                    fontSize: '11px',
                                    fontFamily: 'Inter, ui-sans-serif',
                                    fontWeight: 400
                                },
                                offsetX: -2,
                                formatter: (title) => title.slice(0, 3)
                            },
                            yaxis: {
                                labels: {
                                    align: 'left',
                                    minWidth: 0,
                                    maxWidth: 140,
                                    style: {
                                        colors: '#9ca3af',
                                        fontSize: '11px',
                                        fontFamily: 'Inter, ui-sans-serif',
                                        fontWeight: 400
                                    },
                                    formatter: (value) => value >= 1000 ? `${value / 1000}k` : value
                                }
                            },
                        },
                    }]
                }), {
                    colors: ['#2563eb', '#9333ea'],
                    fill: {
                        gradient: {
                            stops: [0, 90, 100]
                        }
                    },
                    xaxis: {
                        labels: {
                            style: {
                                colors: '#9ca3af'
                            }
                        }
                    },
                    yaxis: {
                        labels: {
                            style: {
                                colors: '#9ca3af'
                            }
                        }
                    },
                    grid: {
                        borderColor: '#e5e7eb'
                    }
                }, {
                    colors: ['#3b82f6', '#a855f7'],
                    fill: {
                        gradient: {
                            stops: [100, 90, 0]
                        }
                    },
                    xaxis: {
                        labels: {
                            style: {
                                colors: '#a3a3a3',
                            }
                        }
                    },
                    yaxis: {
                        labels: {
                            style: {
                                colors: '#a3a3a3'
                            }
                        }
                    },
                    grid: {
                        borderColor: '#404040'
                    }
                });
            })();
        });
    </script>
</body>
</html>