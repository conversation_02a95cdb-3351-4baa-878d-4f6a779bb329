<!-- UNUSED FILE -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
  <title>Issues & Live Chat</title>
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  
  <!-- Custom Styles -->
  <link rel="stylesheet" href="../static/styles/custom.css">
  <link rel="stylesheet" href="../static/styles/loadinganimations.css">
  
  <!-- UIkit Styles -->
  <link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/yellow.min.css"/>
  
  <!-- Preloaded Fonts -->
  <link rel="preload" href="/fonts/geist-font/fonts/GeistVariableVF.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="/fonts/geist-font/fonts/GeistMonoVariableVF.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="stylesheet" href="/fonts/geist-font/style.css">
  
  <!-- UIkit Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit-icons.min.js"></script>
  
  <!-- Additional Scripts -->
  <script src="/js/htmx@2.0.0/htmx.min.js"></script>
  <script src="https://unpkg.com/@phosphor-icons/web"></script>
  <script type="module" src="/js/franken-wc@0.0.6/wc.iife.js"></script>
  
  <!-- Astro Styles -->
  <link rel="stylesheet" href="/_astro/master.CZ5-T1HD.css">
  <!-- AM CHARTS -->
  <script src="https://cdn.amcharts.com/lib/4/core.js"></script>
  <script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
  <script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
  <!-- Loading Animations -->
  <link rel="stylesheet" href="../static/styles/loadinganimations.css">
  <link rel="stylesheet" href="../static/styles/scrollbar.css">
  <script src="../static/js/loading.js" defer></script>
  <script src="../static/js/themes.js" defer></script>
  <style>
    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      display: inline-block;
      position: relative;
    }
    
    .glow-green {
      animation: glow-green 1.5s infinite alternate;
    }
    
    .glow-red {
      animation: glow-red 1.5s infinite alternate;
    }
    
    @keyframes glow-green {
      from {
        box-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
      }
      to {
        box-shadow: 0 0 15px rgba(0, 255, 0, 1);
      }
    }
    
    @keyframes glow-red {
      from {
        box-shadow: 0 0 5px rgba(255, 0, 0, 0.5);
      }
      to {
        box-shadow: 0 0 15px rgba(255, 0, 0, 1);
      }
    }
        /* Chart Styles */
        .chart-container {
          position: relative;
          width: 100%;
          height: 150px;
          margin-top: 10px;
        }
        
        #chartdiv {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          right: 0;
          pointer-events: none; /* Allows text interaction beneath the chart */
        }
        
        .total-users {
          position: absolute;
          top: 0;
          left: 0;
          background: rgba(255, 255, 255, 0.8);
          padding: 5px 10px;
          border-radius: 5px;
          z-index: 1;
        }
  </style>
</head>
<body class="light">
  <!-- Loading Overlay -->
  <div id="loading-overlay" class="loading-overlay">
    <div class="typing-indicator">
      <div class="typing-circle"></div>
      <div class="typing-circle"></div>
      <div class="typing-circle"></div>
      <div class="typing-shadow"></div>
      <div class="typing-shadow"></div>
      <div class="typing-shadow"></div>
    </div>
  </div>

  <!-- Application Container -->
  <div id="app">
    <!-- Main Grid Layout -->
    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr]">
      <!-- Sidebar -->
      {% include 'sidebar.html' %}
      
      <!-- Main Content -->
      <div class="flex flex-col">
        <!-- Header -->
        <header class="flex h-14 lg:h-[60px] items-center gap-4 border-b px-6 card">
          <a class="lg:hidden" href="#">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                  <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                  <path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9"></path>
                  <path d="M12 3v6"></path>
              </svg>
              <span class="sr-only">Home</span>
          </a>
          <h1 class="font-semibold text-lg">Product Analytics</h1>
        </header>
        <main class="flex-1 p-3">
          <div class="container ml-0">
            <!-- Grid container for all cards -->
            <div class="flex flex-col gap-6">
              <div class="flex flex-wrap gap-3">
                <!-- WhatsApp Card -->
                <div class="rounded-lg border p-4 w-[240px] h-[220px] flex flex-col justify-between">
                  <div class="flex justify-between items-center w-full">
                    <div class="text-lg font-semibold">WhatsApp</div>
                    <div class="flex items-center gap-1">
                      <span class="text-sm">Status</span>
                      <span class="status-dot bg-green-500 glow-green"></span> <!-- Use bg-red-500 for red dot -->
                    </div>
                  </div>
                  <div class="flex flex-col items-center mt-4">
                    <img 
                      src="static/images/wasvg.svg" 
                      alt="WhatsApp"
                      class="w-20 h-20 object-contain"
                    >
                  </div>
                  <div class="text-sm text-gray-600 text-center mt-4">
                    <div>connected no: +14158135199</div>
                    <div>whatsapp id: wa_14158135199</div>
                  </div>
                </div>
              
                <div class="flex flex-col gap-2"> <!-- Changed h-full to flex-1 -->
                  <div class="rounded-lg border p-4 pt-1 pl-2 pb-0 w-[240px] flex-1 flex flex-col justify-between relative overflow-hidden">
                    <div>
                                          <!-- Text Elements with Higher Z-Index -->
                    <h2 class="text-lg font-semibold">Total Weekly Users</h2>
                      <!-- Subheading -->
                      <p class="text-sm font-semibold">1324 Users</p>
                    </div>

                    
                    <!-- Container for the Chart with Lower Z-Index -->
                    <div class="absolute bottom-0 left-0 w-full h-24 z-0">
                      <!-- Canvas for Chart.js -->
                      <canvas id="lineChartStatic" aria-label="Service Usage Line Chart" role="img" class="w-full h-full"></canvas>
                    </div>
                    
                    <!-- Include Chart.js from CDN -->
                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                    <script>
                      // Initialize the chart
                      const ctxStatic = document.getElementById('lineChartStatic').getContext('2d');
                      const lineChartStatic = new Chart(ctxStatic, {
                        type: 'line',
                        data: {
                          labels: ['Start', 'Q1', 'Q2', 'Mid', 'Q3', 'Q4', 'End'],
                          datasets: [{
                            label: 'Service Usage',
                            data: [0, 20, 40, 50, 70, 90, 100], // More data points
                            fill: true, // Enable fill under the line
                            backgroundColor: 'rgba(83, 71, 206, 0.2)', // Semi-transparent purple
                            borderColor: '#5347ce', // Line color
                            tension: 0.4, // Smooth curves
                            pointRadius: 5, // Add points
                          }]
                        },
                        options: {
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false // Hide the legend for a cleaner look
                            },
                            tooltip: {
                              enabled: true // Enable tooltips
                            }
                          },
                          scales: {
                            x: {
                              display: false, // Hide the x-axis
                              grid: {
                                display: false // Hide grid lines
                              }
                            },
                            y: {
                              display: false, // Hide the y-axis
                              grid: {
                                display: false // Hide grid lines
                              },
                              beginAtZero: true
                            }
                          },
                          elements: {
                            line: {
                              borderWidth: 2 // Thickness of the line
                            }
                          }
                        }
                      });
                    </script>
                  </div>
                  <div class="rounded-lg border p-4 pt-1 pl-2 pb-0 w-[240px] flex-1 flex flex-col justify-between relative overflow-hidden">
                    <div>
                                          <!-- Text Elements with Higher Z-Index -->
                    <h2 class="text-lg font-semibold">Total Daily Users</h2>
                      <!-- Subheading -->
                      <p class="text-sm font-semibold">92 Users</p>
                    </div>

                    
                    <!-- Container for the Chart with Lower Z-Index -->
                    <div class="absolute bottom-0 left-0 w-full h-24 z-0">
                      <!-- Canvas for Chart.js -->
                      <canvas id="lineChartStatic2" aria-label="Service Usage Line Chart" role="img" class="w-full h-full"></canvas>
                    </div>
                    
                    <!-- Include Chart.js from CDN -->
                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                    <script>
                      // Initialize the chart
                      const ctxStatic2 = document.getElementById('lineChartStatic2').getContext('2d');
                      const lineChartStatic2 = new Chart(ctxStatic2, {
                        type: 'line',
                        data: {
                          labels: ['Start', 'Q1', 'Q2', 'Mid', 'Q3', 'Q4', 'End'],
                          datasets: [{
                            label: 'Service Usage',
                            data: [0, 20, 53, 50, 30, 60, 100], // More data points
                            fill: true, // Enable fill under the line
                            backgroundColor: 'rgba(0, 123, 255, 0.2)', // Semi-transparent blue
                            borderColor: '#007bff', // Line color
                            tension: 0.4, // Smooth curves
                            pointRadius: 5, // Add points
                          }]
                        },
                        options: {
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false // Hide the legend for a cleaner look
                            },
                            tooltip: {
                              enabled: true // Enable tooltips
                            }
                          },
                          scales: {
                            x: {
                              display: false, // Hide the x-axis
                              grid: {
                                display: false // Hide grid lines
                              }
                            },
                            y: {
                              display: false, // Hide the y-axis
                              grid: {
                                display: false // Hide grid lines
                              },
                              beginAtZero: true
                            }
                          },
                          elements: {
                            line: {
                              borderWidth: 2 // Thickness of the line
                            }
                          }
                        }
                      });
                    </script>
                  </div>
                </div>
                
                                <!-- templates/productana.html -->
                <div class="rounded-lg border  p-4 pl-2 pb-0 pt-2 flex-1 h-[220px] relative">
                  <!-- Heading -->
                  <h2 class="text-lg font-semibold">AI vs Manual Interactions</h2>
                  <!-- Subheading -->
                  <p class="text-sm text-gray-600 ">Comparison of interaction types</p>
                  <!-- Chart Container -->
                  <div id="newService2Chart" class="absolute bottom-0 left-0 w-full h-[150px]"></div>
                  <script>
                    am4core.ready(function() {
                      // Themes
                      am4core.useTheme(am4themes_animated);
                  
                      // Create chart instance
                      var chart = am4core.create("newService2Chart", am4charts.XYChart);
                  
                      // Disable amCharts logo
                      chart.logo.disabled = true;
                  
                      // Add data
                      chart.data = [
                        { "day": "Mon", "interactions": 120 },
                        { "day": "Tue", "interactions": 152 },
                        { "day": "Wed", "interactions": 138 },
                        { "day": "Thu", "interactions": 145 },
                        { "day": "Fri", "interactions": 162 },
                        { "day": "Sat", "interactions": 136 },
                        { "day": "Sun", "interactions": 127 }
                      ];
                  
                      // Create axes
                      var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
                      categoryAxis.dataFields.category = "day";
                      categoryAxis.renderer.grid.template.location = 0;
                      categoryAxis.renderer.minGridDistance = 30;
                      categoryAxis.renderer.grid.template.disabled = true;
                      categoryAxis.renderer.labels.template.fill = am4core.color("#34495e");
                  
                      var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
                      valueAxis.renderer.grid.template.strokeOpacity = 0.1;
                      valueAxis.renderer.grid.template.strokeWidth = 1;
                      valueAxis.renderer.grid.template.stroke = am4core.color("#000000");
                      valueAxis.renderer.labels.template.fill = am4core.color("#34495e");
                  
                      // Adjust padding to remove extra space
                      chart.paddingBottom = 0; // Remove bottom padding
                      chart.paddingTop = 0;    // Remove top padding
                      chart.paddingLeft = 0;   // Remove left padding
                      chart.paddingRight = 0;  // Remove right padding
                  
                      // Create series
                      var series = chart.series.push(new am4charts.ColumnSeries());
                      series.dataFields.valueY = "interactions";
                      series.dataFields.categoryX = "day";
                      series.name = "AI Interactions";
                      series.columns.template.tooltipText = "{categoryX}: {valueY}";
                  
                      // Set column colors with gradient
                      var gradient = new am4core.LinearGradient();
                      gradient.rotation = 90; // Rotate gradient for top-to-bottom
                      gradient.addColor(am4core.color("#5347ce")); // Top color
                      gradient.addColor(am4core.color("#a9a3e6")); // Bottom color
                      series.columns.template.fill = gradient;
                      series.columns.template.stroke = am4core.color("#5347ce"); // Match border color to top gradient color
                      series.columns.template.strokeWidth = 0.4; // Reduced border width
                      series.columns.template.strokeOpacity = 0.4; // Border opacity
                  
                      // Rounded corners
                      series.columns.template.column.cornerRadiusTopLeft = 6;
                      series.columns.template.column.cornerRadiusTopRight = 6;
                  
                      // Add cursor
                      chart.cursor = new am4charts.XYCursor();
                      chart.cursor.behavior = "none";
                      chart.cursor.lineY.disabled = true;
                      chart.cursor.lineX.disabled = true;
                  
                      // Animate chart on load
                      series.appear(1000);
                      chart.appear(1000, 100);
                  
                      // Ensure the chart resizes with the container
                      window.addEventListener("resize", function() {
                        chart.invalidateSize();
                      });
                    }); // end am4core.ready()
                  </script>

                </div>
                <!-- New Card 3: Flexible Width -->
                <div class="rounded-lg border bg-card text-card-foreground p-4 pl-2 pb-2 pt-2 flex-1 h-[220px] flex flex-col">
                  <!-- Header Section -->
                  <div class="flex justify-between items-center mb-2">
                    <!-- Left Side: Headings -->
                    <div>
                      <h2 class="text-xl font-semibold">Total Users</h2>
                      <p class="text-sm text-gray-600">Overview of total users</p>
                    </div>
                    <!-- Right Side: Dropdown Button -->
                    <div class="relative inline-block text-left">
                      <div>
                        <button type="button" class="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none" id="options-menu" aria-expanded="true" aria-haspopup="true">
                          Options
                          <!-- Dropdown Icon -->
                          <svg class="-mr-1 ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.584l3.71-4.354a.75.75 0 111.14.976l-4.25 5a.75.75 0 01-1.14 0l-4.25-5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                          </svg>
                        </button>
                      </div>

                      <!-- Dropdown Menu -->
                      <div class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                        <div class="py-1" role="none">
                          <!-- Active: "bg-gray-100 text-gray-900", Not Active: "text-gray-700" -->
                          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Action</a>
                          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Another action</a>
                          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Something else here</a>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Chart Container -->
                  <div id="newService3Chart2" class="w-full h-full mt-auto"></div>

                  <!-- Resources (Ensure these are loaded once, preferably in the <head> section) -->
                  <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
                  <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
                  <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>

                  <!-- Chart Code -->
                  <script>
                    am5.ready(function() {

                      // Create root element
                      var root = am5.Root.new("newService3Chart2");

                      // Disable amCharts logo
                      root._logo.dispose();

                      // Set themes
                      root.setThemes([
                        am5themes_Animated.new(root)
                      ]);

                      // Create chart with padding
                      var chart = root.container.children.push(am5xy.XYChart.new(root, {
                        panX: true,
                        panY: true,
                        wheelX: "panX",
                        wheelY: "zoomX",
                        pinchZoomX: true,
                        paddingLeft: 0,    // Added padding
                        paddingRight: 0,   // Added padding
                        paddingTop: 0,     // Added padding
                        paddingBottom: 0   // Added padding
                      }));

                      // Add cursor
                      var cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
                        behavior: "zoomXY"
                      }));
                      cursor.lineY.set("visible", false);

                      // Sample data for total users
                      var data = [
                        { date: new Date(2023, 0, 1).getTime(), value: 1500 },
                        { date: new Date(2023, 1, 1).getTime(), value: 1600 },
                        { date: new Date(2023, 2, 1).getTime(), value: 1700 },
                        { date: new Date(2023, 3, 1).getTime(), value: 1650 },
                        { date: new Date(2023, 4, 1).getTime(), value: 1800 },
                        { date: new Date(2023, 5, 1).getTime(), value: 1900 },
                        { date: new Date(2023, 6, 1).getTime(), value: 2000 },
                        { date: new Date(2023, 7, 1).getTime(), value: 2100 },
                        { date: new Date(2023, 8, 1).getTime(), value: 2200 },
                        { date: new Date(2023, 9, 1).getTime(), value: 2300 }
                      ];

                      var xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
                        maxDeviation: 0.5,
                        baseInterval: {
                          timeUnit: "month",
                          count: 1
                        },
                        renderer: am5xy.AxisRendererX.new(root, {
                          minGridDistance: 50,
                          pan: "zoom"
                        }),
                        tooltip: am5.Tooltip.new(root, {})
                      }));
                
                      // Format X-Axis labels to show only month names without years
                      xAxis.get("renderer").labels.template.setAll({
                        text: "{valueX.formatDate('MMM')}"
                      });
                      var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                        renderer: am5xy.AxisRendererY.new(root, {
                          pan: "zoom"
                        })
                      }));

                      // Remove horizontal grid lines
                      yAxis.get("renderer").grid.template.setAll({
                        visible: false
                      });

                      // Add series
                      var series = chart.series.push(am5xy.LineSeries.new(root, {
                        name: "Total Users",
                        xAxis: xAxis,
                        yAxis: yAxis,
                        valueYField: "value",
                        valueXField: "date",
                        tooltip: am5.Tooltip.new(root, {
                          labelText: "{valueY}"
                        })
                      }));
                      
                      // Set line color to light blue
                      series.strokes.template.setAll({
                        stroke: am5.color(0xADD8E6), // Light blue color
                        strokeWidth: 2
                      });

                      // Add bullets
                      series.bullets.push(function () {
                        return am5.Bullet.new(root, {
                          sprite: am5.Circle.new(root, {
                            radius: 4,
                            fill: series.get("fill")
                          })
                        });
                      });

                      // Remove scrollbar by not adding it

                      // Bind data
                      series.data.setAll(data);
                      xAxis.data.setAll(data);
                      yAxis.data.setAll(data);

                      // Animate chart on load
                      series.appear(1000);
                      chart.appear(1000, 100);

                      // Ensure the chart resizes with the container
                      window.addEventListener("resize", function() {
                        chart.invalidateSize();
                      });

                    }); // end am5.ready()
                  </script>
                </div>
              </div>
    
              <div class="flex flex-wrap gap-3">
                <!-- messenger card -->
                <div class="rounded-lg border p-4 w-[240px] h-[220px] flex flex-col justify-between">
                  <div class="flex justify-between items-center w-full">
                    <div class="text-lg font-semibold">Messenger</div>
                    <div class="flex items-center gap-1">
                      <span class="text-sm">Status</span>
                      <span class="status-dot bg-red-500 glow-red"></span> <!-- Use bg-red-500 for red dot -->
                    </div>
                  </div>
                  <div class="flex flex-col items-center mt-4">
                    <img 
                      src="static/images/wasvg.svg" 
                      alt="WhatsApp"
                      class="w-20 h-20 object-contain"
                    >
                  </div>
                  <div class="text-sm text-gray-600 text-center mt-4">
                    <div>connected no: +14158135199</div>
                    <div>whatsapp id: wa_14158135199</div>
                  </div>
                </div>
              
                <div class="flex flex-col gap-2"> <!-- Changed h-full to flex-1 -->
                  <div class="rounded-lg border p-4 pt-1 pl-2 pb-0 w-[240px] flex-1 flex flex-col justify-between relative overflow-hidden">
                    <div>
                                          <!-- Text Elements with Higher Z-Index -->
                    <h2 class="text-lg font-semibold">Total Weekly Users</h2>
                      <!-- Subheading -->
                      <p class="text-sm font-semibold">1324 Users</p>
                    </div>

                    
                    <!-- Container for the Chart with Lower Z-Index -->
                    <div class="absolute bottom-0 left-0 w-full h-24 z-0">
                      <!-- Canvas for Chart.js -->
                      <canvas id="lineChartStatic3" aria-label="Service Usage Line Chart" role="img" class="w-full h-full"></canvas>
                    </div>
                    
                    <!-- Include Chart.js from CDN -->
                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                    <script>
                      // Initialize the chart
                      const ctxStatic3 = document.getElementById('lineChartStatic3').getContext('2d');
                      const lineChartStatic3 = new Chart(ctxStatic3, {
                        type: 'line',
                        data: {
                          labels: ['Start', 'Q1', 'Q2', 'Mid', 'Q3', 'Q4', 'End'],
                          datasets: [{
                            label: 'Service Usage',
                            data: [0, 20, 40, 50, 70, 90, 100], // More data points
                            fill: true, // Enable fill under the line
                            backgroundColor: 'rgba(83, 71, 206, 0.2)', // Semi-transparent purple
                            borderColor: '#5347ce', // Line color
                            tension: 0.4, // Smooth curves
                            pointRadius: 5, // Add points
                          }]
                        },
                        options: {
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false // Hide the legend for a cleaner look
                            },
                            tooltip: {
                              enabled: true // Enable tooltips
                            }
                          },
                          scales: {
                            x: {
                              display: false, // Hide the x-axis
                              grid: {
                                display: false // Hide grid lines
                              }
                            },
                            y: {
                              display: false, // Hide the y-axis
                              grid: {
                                display: false // Hide grid lines
                              },
                              beginAtZero: true
                            }
                          },
                          elements: {
                            line: {
                              borderWidth: 2 // Thickness of the line
                            }
                          }
                        }
                      });
                    </script>
                  </div>
                  <div class="rounded-lg border p-4 pt-1 pl-2 pb-0 w-[240px] flex-1 flex flex-col justify-between relative overflow-hidden">
                    <div>
                                          <!-- Text Elements with Higher Z-Index -->
                    <h2 class="text-lg font-semibold">Total Daily Users</h2>
                      <!-- Subheading -->
                      <p class="text-sm font-semibold">92 Users</p>
                    </div>

                    
                    <!-- Container for the Chart with Lower Z-Index -->
                    <div class="absolute bottom-0 left-0 w-full h-24 z-0">
                      <!-- Canvas for Chart.js -->
                      <canvas id="lineChartStatic4" aria-label="Service Usage Line Chart" role="img" class="w-full h-full"></canvas>
                    </div>
                    
                    <!-- Include Chart.js from CDN -->
                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                    <script>
                      // Initialize the chart
                      const ctxStatic4 = document.getElementById('lineChartStatic4').getContext('2d');
                      const lineChartStatic4 = new Chart(ctxStatic4, {
                        type: 'line',
                        data: {
                          labels: ['Start', 'Q1', 'Q2', 'Mid', 'Q3', 'Q4', 'End'],
                          datasets: [{
                            label: 'Service Usage',
                            data: [0, 20, 53, 50, 30, 60, 100], // More data points
                            fill: true, // Enable fill under the line
                            backgroundColor: 'rgba(0, 123, 255, 0.2)', // Semi-transparent blue
                            borderColor: '#007bff', // Line color
                            tension: 0.4, // Smooth curves
                            pointRadius: 5, // Add points
                          }]
                        },
                        options: {
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false // Hide the legend for a cleaner look
                            },
                            tooltip: {
                              enabled: true // Enable tooltips
                            }
                          },
                          scales: {
                            x: {
                              display: false, // Hide the x-axis
                              grid: {
                                display: false // Hide grid lines
                              }
                            },
                            y: {
                              display: false, // Hide the y-axis
                              grid: {
                                display: false // Hide grid lines
                              },
                              beginAtZero: true
                            }
                          },
                          elements: {
                            line: {
                              borderWidth: 2 // Thickness of the line
                            }
                          }
                        }
                      });
                    </script>
                  </div>
                </div>
                
                                <!-- templates/productana.html -->
                <div class="rounded-lg border  p-4 pl-2 pb-0 pt-2 flex-1 h-[220px] relative">
                  <!-- Heading -->
                  <h2 class="text-lg font-semibold">AI vs Manual Interactions</h2>
                  <!-- Subheading -->
                  <p class="text-sm text-gray-600 ">Comparison of interaction types</p>
                  <!-- Chart Container -->
                  <div id="newService2Chart2" class="absolute bottom-0 left-0 w-full h-[150px]"></div>
                  <script>
                    am4core.ready(function() {
                      // Themes
                      am4core.useTheme(am4themes_animated);
                  
                      // Create chart instance
                      var chart = am4core.create("newService2Chart2", am4charts.XYChart);
                  
                      // Disable amCharts logo
                      chart.logo.disabled = true;
                  
                      // Add data
                      chart.data = [
                        { "day": "Mon", "interactions": 120 },
                        { "day": "Tue", "interactions": 152 },
                        { "day": "Wed", "interactions": 138 },
                        { "day": "Thu", "interactions": 145 },
                        { "day": "Fri", "interactions": 162 },
                        { "day": "Sat", "interactions": 136 },
                        { "day": "Sun", "interactions": 127 }
                      ];
                  
                      // Create axes
                      var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
                      categoryAxis.dataFields.category = "day";
                      categoryAxis.renderer.grid.template.location = 0;
                      categoryAxis.renderer.minGridDistance = 30;
                      categoryAxis.renderer.grid.template.disabled = true;
                      categoryAxis.renderer.labels.template.fill = am4core.color("#34495e");
                  
                      var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
                      valueAxis.renderer.grid.template.strokeOpacity = 0.1;
                      valueAxis.renderer.grid.template.strokeWidth = 1;
                      valueAxis.renderer.grid.template.stroke = am4core.color("#000000");
                      valueAxis.renderer.labels.template.fill = am4core.color("#34495e");
                  
                      // Adjust padding to remove extra space
                      chart.paddingBottom = 0; // Remove bottom padding
                      chart.paddingTop = 0;    // Remove top padding
                      chart.paddingLeft = 0;   // Remove left padding
                      chart.paddingRight = 0;  // Remove right padding
                  
                      // Create series
                      var series = chart.series.push(new am4charts.ColumnSeries());
                      series.dataFields.valueY = "interactions";
                      series.dataFields.categoryX = "day";
                      series.name = "AI Interactions";
                      series.columns.template.tooltipText = "{categoryX}: {valueY}";
                  
                      // Set column colors with gradient
                      var gradient = new am4core.LinearGradient();
                      gradient.rotation = 90; // Rotate gradient for top-to-bottom
                      gradient.addColor(am4core.color("#5347ce")); // Top color
                      gradient.addColor(am4core.color("#a9a3e6")); // Bottom color
                      series.columns.template.fill = gradient;
                      series.columns.template.stroke = am4core.color("#5347ce"); // Match border color to top gradient color
                      series.columns.template.strokeWidth = 0.4; // Reduced border width
                      series.columns.template.strokeOpacity = 0.4; // Border opacity
                  
                      // Rounded corners
                      series.columns.template.column.cornerRadiusTopLeft = 6;
                      series.columns.template.column.cornerRadiusTopRight = 6;
                  
                      // Add cursor
                      chart.cursor = new am4charts.XYCursor();
                      chart.cursor.behavior = "none";
                      chart.cursor.lineY.disabled = true;
                      chart.cursor.lineX.disabled = true;
                  
                      // Animate chart on load
                      series.appear(1000);
                      chart.appear(1000, 100);
                  
                      // Ensure the chart resizes with the container
                      window.addEventListener("resize", function() {
                        chart.invalidateSize();
                      });
                    }); // end am4core.ready()
                  </script>

                </div>
                <!-- New Card 3: Flexible Width -->
                <div class="rounded-lg border bg-card text-card-foreground p-4 pl-2 pb-2 pt-2 flex-1 h-[220px] flex flex-col">
                  <!-- Header Section -->
                  <div class="flex justify-between items-center mb-2">
                    <!-- Left Side: Headings -->
                    <div>
                      <h2 class="text-xl font-semibold">Total Users</h2>
                      <p class="text-sm text-gray-600">Overview of total users</p>
                    </div>
                    <!-- Right Side: Dropdown Button -->
                    <div class="relative inline-block text-left">
                      <div>
                        <button type="button" class="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none" id="options-menu" aria-expanded="true" aria-haspopup="true">
                          Options
                          <!-- Dropdown Icon -->
                          <svg class="-mr-1 ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.584l3.71-4.354a.75.75 0 111.14.976l-4.25 5a.75.75 0 01-1.14 0l-4.25-5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                          </svg>
                        </button>
                      </div>

                      <!-- Dropdown Menu -->
                      <div class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                        <div class="py-1" role="none">
                          <!-- Active: "bg-gray-100 text-gray-900", Not Active: "text-gray-700" -->
                          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Action</a>
                          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Another action</a>
                          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Something else here</a>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Chart Container -->
                  <div id="newService3Chart3" class="w-full h-full mt-auto"></div>

                  <!-- Resources (Ensure these are loaded once, preferably in the <head> section) -->
                  <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
                  <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
                  <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>

                  <!-- Chart Code -->
                  <script>
                    am5.ready(function() {

                      // Create root element
                      var root = am5.Root.new("newService3Chart3");

                      // Disable amCharts logo
                      root._logo.dispose();

                      // Set themes
                      root.setThemes([
                        am5themes_Animated.new(root)
                      ]);

                      // Create chart with padding
                      var chart = root.container.children.push(am5xy.XYChart.new(root, {
                        panX: true,
                        panY: true,
                        wheelX: "panX",
                        wheelY: "zoomX",
                        pinchZoomX: true,
                        paddingLeft: 0,    // Added padding
                        paddingRight: 0,   // Added padding
                        paddingTop: 0,     // Added padding
                        paddingBottom: 0   // Added padding
                      }));

                      // Add cursor
                      var cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
                        behavior: "zoomXY"
                      }));
                      cursor.lineY.set("visible", false);

                      // Sample data for total users
                      var data = [
                        { date: new Date(2023, 0, 1).getTime(), value: 1500 },
                        { date: new Date(2023, 1, 1).getTime(), value: 1600 },
                        { date: new Date(2023, 2, 1).getTime(), value: 1700 },
                        { date: new Date(2023, 3, 1).getTime(), value: 1650 },
                        { date: new Date(2023, 4, 1).getTime(), value: 1800 },
                        { date: new Date(2023, 5, 1).getTime(), value: 1900 },
                        { date: new Date(2023, 6, 1).getTime(), value: 2000 },
                        { date: new Date(2023, 7, 1).getTime(), value: 2100 },
                        { date: new Date(2023, 8, 1).getTime(), value: 2200 },
                        { date: new Date(2023, 9, 1).getTime(), value: 2300 }
                      ];

                      var xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
                        maxDeviation: 0.5,
                        baseInterval: {
                          timeUnit: "month",
                          count: 1
                        },
                        renderer: am5xy.AxisRendererX.new(root, {
                          minGridDistance: 50,
                          pan: "zoom"
                        }),
                        tooltip: am5.Tooltip.new(root, {})
                      }));
                
                      // Format X-Axis labels to show only month names without years
                      xAxis.get("renderer").labels.template.setAll({
                        text: "{valueX.formatDate('MMM')}"
                      });
                      var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                        renderer: am5xy.AxisRendererY.new(root, {
                          pan: "zoom"
                        })
                      }));

                      // Remove horizontal grid lines
                      yAxis.get("renderer").grid.template.setAll({
                        visible: false
                      });

                      // Add series
                      var series = chart.series.push(am5xy.LineSeries.new(root, {
                        name: "Total Users",
                        xAxis: xAxis,
                        yAxis: yAxis,
                        valueYField: "value",
                        valueXField: "date",
                        tooltip: am5.Tooltip.new(root, {
                          labelText: "{valueY}"
                        })
                      }));
                      
                      // Set line color to light blue
                      series.strokes.template.setAll({
                        stroke: am5.color(0xADD8E6), // Light blue color
                        strokeWidth: 2
                      });

                      // Add bullets
                      series.bullets.push(function () {
                        return am5.Bullet.new(root, {
                          sprite: am5.Circle.new(root, {
                            radius: 4,
                            fill: series.get("fill")
                          })
                        });
                      });

                      // Remove scrollbar by not adding it

                      // Bind data
                      series.data.setAll(data);
                      xAxis.data.setAll(data);
                      yAxis.data.setAll(data);

                      // Animate chart on load
                      series.appear(1000);
                      chart.appear(1000, 100);

                      // Ensure the chart resizes with the container
                      window.addEventListener("resize", function() {
                        chart.invalidateSize();
                      });

                    }); // end am5.ready()
                  </script>
                </div>
              </div>
        

              <div class="flex flex-wrap gap-3">
                <!-- Instagram Card -->
                <div class="rounded-lg border p-4 w-[240px] h-[220px] flex flex-col justify-between">
                  <div class="flex justify-between items-center w-full">
                    <div class="text-lg font-semibold">Instagram</div>
                    <div class="flex items-center gap-1">
                      <span class="text-sm">Status</span>
                      <span class="status-dot bg-green-500 glow-green"></span> <!-- Use bg-red-500 for red dot -->
                    </div>
                  </div>
                  <div class="flex flex-col items-center mt-4">
                    <img 
                      src="static/images/wasvg.svg" 
                      alt="WhatsApp"
                      class="w-20 h-20 object-contain"
                    >
                  </div>
                  <div class="text-sm text-gray-600 text-center mt-4">
                    <div>connected no: +14158135199</div>
                    <div>whatsapp id: wa_14158135199</div>
                  </div>
                </div>
              
                <div class="flex flex-col gap-2"> <!-- Changed h-full to flex-1 -->
                  <div class="rounded-lg border p-4 pt-1 pl-2 pb-0 w-[240px] flex-1 flex flex-col justify-between relative overflow-hidden">
                    <div>
                                          <!-- Text Elements with Higher Z-Index -->
                    <h2 class="text-lg font-semibold">Total Weekly Users</h2>
                      <!-- Subheading -->
                      <p class="text-sm font-semibold">1324 Users</p>
                    </div>

                    
                    <!-- Container for the Chart with Lower Z-Index -->
                    <div class="absolute bottom-0 left-0 w-full h-24 z-0">
                      <!-- Canvas for Chart.js -->
                      <canvas id="lineChartStatic5" aria-label="Service Usage Line Chart" role="img" class="w-full h-full"></canvas>
                    </div>
                    
                    <!-- Include Chart.js from CDN -->
                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                    <script>
                      // Initialize the chart
                      const ctxStatic5 = document.getElementById('lineChartStatic5').getContext('2d');
                      const lineChartStatic5 = new Chart(ctxStatic5, {
                        type: 'line',
                        data: {
                          labels: ['Start', 'Q1', 'Q2', 'Mid', 'Q3', 'Q4', 'End'],
                          datasets: [{
                            label: 'Service Usage',
                            data: [0, 20, 40, 50, 70, 90, 100], // More data points
                            fill: true, // Enable fill under the line
                            backgroundColor: 'rgba(83, 71, 206, 0.2)', // Semi-transparent purple
                            borderColor: '#5347ce', // Line color
                            tension: 0.4, // Smooth curves
                            pointRadius: 5, // Add points
                          }]
                        },
                        options: {
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false // Hide the legend for a cleaner look
                            },
                            tooltip: {
                              enabled: true // Enable tooltips
                            }
                          },
                          scales: {
                            x: {
                              display: false, // Hide the x-axis
                              grid: {
                                display: false // Hide grid lines
                              }
                            },
                            y: {
                              display: false, // Hide the y-axis
                              grid: {
                                display: false // Hide grid lines
                              },
                              beginAtZero: true
                            }
                          },
                          elements: {
                            line: {
                              borderWidth: 2 // Thickness of the line
                            }
                          }
                        }
                      });
                    </script>
                  </div>
                  <div class="rounded-lg border p-4 pt-1 pl-2 pb-0 w-[240px] flex-1 flex flex-col justify-between relative overflow-hidden">
                    <div>
                                          <!-- Text Elements with Higher Z-Index -->
                    <h2 class="text-lg font-semibold">Total Daily Users</h2>
                      <!-- Subheading -->
                      <p class="text-sm font-semibold">92 Users</p>
                    </div>

                    
                    <!-- Container for the Chart with Lower Z-Index -->
                    <div class="absolute bottom-0 left-0 w-full h-24 z-0">
                      <!-- Canvas for Chart.js -->
                      <canvas id="lineChartStatic6" aria-label="Service Usage Line Chart" role="img" class="w-full h-full"></canvas>
                    </div>
                    
                    <!-- Include Chart.js from CDN -->
                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                    <script>
                      // Initialize the chart
                      const ctxStatic6 = document.getElementById('lineChartStatic6').getContext('2d');
                      const lineChartStatic6 = new Chart(ctxStatic6, {
                        type: 'line',
                        data: {
                          labels: ['Start', 'Q1', 'Q2', 'Mid', 'Q3', 'Q4', 'End'],
                          datasets: [{
                            label: 'Service Usage',
                            data: [0, 20, 53, 50, 30, 60, 100], // More data points
                            fill: true, // Enable fill under the line
                            backgroundColor: 'rgba(0, 123, 255, 0.2)', // Semi-transparent blue
                            borderColor: '#007bff', // Line color
                            tension: 0.4, // Smooth curves
                            pointRadius: 5, // Add points
                          }]
                        },
                        options: {
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false // Hide the legend for a cleaner look
                            },
                            tooltip: {
                              enabled: true // Enable tooltips
                            }
                          },
                          scales: {
                            x: {
                              display: false, // Hide the x-axis
                              grid: {
                                display: false // Hide grid lines
                              }
                            },
                            y: {
                              display: false, // Hide the y-axis
                              grid: {
                                display: false // Hide grid lines
                              },
                              beginAtZero: true
                            }
                          },
                          elements: {
                            line: {
                              borderWidth: 2 // Thickness of the line
                            }
                          }
                        }
                      });
                    </script>
                  </div>
                </div>
                
                                <!-- templates/productana.html -->
                <div class="rounded-lg border  p-4 pl-2 pb-0 pt-2 flex-1 h-[220px] relative">
                  <!-- Heading -->
                  <h2 class="text-lg font-semibold">AI vs Manual Interactions</h2>
                  <!-- Subheading -->
                  <p class="text-sm text-gray-600 ">Comparison of interaction types</p>
                  <!-- Chart Container -->
                  <div id="newService2Chart6" class="absolute bottom-0 left-0 w-full h-[150px]"></div>
                  <script>
                    am4core.ready(function() {
                      // Themes
                      am4core.useTheme(am4themes_animated);
                  
                      // Create chart instance
                      var chart = am4core.create("newService2Chart6", am4charts.XYChart);
                  
                      // Disable amCharts logo
                      chart.logo.disabled = true;
                  
                      // Add data
                      chart.data = [
                        { "day": "Mon", "interactions": 120 },
                        { "day": "Tue", "interactions": 152 },
                        { "day": "Wed", "interactions": 138 },
                        { "day": "Thu", "interactions": 145 },
                        { "day": "Fri", "interactions": 162 },
                        { "day": "Sat", "interactions": 136 },
                        { "day": "Sun", "interactions": 127 }
                      ];
                  
                      // Create axes
                      var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
                      categoryAxis.dataFields.category = "day";
                      categoryAxis.renderer.grid.template.location = 0;
                      categoryAxis.renderer.minGridDistance = 30;
                      categoryAxis.renderer.grid.template.disabled = true;
                      categoryAxis.renderer.labels.template.fill = am4core.color("#34495e");
                  
                      var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
                      valueAxis.renderer.grid.template.strokeOpacity = 0.1;
                      valueAxis.renderer.grid.template.strokeWidth = 1;
                      valueAxis.renderer.grid.template.stroke = am4core.color("#000000");
                      valueAxis.renderer.labels.template.fill = am4core.color("#34495e");
                  
                      // Adjust padding to remove extra space
                      chart.paddingBottom = 0; // Remove bottom padding
                      chart.paddingTop = 0;    // Remove top padding
                      chart.paddingLeft = 0;   // Remove left padding
                      chart.paddingRight = 0;  // Remove right padding
                  
                      // Create series
                      var series = chart.series.push(new am4charts.ColumnSeries());
                      series.dataFields.valueY = "interactions";
                      series.dataFields.categoryX = "day";
                      series.name = "AI Interactions";
                      series.columns.template.tooltipText = "{categoryX}: {valueY}";
                  
                      // Set column colors with gradient
                      var gradient = new am4core.LinearGradient();
                      gradient.rotation = 90; // Rotate gradient for top-to-bottom
                      gradient.addColor(am4core.color("#5347ce")); // Top color
                      gradient.addColor(am4core.color("#a9a3e6")); // Bottom color
                      series.columns.template.fill = gradient;
                      series.columns.template.stroke = am4core.color("#5347ce"); // Match border color to top gradient color
                      series.columns.template.strokeWidth = 0.4; // Reduced border width
                      series.columns.template.strokeOpacity = 0.4; // Border opacity
                  
                      // Rounded corners
                      series.columns.template.column.cornerRadiusTopLeft = 6;
                      series.columns.template.column.cornerRadiusTopRight = 6;
                  
                      // Add cursor
                      chart.cursor = new am4charts.XYCursor();
                      chart.cursor.behavior = "none";
                      chart.cursor.lineY.disabled = true;
                      chart.cursor.lineX.disabled = true;
                  
                      // Animate chart on load
                      series.appear(1000);
                      chart.appear(1000, 100);
                  
                      // Ensure the chart resizes with the container
                      window.addEventListener("resize", function() {
                        chart.invalidateSize();
                      });
                    }); // end am4core.ready()
                  </script>

                </div>
                <!-- New Card 3: Flexible Width -->
                <div class="rounded-lg border bg-card text-card-foreground p-4 pl-2 pb-2 pt-2 flex-1 h-[220px] flex flex-col">
                  <!-- Header Section -->
                  <div class="flex justify-between items-center mb-2">
                    <!-- Left Side: Headings -->
                    <div>
                      <h2 class="text-xl font-semibold">Total Users</h2>
                      <p class="text-sm text-gray-600">Overview of total users</p>
                    </div>
                    <!-- Right Side: Dropdown Button -->
                    <div class="relative inline-block text-left">
                      <div>
                        <button type="button" class="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none" id="options-menu" aria-expanded="true" aria-haspopup="true">
                          Options
                          <!-- Dropdown Icon -->
                          <svg class="-mr-1 ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.584l3.71-4.354a.75.75 0 111.14.976l-4.25 5a.75.75 0 01-1.14 0l-4.25-5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                          </svg>
                        </button>
                      </div>

                      <!-- Dropdown Menu -->
                      <div class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                        <div class="py-1" role="none">
                          <!-- Active: "bg-gray-100 text-gray-900", Not Active: "text-gray-700" -->
                          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Action</a>
                          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Another action</a>
                          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Something else here</a>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Chart Container -->
                  <div id="newService3Chart7" class="w-full h-full mt-auto"></div>

                  <!-- Resources (Ensure these are loaded once, preferably in the <head> section) -->
                  <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
                  <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
                  <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>

                  <!-- Chart Code -->
                  <script>
                    am5.ready(function() {

                      // Create root element
                      var root = am5.Root.new("newService3Chart7");

                      // Disable amCharts logo
                      root._logo.dispose();

                      // Set themes
                      root.setThemes([
                        am5themes_Animated.new(root)
                      ]);

                      // Create chart with padding
                      var chart = root.container.children.push(am5xy.XYChart.new(root, {
                        panX: true,
                        panY: true,
                        wheelX: "panX",
                        wheelY: "zoomX",
                        pinchZoomX: true,
                        paddingLeft: 0,    // Added padding
                        paddingRight: 0,   // Added padding
                        paddingTop: 0,     // Added padding
                        paddingBottom: 0   // Added padding
                      }));

                      // Add cursor
                      var cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
                        behavior: "zoomXY"
                      }));
                      cursor.lineY.set("visible", false);

                      // Sample data for total users
                      var data = [
                        { date: new Date(2023, 0, 1).getTime(), value: 1500 },
                        { date: new Date(2023, 1, 1).getTime(), value: 1600 },
                        { date: new Date(2023, 2, 1).getTime(), value: 1700 },
                        { date: new Date(2023, 3, 1).getTime(), value: 1650 },
                        { date: new Date(2023, 4, 1).getTime(), value: 1800 },
                        { date: new Date(2023, 5, 1).getTime(), value: 1900 },
                        { date: new Date(2023, 6, 1).getTime(), value: 2000 },
                        { date: new Date(2023, 7, 1).getTime(), value: 2100 },
                        { date: new Date(2023, 8, 1).getTime(), value: 2200 },
                        { date: new Date(2023, 9, 1).getTime(), value: 2300 }
                      ];

                      var xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
                        maxDeviation: 0.5,
                        baseInterval: {
                          timeUnit: "month",
                          count: 1
                        },
                        renderer: am5xy.AxisRendererX.new(root, {
                          minGridDistance: 50,
                          pan: "zoom"
                        }),
                        tooltip: am5.Tooltip.new(root, {})
                      }));
                
                      // Format X-Axis labels to show only month names without years
                      xAxis.get("renderer").labels.template.setAll({
                        text: "{valueX.formatDate('MMM')}"
                      });
                      var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                        renderer: am5xy.AxisRendererY.new(root, {
                          pan: "zoom"
                        })
                      }));

                      // Remove horizontal grid lines
                      yAxis.get("renderer").grid.template.setAll({
                        visible: false
                      });

                      // Add series
                      var series = chart.series.push(am5xy.LineSeries.new(root, {
                        name: "Total Users",
                        xAxis: xAxis,
                        yAxis: yAxis,
                        valueYField: "value",
                        valueXField: "date",
                        tooltip: am5.Tooltip.new(root, {
                          labelText: "{valueY}"
                        })
                      }));
                      
                      // Set line color to light blue
                      series.strokes.template.setAll({
                        stroke: am5.color(0xADD8E6), // Light blue color
                        strokeWidth: 2
                      });

                      // Add bullets
                      series.bullets.push(function () {
                        return am5.Bullet.new(root, {
                          sprite: am5.Circle.new(root, {
                            radius: 4,
                            fill: series.get("fill")
                          })
                        });
                      });

                      // Remove scrollbar by not adding it

                      // Bind data
                      series.data.setAll(data);
                      xAxis.data.setAll(data);
                      yAxis.data.setAll(data);

                      // Animate chart on load
                      series.appear(1000);
                      chart.appear(1000, 100);

                      // Ensure the chart resizes with the container
                      window.addEventListener("resize", function() {
                        chart.invalidateSize();
                      });

                    }); // end am5.ready()
                  </script>
                </div>
              </div>
        
              <div class="flex flex-wrap gap-3">
                <!-- Voice AI Card -->
                <div class="rounded-lg border p-4 w-[240px] h-[220px] flex flex-col justify-between">
                  <div class="flex justify-between items-center w-full">
                    <div class="text-lg font-semibold">WhatsApp</div>
                    <div class="flex items-center gap-1">
                      <span class="text-sm">Status</span>
                      <span class="status-dot bg-green-500 glow-green"></span> <!-- Use bg-red-500 for red dot -->
                    </div>
                  </div>
                  <div class="flex flex-col items-center mt-4">
                    <img 
                      src="static/images/wasvg.svg" 
                      alt="WhatsApp"
                      class="w-20 h-20 object-contain"
                    >
                  </div>
                  <div class="text-sm text-gray-600 text-center mt-4">
                    <div>connected no: +14158135199</div>
                    <div>whatsapp id: wa_14158135199</div>
                  </div>
                </div>
              
                <div class="flex flex-col gap-2"> <!-- Changed h-full to flex-1 -->
                  <div class="rounded-lg border p-4 pt-1 pl-2 pb-0 w-[240px] flex-1 flex flex-col justify-between relative overflow-hidden">
                    <div>
                                          <!-- Text Elements with Higher Z-Index -->
                    <h2 class="text-lg font-semibold">Total Weekly Users</h2>
                      <!-- Subheading -->
                      <p class="text-sm font-semibold">1324 Users</p>
                    </div>

                    
                    <!-- Container for the Chart with Lower Z-Index -->
                    <div class="absolute bottom-0 left-0 w-full h-24 z-0">
                      <!-- Canvas for Chart.js -->
                      <canvas id="lineChartStatic7" aria-label="Service Usage Line Chart" role="img" class="w-full h-full"></canvas>
                    </div>
                    
                    <!-- Include Chart.js from CDN -->
                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                    <script>
                      // Initialize the chart
                      const ctxStatic7 = document.getElementById('lineChartStatic7').getContext('2d');
                      const lineChartStatic7 = new Chart(ctxStatic7, {
                        type: 'line',
                        data: {
                          labels: ['Start', 'Q1', 'Q2', 'Mid', 'Q3', 'Q4', 'End'],
                          datasets: [{
                            label: 'Service Usage',
                            data: [0, 20, 40, 50, 70, 90, 100], // More data points
                            fill: true, // Enable fill under the line
                            backgroundColor: 'rgba(83, 71, 206, 0.2)', // Semi-transparent purple
                            borderColor: '#5347ce', // Line color
                            tension: 0.4, // Smooth curves
                            pointRadius: 5, // Add points
                          }]
                        },
                        options: {
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false // Hide the legend for a cleaner look
                            },
                            tooltip: {
                              enabled: true // Enable tooltips
                            }
                          },
                          scales: {
                            x: {
                              display: false, // Hide the x-axis
                              grid: {
                                display: false // Hide grid lines
                              }
                            },
                            y: {
                              display: false, // Hide the y-axis
                              grid: {
                                display: false // Hide grid lines
                              },
                              beginAtZero: true
                            }
                          },
                          elements: {
                            line: {
                              borderWidth: 2 // Thickness of the line
                            }
                          }
                        }
                      });
                    </script>
                  </div>
                  <div class="rounded-lg border p-4 pt-1 pl-2 pb-0 w-[240px] flex-1 flex flex-col justify-between relative overflow-hidden">
                    <div>
                                          <!-- Text Elements with Higher Z-Index -->
                    <h2 class="text-lg font-semibold">Total Daily Users</h2>
                      <!-- Subheading -->
                      <p class="text-sm font-semibold">92 Users</p>
                    </div>

                    
                    <!-- Container for the Chart with Lower Z-Index -->
                    <div class="absolute bottom-0 left-0 w-full h-24 z-0">
                      <!-- Canvas for Chart.js -->
                      <canvas id="lineChartStatic8" aria-label="Service Usage Line Chart" role="img" class="w-full h-full"></canvas>
                    </div>
                    
                    <!-- Include Chart.js from CDN -->
                    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                    <script>
                      // Initialize the chart
                      const ctxStatic8 = document.getElementById('lineChartStatic8').getContext('2d');
                      const lineChartStatic8 = new Chart(ctxStatic8, {
                        type: 'line',
                        data: {
                          labels: ['Start', 'Q1', 'Q2', 'Mid', 'Q3', 'Q4', 'End'],
                          datasets: [{
                            label: 'Service Usage',
                            data: [0, 20, 53, 50, 30, 60, 100], // More data points
                            fill: true, // Enable fill under the line
                            backgroundColor: 'rgba(0, 123, 255, 0.2)', // Semi-transparent blue
                            borderColor: '#007bff', // Line color
                            tension: 0.4, // Smooth curves
                            pointRadius: 5, // Add points
                          }]
                        },
                        options: {
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false // Hide the legend for a cleaner look
                            },
                            tooltip: {
                              enabled: true // Enable tooltips
                            }
                          },
                          scales: {
                            x: {
                              display: false, // Hide the x-axis
                              grid: {
                                display: false // Hide grid lines
                              }
                            },
                            y: {
                              display: false, // Hide the y-axis
                              grid: {
                                display: false // Hide grid lines
                              },
                              beginAtZero: true
                            }
                          },
                          elements: {
                            line: {
                              borderWidth: 2 // Thickness of the line
                            }
                          }
                        }
                      });
                    </script>
                  </div>
                </div>
                
                                <!-- templates/productana.html -->
                <div class="rounded-lg border  p-4 pl-2 pb-0 pt-2 flex-1 h-[220px] relative">
                  <!-- Heading -->
                  <h2 class="text-lg font-semibold">AI vs Manual Interactions</h2>
                  <!-- Subheading -->
                  <p class="text-sm text-gray-600 ">Comparison of interaction types</p>
                  <!-- Chart Container -->
                  <div id="newService2Chart9" class="absolute bottom-0 left-0 w-full h-[150px]"></div>
                  <script>
                    am4core.ready(function() {
                      // Themes
                      am4core.useTheme(am4themes_animated);
                  
                      // Create chart instance
                      var chart = am4core.create("newService2Chart9", am4charts.XYChart);
                  
                      // Disable amCharts logo
                      chart.logo.disabled = true;
                  
                      // Add data
                      chart.data = [
                        { "day": "Mon", "interactions": 120 },
                        { "day": "Tue", "interactions": 152 },
                        { "day": "Wed", "interactions": 138 },
                        { "day": "Thu", "interactions": 145 },
                        { "day": "Fri", "interactions": 162 },
                        { "day": "Sat", "interactions": 136 },
                        { "day": "Sun", "interactions": 127 }
                      ];
                  
                      // Create axes
                      var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
                      categoryAxis.dataFields.category = "day";
                      categoryAxis.renderer.grid.template.location = 0;
                      categoryAxis.renderer.minGridDistance = 30;
                      categoryAxis.renderer.grid.template.disabled = true;
                      categoryAxis.renderer.labels.template.fill = am4core.color("#34495e");
                  
                      var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
                      valueAxis.renderer.grid.template.strokeOpacity = 0.1;
                      valueAxis.renderer.grid.template.strokeWidth = 1;
                      valueAxis.renderer.grid.template.stroke = am4core.color("#000000");
                      valueAxis.renderer.labels.template.fill = am4core.color("#34495e");
                  
                      // Adjust padding to remove extra space
                      chart.paddingBottom = 0; // Remove bottom padding
                      chart.paddingTop = 0;    // Remove top padding
                      chart.paddingLeft = 0;   // Remove left padding
                      chart.paddingRight = 0;  // Remove right padding
                  
                      // Create series
                      var series = chart.series.push(new am4charts.ColumnSeries());
                      series.dataFields.valueY = "interactions";
                      series.dataFields.categoryX = "day";
                      series.name = "AI Interactions";
                      series.columns.template.tooltipText = "{categoryX}: {valueY}";
                  
                      // Set column colors with gradient
                      var gradient = new am4core.LinearGradient();
                      gradient.rotation = 90; // Rotate gradient for top-to-bottom
                      gradient.addColor(am4core.color("#5347ce")); // Top color
                      gradient.addColor(am4core.color("#a9a3e6")); // Bottom color
                      series.columns.template.fill = gradient;
                      series.columns.template.stroke = am4core.color("#5347ce"); // Match border color to top gradient color
                      series.columns.template.strokeWidth = 0.4; // Reduced border width
                      series.columns.template.strokeOpacity = 0.4; // Border opacity
                  
                      // Rounded corners
                      series.columns.template.column.cornerRadiusTopLeft = 6;
                      series.columns.template.column.cornerRadiusTopRight = 6;
                  
                      // Add cursor
                      chart.cursor = new am4charts.XYCursor();
                      chart.cursor.behavior = "none";
                      chart.cursor.lineY.disabled = true;
                      chart.cursor.lineX.disabled = true;
                  
                      // Animate chart on load
                      series.appear(1000);
                      chart.appear(1000, 100);
                  
                      // Ensure the chart resizes with the container
                      window.addEventListener("resize", function() {
                        chart.invalidateSize();
                      });
                    }); // end am4core.ready()
                  </script>

                </div>
                <!-- New Card 3: Flexible Width -->
                <div class="rounded-lg border bg-card text-card-foreground p-4 pl-2 pb-2 pt-2 flex-1 h-[220px] flex flex-col">
                  <!-- Header Section -->
                  <div class="flex justify-between items-center mb-2">
                    <!-- Left Side: Headings -->
                    <div>
                      <h2 class="text-xl font-semibold">Total Users</h2>
                      <p class="text-sm text-gray-600">Overview of total users</p>
                    </div>
                    <!-- Right Side: Dropdown Button -->
                    <div class="relative inline-block text-left">
                      <div>
                        <button type="button" class="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none" id="options-menu" aria-expanded="true" aria-haspopup="true">
                          Options
                          <!-- Dropdown Icon -->
                          <svg class="-mr-1 ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.584l3.71-4.354a.75.75 0 111.14.976l-4.25 5a.75.75 0 01-1.14 0l-4.25-5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                          </svg>
                        </button>
                      </div>

                      <!-- Dropdown Menu -->
                      <div class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                        <div class="py-1" role="none">
                          <!-- Active: "bg-gray-100 text-gray-900", Not Active: "text-gray-700" -->
                          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Action</a>
                          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Another action</a>
                          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Something else here</a>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Chart Container -->
                  <div id="newService3Chart4" class="w-full h-full mt-auto"></div>

                  <!-- Resources (Ensure these are loaded once, preferably in the <head> section) -->
                  <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
                  <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
                  <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>

                  <!-- Chart Code -->
                  <script>
                    am5.ready(function() {

                      // Create root element
                      var root = am5.Root.new("newService3Chart4");

                      // Disable amCharts logo
                      root._logo.dispose();

                      // Set themes
                      root.setThemes([
                        am5themes_Animated.new(root)
                      ]);

                      // Create chart with padding
                      var chart = root.container.children.push(am5xy.XYChart.new(root, {
                        panX: true,
                        panY: true,
                        wheelX: "panX",
                        wheelY: "zoomX",
                        pinchZoomX: true,
                        paddingLeft: 0,    // Added padding
                        paddingRight: 0,   // Added padding
                        paddingTop: 0,     // Added padding
                        paddingBottom: 0   // Added padding
                      }));

                      // Add cursor
                      var cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
                        behavior: "zoomXY"
                      }));
                      cursor.lineY.set("visible", false);

                      // Sample data for total users
                      var data = [
                        { date: new Date(2023, 0, 1).getTime(), value: 1500 },
                        { date: new Date(2023, 1, 1).getTime(), value: 1600 },
                        { date: new Date(2023, 2, 1).getTime(), value: 1700 },
                        { date: new Date(2023, 3, 1).getTime(), value: 1650 },
                        { date: new Date(2023, 4, 1).getTime(), value: 1800 },
                        { date: new Date(2023, 5, 1).getTime(), value: 1900 },
                        { date: new Date(2023, 6, 1).getTime(), value: 2000 },
                        { date: new Date(2023, 7, 1).getTime(), value: 2100 },
                        { date: new Date(2023, 8, 1).getTime(), value: 2200 },
                        { date: new Date(2023, 9, 1).getTime(), value: 2300 }
                      ];

                      var xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
                        maxDeviation: 0.5,
                        baseInterval: {
                          timeUnit: "month",
                          count: 1
                        },
                        renderer: am5xy.AxisRendererX.new(root, {
                          minGridDistance: 50,
                          pan: "zoom"
                        }),
                        tooltip: am5.Tooltip.new(root, {})
                      }));
                
                      // Format X-Axis labels to show only month names without years
                      xAxis.get("renderer").labels.template.setAll({
                        text: "{valueX.formatDate('MMM')}"
                      });
                      var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
                        renderer: am5xy.AxisRendererY.new(root, {
                          pan: "zoom"
                        })
                      }));

                      // Remove horizontal grid lines
                      yAxis.get("renderer").grid.template.setAll({
                        visible: false
                      });

                      // Add series
                      var series = chart.series.push(am5xy.LineSeries.new(root, {
                        name: "Total Users",
                        xAxis: xAxis,
                        yAxis: yAxis,
                        valueYField: "value",
                        valueXField: "date",
                        tooltip: am5.Tooltip.new(root, {
                          labelText: "{valueY}"
                        })
                      }));
                      
                      // Set line color to light blue
                      series.strokes.template.setAll({
                        stroke: am5.color(0xADD8E6), // Light blue color
                        strokeWidth: 2
                      });

                      // Add bullets
                      series.bullets.push(function () {
                        return am5.Bullet.new(root, {
                          sprite: am5.Circle.new(root, {
                            radius: 4,
                            fill: series.get("fill")
                          })
                        });
                      });

                      // Remove scrollbar by not adding it

                      // Bind data
                      series.data.setAll(data);
                      xAxis.data.setAll(data);
                      yAxis.data.setAll(data);

                      // Animate chart on load
                      series.appear(1000);
                      chart.appear(1000, 100);

                      // Ensure the chart resizes with the container
                      window.addEventListener("resize", function() {
                        chart.invalidateSize();
                      });

                    }); // end am5.ready()
                  </script>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  </div>
</body>
</html>