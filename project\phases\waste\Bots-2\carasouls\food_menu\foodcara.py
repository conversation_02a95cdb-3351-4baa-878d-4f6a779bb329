import requests
import os
import json

# Authentication credentials
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')

# Payload data
payload = {
    "friendly_name": "food_menu_cara",
    "language": "en",
    "types": {
        "twilio/carousel": {
            "body": "Explore Our Exquisite Menu Selections!",
            "cards": [
                {
                    "title": "Burrata - €21.50",
                    "body": "Roasted figs, Jamón Ibérico, watercress, truffle & toasted almonds",
                    "media": "https://images-new.vercel.app/foodmenu/burrata.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Add to Cart",
                            "id": "cart_burrata"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "allergy_burrata"
                        }
                    ]
                },
                {
                    "title": "Roasted Pumpkin Dish - €16.00",
                    "body": "Roasted pumpkin with coconut soup",
                    "media": "https://images-new.vercel.app/foodmenu/pumpkinwithcoconut.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Add to Cart",
                            "id": "cart_pumpkin"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "allergy_pumpkin"
                        }
                    ]
                },
                {
                    "title": "Lobster Roll - €28.00",
                    "body": "Buttered brioche, lime mayo, leek, lemon",
                    "media": "https://images-new.vercel.app/foodmenu/lobsterroll.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Add to Cart",
                            "id": "cart_lobster_roll"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "allergy_lobster_roll"
                        }
                    ]
                },
                {
                    "title": "Sirloin - €32.00",
                    "body": "300g - Tender yet succulent with a strip of juicy crackling",
                    "media": "https://images-new.vercel.app/foodmenu/filletsteak.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Add to Cart",
                            "id": "cart_sirloin"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "allergy_sirloin"
                        }
                    ]
                },
                {
                    "title": "Cornfed Chicken - €28.00",
                    "body": "Cornfed chicken breast with truffled potato, spring onion and morels",
                    "media": "https://images-new.vercel.app/foodmenu/chicken.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Add to Cart",
                            "id": "cart_chicken"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "allergy_chicken"
                        }
                    ]
                },
                {
                    "title": "T-bone - €110.00",
                    "body": "900g - Ideal to share, all the sauces & fries",
                    "media": "https://images-new.vercel.app/foodmenu/tbone.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Add to Cart",
                            "id": "cart_tbone"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "allergy_tbone"
                        }
                    ]
                },
                {
                    "title": "Steak & Lobster - €79.00",
                    "body": "Ribeye marbled for flavour 1/2 Lobster with fries, garlic butter, lemon",
                    "media": "https://images-new.vercel.app/foodmenu/steakandlobster.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Add to Cart",
                            "id": "cart_steak_lobster"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "allergy_steak_lobster"
                        }
                    ]
                },
                {
                    "title": "Grilled Salmon - €31.00",
                    "body": "Miso, honey, lemon, watercress, spinach, salmon roe",
                    "media": "https://images-new.vercel.app/foodmenu/grilledsalmon.png",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Add to Cart",
                            "id": "cart_salmon"
                        },
                        {
                            "type": "QUICK_REPLY",
                            "title": "Allergies",
                            "id": "allergy_salmon"
                        }
                    ]
                }
            ]
        }
    }
}

# Make the POST request
response = requests.post(
    'https://content.twilio.com/v1/Content',
    auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN),
    json=payload
)

# Enhanced response handling
print("\n=== Response Details ===")
print(f"Status Code: {response.status_code}")
print("\n=== Headers ===")
for header, value in response.headers.items():
    print(f"{header}: {value}")

print("\n=== Response Content ===")
try:
    # Try to print formatted JSON
    print(json.dumps(response.json(), indent=2))
except json.JSONDecodeError:
    # If not JSON, print raw text
    print(response.text)

print("\n=== Request Details ===")
print(f"Request URL: {response.request.url}")
print(f"Request Method: {response.request.method}")
print("Request Headers:")
for header, value in response.request.headers.items():
    print(f"{header}: {value}")

print("\n=== Timing ===")
print(f"Elapsed Time: {response.elapsed.total_seconds()} seconds")

if response.status_code != 200:
    print("\n=== Error Details ===")
    print(f"Error Status Code: {response.status_code}")
    print("Error Response:", response.text)
