function createCustomTooltipForTips(context) {
    // Create tooltip element if it doesn't exist
    let tooltipEl = document.getElementById('tips-chart-tooltip');

    if (!tooltipEl) {
        tooltipEl = document.createElement('div');
        tooltipEl.id = 'tips-chart-tooltip';
        tooltipEl.style.position = 'absolute';
        tooltipEl.style.pointerEvents = 'none';
        tooltipEl.style.transition = 'left 0.3s ease, top 0.3s ease, opacity 0.3s ease'; // Smoother animation
        tooltipEl.style.zIndex = 1000;
        document.body.appendChild(tooltipEl);
    }

    // Hide tooltip when not active
    const tooltipModel = context.tooltip;
    if (tooltipModel.opacity === 0) {
        tooltipEl.style.opacity = 0;
        return;
    }

    // Set tooltip content
    if (tooltipModel.body) {
        const dataPoint = tooltipModel.dataPoints[0];
        const value = dataPoint.raw;
        const label = tooltipModel.title[0]; // Day label (<PERSON>, <PERSON><PERSON>, etc.)
        
        // Get the correct color from the specific dataset being hovered
        const datasetIndex = dataPoint.datasetIndex;
        const chartBarColor = context.chart.config.data.datasets[datasetIndex].backgroundColor;
        const datasetLabel = context.chart.config.data.datasets[datasetIndex].label || '';
        
        // Using the same color scheme as externalCustomTooltip function
        tooltipEl.innerHTML = `
      <div class="flex items-center rounded-md px-1.5 py-1 shadow-md " 
           style="background-color: var(--dropdown-bg); border: 1px solid var(--dropdown-border, #dee2e6); font-weight:300; color: var(--dropdown-text, #111827);">
        <span class="w-3 h-3 rounded-sm mr-2 flex-shrink-0" style="background-color: ${chartBarColor}"></span>
        <span class="text-sm">${label}</span>
        <span class="ml-4 text-sm font-medium">${value}</span>
      </div>
    `;
    }

    // Position the tooltip with improved positioning
    const position = context.chart.canvas.getBoundingClientRect();
    const tooltipWidth = tooltipEl.offsetWidth;
    
    // Calculate position (center aligned with bar)
    const positionX = position.left + window.pageXOffset + tooltipModel.caretX - (tooltipWidth / 2);
    const positionY = position.top + window.pageYOffset + tooltipModel.caretY - 40; // Position above the bar
    
    // Apply position with smooth transition
    tooltipEl.style.opacity = 1;
    tooltipEl.style.left = positionX + 'px';
    tooltipEl.style.top = positionY + 'px';
}
function externalCustomTooltip(context) {
    let tooltipEl = document.getElementById('chartjs-tooltip');
    if (!tooltipEl) {
        tooltipEl = document.createElement('div');
        tooltipEl.id = 'chartjs-tooltip';
        tooltipEl.innerHTML = '<table></table>';
        // Include left and top in the transition for smooth repositioning
        tooltipEl.style.transition = 'left 0.3s ease, top 0.3s ease, opacity 0.3s ease';
        tooltipEl.style.position = 'absolute';
        tooltipEl.style.pointerEvents = 'none';
        tooltipEl.style.whiteSpace = 'nowrap';
        document.body.appendChild(tooltipEl);
    }
    const tooltipModel = context.tooltip;
    if (tooltipModel.opacity === 0) {
        tooltipEl.style.opacity = 0;
        return;
    }

    // Build the inner HTML
    let innerHtml = '<table>';
    if (tooltipModel.title) {
        tooltipModel.title.forEach(title => {
            innerHtml += `
    <tr>
        <th style="text-align:left; padding:2px 4px; border-bottom:1px solid var(--dropdown-border, #dee2e6); font-weight:600; color: var(--dropdown-text, #111827);">
            ${title}
        </th>
    </tr>`;
        });
    }
    if (tooltipModel.body) {
        tooltipModel.body.forEach((body, i) => {
            const legend = tooltipModel.labelColors && tooltipModel.labelColors[i];
            const legendColor = legend && legend.backgroundColor ? legend.backgroundColor : 'transparent';
            const bodyText = body.lines.join('');
            innerHtml += `
    <tr>
        <td style="display:flex; align-items:center; padding:4px 8px; color: var(--dropdown-text, #111827);">
            <span style="background:${legendColor}; width:10px; height:10px; border-radius:50%; display:inline-block; margin-right:5px;"></span>
            <span>${bodyText}</span>
        </td>
    </tr>`;
        });
    }
    innerHtml += '</table>';
    tooltipEl.querySelector('table').innerHTML = innerHtml;

    // Calculate initial position based on caretX and caretY
    const position = context.chart.canvas.getBoundingClientRect();
    let left = position.left + window.pageXOffset + tooltipModel.caretX;
    let top = position.top + window.pageYOffset + tooltipModel.caretY;

    // Temporarily set the position to measure size
    tooltipEl.style.opacity = 1;
    tooltipEl.style.left = left + 'px';
    tooltipEl.style.top = top + 'px';
    tooltipEl.style.padding = tooltipModel.options.padding + 'px';

    // Measure the tooltip's bounding rectangle
    const tooltipRect = tooltipEl.getBoundingClientRect();

    // If tooltip overflows to the left, reposition it using the right side of the caret
    if (tooltipRect.left < 0) {
        left = position.left + window.pageXOffset + tooltipModel.caretX + 10;
    }
    // If tooltip overflows to the right of the viewport, reposition it to the left
    if (tooltipRect.right > window.innerWidth) {
        left = position.left + window.pageXOffset + tooltipModel.caretX - tooltipRect.width - 10;
    }
    tooltipEl.style.left = left + 'px';
    tooltipEl.style.top = top + 'px';
}