import os
from twilio.rest import Client
import json


TWILIO_ACCOUNT_SID="**********************************"
TWILIO_AUTH_TOKEN="005d910f85546392a91f58a3878c437c"

# -------------------- Starter Interactive Templates -----------------------------

FOOD_MENU_SID = ""
BEVERAGE_MENU_SID = ""
MASSAGE_INTERACTIVETEMPLATE_SID = ""
SPA_INTERACTIVETEMPLATE_SID = ""
ROOM_INTERACTIVETEMPLATE_SID = ""


# --------------------------------------------------------------------------------

sid = ""

try:
    client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

    message = client.messages.create(
        content_sid=sid,
        to="whatsapp:+************",
        from_="whatsapp:+***********"
    )

    print(f"Message sent using SID {sid}. Message SID: {message.sid}")

except Exception as e:
    print(f"Error sending message: {str(e)}")