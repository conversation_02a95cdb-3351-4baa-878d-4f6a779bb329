from flask import Flask, jsonify
import requests

app = Flask(__name__)

SUPABASE_URL = 'https://nuqxdjuaoccswunhqixz.supabase.co'
SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im51cXhkanVhb2Njc3d1bmhxaXh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTcxMjI2OTcsImV4cCI6MjAzMjY5ODY5N30.sHkkzEb5oCTlLB3MQ0420XtJpURXW1DIHuHm4M9kDPI'

@app.route('/customers', methods=['GET'])
def get_customers():
    url = f"{SUPABASE_URL}/rest/v1/firstrowdash"
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f"Bearer {SUPABASE_ANON_KEY}"
    }
    response = requests.get(url, headers=headers)
    return jsonify(response.json())

if __name__ == '__main__':
    app.run(debug=True)


#------------------------