import os
import sys
from openai import OpenAI
from typing import List, Dict, Any
import time
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Check if the API key is available
if not os.getenv("OPENAI_API_KEY"):
    print("Error: OPENAI_API_KEY not found in .env file")
    sys.exit(1)

# Define system prompt with strict instructions for direct responses
SYSTEM_PROMPT = """You are a hotel assistant AI called guest genius, be professional, friendly and charming and make sure you don't make any info up and be short, precise and direct"""

# Define data file path
DATA_FILE_PATH = os.path.join("data", "data.txt")

# Initialize OpenAI client
client = OpenAI()

def setup_vector_store(data_file_path: str) -> str:
    """
    Create a vector store and upload data file

    Args:
        data_file_path: Path to the data file to upload

    Returns:
        The vector store ID
    """
    try:
        print(f"Creating vector store...")
        vector_store = client.vector_stores.create(
            name="Cha<PERSON>botD<PERSON>"
        )

        print(f"Uploading file: {data_file_path}")
        with open(data_file_path, "rb") as file:
            upload_response = client.vector_stores.files.upload_and_poll(
                vector_store_id=vector_store.id,
                file=file
            )

        print(f"Vector store created with ID: {vector_store.id}")
        return vector_store.id
    except Exception as e:
        print(f"Error setting up vector store: {e}")
        sys.exit(1)

def search_vector_store(vector_store_id: str, query: str) -> Dict[str, Any]:
    """
    Search the vector store with a user query

    Args:
        vector_store_id: ID of the vector store to search
        query: User's query

    Returns:
        Search results from the vector store
    """
    try:
        results = client.vector_stores.search(
            vector_store_id=vector_store_id,
            query=query,
            max_num_results=5,
            rewrite_query=True
        )
        return results
    except Exception as e:
        print(f"Error searching vector store: {e}")
        return None

def format_search_results(results) -> str:
    """
    Format search results for the model

    Args:
        results: Results from vector store search

    Returns:
        Formatted string of results
    """
    if not results or not results.data:
        return "No relevant information found."

    formatted_text = ""
    for result in results.data:
        formatted_text += f"--- Source: {result.filename} ---\n"
        for content_part in result.content:
            if hasattr(content_part, 'text'):
                formatted_text += f"{content_part.text}\n"

    return formatted_text

def generate_response(query: str, search_results) -> str:
    """
    Generate a response based on search results

    Args:
        query: User's original query
        search_results: Results from the vector store search

    Returns:
        Generated response
    """
    formatted_results = format_search_results(search_results)

    try:
        completion = client.chat.completions.create(
            model="gpt-4o-2024-11-20",
            messages=[
                {
                    "role": "system",
                    "content": SYSTEM_PROMPT
                },
                {
                    "role": "user",
                    "content": f"Based on this data: {formatted_results}\n\nAnswer: {query}"
                }
            ],
            temperature=0.7,  # Lower temperature for even more direct responses
            max_tokens=4000     # Limit token count to enforce brevity
        )

        return completion.choices[0].message.content
    except Exception as e:
        print(f"Error generating response: {e}")
        return "Data unavailable."

def main():
    """Main function to run the retrieval agent"""
    print("Guest Genius Ready")
    print("------------------")

    # Check if data.txt exists
    if not os.path.exists(DATA_FILE_PATH):
        print(f"Error: File '{DATA_FILE_PATH}' not found.")
        return

    print("Initializing...")
    vector_store_id = setup_vector_store(DATA_FILE_PATH)

    print("\nReady. Type 'exit' to quit.")

    while True:
        query = input("\n> ").strip()

        if query.lower() in ['exit', 'quit']:
            print("Goodbye.")
            break

        if not query:
            continue

        results = search_vector_store(vector_store_id, query)

        if results:
            response = generate_response(query, results)
            print(response)
        else:
            print("No data available on that.")

if __name__ == "__main__":
    main()
