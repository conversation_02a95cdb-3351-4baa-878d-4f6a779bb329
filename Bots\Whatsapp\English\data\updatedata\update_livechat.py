import requests
import json
from datetime import datetime, timedelta

SUPABASE_URL = 'https://nuqxdjuaoccswunhqixz.supabase.co'
SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im51cXhkanVhb2Njc3d1bmhxaXh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTcxMjI2OTcsImV4cCI6MjAzMjY5ODY5N30.sHkkzEb5oCTlLB3MQ0420XtJpURXW1DIHuHm4M9kDPI'

def delete_all_chats():
    """Deletes all records from the 'chats' table in Supabase."""
    table_name = "chats"
    url = f"{SUPABASE_URL}/rest/v1/{table_name}"
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        # First, get all records to confirm deletion
        response = requests.get(url, headers=headers, params={"select": "*"})
        response.raise_for_status()
        data = response.json()
        record_count = len(data)
        
        # Delete all records
        delete_url = f"{SUPABASE_URL}/rest/v1/{table_name}"
        response = requests.delete(
            delete_url, 
            headers=headers,
            params={"id": "neq.0"}  # Delete all records
        )
        response.raise_for_status()
        
        print(f"Successfully deleted {record_count} records from '{table_name}'")
        return True
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
    except Exception as err:
        print(f"An error occurred: {err}")
    return False

def create_new_chats():
    """Creates 3 new chat conversations with Spanish users about hotel issues."""
    table_name = "chats"
    url = f"{SUPABASE_URL}/rest/v1/{table_name}"
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=minimal"
    }
    
    # Create timestamp for today
    now = datetime.now()
    
    # Chat 1: Air conditioning issue
    chat1 = [
        {
            "user_id": 1,
            "sender": "Carlos Fernández",
            "message": "Hola, el aire acondicionado de mi habitación no funciona correctamente. Hace demasiado calor.",
            "timestamp": (now - timedelta(minutes=45)).isoformat(),
            "customer": "true",
            "phone_number": "34612345678"
        },
        {
            "user_id": 1,
            "sender": "Agent",
            "message": "Lo siento por el inconveniente, Sr. Fernández. Enviaré a un técnico de mantenimiento inmediatamente. ¿Estará en su habitación en los próximos 15 minutos?",
            "timestamp": (now - timedelta(minutes=43)).isoformat(),
            "customer": "false",
            "phone_number": "34612345678"
        },
        {
            "user_id": 1,
            "sender": "Carlos Fernández",
            "message": "Sí, estaré aquí. Gracias por la rápida respuesta.",
            "timestamp": (now - timedelta(minutes=40)).isoformat(),
            "customer": "true",
            "phone_number": "34612345678"
        },
        {
            "user_id": 1,
            "sender": "Agent",
            "message": "El técnico está en camino. ¿Hay algo más en lo que pueda ayudarle mientras tanto?",
            "timestamp": (now - timedelta(minutes=38)).isoformat(),
            "customer": "false",
            "phone_number": "34612345678"
        },
        {
            "user_id": 1,
            "sender": "Carlos Fernández",
            "message": "No, eso es todo por ahora. Gracias.",
            "timestamp": (now - timedelta(minutes=35)).isoformat(),
            "customer": "true",
            "phone_number": "34612345678"
        }
    ]
    
    # Chat 2: Room service request
    chat2 = [
        {
            "user_id": 2,
            "sender": "Elena Rodríguez",
            "message": "Buenas tardes, ¿puedo pedir el servicio de habitaciones? Me gustaría ordenar una cena.",
            "timestamp": (now - timedelta(minutes=120)).isoformat(),
            "customer": "true",
            "phone_number": "34623456789"
        },
        {
            "user_id": 2,
            "sender": "Agent",
            "message": "Por supuesto, Sra. Rodríguez. ¿Le gustaría ver nuestro menú?",
            "timestamp": (now - timedelta(minutes=118)).isoformat(),
            "customer": "false",
            "phone_number": "34623456789"
        },
        {
            "user_id": 2,
            "sender": "Elena Rodríguez",
            "message": "Sí, por favor.",
            "timestamp": (now - timedelta(minutes=115)).isoformat(),
            "customer": "true",
            "phone_number": "34623456789"
        },
        {
            "user_id": 2,
            "sender": "Agent",
            "message": "Le he enviado el menú a su habitación. También puede verlo en la aplicación del hotel. ¿Qué le gustaría ordenar?",
            "timestamp": (now - timedelta(minutes=112)).isoformat(),
            "customer": "false",
            "phone_number": "34623456789"
        },
        {
            "user_id": 2,
            "sender": "Elena Rodríguez",
            "message": "Me gustaría la paella de mariscos y una botella de vino blanco Albariño.",
            "timestamp": (now - timedelta(minutes=108)).isoformat(),
            "customer": "true",
            "phone_number": "34623456789"
        },
        {
            "user_id": 2,
            "sender": "Agent",
            "message": "Excelente elección. Su pedido llegará en aproximadamente 30 minutos. ¿Algo más?",
            "timestamp": (now - timedelta(minutes=105)).isoformat(),
            "customer": "false",
            "phone_number": "34623456789"
        },
        {
            "user_id": 2,
            "sender": "Elena Rodríguez",
            "message": "No, eso es todo. Gracias.",
            "timestamp": (now - timedelta(minutes=103)).isoformat(),
            "customer": "true",
            "phone_number": "34623456789"
        }
    ]
    
    # Chat 3: Lost item
    chat3 = [
        {
            "user_id": 3,
            "sender": "Miguel López",
            "message": "Creo que olvidé mi reloj en el gimnasio del hotel esta mañana. ¿Podrían verificar si alguien lo encontró?",
            "timestamp": (now - timedelta(minutes=180)).isoformat(),
            "customer": "true",
            "phone_number": "34634567890"
        },
        {
            "user_id": 3,
            "sender": "Agent",
            "message": "Lamento escuchar eso, Sr. López. Verificaré con nuestro departamento de objetos perdidos inmediatamente. ¿Puede describir el reloj?",
            "timestamp": (now - timedelta(minutes=178)).isoformat(),
            "customer": "false",
            "phone_number": "34634567890"
        },
        {
            "user_id": 3,
            "sender": "Miguel López",
            "message": "Es un reloj Omega plateado con correa de cuero negro. Tiene un valor sentimental importante para mí.",
            "timestamp": (now - timedelta(minutes=175)).isoformat(),
            "customer": "true",
            "phone_number": "34634567890"
        },
        {
            "user_id": 3,
            "sender": "Agent",
            "message": "Entiendo la importancia. Estoy consultando con el personal del gimnasio y el equipo de limpieza. Le informaré tan pronto como tenga noticias.",
            "timestamp": (now - timedelta(minutes=172)).isoformat(),
            "customer": "false",
            "phone_number": "34634567890"
        },
        {
            "user_id": 3,
            "sender": "Agent",
            "message": "¡Buenas noticias! Uno de nuestros empleados encontró su reloj en el gimnasio. Lo tenemos guardado en la recepción. Puede pasar a recogerlo cuando le sea conveniente.",
            "timestamp": (now - timedelta(minutes=160)).isoformat(),
            "customer": "false",
            "phone_number": "34634567890"
        },
        {
            "user_id": 3,
            "sender": "Miguel López",
            "message": "¡Qué alivio! Muchas gracias. Pasaré a recogerlo en una hora.",
            "timestamp": (now - timedelta(minutes=158)).isoformat(),
            "customer": "true",
            "phone_number": "34634567890"
        },
        {
            "user_id": 3,
            "sender": "Agent",
            "message": "Perfecto. Lo tendremos listo para usted en la recepción. Gracias por su paciencia.",
            "timestamp": (now - timedelta(minutes=155)).isoformat(),
            "customer": "false",
            "phone_number": "34634567890"
        }
    ]
    
    all_chats = chat1 + chat2 + chat3
    
    try:
        # Insert all chat messages
        for chat_message in all_chats:
            response = requests.post(url, headers=headers, json=chat_message)
            response.raise_for_status()
        
        print(f"Successfully created 3 new chat conversations with {len(all_chats)} messages")
        return True
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
    except Exception as err:
        print(f"An error occurred: {err}")
    return False

def get_livechat_data():
    """Fetches data from the 'chats' table in Supabase."""
    table_name = "chats"
    url = f"{SUPABASE_URL}/rest/v1/{table_name}"
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers, params={"select": "*"})
        response.raise_for_status()
        data = response.json()
        print(f"Current data in '{table_name}':")
        for row in data:
            print(row)
        return data
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
    except Exception as err:
        print(f"An error occurred: {err}")
    return None

if __name__ == "__main__":
    print("Checking current chat data...")
    get_livechat_data()
    
    print("\nDeleting all existing chats...")
    if delete_all_chats():
        print("\nCreating new Spanish user chats...")
        create_new_chats()
        
        print("\nVerifying new chat data...")
        get_livechat_data()
    else:
        print("Failed to delete existing chats. Operation aborted.")
