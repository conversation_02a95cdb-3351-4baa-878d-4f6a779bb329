import requests
import json

# Your Twilio account details (replace with your actual Account SID and Auth Token)
ACCOUNT_SID = '**********************************'
AUTH_TOKEN = '005d910f85546392a91f58a3878c437c'

# Twilio API endpoint for content creation
url = 'https://content.twilio.com/v1/Content'

# Payload data for the carousel with room booking menu items and descriptions
payload = {
    "friendly_name": "roombooking",
    "language": "en",
    "types": {
        "twilio/carousel": {
            "body": "Explore our luxurious room options!",
            "cards": [
                {
                    "title": "Junior Suite",
                    "body": "Cozy 34 m² suite with premium amenities. Perfect for a relaxing stay.",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/deluxe.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Confirm Reservation",
                            "id": "book_junior_suite"
                        }
                    ]
                },
                {
                    "title": "The Raid",
                    "body": "Spacious 380 m² Presidential Suite with 40 m² terrace. Ideal for families or long stays.",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/ultradeluxe.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Confirm Reservation",
                            "id": "book_the_raid"
                        }
                    ]
                },
                {
                    "title": "Junior Suite Deluxe",
                    "body": "Bright 40 m² suite with modern tech and Mallorcan palace sophistication.",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/ultra2.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Confirm Reservation",
                            "id": "book_junior_suite_deluxe"
                        }
                    ]
                },
                {
                    "title": "Executive Suite",
                    "body": "60 m² suite with kitchen, ideal for extended stays. Serene and spacious.",
                    "media": "https://jadhavharshh.github.io/HOSTEDIMAGES/static/ultra3.jpeg",
                    "actions": [
                        {
                            "type": "QUICK_REPLY",
                            "title": "Confirm Reservation",
                            "id": "book_executive_suite"
                        }
                    ]
                }
            ]
        }
    }
}


# Make the POST request to the Twilio API
response = requests.post(
    url,
    auth=(ACCOUNT_SID, AUTH_TOKEN),
    headers={'Content-Type': 'application/json'},
    data=json.dumps(payload)
)

# Check the response status
if response.status_code == 201:
    print("Carousel created successfully!")
    print(response.json())
else:
    print(f"Failed to create carousel: {response.status_code}")
    print(response.text)