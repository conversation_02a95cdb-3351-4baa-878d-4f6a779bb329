# ==================== IMPORTS AND CONFIGURATIONS ====================
from flask import Flask, request, render_template, jsonify
from twilio.rest import Client
from dotenv import load_dotenv
import os
import json
import textwrap
import time
import re
import requests
from datetime import datetime, timedelta  # Import datetime and timedelta
import random  # Import random module for catalogue selection

# ==================== GLOBAL VARIABLES ====================

# ==================== DEPLOYMENT =========================

# --- Supabase Configuration ---
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY')

# --- Terminal Output Configuration ---
TERMINAL_RAW_OUTPUT = True

# --- Twilio Template SIDs ---
STARTER_MESSAGE_TEMPLATE = "HXa2ef28348a40ef36371b6631ea278877"  # Template SID for starter message
BOOK_SPA_TEMPLATE = "HX0ea1611c7a662becc730c2b9dc788554"  # Template SID for spa booking
ORDER_NOW_TEMPLATE = "HX8753a155bd88ef79de7699c8e6558362"  # Template SID for order now
ORDER_FOOD_TEMPLATE = "HX74a4f19dc28d264a2f110d1259544995"  # Template SID for order food
ORDER_BEVERAGES_TEMPLATE = "HXa95d31210871c5d9f80465bd7177d3bc"  # Template SID for order beverages
BOOK_ROOM_TEMPLATE = "HX90086324ff4757c28a22446152eab6e6"  # Template SID for room booking (initial selection)
NEW_OPTION_BUTTON_TEMPLATE = "HXd203b1689cac21c0cef3d1d926b525b2"  # Template SID for date selection (Rooms)
REVIEW_TEMPLATE = "HXed90b6b9e2c773c459b6860b44b81292"  # Template SID for service review
TIP_TEMPLATE = "HX2df9ec3a8bfd5cffb7eb9d38951f806b"  # Template SID for tip selection
NIGHTS_SELECTION_TEMPLATE = "HX2ffc5e1151cde6ab03cb8d944b6d3bfe" # Template SID for nights selection
MASSAGE_DATE_SELECTION_TEMPLATE = "HXf5729041dbb8edc8207440c51e606744" # Template SID for date selection (Massages)
MASSAGE_TYPE_SELECTION_TEMPLATE = "HX37984b929b741f01a1d97c120da1cefb" # Template SID for massage type selection
SPA_DATE_SELECTION_TEMPLATE = "HXd8a2e74e68d6f4679dc5b96fdeb66cd7" # Template SID for spa date selection
SPA_TIME_SLOT_SELECTION_TEMPLATE = "HX8373674c94d02d0576fbc9de4bbba938" # Template SID for spa time slot selection
EXPLORE_EXPERIENCES_TEMPLATE = "HX50460e90855bb0fd7417eccb26e4e63a" # Template SID for explore experiences

# --- Food Allergy Information ---
# Manually configured allergies information for each dish with 100% accuracy
DISH_ALLERGIES = {
    "burrata": "Burrata\nAllergens: Dairy (burrata), Nuts (almonds), Sulphites (truffle), Pork (Jamón Ibérico)",
    "pumpkin": "Roasted Pumpkin Dish\nAllergens: Tree Nuts (coconut)",
    "lobster_roll": "Lobster Roll\nAllergens: Crustaceans (lobster), Eggs (mayonnaise), Dairy (buttered brioche), Gluten (brioche), Allium (leek), Citrus (lime, lemon)",
    "sirloin": "Sirloin\nAllergens: None explicitly listed, but may contain Sulphites (depending on seasoning)",
    "chicken": "Cornfed Chicken\nAllergens: Dairy (truffled potato), Mushrooms (morels), Allium (spring onion)",
    "tbone": "T-Bone\nAllergens: None explicitly listed, but possible Sulphites (depending on sauces)",
    "steak_lobster": "Steak & Lobster\nAllergens: Crustaceans (lobster), Dairy (butter), Sulphites (garlic butter), Possible Gluten (depending on fries preparation), Citrus (lemon)",
    "salmon": "Grilled Salmon\nAllergens: Fish (salmon, roe), Soy (miso), Honey (possible allergen for some), Sulphites (depending on miso)"
}

# --- Food Catalogue Configuration ---
# Products list for the food catalogue
FOOD_PRODUCTS = [
    # Room Booking
    {"id": "98o9bhjshv", "section_title": "Room Booking"},
    {"id": "6izhhyylks", "section_title": "Room Booking"},
    {"id": "c0qd6ldgkt", "section_title": "Room Booking"},
    {"id": "6wz8tf0n7y", "section_title": "Room Booking"},
    # Food Menu
    {"id": "cckhxov5k2", "section_title": "Food Menu"},
    {"id": "zwuw3m67mn", "section_title": "Food Menu"},
    {"id": "42uaridjbf", "section_title": "Food Menu"},
    {"id": "jflfigxy78", "section_title": "Food Menu"},
    {"id": "eyb1hi65xl", "section_title": "Food Menu"},
    {"id": "n86o421iyw", "section_title": "Food Menu"},
    {"id": "0j2d89sy2t", "section_title": "Food Menu"},
    {"id": "rz84jsys6a", "section_title": "Food Menu"},
]

# Catalogue ID and template SID for food menu
FOOD_CATALOG_ID = '8855764864434585'
FOOD_CATALOGUE_TEMPLATE = 'HXcd49a2926d65bf604099c6045792ce1c'

# --- Beverage Catalogue Configuration ---
# Products list for the beverage catalogue with their IDs
BEVERAGE_PRODUCTS = [
    # Beers
    {"id": "18dwmdtmcw", "section_title": "Beers"},
    {"id": "hx5xv73rup", "section_title": "Beers"},
    {"id": "tc4x3fd2pz", "section_title": "Beers"},
    {"id": "vp24hk5pep", "section_title": "Beers"},
    # Cocktails
    {"id": "te31ijpqay", "section_title": "Cocktails"},
    {"id": "jobld33phx", "section_title": "Cocktails"},
    {"id": "m098oit47h", "section_title": "Cocktails"},
    {"id": "eg5p6rf2qt", "section_title": "Cocktails"},
    {"id": "263kpuc458", "section_title": "Cocktails"},
    {"id": "9e35mhkz83", "section_title": "Cocktails"},
    {"id": "6hyksmnajg", "section_title": "Cocktails"},
    # Sodas
    {"id": "poa3uwk0rk", "section_title": "Sodas"},
    {"id": "dy1l2losvg", "section_title": "Sodas"},
    {"id": "oq5pu8hsus", "section_title": "Sodas"},
    # Water
    {"id": "uypqyg2797", "section_title": "Water"},
    {"id": "a735apzlz1", "section_title": "Water"},
    {"id": "hrrtempkle", "section_title": "Water"},
    {"id": "8gl19qngmi", "section_title": "Water"},
    # Sweet Wine
    {"id": "u55oawwalq", "section_title": "Sweet Wine"},
    {"id": "u9gdek4dlj", "section_title": "Sweet Wine"},
    {"id": "ie2xu8ctm4", "section_title": "Sweet Wine"},
    # Red Wine
    {"id": "7pf7drhr96", "section_title": "Red Wine"},
    {"id": "qj6u5zqpyt", "section_title": "Red Wine"},
    {"id": "gaps0z8wsa", "section_title": "Red Wine"},
    {"id": "g8hmav5qic", "section_title": "Red Wine"},
    # Rosé & White Wine
    {"id": "98zaob4b96", "section_title": "Rosé & White Wine"},
    {"id": "ie748x26kh", "section_title": "Rosé & White Wine"},
    {"id": "cl7rw8zdog", "section_title": "Rosé & White Wine"},
    {"id": "b9aa5e095z", "section_title": "Rosé & White Wine"},
    {"id": "ozul6wsc5w", "section_title": "Rosé & White Wine"},
    {"id": "hny2i5u8y2", "section_title": "Rosé & White Wine"},
    {"id": "zytd4p6jfy", "section_title": "Rosé & White Wine"},
    {"id": "9xqptp2ej3", "section_title": "Rosé & White Wine"},
    # Sparkling Wine
    {"id": "j57kbkoph4", "section_title": "Sparkling Wine"},
    {"id": "hetrpx8zj1", "section_title": "Sparkling Wine"},
    {"id": "8jo8zpo90p", "section_title": "Sparkling Wine"},
]

# Catalogue ID and template SID for beverage menu (using the same as food for now)
BEVERAGE_CATALOG_ID = '8855764864434585'
BEVERAGE_CATALOGUE_TEMPLATE = 'HXcd49a2926d65bf604099c6045792ce1c'

# Flag to control whether to send catalog messages
# Set to False to disable catalog messages and only send text menus
# Set to True when you want to enable the catalog feature again
# Disabled due to WhatsApp error 131009: "None of the products provided could be sent. Please check your catalog."
SEND_BEVERAGE_CATALOG = True

# Beverage menu text with prices and content IDs
BEVERAGE_MENU_TEXT = {
    "Beers": """Beers
- Coronita — €30.00 (Content ID: 18dwmdtmcw)
- Estrella Galicia — €30.00 (Content ID: hx5xv73rup)
- Estrella Galicia 0.0% (Non-Alcoholic) — €30.00 (Content ID: tc4x3fd2pz)
- Peroni — €30.00 (Content ID: vp24hk5pep)""",

    "Cocktails": """Cocktails
- Beatnik Spritz — €30.00 (Content ID: te31ijpqay)
- Floral Fusion — €30.00 (Content ID: jobld33phx)
- Blake Vission — €30.00 (Content ID: m098oit47h)
- Duck Pond — €30.00 (Content ID: eg5p6rf2qt)
- Ciudad Del SOI — €30.00 (Content ID: 263kpuc458)
- La Passion — €30.00 (Content ID: 9e35mhkz83)
- Tiki Punch — €30.00 (Content ID: 6hyksmnajg)""",

    "Sodas": """Sodas
- Summer Cooler — €40.00 (Content ID: poa3uwk0rk)
- Mr Ginger — €40.00 (Content ID: dy1l2losvg)
- Berries Mojito — €40.00 (Content ID: oq5pu8hsus)""",

    "Water": """Water
- Creative Tonic Water — €20.00 (Content ID: uypqyg2797)
- Exotic Yuzu Sensation — €20.00 (Content ID: a735apzlz1)
- Fever Tree Tonic — €20.00 (Content ID: hrrtempkle)
- Zero Azucar Tonic — €20.00 (Content ID: 8gl19qngmi)""",

    "Sweet Wine": """Sweet Wine
- Morentia Cream — €30.00 (Content ID: u55oawwalq)
- Castano Tinto Dulce — €30.00 (Content ID: u9gdek4dlj)
- Jose Pariente (Sweet) — €30.00 (Content ID: ie2xu8ctm4)""",

    "Red Wine": """Red Wine
- Puro Red — €50.00 (Content ID: 7pf7drhr96)
- Dos Marias — €50.00 (Content ID: qj6u5zqpyt)
- Gomez Cruzado — €50.00 (Content ID: gaps0z8wsa)
- Viña Sastre Roble — €50.00 (Content ID: g8hmav5qic)""",

    "Rosé & White Wine": """Rosé & White Wine
- Puro Rosé — €40.00 (Content ID: 98zaob4b96)
- Puro White — €45.00 (Content ID: ie748x26kh)
- Le Bijou Sophie — €40.00 (Content ID: cl7rw8zdog)
- Barbuntin — (Content ID: b9aa5e095z)
- Jose Pariente (White) — €45.00 (Content ID: ozul6wsc5w)
- Cucu — €45.00 (Content ID: hny2i5u8y2)
- Miraval Rosé — €40.00 (Content ID: zytd4p6jfy)
- Jean Leon — (Content ID: 9xqptp2ej3)""",

    "Sparkling Wine": """Sparkling Wine
- Moët & Chandon Ice — €35.00 (Content ID: j57kbkoph4)
- Moët & Chandon — (Content ID: hetrpx8zj1)
- Veuve Clicquot — (Content ID: 8jo8zpo90p)""",

    "All": """Beers
- Coronita — €30.00 (Content ID: 18dwmdtmcw)
- Estrella Galicia — €30.00 (Content ID: hx5xv73rup)
- Estrella Galicia 0.0% (Non-Alcoholic) — €30.00 (Content ID: tc4x3fd2pz)
- Peroni — €30.00 (Content ID: vp24hk5pep)

Cocktails
- Beatnik Spritz — €30.00 (Content ID: te31ijpqay)
- Floral Fusion — €30.00 (Content ID: jobld33phx)
- Blake Vission — €30.00 (Content ID: m098oit47h)
- Duck Pond — €30.00 (Content ID: eg5p6rf2qt)
- Ciudad Del SOI — €30.00 (Content ID: 263kpuc458)
- La Passion — €30.00 (Content ID: 9e35mhkz83)
- Tiki Punch — €30.00 (Content ID: 6hyksmnajg)

Sodas
- Summer Cooler — €40.00 (Content ID: poa3uwk0rk)
- Mr Ginger — €40.00 (Content ID: dy1l2losvg)
- Berries Mojito — €40.00 (Content ID: oq5pu8hsus)

Water
- Creative Tonic Water — €20.00 (Content ID: uypqyg2797)
- Exotic Yuzu Sensation — €20.00 (Content ID: a735apzlz1)
- Fever Tree Tonic — €20.00 (Content ID: hrrtempkle)
- Zero Azucar Tonic — €20.00 (Content ID: 8gl19qngmi)

Sweet Wine
- Morentia Cream — €30.00 (Content ID: u55oawwalq)
- Castano Tinto Dulce — €30.00 (Content ID: u9gdek4dlj)
- Jose Pariente (Sweet) — €30.00 (Content ID: ie2xu8ctm4)

Red Wine
- Puro Red — €50.00 (Content ID: 7pf7drhr96)
- Dos Marias — €50.00 (Content ID: qj6u5zqpyt)
- Gomez Cruzado — €50.00 (Content ID: gaps0z8wsa)
- Viña Sastre Roble — €50.00 (Content ID: g8hmav5qic)

Rosé & White Wine
- Puro Rosé — €40.00 (Content ID: 98zaob4b96)
- Puro White — €45.00 (Content ID: ie748x26kh)
- Le Bijou Sophie — €40.00 (Content ID: cl7rw8zdog)
- Barbuntin — (Content ID: b9aa5e095z)
- Jose Pariente (White) — €45.00 (Content ID: ozul6wsc5w)
- Cucu — €45.00 (Content ID: hny2i5u8y2)
- Miraval Rosé — €40.00 (Content ID: zytd4p6jfy)
- Jean Leon — (Content ID: 9xqptp2ej3)

Sparkling Wine
- Moët & Chandon Ice — €35.00 (Content ID: j57kbkoph4)
- Moët & Chandon — (Content ID: hetrpx8zj1)
- Veuve Clicquot — (Content ID: 8jo8zpo90p)"""
}

# --- Staff Contact Information ---
STAFF_CONTACT = "For assistance, please call the staff at +34 603424778"
STAFF_CHAT_MESSAGE = "The staff has been notified to chat with you regarding your issue. Please wait for their response. To end the chat please type /endchat."

# --- Room Booking Special Messages ---
ROOM_SPECIAL_MESSAGES = [
    "Discount Request\nThank you for your room booking, our best available rates are already applied, but we'll make sure your stay is extra special!",
    "Bottle of Wine Upon Arrival\nThank you for your room booking, we'd be happy to arrange a bottle of wine in your room for your arrival – shall we go ahead?"
]

# --- Terminal Styling Colors ---
# Modern color scheme
BLUE = '\033[38;2;100;149;237m'    # Cornflower Blue
GREEN = '\033[38;2;72;187;120m'    # Emerald Green
RED = '\033[38;2;235;87m'       # Soft Red
RESET = '\033[0m'
DIVIDER = '─' * 100  # More minimal and elegant divider

# --- Authorization Control ---
# Set ALLOW_ALL_NUMBERS to True to allow everyone, or False to restrict to AUTHORIZED_NUMBERS
ALLOW_ALL_NUMBERS = True

AUTHORIZED_NUMBERS = [
    "whatsapp:+************",  # Original authorized number
    "whatsapp:+************",   # New authorized number
    "whatsapp:+***********",   # New authorized number
    "whatsapp:+***********"
]

# ==================== APP INITIALIZATION ====================
# Load environment variables
load_dotenv()

app = Flask(__name__)

# Initialize Twilio client
client = Client(
    os.getenv('TWILIO_ACCOUNT_SID'),
    os.getenv('TWILIO_AUTH_TOKEN')
)

# ==================== BOOKING STATE MANAGEMENT ====================
# Define the directory and file path for booking state
STATE_DIR = 'data'
BOOKING_STATE_FILE = os.path.join(STATE_DIR, 'booking_state.json')
user_booking_state = {} # In-memory store for user booking progress

def load_booking_state():
    """Loads booking state from the JSON file."""
    global user_booking_state
    try:
        # Use the full path defined in BOOKING_STATE_FILE
        if os.path.exists(BOOKING_STATE_FILE):
            with open(BOOKING_STATE_FILE, 'r') as f:
                user_booking_state = json.load(f)
        else:
            user_booking_state = {} # Initialize if file doesn't exist
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error loading booking state: {e}. Initializing empty state.")
        user_booking_state = {}

def save_booking_state():
    """Saves the current booking state to the JSON file."""
    global user_booking_state
    try:
        # Ensure the state directory exists
        os.makedirs(STATE_DIR, exist_ok=True)
        # Use the full path defined in BOOKING_STATE_FILE
        with open(BOOKING_STATE_FILE, 'w') as f:
            json.dump(user_booking_state, f, indent=4)
    except IOError as e:
        print(f"Error saving booking state: {e}")

# ==================== HELPER FUNCTIONS ====================

def is_authorized(phone_number):
    """Checks if the sender's phone number is authorized."""
    if ALLOW_ALL_NUMBERS:
        return True
    return phone_number in AUTHORIZED_NUMBERS

def wrap_text(text, width=90):
    """Wraps text for terminal display."""
    return textwrap.fill(text, width=width)

def get_next_available_user_id(sender_number):
    """
    Gets the user ID for a sender number or assigns a new one if not found.
    First checks if the sender already has a user_id in the database,
    otherwise assigns the next available ID.
    
    Args:
        sender_number: The sender's phone number
        
    Returns:
        int: The user ID for this sender
    """
    try:
        # Extract actual phone number without WhatsApp prefix
        phone_number = sender_number.replace("whatsapp:+", "") if sender_number.startswith("whatsapp:+") else sender_number
        
        # Prepare the headers for Supabase
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }
        
        # First, check if this phone number already has an assigned user_id
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/chats?phone_number=eq.{phone_number}&select=user_id",
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            if data:
                # This phone number already has messages with a user_id, use the same ID
                return data[0]['user_id']
            
            # If no existing user_id, we need to assign a new one
            # Get all existing user_ids to find an available one
            all_ids_response = requests.get(
                f"{SUPABASE_URL}/rest/v1/chats?select=user_id",
                headers=headers
            )
            
            if all_ids_response.status_code == 200:
                all_data = all_ids_response.json()
                used_ids = set(item['user_id'] for item in all_data if 'user_id' in item)
                
                # Find the next available ID starting from 1
                # Skip 3 since it's already in use (as mentioned in the task)
                for user_id in range(1, 100):  # Limit to reasonable range
                    if user_id != 3 and user_id not in used_ids:
                        return user_id
                
                # If all ids in range are used, return a higher number
                return max(used_ids) + 1 if used_ids else 1
        
        # If API calls fail, fall back to hash-based ID but avoid using 3
        fallback_id = abs(hash(sender_number)) % 10
        return 4 if fallback_id == 3 else fallback_id
            
    except Exception as e:
        print(f"Error finding user_id for {sender_number}: {e}")
        # Default fallback - ensure we don't use 3
        fallback_id = abs(hash(sender_number)) % 10
        return 4 if fallback_id == 3 else fallback_id

def send_chat_to_supabase(sender_number, sender_name, phone_number, message, is_customer=False):
    """
    Sends a chat message to the Supabase chats table.

    Args:
        sender_number: The WhatsApp sender number (for hashing user_id)
        sender_name: The display name of the sender (ProfileName)
        phone_number: The WhatsApp ID (WaId)
        message: The message content
        is_customer: Boolean indicating if the message is from a customer (True) or agent (False)

    Returns:
        Boolean indicating success or failure
    """
    try:
        # Prepare the headers for Supabase
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }

        # Get the next available user ID
        user_id = get_next_available_user_id(sender_number)

        # Prepare the data for the chat message
        chat_data = {
            "user_id": user_id,
            "sender": sender_name if is_customer else "Agent",
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "customer": is_customer,
            "phone_number": phone_number  # Always include phone_number regardless of customer/agent
        }

        # Send the data to Supabase
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/chats",
            headers=headers,
            json=chat_data
        )

        # Check if the request was successful
        if response.status_code in [200, 201, 204]:
            print(f"Chat message sent to Supabase successfully: {message[:30]}...")
            return True
        else:
            print(f"Error sending chat message to Supabase: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"Exception sending chat message to Supabase: {e}")
        return False

def add_order_to_task_list(sender_number, order_items, quantities, prices, total_price, profile_name='Guest', task_id=None):
    """
    Adds an order to the Task_list table in Supabase.

    Args:
        sender_number: The phone number of the sender
        order_items: List of ordered items
        quantities: Dictionary of quantities for each item
        prices: Dictionary of prices for each item
        total_price: Total price of the order
        profile_name: Name of the customer (default: 'Guest')
        task_id: Task ID to use in Supabase (default: None, will generate a new one in TASK-XXXX format)

    Returns:
        Boolean indicating success or failure
    """
    try:
        # Prepare the headers for Supabase
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }

        # Use the provided task_id or generate a unique Task ID in TASK-XXXX format
        if not task_id:
            task_id = f"TASK-{random.randint(1000, 9999)}"

        # Extract phone number from sender number
        if sender_number.startswith("whatsapp:+"):
            # Remove the "whatsapp:+" prefix to get just the number
            phone_number = int(sender_number.replace("whatsapp:+", ""))
        else:
            phone_number = int(sender_number)

        # Format items as comma-separated string
        items_str = ", ".join(order_items)

        # Format quantities as JSON string
        quantities_json = json.dumps({item: f"x{quantities[item]}" for item in quantities})

        # Format prices as JSON string
        prices_json = json.dumps({item: str(prices[item]) for item in prices})

        # Determine category based on items
        # This is a simple implementation - you might want to enhance this logic
        if any(item.lower() in ["beer", "wine", "cocktail", "soda", "water"] for item in order_items):
            category = "Beverage"
        else:
            category = "Food & Beverage"

        # Ensure total_price is an integer
        if isinstance(total_price, float):
            # Convert to integer by rounding
            total_price_int = int(round(total_price))
        else:
            # Try to convert to integer
            try:
                total_price_int = int(round(float(total_price)))
            except (ValueError, TypeError):
                total_price_int = 0
                print(f"Warning: Could not convert total_price to integer: {total_price}")

        # Prepare the data for the task
        task_data = {
            "Task ID": task_id,
            "Items": items_str,
            "Quantity": quantities_json,
            "Category": category,
            "Room Number": 212,  # Hardcoded as specified
            "Phone Number": phone_number,
            "Guest Name": profile_name,
            "Platform": "Whatsapp",  # Hardcoded as specified
            "Language": "English",  # Hardcoded as specified
            "Pricing": prices_json,
            "Total Price": total_price_int  # Use the integer version
        }

        # Send the data to Supabase
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/Task_list",
            headers=headers,
            json=task_data
        )

        # Check if the request was successful
        if response.status_code in [200, 201, 204]:
            print(f"Order added to Task_list successfully: {task_id}")
            return True
        else:
            print(f"Error adding order to Task_list: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"Exception adding order to Task_list: {e}")
        return False

def get_room_type_name(payload):
    """Maps room booking payload to a user-friendly name."""
    room_map = {
        'book_junior_suite_cozy': 'Junior Suite Cozy',
        'book_the_raid': 'The Raid',
        'book_junior_suite_deluxe_bright': 'Junior Suite Deluxe Bright',
        'book_executive_suite': 'Executive Suite'
    }
    return room_map.get(payload, 'Selected Room') # Default if not found

def get_massage_type_name(payload):
    """Maps massage scheduling payload to a user-friendly name."""
    massage_map = {
        'schedule_massage_90': '90 Minute Massage',
        'schedule_massage_60_body': '60 Minute Body Massage',
        'schedule_massage_60_facial': '60 Minute Facial Massage'
    }
    return massage_map.get(payload, 'Selected Massage') # Default if not found

def get_nights_text(payload_or_title):
    """Maps night selection payload/title to user-friendly text."""
    night_map = {
        'night_1': '1 Night',
        'night_2': '2 Nights',
        'night_3': '3 Nights',
        'night_4': '4 Nights',
        'night_5': '5 Nights',
        'night_5_plus': '5+ Nights'
    }
    # Check both payload and title for flexibility
    if payload_or_title in night_map:
        return night_map[payload_or_title]
    elif '1 Night' in payload_or_title: return '1 Night'
    elif '2 Nights' in payload_or_title: return '2 Nights'
    elif '3 Nights' in payload_or_title: return '3 Nights'
    elif '4 Nights' in payload_or_title: return '4 Nights'
    elif '5 Nights' in payload_or_title: return '5 Nights'
    elif '5+ Nights' in payload_or_title: return '5+ Nights'
    else:
        return payload_or_title # Return original if no match

def split_menu_into_chunks(menu_text):
    """Splits a long menu text into smaller chunks to avoid the 1600 character limit."""
    # Split the menu by category sections
    sections = menu_text.strip().split('\n\n')

    chunks = []
    current_chunk = ""

    for section in sections:
        # If adding this section would exceed the limit, start a new chunk
        if len(current_chunk) + len(section) > 1500:  # Leave some buffer
            if current_chunk:  # Only append if not empty
                chunks.append(current_chunk.strip())
            current_chunk = section
        else:
            if current_chunk:  # Add a separator if not the first section in the chunk
                current_chunk += "\n\n"
            current_chunk += section

    # Add the last chunk if not empty
    if current_chunk:
        chunks.append(current_chunk.strip())

    return chunks

# ==================== TERMINAL STYLING FUNCTIONS ====================

def display_status_update(status, message_sid):
    """Displays message status updates in the terminal."""
    if not TERMINAL_RAW_OUTPUT:
        print(f"\n{BLUE}╭{'─' * 98}╮")
        print(f"│ Status: {status} - SID: {message_sid[:8]}...")
        print(f"╰{'─' * 98}╯{RESET}")

def display_incoming_message(sender, message):
    """Displays incoming messages in the terminal."""
    if not TERMINAL_RAW_OUTPUT:
        print(f"\n{GREEN}╭{'─' * 98}╮")
        print(f"│ 📱 INCOMING MESSAGE")
        print(f"├{'─' * 98}┤")
        print(f"│ From: {sender}")
        for line in wrap_text(f"Message: {message}").split('\n'):
            print(f"│ {line}")
        print(f"╰{'─' * 98}╯{RESET}\n")

def display_outgoing_response(response, message_sid):
    """Displays outgoing responses in the terminal."""
    if not TERMINAL_RAW_OUTPUT:
        print(f"{RED}╭{'─' * 98}╮")
        print(f"│ 📤 OUTGOING RESPONSE")
        print(f"├{'─' * 98}┤")
        for line in wrap_text(f"Response: {response}").split('\n'):
            print(f"│ {line}")
        print(f"│ Message SID: {message_sid[:8]}...")
        print(f"╰{'─' * 98}╯{RESET}\n")

def display_raw_webhook_data(data_dict):
    """Displays raw webhook data in the terminal if enabled."""
    if TERMINAL_RAW_OUTPUT:
        pretty_json = json.dumps(data_dict, indent=4)
        print(f"\n{DIVIDER}")
        print(pretty_json)
        print(f"{DIVIDER}\n")

# ==================== WEBHOOK HANDLER ====================
@app.route("/", methods=['POST'])
def webhook():
    global user_booking_state # Ensure we're using the global state

    # --- Webhook Data Processing & Initial Checks ---
    webhook_data = request.values.to_dict()
    display_raw_webhook_data(webhook_data)

    if TERMINAL_RAW_OUTPUT:
        print(request.get_data(as_text=True))

    # Handle message status updates
    if webhook_data.get('MessageStatus') in ['sent', 'delivered']:
        display_status_update(
            webhook_data.get('MessageStatus'),
            webhook_data.get('MessageSid')
        )
        return 'OK'

    # Extract message details
    incoming_msg = webhook_data.get('Body', '').strip()
    sender_number = webhook_data.get('From', '')
    message_type = webhook_data.get('MessageType') # e.g., 'text', 'interactive', 'order'
    button_payload = webhook_data.get('ButtonPayload', '') # For button replies
    list_id = webhook_data.get('ListId', '') # For list replies (e.g., date selection)
    list_title = webhook_data.get('ListTitle', '') # For list replies (e.g., selected date text)
    order_data = webhook_data.get('Order', '') # For order messages

    display_incoming_message(sender_number, incoming_msg or list_title or button_payload or "Interactive Message") # Display something meaningful

    # Skip empty text messages (allow interactive and order messages)
    if not incoming_msg and message_type not in ['interactive', 'order']:
        if not TERMINAL_RAW_OUTPUT:
            print("=== Skipping empty text message webhook ===")
        return 'OK'

    # --- Order Processing ---
    if message_type == 'order':
        try:
            # Parse the order data
            if order_data:
                order_json = json.loads(order_data)
                # Get product items from the order JSON
                product_items = order_json.get('product_items', [])

                # Generate a unique ID number
                order_id = random.randint(1000, 9999)
                # Format for customer display in WhatsApp
                order_number = f"ORDER-ID-{order_id}"
                # Format for Supabase Task_list
                task_id = f"TASK-{order_id}"

                # Calculate total amount
                total_amount = sum(item.get('item_price', 0) * item.get('quantity', 0) for item in product_items)
                currency = product_items[0].get('currency', 'EUR') if product_items else 'EUR'

                # Create a list of ordered items
                ordered_items = []
                # Dictionaries to store quantities and prices for each product
                product_quantities = {}
                product_prices = {}
                product_names = []

                for item in product_items:
                    product_id = item.get('product_retailer_id', '')
                    quantity = item.get('quantity', 0)
                    price = item.get('item_price', 0)

                    # Find product name from FOOD_PRODUCTS or BEVERAGE_PRODUCTS
                    product_name = "Unknown Product"

                    # Check if it's a food product
                    food_found = False
                    for product in FOOD_PRODUCTS:
                        if product['id'] == product_id:
                            # Use the actual dish names based on the product ID
                            if product_id == 'n86o421iyw':
                                product_name = "Grilled salmon"
                            elif product_id == 'zwuw3m67mn':
                                product_name = "T-bone"
                            elif product_id == 'cckhxov5k2':
                                product_name = "Farm raised chicken breast"
                            elif product_id == 'rz84jsys6a':
                                product_name = "Roasted Pumpkin and Coconut Soup"
                            elif product_id == '42uaridjbf':
                                product_name = "Steak and Lobster"
                            elif product_id == 'jflfigxy78':
                                product_name = "Fillet steak"
                            elif product_id == 'eyb1hi65xl':
                                product_name = "Burrata"
                            elif product_id == '0j2d89sy2t':
                                product_name = "Lobster roll"
                            else:
                                # If we don't have a specific mapping, try to get a better name
                                # by looking at the catalog data or using a more generic name
                                product_name = f"Food Item ({product_id})"
                            food_found = True
                            break

                    # If not a food product, check if it's a beverage
                    if not food_found:
                        for product in BEVERAGE_PRODUCTS:
                            if product['id'] == product_id:
                                # Get the category of the beverage
                                category = product.get('section_title', '')

                                # Use the actual beverage names based on the product ID
                                if product_id == '18dwmdtmcw':
                                    product_name = "Coronita"
                                elif product_id == 'hx5xv73rup':
                                    product_name = "Estrella Galicia"
                                elif product_id == 'tc4x3fd2pz':
                                    product_name = "Estrella Galicia 0.0% (Non-Alcoholic)"
                                elif product_id == 'vp24hk5pep':
                                    product_name = "Peroni"
                                elif product_id == 'te31ijpqay':
                                    product_name = "Beatnik Spritz"
                                elif product_id == 'jobld33phx':
                                    product_name = "Floral Fusion"
                                elif product_id == 'm098oit47h':
                                    product_name = "Blake Vission"
                                elif product_id == 'eg5p6rf2qt':
                                    product_name = "Duck Pond"
                                elif product_id == '263kpuc458':
                                    product_name = "Ciudad Del SOI"
                                elif product_id == '9e35mhkz83':
                                    product_name = "La Passion"
                                elif product_id == '6hyksmnajg':
                                    product_name = "Tiki Punch"
                                # Sodas
                                elif product_id == 'poa3uwk0rk':
                                    product_name = "Summer Cooler"
                                elif product_id == 'dy1l2losvg':
                                    product_name = "Mr Ginger"
                                elif product_id == 'oq5pu8hsus':
                                    product_name = "Berries Mojito"
                                # Water
                                elif product_id == 'uypqyg2797':
                                    product_name = "Creative Tonic Water"
                                elif product_id == 'a735apzlz1':
                                    product_name = "Exotic Yuzu Sensation"
                                elif product_id == 'hrrtempkle':
                                    product_name = "Fever Tree Tonic"
                                elif product_id == '8gl19qngmi':
                                    product_name = "Zero Azucar Tonic"
                                # Sweet Wine
                                elif product_id == 'u55oawwalq':
                                    product_name = "Morentia Cream"
                                elif product_id == 'u9gdek4dlj':
                                    product_name = "Castano Tinto Dulce"
                                elif product_id == 'ie2xu8ctm4':
                                    product_name = "Jose Pariente (Sweet)"
                                # Red Wine
                                elif product_id == '7pf7drhr96':
                                    product_name = "Puro Red"
                                elif product_id == 'qj6u5zqpyt':
                                    product_name = "Dos Marias"
                                elif product_id == 'gaps0z8wsa':
                                    product_name = "Gomez Cruzado"
                                elif product_id == 'g8hmav5qic':
                                    product_name = "Viña Sastre Roble"
                                # Rosé & White Wine
                                elif product_id == '98zaob4b96':
                                    product_name = "Puro Rosé"
                                elif product_id == 'ie748x26kh':
                                    product_name = "Puro White"
                                elif product_id == 'cl7rw8zdog':
                                    product_name = "Le Bijou Sophie"
                                elif product_id == 'b9aa5e095z':
                                    product_name = "Barbuntin"
                                elif product_id == 'ozul6wsc5w':
                                    product_name = "Jose Pariente (White)"
                                elif product_id == 'hny2i5u8y2':
                                    product_name = "Cucu"
                                elif product_id == 'zytd4p6jfy':
                                    product_name = "Miraval Rosé"
                                elif product_id == '9xqptp2ej3':
                                    product_name = "Jean Leon"
                                # Sparkling Wine
                                elif product_id == 'j57kbkoph4':
                                    product_name = "Moët & Chandon Ice"
                                elif product_id == 'hetrpx8zj1':
                                    product_name = "Moët & Chandon"
                                elif product_id == '8jo8zpo90p':
                                    product_name = "Veuve Clicquot"
                                else:
                                    # If we don't have a specific mapping, use the category
                                    product_name = f"{category} Item ({product_id})"
                                break

                    # Format the price with 2 decimal places
                    formatted_price = f"{price:.2f}"

                    # Store product information for Task_list
                    product_names.append(product_name)
                    product_quantities[product_name] = quantity
                    product_prices[product_name] = price

                    # Add the item to the ordered items list with better formatting
                    ordered_items.append(f"• {product_name} x{quantity} — {formatted_price} {currency}")

                # Create confirmation message
                confirmation_message = (
                    f"Thank you for your order!\n\n"
                    f"Your order #{order_number} has been confirmed.\n\n"
                    f"Order Details:\n"
                    f"{chr(10).join(ordered_items)}\n\n"
                    f"Total: {total_amount:.2f} {currency}\n\n"
                    f"Our staff will prepare your order shortly. You will be notified when it's ready for delivery or pickup.\n\n"
                    f"If you have any questions, please reply to this message or contact our staff."
                )

                # Get the profile name from the webhook data
                profile_name = webhook_data.get('ProfileName', 'Guest')

                # Add the order to the Task_list table in Supabase with the task_id format
                add_order_to_task_list(sender_number, product_names, product_quantities, product_prices, total_amount, profile_name, task_id)

                # Send confirmation message
                message = client.messages.create(
                    body=confirmation_message,
                    to=sender_number,
                    from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                )
                display_outgoing_response(f"Order confirmation sent for order #{order_number}", message.sid)

                # Wait a moment before sending review and tip messages
                time.sleep(2)

                # Send review message
                try:
                    # Define the content variables for the review template
                    content_vars_review = {
                        "1": "Please rate our service:", # Body text for service review
                        "2": "Select Rating" # Button text for service review
                    }

                    # Send the review message
                    review_message = client.messages.create(
                        content_sid=REVIEW_TEMPLATE,
                        content_variables=json.dumps(content_vars_review),
                        to=sender_number,
                        from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                    )
                    display_outgoing_response("Review request sent", review_message.sid)

                    # Wait a moment before sending the tip message
                    time.sleep(2)

                    # Define the content variables for the tip template
                    content_vars_tip = {
                        "1": "Please select a tip amount:", # Body text for tip
                        "2": "Select Tip" # Button text for tip
                    }

                    # Send the tip message
                    tip_message = client.messages.create(
                        content_sid=TIP_TEMPLATE,
                        content_variables=json.dumps(content_vars_tip),
                        to=sender_number,
                        from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                    )
                    display_outgoing_response("Tip request sent", tip_message.sid)

                except Exception as e:
                    print(f"Error sending review or tip messages: {e}")
                    # Continue even if review/tip messages fail - the order was already confirmed

                return 'OK'
            else:
                # Handle empty order data
                error_message = "We received your order, but there was an issue processing it. Please try again or contact our staff for assistance."
                message = client.messages.create(
                    body=error_message,
                    to=sender_number,
                    from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                )
                display_outgoing_response("Empty order data error message sent", message.sid)
                return 'OK'
        except Exception as e:
            print(f"Error processing order: {e}")
            error_message = "We encountered an issue processing your order. Please try again or contact our staff for assistance."
            message = client.messages.create(
                body=error_message,
                to=sender_number,
                from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )
            display_outgoing_response("Order processing error message sent", message.sid)
            return 'OK'

    # Authorization Check
    if not is_authorized(sender_number):
        unauthorized_message = "Unauthorized access. This service is not available for your number."
        message = client.messages.create(
            from_=os.getenv('TWILIO_WHATSAPP_NUMBER'),
            body=unauthorized_message,
            to=sender_number
        )
        display_outgoing_response("Unauthorized access attempt", message.sid)
        return 'Unauthorized'

    # --- Room Booking Flow ---
    room_payloads = ['book_junior_suite_cozy', 'book_the_raid', 'book_junior_suite_deluxe_bright', 'book_executive_suite']
    if button_payload in room_payloads:
        room_type_name = get_room_type_name(button_payload)
        user_booking_state[sender_number] = {'booking_type': 'room', 'room_type': room_type_name} # Mark as room booking
        save_booking_state()

        today = datetime.now()
        start_date = today + timedelta(days=1)
        dates = [(start_date + timedelta(days=i)).strftime('%d-%m-%Y') for i in range(10)]
        content_vars = {str(i+1): date for i, date in enumerate(dates)}

        message = client.messages.create(
            content_sid=NEW_OPTION_BUTTON_TEMPLATE, # ROOM date selection
            content_variables=json.dumps(content_vars),
            to=sender_number,
            from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )
        display_outgoing_response(f"Room Date selection sent for {room_type_name}", message.sid)
        return 'OK'

    # --- Massage Booking Flow ---
    massage_payloads = ['schedule_massage_90', 'schedule_massage_60_body', 'schedule_massage_60_facial']
    if button_payload in massage_payloads:
        massage_type_name = get_massage_type_name(button_payload)
        user_booking_state[sender_number] = {'booking_type': 'massage', 'massage_type': massage_type_name} # Mark as massage booking
        save_booking_state()

        today = datetime.now()
        start_date = today + timedelta(days=1)
        dates = [(start_date + timedelta(days=i)).strftime('%d-%m-%Y') for i in range(10)]
        content_vars = {str(i+1): date for i, date in enumerate(dates)}

        message = client.messages.create(
            content_sid=MASSAGE_DATE_SELECTION_TEMPLATE, # MASSAGE date selection
            content_variables=json.dumps(content_vars),
            to=sender_number,
            from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )
        display_outgoing_response(f"Massage Date selection sent for {massage_type_name}", message.sid)
        return 'OK'

    # --- Interactive Reply Handling (Dates, Nights, Reviews, Tips, Spa Dates) ---
    if message_type == 'interactive' and (button_payload or list_id): # Check if it's an interactive message with a payload or list ID
        # Handle review ratings (1_star to 5_stars)
        if incoming_msg and incoming_msg.endswith('_stars') or incoming_msg.endswith('_star'):
            # Extract the rating from the incoming message
            rating = incoming_msg.split('_')[0]

            # Create a personalized thank you message based on the rating
            if rating in ['1', '2']:
                thank_you_message = f"Thank you for your {rating}-star review. We're sorry to hear your experience wasn't satisfactory. We value your feedback and will work to improve our service."
            elif rating == '3':
                thank_you_message = f"Thank you for your {rating}-star review. We appreciate your feedback and will continue to work on improving our service to exceed your expectations next time."
            else:  # 4 or 5 stars
                thank_you_message = f"Thank you for your {rating}-star review! We're delighted that you enjoyed your experience with us and look forward to serving you again soon."

            # Send the thank you message
            message = client.messages.create(
                body=thank_you_message,
                to=sender_number,
                from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )
            display_outgoing_response(f"Review thank you message sent for {rating}-star rating", message.sid)
            return 'OK'

        # Handle tip selections
        if incoming_msg and incoming_msg.startswith('tip_'):
            # Extract the tip option from the incoming message
            tip_option = incoming_msg.replace('tip_', '')

            # Handle custom tip amount (option_3)
            if tip_option == 'option_3':
                # Set state to indicate waiting for custom tip amount
                user_booking_state[sender_number] = {'booking_type': 'custom_tip'}
                save_booking_state()

                # Ask for custom tip amount
                custom_tip_prompt = "Please enter your custom tip amount in euros."
                message = client.messages.create(
                    body=custom_tip_prompt,
                    to=sender_number,
                    from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                )
                display_outgoing_response("Custom tip amount prompt sent", message.sid)
                return 'OK'
            else:
                # Handle predefined tip options
                if tip_option == 'option_1':
                    tip_amount = "5%"
                elif tip_option == 'option_2':
                    tip_amount = "10%"
                else:
                    tip_amount = tip_option  # For any other options

                # Send a thank you message for the tip
                thank_you_message = f"Thank you for your generous {tip_amount} tip! We appreciate your support and look forward to serving you again soon."

                message = client.messages.create(
                    body=thank_you_message,
                    to=sender_number,
                    from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                )
                display_outgoing_response(f"Tip thank you message sent for {tip_amount}", message.sid)
                return 'OK'

        # Continue with existing booking flow handling
        current_state = user_booking_state.get(sender_number, {})
        booking_type = current_state.get('booking_type')

        # Room Date Selection
        if list_id.startswith('date_') and booking_type == 'room':
            if 'room_type' not in current_state:
                # Handle missing room type state
                message = client.messages.create(
                    body="Please select a room type first before choosing a date.",
                    to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                )
                if sender_number in user_booking_state: del user_booking_state[sender_number]; save_booking_state()
                display_outgoing_response("Prompted user to select room first (date selection)", message.sid)
                return 'OK'

            selected_date = list_title
            user_booking_state[sender_number]['date'] = selected_date
            save_booking_state()

            message = client.messages.create(
                content_sid=NIGHTS_SELECTION_TEMPLATE, # Send nights selection
                to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )
            display_outgoing_response(f"Nights selection sent for room date {selected_date}", message.sid)
            return 'OK'

        # Massage Date Selection
        elif list_id.startswith('massage_date_') and booking_type == 'massage':
            if 'massage_type' not in current_state:
                # Handle missing massage type state
                message = client.messages.create(
                    body="Sorry, I couldn't find your previous massage selection. Please select a massage type again.",
                    to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                )
                if sender_number in user_booking_state: del user_booking_state[sender_number]; save_booking_state()
                display_outgoing_response("State not found/invalid for massage date selection", message.sid)
                return 'OK'

            selected_date = list_title
            massage_type = current_state['massage_type']

            confirmation_message = (
                f"Thank you for your massage booking request!\n\n"
                f"Your request for a {massage_type} on {selected_date} has been received.\n\n"
                f"Our staff will contact you shortly to confirm the exact time and therapist availability."
            )
            message = client.messages.create(
                body=confirmation_message, to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )

            del user_booking_state[sender_number] # Clear state after confirmation
            save_booking_state()
            display_outgoing_response("Massage booking confirmation sent", message.sid)
            return 'OK'

        # Spa Date Selection (NEW)
        elif list_id.startswith('spa_date_'):
            selected_date = list_title # Get the date from the list title
            user_booking_state[sender_number] = {'booking_type': 'spa', 'date': selected_date}
            save_booking_state()

            # Placeholder values for the content variables.
            content_vars = {
                '1': 'Please select a time slot for your spa session:',  # Body
                '2': 'Available Times',          # List button text
                '3': '9:00 AM - 10:00 AM',       # Item name for spa_time_slot_1
                '4': '10:00 AM - 11:00 AM',      # Item name for spa_time_slot_2
                '5': '11:00 AM - 12:00 PM',      # Item name for spa_time_slot_3
                '6': '1:00 PM - 2:00 PM'         # Item name for spa_time_slot_4
            }

            try:
                message = client.messages.create(
                    content_sid=SPA_TIME_SLOT_SELECTION_TEMPLATE,
                    from_=os.getenv('TWILIO_WHATSAPP_NUMBER'),
                    to=sender_number,
                    content_variables=json.dumps(content_vars)
                )
                display_outgoing_response(f"Spa time slot selection sent for {selected_date}", message.sid)
            except Exception as e:
                print(f"Error sending spa time slot selection message: {e}")
                # Optionally send an error message to the user
                error_message_text = "Sorry, we couldn't load the available time slots right now. Please try again later."
                error_message = client.messages.create(
                    body=error_message_text,
                    to=sender_number,
                    from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                )
                display_outgoing_response("Error sending spa time slot selection", error_message.sid)
            return 'OK'

        # Spa Time Slot Selection
        elif list_id.startswith('spa_time_slot_') and booking_type == 'spa': # Use list_id for consistency
            if 'date' not in current_state:
                # Handle missing date state
                message = client.messages.create(
                    body="Sorry, I couldn't find your previously selected date. Please select a date for your spa session again.",
                    to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                )
                if sender_number in user_booking_state: del user_booking_state[sender_number]; save_booking_state()
                display_outgoing_response("State not found/invalid for spa time slot selection", message.sid)
                return 'OK'

            selected_date = current_state['date']
            selected_time_slot = list_title # This should be the human-readable time like "9:00 AM - 10:00 AM"

            confirmation_message_text = (
                f"Thank you for your spa booking!\n\n"
                f"Your session is confirmed for {selected_date} at {selected_time_slot}.\n\n"
                f"Our staff will contact you shortly if any further details are needed."
            )
            message = client.messages.create(
                body=confirmation_message_text,
                to=sender_number,
                from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )

            del user_booking_state[sender_number] # Clear state after confirmation
            save_booking_state()
            display_outgoing_response("Spa booking confirmation with time slot sent", message.sid)
            return 'OK'

        # Room Nights Selection
        elif list_id.startswith('night_') and booking_type == 'room':
            if 'room_type' not in current_state or 'date' not in current_state:
                # Handle incomplete room booking state
                message = client.messages.create(
                    body="There was an issue retrieving your room booking details. Please start by selecting a room again.",
                    to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                )
                if sender_number in user_booking_state: del user_booking_state[sender_number]; save_booking_state()
                display_outgoing_response("Room booking state incomplete for nights selection", message.sid)
                return 'OK'

            nights_text = get_nights_text(list_title) # Use helper for consistency
            room_type = current_state['room_type']
            selected_date = current_state['date']

            confirmation_message = (
                f"Thank you for your booking request!\n\n"
                f"Your reservation for the {room_type} starting on {selected_date} for {nights_text} is confirmed.\n\n"
                f"Our staff will contact you shortly with further details and payment options. We look forward to welcoming you!"
            )
            message = client.messages.create(
                body=confirmation_message, to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )
            display_outgoing_response("Room booking confirmation sent", message.sid)

            # Wait a moment before sending the special message
            time.sleep(2)

            # Send a random special message from the ROOM_SPECIAL_MESSAGES list
            random_special_message = random.choice(ROOM_SPECIAL_MESSAGES)
            special_message = client.messages.create(
                body=random_special_message,
                to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )
            display_outgoing_response(f"Special room booking message sent: {random_special_message[:30]}...", special_message.sid)

            del user_booking_state[sender_number] # Clear state after confirmation
            save_booking_state()
            return 'OK'

        # Handle cases where interactive reply doesn't match expected flow/state
        else:
            # Generic message if state doesn't match the interactive reply type
            message = client.messages.create(
                body="Sorry, something went wrong with your selection. Please try starting the process again.",
                to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )
            if sender_number in user_booking_state: del user_booking_state[sender_number]; save_booking_state() # Clear potentially inconsistent state
            display_outgoing_response("Interactive reply received but state mismatch", message.sid)
            return 'OK'


    # --- Allergy Information Handling ---
    if button_payload.startswith('allergy_'):
        dish_name = button_payload.replace('allergy_', '')
        allergy_info = DISH_ALLERGIES.get(dish_name, "Allergy information not available for this dish.")
        message = client.messages.create(
            body=allergy_info,
            to=sender_number,
            from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )
        display_outgoing_response(f"Allergy information for {dish_name} sent", message.sid)
        return 'OK'

    # --- Other Button Payload Handling ---
    if button_payload == 'book_spa':
        message = client.messages.create(
            content_sid=BOOK_SPA_TEMPLATE,
            to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )
        display_outgoing_response("Spa booking template sent", message.sid)
        return 'OK'

    if button_payload == 'explore_experiences':
        message = client.messages.create(
            content_sid=EXPLORE_EXPERIENCES_TEMPLATE,
            to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )
        display_outgoing_response("Explore experiences template sent", message.sid)
        return 'OK'

    elif button_payload == 'book_spa_option': # Handle the new spa option button
        # Calculate the next 10 dates starting from tomorrow
        today = datetime.now() # Use datetime which is already imported
        start_date = today + timedelta(days=1)
        dates = [(start_date + timedelta(days=i)).strftime('%d-%m-%Y') for i in range(10)]
        # Assuming the template expects variables "1" through "10" for the dates
        content_vars_date = {str(i+1): date for i, date in enumerate(dates)}

        try:
            message = client.messages.create(
                content_sid=SPA_DATE_SELECTION_TEMPLATE, # Use the spa date selection SID
                content_variables=json.dumps(content_vars_date), # Pass the date variables
                to=sender_number,
                from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )
            display_outgoing_response("Spa date selection sent", message.sid)
        except Exception as e:
            print(f"Error sending spa date selection message: {str(e)}")
            # Optionally send an error message to the user
            error_message = "Sorry, we couldn't load the available dates right now. Please try again later."
            message = client.messages.create(
                body=error_message,
                to=sender_number,
                from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )
            display_outgoing_response("Error sending spa date selection", message.sid)
        return 'OK'

    elif button_payload == 'book_massage': # Shows massage type options
        message = client.messages.create(
            content_sid=MASSAGE_TYPE_SELECTION_TEMPLATE,
            to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )
        display_outgoing_response("Massage type selection template sent", message.sid)
        return 'OK'

    elif button_payload == 'book_room': # Shows room type options
        message = client.messages.create(
            content_sid=BOOK_ROOM_TEMPLATE,
            to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )
        display_outgoing_response("Room selection template sent", message.sid)
        return 'OK'

    elif button_payload == 'order_now':
        message = client.messages.create(
            content_sid=ORDER_NOW_TEMPLATE,
            to=sender_number,
            from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )
        display_outgoing_response("Order now template sent", message.sid)
        return 'OK'

    elif button_payload == 'order_food':
        # Get all food menu products (filter to only include Food Menu items)
        food_menu_products = [p for p in FOOD_PRODUCTS if p["section_title"] == "Food Menu"]

        # Construct the items list for the 'products' variable with ALL food menu items
        items_payload = [{"id": p["id"]} for p in food_menu_products]

        # Choose a thumbnail ID (the first food menu product)
        thumbnail_id = food_menu_products[0]["id"] if food_menu_products else ""

        # Construct the content variables payload
        content_vars = {
            '1': FOOD_CATALOG_ID,
            '2': "Food Menu",  # Title
            '3': "Explore our full selection of delicious dishes!",  # Body
            '4': "Available for room service or at our restaurant.",  # Subtitle
            '5': thumbnail_id,
            'products': json.dumps(items_payload)
        }

        try:
            message = client.messages.create(
                content_sid=FOOD_CATALOGUE_TEMPLATE,
                content_variables=json.dumps(content_vars),
                to=sender_number,
                from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )
            display_outgoing_response("Food menu catalogue sent", message.sid)
        except Exception as e:
            print(f"Error sending food catalogue: {e}")
            # Send a fallback message if catalogue fails
            message = client.messages.create(
                content_sid=ORDER_FOOD_TEMPLATE,  # Fallback to original template
                to=sender_number,
                from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )
            display_outgoing_response("Fallback food template sent due to error", message.sid)
        return 'OK'

    elif button_payload == 'order_beverages':
        message = client.messages.create(
            content_sid=ORDER_BEVERAGES_TEMPLATE,
            to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )
        display_outgoing_response("Order beverages template sent", message.sid)
        return 'OK'

    # --- Beverage Category Selection Handling ---
    # Handle specific beverage category selections
    elif button_payload in ['view_beers', 'view_cocktails', 'view_mocktails', 'view_soda', 'view_water',
                           'view_sweet_wine', 'view_red_wine', 'view_rose_white_wine', 'view_sparkling_wine']:
        # Map button payloads to category names
        category_map = {
            'view_beers': 'Beers',
            'view_cocktails': 'Cocktails',
            'view_mocktails': 'Sodas',  # Mocktails are in the Sodas category
            'view_soda': 'Water',  # Soda items are listed in the Water category in our data structure
            'view_water': 'Water',
            'view_sweet_wine': 'Sweet Wine',
            'view_red_wine': 'Red Wine',
            'view_rose_white_wine': 'Rosé & White Wine',
            'view_sparkling_wine': 'Sparkling Wine'
        }

        category = category_map.get(button_payload)

        # We now only send the catalog without the text menu
        # This provides a cleaner user experience

        # Debug log to check which category was selected
        print(f"Selected beverage category: '{category}'")

        # Note: We're no longer sending the text menu, but keeping the code commented
        # in case we need to re-enable it in the future
        # menu_text = BEVERAGE_MENU_TEXT.get(category, BEVERAGE_MENU_TEXT['All'])
        # menu_chunks = split_menu_into_chunks(menu_text)

        try:
            # Text menu is now disabled by default - we'll only send the catalog
            # Uncomment the below code if you want to enable text menu again
            # # Send each chunk as a separate message
            # for chunk in menu_chunks:
            #     menu_message = client.messages.create(
            #         body=chunk,
            #         to=sender_number,
            #         from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            #     )
            #     display_outgoing_response(f"{category} menu text sent", menu_message.sid)

            # Only send the catalog if the flag is enabled
            if SEND_BEVERAGE_CATALOG:
                # Prepare catalog data - ensure we get all items for the category
                category_products = [p for p in BEVERAGE_PRODUCTS if p["section_title"] == category]

                # Debug log
                print(f"Found {len(category_products)} products for category '{category}'")

                # Special handling for all categories to ensure all items are included
                # This ensures that for any category, we get all items of that category
                # We're being explicit with each category to make sure nothing is missed
                if category == "Beers":
                    all_items = [p for p in BEVERAGE_PRODUCTS if p["section_title"] == "Beers"]
                    category_products = all_items
                    print(f"Including all {len(all_items)} beer products")
                elif category == "Cocktails":
                    all_items = [p for p in BEVERAGE_PRODUCTS if p["section_title"] == "Cocktails"]
                    category_products = all_items
                    print(f"Including all {len(all_items)} cocktail products")
                elif category == "Sodas":
                    all_items = [p for p in BEVERAGE_PRODUCTS if p["section_title"] == "Sodas"]
                    category_products = all_items
                    print(f"Including all {len(all_items)} soda products")
                elif category == "Water":
                    all_items = [p for p in BEVERAGE_PRODUCTS if p["section_title"] == "Water"]
                    category_products = all_items
                    print(f"Including all {len(all_items)} water products")
                elif category == "Sweet Wine":
                    all_items = [p for p in BEVERAGE_PRODUCTS if p["section_title"] == "Sweet Wine"]
                    category_products = all_items
                    print(f"Including all {len(all_items)} sweet wine products")
                elif category == "Red Wine":
                    all_items = [p for p in BEVERAGE_PRODUCTS if p["section_title"] == "Red Wine"]
                    category_products = all_items
                    print(f"Including all {len(all_items)} red wine products")
                elif category == "Rosé & White Wine":
                    all_items = [p for p in BEVERAGE_PRODUCTS if p["section_title"] == "Rosé & White Wine"]
                    category_products = all_items
                    print(f"Including all {len(all_items)} rosé & white wine products")
                elif category == "Sparkling Wine":
                    all_items = [p for p in BEVERAGE_PRODUCTS if p["section_title"] == "Sparkling Wine"]
                    category_products = all_items
                    print(f"Including all {len(all_items)} sparkling wine products")

                # If no products found, use all products (up to 30)
                if not category_products:
                    category_products = BEVERAGE_PRODUCTS[:30]

                # Limit to 30 items (Twilio's limit)
                items_payload = [{"id": p["id"]} for p in category_products[:30]]
                thumbnail_id = category_products[0]["id"] if category_products else BEVERAGE_PRODUCTS[0]["id"]

                content_vars = {
                    '1': BEVERAGE_CATALOG_ID,
                    '2': f"{category} Menu",
                    '3': f"Explore our selection of {category.lower()}!",
                    '4': "Available for room service or at our bar.",
                    '5': thumbnail_id,
                    'products': json.dumps(items_payload)
                }

                # Send the catalog message
                catalog_message = client.messages.create(
                    content_sid=BEVERAGE_CATALOGUE_TEMPLATE,
                    content_variables=json.dumps(content_vars),
                    to=sender_number,
                    from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                )
                display_outgoing_response(f"{category} catalog sent with {len(items_payload)} items", catalog_message.sid)

            # Send a confirmation message
            # confirmation = f"Above is our complete {category} menu. Would you like to order any of these items?"
            # message = client.messages.create(
            #     body=confirmation,
            #     to=sender_number,
            #     from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            # )
            # display_outgoing_response(f"Confirmation message sent for {category}", message.sid)

        except Exception as e:
            print(f"Error sending beverage menu: {e}")
            print(f"Error details: {type(e).__name__}")

            # Log more detailed error information if available
            if hasattr(e, 'msg'):
                print(f"Error message: {e.msg}")
            if hasattr(e, 'code'):
                print(f"Error code: {e.code}")
            if hasattr(e, 'status'):
                print(f"Error status: {e.status}")
            if hasattr(e, 'details'):
                print(f"Error details: {e.details}")

            # Send a fallback message if text menu fails
            message = client.messages.create(
                content_sid=ORDER_BEVERAGES_TEMPLATE,  # Fallback to original template
                to=sender_number,
                from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )
            display_outgoing_response("Fallback beverage template sent due to error", message.sid)

        return 'OK'

    elif button_payload == 'call_the_staff':
        message = client.messages.create(
            body=STAFF_CONTACT,
            to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )
        display_outgoing_response("Staff contact information sent", message.sid)
        return 'OK'

    elif button_payload == 'chat_staff':
        # Send the customer's request to chat with staff to Supabase
        customer_message = "Customer requested to chat with staff"
        send_chat_to_supabase(
            sender_number,
            webhook_data.get('ProfileName', 'customer'),
            webhook_data.get('WaId', sender_number.replace("whatsapp:+", "")),
            customer_message,
            is_customer=True
        )

        # Set the user's state to indicate they're in a chat with staff
        user_booking_state[sender_number] = {'booking_type': 'staff_chat'}
        save_booking_state()

        # Only send the chat notification message
        message = client.messages.create(
            body=STAFF_CHAT_MESSAGE,
            to=sender_number,
            from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )

        # Log the agent's response to Supabase
        send_chat_to_supabase(
            sender_number,
            "Agent",
            webhook_data.get('WaId', sender_number.replace("whatsapp:+", "")),
            STAFF_CHAT_MESSAGE,
            is_customer=False
        )

        display_outgoing_response("Staff chat alert sent", message.sid)
        return 'OK'

    # --- GGTEST Command ---
    if incoming_msg.upper() == "GGTEST":
        # Send welcome message first
        welcome_message_text = "Welcome to Palacio Can Marqués!"
        welcome_message = client.messages.create(
            body=welcome_message_text,
            to=sender_number,
            from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )
        display_outgoing_response("Welcome message sent via GGTEST", welcome_message.sid)

        # Then send the starter template
        message = client.messages.create(
            content_sid=STARTER_MESSAGE_TEMPLATE, # Send the initial welcome/options template
            to=sender_number,
            from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )
        display_outgoing_response("Starter template sent via GGTEST", message.sid)
        return 'OK'

    # --- /staff Command ---
    if incoming_msg.lower() == "/staff":
        # Send the customer's request to chat with staff to Supabase
        customer_message = "Customer requested to chat with staff using /staff command"
        send_chat_to_supabase(
            sender_number,
            webhook_data.get('ProfileName', 'customer'),
            webhook_data.get('WaId', sender_number.replace("whatsapp:+", "")),
            customer_message,
            is_customer=True
        )

        # Set the user's state to indicate they're in a chat with staff
        user_booking_state[sender_number] = {'booking_type': 'staff_chat'}
        save_booking_state()

        # Only send the chat notification message
        message = client.messages.create(
            body=STAFF_CHAT_MESSAGE,
            to=sender_number,
            from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )

        # Log the agent's response to Supabase
        send_chat_to_supabase(
            sender_number,
            "Agent",
            webhook_data.get('WaId', sender_number.replace("whatsapp:+", "")),
            STAFF_CHAT_MESSAGE,
            is_customer=False
        )

        display_outgoing_response("Staff chat alert sent via /staff command", message.sid)
        return 'OK'

    # --- /endchat Command ---
    if incoming_msg.lower() == "/endchat" and user_booking_state.get(sender_number, {}).get('booking_type') == 'staff_chat':
        # End the staff chat session
        user_booking_state.pop(sender_number, None)
        save_booking_state()
        endchat_message = "Your chat with staff has ended. If you need further assistance, type /staff."
        message = client.messages.create(
            body=endchat_message,
            to=sender_number,
            from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
        )
        display_outgoing_response("Staff chat ended via /endchat command", message.sid)
        return 'OK'

    # --- Check for custom tip amount input or staff chat ---
    current_state = user_booking_state.get(sender_number, {})

    # Prevent further processing if /staff command was used
    if incoming_msg.lower() == "/staff":
        return 'OK'

    # Handle staff chat mode
    if current_state.get('booking_type') == 'staff_chat' and incoming_msg and not message_type == 'interactive':
        # Send the customer's message to Supabase
        send_chat_to_supabase(
            sender_number,
            webhook_data.get('ProfileName', 'customer'),  # sender_name
            webhook_data.get('WaId', sender_number.replace("whatsapp:+", "")),  # phone_number
            incoming_msg,  # message
            is_customer=True
        )

        # Only send confirmation once per staff chat session
        if not current_state.get('confirmation_sent'):
            confirmation_message = "Your message has been sent to our staff. They will respond shortly."
            message = client.messages.create(
                body=confirmation_message,
                to=sender_number, from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )

            # Also log the confirmation message to Supabase
            send_chat_to_supabase(
                sender_number,
                "Agent",  # sender_name for agent
                webhook_data.get('WaId', sender_number.replace("whatsapp:+", "")),  # phone_number for agent messages
                confirmation_message,  # message
                is_customer=False
            )

            # Mark confirmation as sent for this session
            user_booking_state[sender_number]['confirmation_sent'] = True

            display_outgoing_response("Staff chat message forwarded", message.sid)
        else:
            display_outgoing_response("Staff chat message forwarded (no confirmation sent)", None)
        return 'OK'

    # Handle custom tip amount
    elif current_state.get('booking_type') == 'custom_tip' and incoming_msg and not message_type == 'interactive':
        try:
            # Try to parse the custom tip amount
            custom_amount_text = incoming_msg.strip()

            # Use regex to find numbers in the message
            match = re.search(r'\d+(\.\d+)?', custom_amount_text)

            if match:
                custom_amount_str = match.group(0)
                try:
                    custom_amount_float = float(custom_amount_str)
                    # Format the amount with pound symbol
                    formatted_amount = f"€{custom_amount_float:.2f}"

                    # Send confirmation message
                    confirmation_message = f"Thank you for confirming your custom tip amount of {formatted_amount}. We appreciate your generosity and look forward to serving you again soon."

                    message = client.messages.create(
                        body=confirmation_message,
                        to=sender_number,
                        from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                    )
                    display_outgoing_response(f"Custom tip confirmation sent for {formatted_amount}", message.sid)

                    # Clear the booking state
                    if sender_number in user_booking_state:
                        del user_booking_state[sender_number]
                        save_booking_state()

                    return 'OK'
                except ValueError:
                    # This case should ideally not be reached if regex is correct
                    error_message = "Could not parse the number from your message. Please enter a valid number for your tip amount (e.g., 10 for €10 or 'I want to tip 10 euros'):"
            else:
                # No number found in the input, ask again
                error_message = "Please enter a valid number for your tip amount (e.g., 10 for €10 or 'I want to tip 10 euros'):"
                message = client.messages.create(
                    body=error_message,
                    to=sender_number,
                    from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
                )
                display_outgoing_response("Invalid custom tip amount prompt sent", message.sid)
                return 'OK'
        except Exception as e:
            print(f"Error processing custom tip amount: {e}")
            # Clear the state and send an error message
            if sender_number in user_booking_state:
                del user_booking_state[sender_number]
                save_booking_state()

            error_message = "Sorry, we couldn't process your tip amount. Please try again later."
            message = client.messages.create(
                body=error_message,
                to=sender_number,
                from_=os.getenv('TWILIO_WHATSAPP_NUMBER')
            )
            display_outgoing_response("Custom tip error message sent", message.sid)
            return 'OK'

    # --- Fallback to AI Agent ---
    # Avoid fallback if it was an interactive message or a button payload that was handled above
    is_handled_interactive = message_type == 'interactive' # Already handled within the interactive block

    # Define beverage category button payloads
    beverage_payloads = ['view_beers', 'view_cocktails', 'view_mocktails', 'view_soda', 'view_water',
                        'view_sweet_wine', 'view_red_wine', 'view_rose_white_wine', 'view_sparkling_wine']

    # Check if it was a handled button
    is_handled_button = bool(button_payload) and button_payload not in ['book_spa', 'book_massage', 'book_room',
                                                                      'order_now', 'order_food', 'order_beverages',
                                                                      'call_the_staff', 'chat_staff', 'explore_experiences'] \
                        and not button_payload.startswith('allergy_') \
                        and button_payload not in room_payloads \
                        and button_payload not in massage_payloads \
                        and button_payload not in beverage_payloads

    # Also avoid fallback if we're in the custom tip flow or staff chat
    is_custom_tip_flow = current_state.get('booking_type') == 'custom_tip'
    is_staff_chat = current_state.get('booking_type') == 'staff_chat'

    # Prevent AI fallback if user is in staff chat mode
    if not is_handled_interactive and not is_handled_button and not is_custom_tip_flow and not is_staff_chat and incoming_msg:
        # Only generate reply if it's a text message and not part of any handled flow
        try:
            # Pass the sender number as user_id to maintain conversation history
            reply = generate_reply(incoming_msg, user_id=sender_number)
            message = client.messages.create(
                from_=os.getenv('TWILIO_WHATSAPP_NUMBER'),
                body=reply,
                to=sender_number
            )
            display_outgoing_response(f"AI Fallback: {reply[:50]}...", message.sid)
        except Exception as e:
            print(f"Error generating AI reply: {e}")
            # Optionally send a generic error message to the user
            error_message = "Sorry, I encountered an issue processing your request. Please try again later."
            message = client.messages.create(
                from_=os.getenv('TWILIO_WHATSAPP_NUMBER'),
                body=error_message,
                to=sender_number
            )
            display_outgoing_response("Sent error message due to AI failure", message.sid)

    # If in staff chat mode, do not send AI fallback, only automated message is sent above

    return 'OK'

# ==================== AI AGENT IMPORT ====================
# Import testagent module for generate_reply function
# Ensure this import happens after necessary configurations if it depends on them
try:
    from aiagent import generate_reply, initialize_vector_store, SYSTEM_PROMPT
except ImportError:
    print("Warning: testagent module not found. AI fallback will not function.")
    # Define dummy functions if the module is missing to avoid runtime errors
    def generate_reply(_text="", _user_id="default_user"):
        return "AI Agent not available."
    def initialize_vector_store(): pass
    SYSTEM_PROMPT = "System prompt not available."

# ==================== SYSTEM PROMPT EDITOR ROUTES ====================
@app.route("/system-prompt", methods=['GET'])
def system_prompt_editor():
    """Renders the system prompt editor page."""
    return render_template('system_prompt.html', system_prompt=SYSTEM_PROMPT)

@app.route("/update_system_prompt", methods=['POST'])
def update_system_prompt():
    """Updates the system prompt in the aiagent.py file."""
    try:
        # Get the new system prompt from the request
        data = request.get_json()
        new_system_prompt = data.get('system_prompt', '')

        if not new_system_prompt:
            return jsonify({"success": False, "message": "System prompt cannot be empty"})

        # Read the current aiagent.py file
        with open('aiagent.py', 'r') as file:
            content = file.read()

        # Define the pattern to find the system prompt definition
        pattern = r'SYSTEM_PROMPT = """[\s\S]*?"""'

        # Replace the system prompt with the new one
        new_content = re.sub(pattern, f'SYSTEM_PROMPT = """{new_system_prompt}"""', content)

        # Write the updated content back to the file
        with open('aiagent.py', 'w') as file:
            file.write(new_content)

        # Return success response
        return jsonify({"success": True, "message": "System prompt updated successfully"})

    except Exception as e:
        print(f"Error updating system prompt: {e}")
        return jsonify({"success": False, "message": str(e)})

# ==================== MAIN EXECUTION ====================
if __name__ == "__main__":
    # Load booking state at startup
    load_booking_state()
    print("Booking state loaded.")

    # Initialize the vector store when the app starts
    print("Loading AI agent...")
    try:
        initialize_vector_store()
        print("AI agent loaded successfully.")
    except Exception as e:
        print(f"Error initializing AI agent: {e}")


    app.run(debug=True, port=5000)
