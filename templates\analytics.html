<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Dashboard</title>
    {% include 'imports.html' %}

    <style>
        .hoverable-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            perspective: 800px;
            box-shadow: 0 0 10px rgba(255, 110, 110, 0.15);
        }

        .hoverable-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 16px rgba(255, 110, 110, 0.25);
        }
    </style>
    <script>
        document.querySelectorAll('.hoverable-card').forEach(card => {
            card.addEventListener('mousemove', function (e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                const deltaX = (x - centerX) / centerX;
                const deltaY = (y - centerY) / centerY;
                // Apply a slight tilt in addition to the floating effect
                const tiltX = deltaY * 1;  // reduced effect
                const tiltY = -deltaX * 1;
                this.style.transform = `translateY(-4px) rotateX(${tiltX}deg) rotateY(${tiltY}deg)`;
            });
            card.addEventListener('mouseleave', function () {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
    <style>
        body {
            visibility: hidden;
            top: 0px !important;
        }

        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }

        .no-animation #loading-overlay {
            display: none;
        }

        .table-container {
            max-height: 640px;
            overflow-y: auto;
        }

        .table-container thead {
            position: sticky;
            top: 0;
            z-index: 1;
            background-color: #fff;
        }

        .icon {
            margin-top: 1px;
        }

        .platform-icon {
            margin-top: 1px;
        }

        #languagesContainer {
            scrollbar-width: thin;
            /* Changed from none to thin */
            -ms-overflow-style: auto;
            /* Changed from none to auto */
            overflow-y: auto !important;
            /* Force enable vertical scrolling */
            max-height: 350px;
            /* Set a max height to enable scrolling */
        }

        #languagesContainer {
            scrollbar-width: none;
            /* For Firefox */
            -ms-overflow-style: none;
            /* For Internet Explorer and Edge */
            overflow-y: auto !important;
            /* Force enable vertical scrolling */
            max-height: 350px;
            /* Set a max height to enable scrolling */
        }

        #languagesContainer::-webkit-scrollbar {
            width: 0;
            /* Hide scrollbar for Chrome, Safari and Opera */
            display: none;
            /* Hide scrollbar completely */
        }

        #languagesContainer::-webkit-scrollbar-track {
            background: transparent;
        }

        #languagesContainer::-webkit-scrollbar-thumb {
            background-color: transparent;
            border-radius: 0;
        }

        #languagesContainer::-webkit-scrollbar-thumb:hover {
            background-color: transparent;
        }

        .sidebar {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 280px;
            overflow-y: auto;
            height: 100%;

        }

        .main-content {
            margin-left: 280px;
            width: calc(100% - 280px);
        }

        @media (max-width: 1024px) {
            .sidebar {
                display: none;
            }

            .main-content {
                margin-left: 0;
                width: 100%;
            }
        }

        #languagesList {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .language-card {
            transition: all 0.2s ease;
            width: 100%;
            height: 110px;
            /* Slightly increased height */
        }

        .language-card:hover {
            transform: translateY(-1px);
        }

        #languagesList {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            /* Increased gap for better spacing */
        }

        .language-card {
            display: flex;
            flex-direction: column;
            padding: 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            transition: transform 0.2s ease;
            height: 110px;
            /* Fixed height */
        }

        .language-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }



        .flag-container {
            width: 36px;
            /* Fixed width for flag */
            height: 28px;
            /* Fixed height for flag */
            margin-top: -4px;
            margin-bottom: 8px;
        }

        .flag-container img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .language-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .language-name {
            font-weight: 500;
            margin-bottom: 0px;
            /* Reduced from default spacing */
        }

        .language-stats {
            font-size: 0.875rem;
            color: #4b5563;
        }

        .percentage {
            position: absolute;
            top: 12px;
            right: 12px;
            font-size: 0.875rem;
            color: #6b7280;
        }

        .mini-graph {
            position: absolute;
            bottom: 0px;
            right: 0px;
            opacity: 0.7;
        }

        .graph-line {
            position: absolute;
            bottom: 4px;
            left: 4px;
            right: 4px;
            height: 12px;
            fill: none;
            stroke: #5347ce;
            stroke-width: 1.5;
            stroke-linecap: round;
        }

        /* Add these button styles after your existing styles */
        .uk-button {
            background-color: transparent !important;
            border: 1px solid var(--theme-selector-border) !important;
            color: inherit !important;
        }

        .uk-button:hover {
            background-color: rgba(0, 0, 0, 0.05) !important;
        }

        /* Specific styles for the number display buttons */
        button.uk-button-default {
            background-color: transparent !important;
            border: 1px solid var(--theme-selector-border) !important;
            color: inherit !important;
        }

        button.uk-button-default:hover {
            background-color: rgba(0, 0, 0, 0.05) !important;
        }

        /* Theme-specific border colors */
        .pure-black .uk-button,
        .pure-black button.uk-button-default {
            border-color: #27272a !important;
        }

        .light .uk-button,
        .light button.uk-button-default {
            border-color: #e5e7eb !important;
        }

        #aiInteractionsChart {
            width: 100%;
            height: 100%;
        }

        .chart-container-1 {
            height: 370px;
            /* Custom height for the first chart */
        }

        .chart-container-3 {
            height: 370px;
            /* Custom height for the third chart */
        }

        .chart-container-2 {
            height: 370px;
            /* Custom height for the second chart */
        }

        .chart-container-2 .amcharts-ColumnSeries-column {
            stroke: none;
            /* Remove the border */
            stroke-width: 0;
            /* Ensure no border width */
            fill: #18181b;
            /* Set the desired fill color */
        }

        #pieChartDiv {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
        }

        #pieChartDiv1 {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
        }

        /* Animation Keyframes */
        @keyframes fadeInScaleUp {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInPop {
            from { opacity: 0; transform: scale(0.85); }
            to { opacity: 1; transform: scale(1); }
        }

        @keyframes rotateInFade {
            from { opacity: 0; transform: rotate(-10deg) scale(0.9); }
            to { opacity: 1; transform: rotate(0deg) scale(1); }
        }

        /* Base animation class */
        .animated-on-load {
            opacity: 0; /* Start hidden */
            animation-fill-mode: forwards; /* Retain styles from the last keyframe */
        }

        /* Specific animation classes */
        .animate-total-revenue { animation-name: fadeInScaleUp; animation-duration: 1800ms; animation-timing-function: ease-out; animation-delay: 100ms; }
        .animate-conversation-analytics { animation-name: slideInFromLeft; animation-duration: 1500ms; animation-timing-function: ease-out; animation-delay: 200ms; }
        
        .animate-stat-card { animation-name: slideUp; animation-duration: 1200ms; animation-timing-function: ease-in-out; }

        .animate-ai-human-response { animation-name: slideUp; animation-duration: 2400ms; animation-timing-function: ease-out; animation-delay: 300ms; }
        .animate-language-analytics { animation-name: fadeInPop; animation-duration: 1500ms; animation-timing-function: ease-in; animation-delay: 500ms; }
        .animate-product-analytics { animation-name: rotateInFade; animation-duration: 2100ms; animation-timing-function: ease-out; animation-delay: 600ms; }
        .animate-total-tips-chart { animation-name: slideUp; animation-duration: 1800ms; animation-timing-function: ease-in-out; animation-delay: 700ms; }
        .animate-tip-analytics-graph { animation-name: fadeInScaleUp; animation-duration: 3000ms; animation-timing-function: ease-in-out; animation-delay: 800ms; }

    </style>

</head>

<body class="">
    {% include 'components/loading.html' %}
    <div id="google_translate_element" style="display:none;"></div>
    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr]">
        {% include 'sidebar.html' %}
        <div class="flex flex-col">
            <header
                class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 md:gap-8 md:px-6 sticky-page-header">
                <div class="flex items-center gap-2 px-4 pl-0">
                    <button id="toggle-btn" class="opacity-100 transition-opacity duration-300 focus:outline-none"
                        style="background-color: transparent !important;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-panel-left">
                            <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                            <path d="M9 3v18"></path>
                        </svg>
                    </button>
                    <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-2 h-4"
                        style="background-color: var(--border-color);"></div>
                    <nav aria-label="breadcrumb">
                        <ol
                            class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">
                            <!-- Dashboard link -->
                            <div class="menubar" role="menubar">
                                <div class="menubar-indicator"></div>
                                <a href="/" role="menuitem">Dashboard</a>
                                <a href="/users" role="menuitem">Users</a>
                                <a href="/products" role="menuitem">Products</a>
                                <a href="/analytics" role="menuitem"  class="active">Overview</a>
                            </div>
                        </ol>
                    </nav>
                </div>
                {% include 'topright.html' %}
            </header>
            <main class="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6 card">
                <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <!-- Total Sales -->
                    <!-- Include ProgressBar.js -->
                    <script src="https://cdn.jsdelivr.net/npm/progressbar.js"></script>

                    <!-- Total Sales Card with Progress Bar -->
                    <div class="card rounded-lg border relative overflow-hidden animated-on-load animate-total-revenue" data-v0-t="card"
                        style="height: 150px;">
                        <div class="p-4 pb-1 flex flex-row items-center justify-between space-y-0">

                            <h3 class="whitespace-nowrap tracking-tight text-sm font-large">Total Revenue</h3>

                        </div>
                        <div class="px-4 pb-2 relative z-10 mt-10">
                            <div class="flex items-baseline">
                                <div style="margin-top: 0px" class="text-2xl font-bold">€13,762</div>
                            </div>
                            <p class="text-xs">Total Lifetime Sales</p>
                        </div>
                        <!-- Progress Bar and Labels -->
                        <div class="px-4" style="width: 100%; height: 50px; position: absolute; bottom: 0;">
                            <div>
                                <div id="progress-bar-container"
                                    style="position: absolute; bottom: 0; right: 0; width: 220px; height: 130px;">
                                    <div id="pieChartDiv"></div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
                    <script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
                    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>




                    <!-- <script src="https://cdn.jsdelivr.net/npm/progressbar.js"></script> -->
                    <!-- Ensure ProgressBar.js is included -->


                    <div class="card rounded-lg border relative overflow-hidden animated-on-load animate-conversation-analytics" data-v0-t="card"
                        style="height: 150px;">
                        <div class="p-4 pb-1 flex flex-row items-center justify-between space-y-0">
                            <h3 class="whitespace-nowrap tracking-tight text-sm font-large">Conversation Analytics</h3>
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-messages-square">
                                <path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2z" />
                                <path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1" />
                            </svg>
                        </div>
                        <div class="px-4 pb-0 relative z-10">
                            <div class="flex items-baseline">
                                <div id="total-tips" style="margin-top: 0px" class="text-2xl font-bold">763</div>
                            </div>
                        </div>

                        <!-- Static Progress Bar - No Animation -->
                        <div class="px-4 absolute bottom-3 left-0 right-0 w-full">
                            <div class="overflow-hidden w-full">
                                <!-- Static Progress Bar Container -->
                                <div class="flex w-full rounded-full bg-gray-100 h-[10px] overflow-hidden">
                                    <!-- Pre-rendered Progress Bars with static widths -->
                                    <div class="h-full bg-cyan-500" style="width: 70%;"></div>
                                    <div class="h-full bg-rose-500" style="width: 30%;"></div>
                                </div>
                                <!-- Static Legend -->
                                <div class="mt-2 flex flex-wrap justify-center text-[12px]">
                                    <div class="flex items-center mr-3 mb-1">
                                        <span class="inline-block w-2.5 h-2.5 rounded-sm bg-cyan-500 mr-1"></span>
                                        <span>In business hours: 105</span>
                                    </div>
                                    <div class="flex items-center mr-3 mb-1">
                                        <span class="inline-block w-2.5 h-2.5 rounded-sm bg-rose-500 mr-1"></span>
                                        <span>Outside business hours: 45</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            console.log('Using static progress bars without animation to prevent overflow issues');
                        });
                    </script>

                    <div class="grid grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <!-- Total Reviews -->
                        <div class="card hoverable-card rounded-lg border relative overflow-hidden animated-on-load animate-stat-card" data-v0-t="card"
                            style="height: 150px; animation-delay: 300ms;">
                            <div class="p-4 flex flex-col items-center space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium text-center">Total
                                    Reviews</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400 text-center">This month</p>
                            </div>
                            <div class="px-4 pb-4 relative z-10">
                                <div class="flex flex-col items-center justify-center">
                                    <button style="margin-top: 1px;"
                                        class="uk-button uk-button-default universal-hover"><svg
                                            xmlns="http://www.w3.org/2000/svg" width="13" height="13"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            style="margin-right: 5px;" stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-star">
                                            <path
                                                d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z" />
                                        </svg>231</button>
                                    <p class="text-xs text-gray-500 text-center mt-2">Today : 324</p>
                                </div>
                            </div>
                        </div>
                        <!-- Total Messages -->
                        <div class="card hoverable-card rounded-lg border relative overflow-hidden animated-on-load animate-stat-card" data-v0-t="card"
                            style="height: 150px; animation-delay: 400ms;">
                            <div class="p-4 flex flex-col items-center space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium  text-center">Staff
                                    chats</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400 text-center">This month</p>
                            </div>
                            <div class="px-4 pb-4 relative z-10">
                                <div class="flex flex-col items-center justify-center">
                                    <button style="margin-top: 1px;" class="uk-button uk-button-default universal-hover"
                                        id="total-messages"><svg xmlns="http://www.w3.org/2000/svg" width="13"
                                            height="13" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                            style="margin-right: 5px;" class="lucide lucide-messages-square">
                                            <path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2z" />
                                            <path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1" />
                                        </svg>104</button>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">Today : 137</p>
                                </div>
                            </div>
                        </div>
                        <!-- Total Messages -->
                        <div class="card hoverable-card rounded-lg border relative overflow-hidden animated-on-load animate-stat-card" data-v0-t="card"
                            style="height: 150px; animation-delay: 500ms;">
                            <div class="p-4 flex flex-col items-center space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium  text-center">Total
                                    Bookings</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400 text-center">This month</p>
                            </div>
                            <div class="px-4 pb-4 relative z-10">
                                <div class="flex flex-col items-center justify-center">
                                    <button style="margin-top: 1px;" class="uk-button uk-button-default universal-hover"
                                        id="total-messages"> <svg xmlns="http://www.w3.org/2000/svg" width="13"
                                            height="13" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                            style="margin-right: 6px;" class="lucide lucide-calendar">
                                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                            <line x1="16" y1="2" x2="16" y2="6"></line>
                                            <line x1="8" y1="2" x2="8" y2="6"></line>
                                            <line x1="3" y1="10" x2="21" y2="10"></line>
                                        </svg>216</button>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">Today : 121
                                    </p>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="col-span-1 md:col-span-2">
                        <div
                            class="card rounded-lg border text-card-fore  min-h-[350px] md:h-[350px] v0-t='card' transition-all duration-300 cursor-pointer overflow-hidden animated-on-load animate-ai-human-response">
                            <div class="p-5 flex flex-row items-center justify-between space-y-0">
                                <h3 class="text-lg font-semibold mt-[-4px]">AI-Human Response Analytics</h3>

                                <div class="flex items-center gap-4">
                                    <div class="flex items-center gap-1">
                                        <span class="inline-block w-2.5 h-2.5 rounded-sm bg-cyan-500 mt-[-4px]"></span>
                                        <span class="text-xs mt-[-4px]">AI-powered responses</span>
                                    </div>
                                    <div class="flex items-center gap-1 mr-1">
                                        <span class="inline-block w-2.5 h-2.5 rounded-sm bg-rose-500 mt-[-4px]"></span>
                                        <span class="text-xs mt-[-4px]">Human agent responses</span>
                                    </div>
                                </div>
                            </div>
                            <div class="px-6 pb-6" style="height: calc(100% - 60px);">
                                <div class="chart-container h-full w-full" style="margin-top: 5px;">
                                    <canvas id="salesOverviewChart"
                                        style="display: block; box-sizing: border-box; height: 100%; width: 100%;"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="languagesContainer"
                        class="col-span-1 rounded-lg p-4 transition-all duration-300 cursor-pointer border card min-h-[350px] md:h-[350px] overflow-y-auto animated-on-load animate-language-analytics">
                        <div class="flex flex-row items-center justify-between space-y-0 mb-4 mt-[-5px]">
                            <h3 class="text-lg font-semibold">Language Analytics</h3>
                            <div class="flex items-center">
                                <button style="margin-top: 2px;"
                                    class="uk-button uk-button-default card universal-hover" type="button" id="language-dropdown-btn"
                                    aria-haspopup="true" aria-expanded="false">
                                    Languages
                                </button>
                                <div class="uk-dropdown dropdown-content" id="language-dropdown"
                                    uk-dropdown="mode: click; pos: bottom-right">
                                    <ul class="uk-dropdown-nav uk-nav">
                                        <li><a href="#" data-label="Guest Languages" data-main-label="Language Analytics">Guest Languages</a></li>
                                        <li><a href="#" data-label="Guest Nationalities" data-main-label="Language Analytics">Guest Nationalities</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div id="languagesList" style="margin-top: 30px;" class="grid grid-cols-2 gap-4">
                            <!-- Language items will be dynamically inserted here -->
                        </div>
                    </div>

                    <script>
                        function generateMountainPath() {
                            // Start from bottom
                            const startY = 16;
                            // Generate 5 points with increasing height trend
                            const points = [];
                            let currentY = startY;

                            for (let i = 0; i < 5; i++) {
                                // Gradually decrease Y (move upward) with some randomness
                                currentY = Math.max(4, currentY - (Math.random() * 4 + 2));
                                points.push(currentY);
                            }

                            // Ensure the last point is near the top
                            points[4] = Math.random() * 3 + 2;

                            // Adjust the starting X position to move the line to the right
                            return `M 50,${startY} ${points.map((p, i) => `L ${(i + 1) * 25},${p}`).join(' ')}`;
                        }

                    </script>
                    <script>
                        function updateLanguages(languages) {
                            const container = document.getElementById('languagesList');
                            container.innerHTML = '';

                            // Sort languages by value in descending order
                            languages.sort((a, b) => b.value - a.value);

                            const totalValue = languages.reduce((sum, lang) => sum + lang.value, 0);

                            languages.forEach(lang => {
                                const percentage = ((lang.value / totalValue) * 100).toFixed(1);
                                const randomUsers = Math.floor(Math.random() * (200 - 100 + 1)) + 100;
                                const pathData = generateMountainPath();

                                const item = document.createElement('div');
                                item.className = 'language-card relative card';
                                item.innerHTML = `
                                    <div class="percentage absolute top-3 right-3">${percentage}%</div>
                                    <div class="flag-container">
                                        <img src="../static/flags/${lang.name.toLowerCase()}.png" 
                                            alt="${lang.name} Flag">
                                    </div>
                                    <div class="language-info">
                                        <div>
                                            <div class="language-name capitalize">${lang.name}</div>
                                            <div class="language-stats">
                                                ${randomUsers} users
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mini-graph-container flex justify-end">
                                        <div class="mini-graph w-2/4">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="10 -24 80 40" class="w-full h-full">
                                                <!-- Define gradient -->
                                                <defs>
                                                    <linearGradient id="fadeGradient" x1="0" y1="0" x2="0" y2="1">
                                                        <stop offset="0%" stop-color="#f97316" stop-opacity="0.3"/>
                                                        <stop offset="100%" stop-color="#f97316" stop-opacity="0"/>
                                                    </linearGradient>
                                                </defs>
                                                
                                                <!-- Gradient fill area -->
                                                <path 
                                                    d="M 10 16 C 15 11, 18 14, 25 8 S 35 1, 45 -2 S 55 -6, 65 -12 S 75 -16, 85 -19 S 88 -22, 90 -24 L 90 16 L 10 16 Z" 
                                                    fill="url(#fadeGradient)"
                                                />
                                                
                                                <!-- Main curve line -->
                                                <path 
                                                    d="M 10 16 C 15 11, 18 14, 25 8 S 35 1, 45 -2 S 55 -6, 65 -12 S 75 -16, 85 -19 S 88 -22, 90 -24" 
                                                    stroke="#f97316" 
                                                    stroke-width="1" 
                                                    fill="none"
                                                />
                                            </svg>
                                        </div>
                                    </div>
                                `;
                                container.appendChild(item);
                            });
                        }

                        const exampleLanguages = [
                            { name: 'english', value: 4800 },  // Added first with higher value
                            { name: 'spanish', value: 4600 },  // Added second with higher value 
                            { name: 'german', value: 4400 },
                            { name: 'dutch', value: 4100 },
                            { name: 'french', value: 3600 },
                            { name: 'italian', value: 3100 },
                            { name: 'swedish', value: 2700 }
                        ];

                        updateLanguages(exampleLanguages);
                    </script>
                </div>
                <!-- <style>
                    .platform-selector {
                        position: relative;
                        z-index: 2000;
                    }

                    .platform-selector-btn {
                        display: flex;
                        align-items: center;
                        padding: 0.5rem 1rem;
                        background-color: var(--theme-selector-bg, #e2e8f0);
                        color: var(--theme-selector-color, #212529);
                        border: 1px solid var(--theme-selector-border, #dee2e6);
                        border-radius: 20px;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        white-space: nowrap;
                        font-size: 0.875rem;
                    }

                    .platform-selector-btn:hover {
                        background-color: var(--theme-selector-hover-bg, rgba(255, 255, 255, 0.1));
                    }

                    .platform-selector-btn i {
                        margin-right: 0.5rem;
                    }

                    .platform-selector-menu {
                        display: block;
                        opacity: 0;
                        visibility: hidden;
                        position: absolute;
                        right: 0;
                        top: calc(100% + 5px);
                        background-color: var(--theme-selector-menu-bg, #ffffff);
                        border: 1px solid var(--theme-selector-menu-border, #dee2e6);
                        border-radius: 0.375rem;
                        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                        z-index: 10;
                        transition: all 0.3s ease;
                        transform: translateY(-10px);
                        width: 150px;
                    }

                    .platform-selector:hover .platform-selector-menu,
                    .platform-selector.active .platform-selector-menu {
                        opacity: 1;
                        visibility: visible;
                        transform: translateY(0);
                    }

                    .platform-option {
                        padding: 0.5rem 1rem;
                        cursor: pointer;
                        color: var(--theme-option-color, #212529);
                        transition: background-color 0.2s ease;
                        display: flex;
                        align-items: center;
                    }

                    .platform-option:hover {
                        background-color: var(--theme-option-hover-bg, #f1f5f9);
                    }

                    .platform-option i {
                        margin-right: 0.5rem;
                        width: 20px;
                        text-align: center;
                    }
                </style> -->
                <script>
                    document.addEventListener('DOMContentLoaded', function () {
                        // All progress items
                        const progressData = [
                            { name: 'In business hours', value: 105, color: 'bg-cyan-500' },
                            { name: 'Outside business hours', value: 45, color: 'bg-rose-500' },
                        ];

                        // Calculate total
                        const total = progressData.reduce((sum, p) => sum + p.value, 0);

                        const container = document.getElementById('myGeneratedProgressBar');
                        const legendContainer = document.getElementById('progressBarLegend');

                        container.innerHTML = '';
                        legendContainer.innerHTML = '';
                        legendContainer.className = 'flex flex-row flex-wrap justify-center mt-2 text-[12px]';

                        // First create all bar elements but with zero width
                        const barElements = progressData.map(item => {
                            const barDiv = document.createElement('div');
                            barDiv.className = `h-full ${item.color} transition-all duration-1000 ease-out`;
                            barDiv.style.width = '0%'; // Start at zero width
                            container.appendChild(barDiv);
                            return { element: barDiv, targetWidth: ((item.value / total) * 100).toFixed(2) };
                        });

                        // Create legend items
                        progressData.forEach(item => {
                            const legendItem = document.createElement('div');
                            legendItem.className = 'flex items-center mr-3 mb-1';
                            legendItem.innerHTML = `
                                <span class="inline-block w-2 h-2 mr-1 ${item.color} w-2.5 h-2.5 rounded-sm"></span>
                                <span>${item.name}: ${item.value}</span>
                            `;
                            legendContainer.appendChild(legendItem);
                        });

                        // Set a small delay before starting the animation to ensure the DOM is ready
                        setTimeout(() => {
                            // Animate each bar to its target width
                            barElements.forEach(bar => {
                                bar.element.style.width = bar.targetWidth + '%';
                            });
                        }, 50);
                    });
                </script>
                <!-- <script>
                    document.addEventListener('DOMContentLoaded', function () {
                        const platformSelector = document.querySelector('.platform-selector');
                        const platformOptions = document.querySelectorAll('.platform-option');
                        const platformSelectorBtn = platformSelector.querySelector('.platform-selector-btn');
                        let timeoutId;

                        function showDropdown() {
                            clearTimeout(timeoutId);
                            platformSelector.classList.add('active');
                        }

                        function hideDropdown() {
                            timeoutId = setTimeout(() => {
                                platformSelector.classList.remove('active');
                            }, 300);
                        }

                        function applyPlatform(platformName, iconClass) {
                            platformSelectorBtn.innerHTML = `<i class="${iconClass}"></i> ${platformName}`;
                            // Add logic to update the chart based on the selected platform
                            console.log('Selected platform:', platformName);
                        }

                        platformSelector.addEventListener('mouseenter', showDropdown);
                        platformSelector.addEventListener('mouseleave', hideDropdown);

                        platformOptions.forEach(option => {
                            option.addEventListener('click', function () {
                                const selectedPlatform = this.getAttribute('data-platform');
                                const iconClass = this.querySelector('i').className;
                                applyPlatform(this.textContent.trim(), iconClass);
                                hideDropdown();
                            });
                            option.addEventListener('mouseenter', showDropdown);
                        });

                        // Set default platform
                        applyPlatform('Facebook', 'fab fa-facebook');
                    });
                </script> -->

                <div class="grid gap-4 md:grid-cols-3 lg:grid-cols-3">
                    <!-- Chart 1 -->
                    <div class="card rounded-lg border shadow-sm chart-container chart-container-1 animated-on-load animate-product-analytics" data-v0-t="card">
                        <div class="flex justify-between items-start p-4 border-b card">
                            <div class="flex flex-col">
                                <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">Product
                                    Analytics</h3>
                                <p class="text-sm text-muted-foreground mt-1">Pie chart representation</p>
                            </div>
                            <div class="relative">
                                <button style="margin-top: 2px;" id="toggle-chart-btn"
                                    class="card uk-button border default" type="button"
                                    onclick="window.location.href='/sales'">
                                    <svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 6px;" width="16"
                                        height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-notepad-text">
                                        <path d="M8 2v4" />
                                        <path d="M12 2v4" />
                                        <path d="M16 2v4" />
                                        <rect width="16" height="18" x="4" y="4" rx="2" />
                                        <path d="M8 10h6" />
                                        <path d="M8 14h8" />
                                        <path d="M8 18h5" />
                                    </svg>
                                    Sales
                                </button>
                            </div>
                        </div>
                        <div class="p-4 relative min-h-[260px] md:h-[260px]">
                            <div id="chartdiv" style="width: 100%; height: 100%; margin-top: 15px;"></div>
                        </div>
                    </div>

                    <!-- Chart 2 -->
                    <div class="card rounded-lg border shadow-sm chart-container chart-container-2 animated-on-load animate-total-tips-chart" data-v0-t="card">
                        <div class="flex justify-between items-start p-4 border-b card">
                            <div class="flex flex-col">
                                <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight dark:"
                                    id="mainLabel">Total tips</h3>
                                <p class="text-sm text-muted-foreground mt-1" id="label">Progress bar represeantation
                                </p>
                            </div>
                            <div class="flex space-x-2">
                                <button style="margin-top: 2px;"
                                    class="uk-button uk-button-default card universal-hover" type="button" id="button"
                                    onclick="toggleDropdown()">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-notebook-text"
                                        style="margin-right: 5px;">
                                        <path d="M2 6h4" />
                                        <path d="M2 10h4" />
                                        <path d="M2 14h4" />
                                        <path d="M2 18h4" />
                                        <rect width="16" height="20" x="4" y="2" rx="2" />
                                        <path d="M9.5 8h5" />
                                        <path d="M9.5 12H16" />
                                        <path d="M9.5 16H14" />
                                    </svg>
                                    Tips
                                </button>
                                <div class="uk-dropdown dropdown-content" id="dropdown"
                                    uk-dropdown="mode: click; pos: bottom-right">
                                    <ul class="uk-dropdown-nav uk-nav">
                                        <li><a href="#"
                                                onclick="changeLabel('Tips', 'Total tips'); toggleDropdown()">Tips</a>
                                        </li>
                                        <li><a href="#"
                                                onclick="changeLabel('Reviews', 'Total reviews'); toggleDropdown()">Reviews</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div style="margin-bottom: 35px; margin-top: 15px;" class="p-4 relative min-h-[80%]">
                            <div class="chart-inner-container">
                                <canvas id="TotalTipsChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="rounded-lg border card shadow-sm h-full overflow-hidden chart-container-3 animated-on-load animate-tip-analytics-graph"
                        data-v0-t="card">
                        <div class="flex justify-between items-start p-4 border-b card">
                            <div class="flex flex-col">
                                <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">
                                    Tip Analytics</h3>
                                <p class="text-sm text-muted-foreground mt-1">Tipped vs Non-tipped guests</p>
                            </div>
                            <a href="/settings">
                                <button style="margin-top: 2px;"
                                    class="uk-button uk-button-default card universal-hover">
                                    <svg xmlns="http://www.w3.org/2000/svg" style="margin-right: 5px;" width="18"
                                        height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-arrow-up-left">
                                        <path d="M7 17V7h10" />
                                        <path d="M17 17 7 7" />
                                    </svg>
                                    Config
                                </button>
                            </a>
                        </div>
                        <div class="p-4 pt-0 h-full flex flex-col relative">
                            <div class="relative w-11/12 mx-auto h-[212px] flex items-center justify-center mb-6">
                                <div class="w-full h-full overflow-hidden">
                                    <svg viewBox="0 0 200 100" class="w-full h-full"
                                        preserveAspectRatio="xMidYMid meet">
                                        <!-- Grey styling circle -->
                                        <path id="greyStylingCircle" d="M10 100 A 90 90 0 0 1 190 100" fill="none"
                                            stroke="#F3F4F6" stroke-width="6" />

                                        <!-- Main half circles -->
                                        <path id="backgroundSemiCircle" d="M20 100 A 80 80 0 0 1 180 100" fill="none"
                                            stroke="#9ca3af" stroke-width="5"
                                            class="transition-all duration-300 hover:filter hover:brightness-105"
                                            filter="url(#shadow)" />
                                        <path id="foregroundSemiCircle" d="M20 100 A 80 80 0 0 1 180 100" fill="none"
                                            stroke="#151519" stroke-width="5" stroke-dasharray="188 251"
                                            class="transition-all duration-300 hover:filter hover:brightness-105"
                                            filter="url(#shadow)" />

                                        <!-- Define shadow filter -->
                                        <defs>
                                            <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                                                <feDropShadow dx="0" dy="1" stdDeviation="2" flood-opacity="0.1" />
                                            </filter>
                                        </defs>
                                    </svg>
                                </div>
                                <div class="absolute inset-0 flex flex-col items-center justify-center"
                                    style="padding-top: 70px;">
                                    <div
                                        class="flex items-center justify-center rounded-full p-1.5 mb-3 border border-gray-300 svg-icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-blend">
                                            <circle cx="9" cy="9" r="7" />
                                            <circle cx="15" cy="15" r="7" />
                                        </svg>
                                    </div>
                                    <span class="text-lg font-semibold ">2,324</span>
                                    <p class="text-xs mt-1">Total tips</p>
                                </div>
                            </div>
                            <div class="flex justify-between w-full">
                                <div class="flex items-center">
                                    <!-- Add an ID for the first legend bar -->
                                    <div id="legendTipped" class="w-1 h-10 rounded-full mr-2"
                                        style="background-color: #16181d;"></div>
                                    <div>
                                        <span class="text-sm font-semibold ">1,809</span>
                                        <p class="text-xs ">Tipped users</p>
                                    </div>
                                </div>
                                <div class="flex items-center justify-end">
                                    <div class="text-right mr-2">
                                        <span class="text-sm font-semibold">515</span>
                                        <p class="text-xs ">Non tipped users</p>
                                    </div>
                                    <!-- Add an ID for the second legend bar -->
                                    <div id="legendNonTipped" class="w-1 h-10 rounded-full"
                                        style="background-color: #ABBDD3;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <style>
                        @keyframes fillAnimation {
                            0% {
                                stroke-dasharray: 0 251;
                            }

                            100% {
                                stroke-dasharray: 188 251;
                            }
                        }

                        svg path:nth-child(3) {
                            animation: fillAnimation 1.5s ease-out forwards;
                        }
                    </style>


            </main>
        </div>
    </div>
    </div>
    <script>
        am5.ready(function () {

            // Create root element
            var root = am5.Root.new("pieChartDiv");

            // Remove padding from root container
            root.container.setAll({
                paddingLeft: 0,
                paddingRight: 0,
                paddingTop: 0,
                paddingBottom: 0
            });

            // Set themes
            root.setThemes([
                am5themes_Animated.new(root)
            ]);
            root._logo.dispose();

            // Create chart
            var chart = root.container.children.push(am5percent.PieChart.new(root, {
                startAngle: 180,
                endAngle: 360,
                layout: root.verticalLayout,
                innerRadius: am5.percent(75), // Reduced from 60% to 50%
                paddingLeft: 0,
                paddingRight: 0,
                paddingTop: 0,
                paddingBottom: 0
            }));

            // Create series
            // Create series
            var series = chart.series.push(am5percent.PieSeries.new(root, {
                startAngle: 180,
                endAngle: 360,
                valueField: "value",
                categoryField: "category",
                alignLabels: false,
                sliceSpacing: 0,
            }));

            // Disable labels and ticks
            series.labels.template.set("visible", false);
            series.ticks.template.set("visible", false);

            series.states.create("hidden", {
                startAngle: 180,
                endAngle: 180,
            });

            // Add specific rendering settings to completely remove borders
            series.slices.template.setAll({
                cornerRadius: 5,          // Remove corner radius completely
                strokeWidth: 0,           // Zero stroke width
                stroke: undefined,        // Remove stroke definition entirely
                fillOpacity: 1,           // Full opacity
                tooltipText: "{category}: {value}",
                interactive: true
            });

            // Set data with colors
            series.data.setAll([
                { value: 10, category: "Whatsapp" },
                { value: 9, category: "Instagram" },
                { value: 6, category: "Messenger" },
                { value: 5, category: "Web chat" },
                { value: 4, category: "Voice bot" }
            ]);

            // Ensure each slice has its fill color set as its stroke color
            series.slices.each(function (slice, index) {
                var colors = [0x9f95fb, 0x43d7f2, 0xf6cf47, 0x45d3d2, 0xaabbd4];
                var color = am5.color(colors[index]);
                slice.set("fill", color);
                slice.set("stroke", color); // Match stroke to fill exactly
            });

            // series.appear(1000, 100);

        }); // end am5.ready()
    </script>
    <script>
        function updateSemiCircleColors() {
            const isPureBlack = document.body.classList.contains('pure-black');

            // Select the SVG paths by their IDs
            const greyStylingCircle = document.getElementById('greyStylingCircle');
            const backgroundSemiCircle = document.getElementById('backgroundSemiCircle');
            const foregroundSemiCircle = document.getElementById('foregroundSemiCircle');

            if (!greyStylingCircle || !backgroundSemiCircle || !foregroundSemiCircle) {
                console.log('One or more SVG elements not found');
                return;
            }

            if (isPureBlack) {
                // Pure black theme - use lighter colors
                greyStylingCircle.setAttribute('stroke', '#27272A'); // Darker background
                backgroundSemiCircle.setAttribute('stroke', '#71717A'); // Mid-tone gray
                foregroundSemiCircle.setAttribute('stroke', '#fafafa'); // White foreground
            } else {
                // Default theme - use original colors
                greyStylingCircle.setAttribute('stroke', '#F3F4F6');
                backgroundSemiCircle.setAttribute('stroke', '#9ca3af');
                foregroundSemiCircle.setAttribute('stroke', '#151519');
            }

            // Update legend colors dynamically to match the chart's stroke colors
            const legendTipped = document.getElementById('legendTipped');
            const legendNonTipped = document.getElementById('legendNonTipped');
            if (legendTipped && legendNonTipped) {
                legendTipped.style.backgroundColor = isPureBlack ? '#fafafa' : '#151519';
                legendNonTipped.style.backgroundColor = isPureBlack ? '#71717A' : '#9ca3af';
            }

            console.log('Semi-circle and legend colors updated:', isPureBlack ? 'Pure black theme' : 'Default theme');
        }

        // Call the function when the page loads
        document.addEventListener('DOMContentLoaded', function () {
            updateSemiCircleColors();

            // Watch for theme changes
            const observer = new MutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        updateSemiCircleColors();
                    }
                });
            });

            observer.observe(document.body, {
                attributes: true
            });
        });
    </script>
    <script>
        let totalTipsChart = null;

        async function initializeTotalTipsChart() {
            // Updated sales data for each day (Monday to Sunday)
            const salesData = [1999, 1500, 1200, 2400, 3000, 1800, 1500];

            // Check if body has pure-black class and set color accordingly
            const isPureBlack = document.body.classList.contains('pure-black');
            const chartBarColor = isPureBlack ? '#fafafa' : '#18181b'; // Removed CSS variable fallback for simplicity

            // Add this line to set grid color based on theme
            const gridColor = isPureBlack ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

            console.log('Chart color:', isPureBlack ? 'Using white bars (#fafafa)' : 'Using dark bars (#18181b)');

            const salesChartData = {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [
                    {
                        data: salesData,
                        backgroundColor: chartBarColor,
                        borderColor: chartBarColor,
                        borderWidth: 0, // Remove border completely
                        borderRadius: {
                            topLeft: 5,
                            topRight: 5,
                            bottomLeft: 5,
                            bottomRight: 5
                        },
                        borderSkipped: false
                    }
                ]
            };

            const ctx = document.getElementById('TotalTipsChart').getContext('2d');

            // Destroy existing chart if it exists
            if (totalTipsChart) {
                totalTipsChart.destroy();
            }

            // Create a high-quality chart with crisp edges
            totalTipsChart = new Chart(ctx, {
                type: 'bar',
                data: salesChartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    layout: {
                        padding: {
                            left: 10,
                            right: 10,
                            top: 20,
                            bottom: 20
                        }
                    },
                    barPercentage: 0.9,
                    categoryPercentage: 0.8,
                    devicePixelRatio: 2,
                    scales: {
                        x: {
                            grid: {
                                display: false,
                                color: 'transparent'
                            },
                            border: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            min: 0,
                            max: 3000,
                            grid: {
                                color: gridColor, // Use theme-aware grid color
                                borderDash: [5, 5]
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            enabled: false,
                            external: createCustomTooltipForTips
                        },
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function () {
            // Initial chart creation
            setTimeout(() => {
                initializeTotalTipsChart();
            }, 100); // Small delay to ensure all theme classes are applied

            // Watch for theme changes and update chart
            const observer = new MutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        // Brief timeout to ensure theme is fully applied
                        setTimeout(() => {
                            initializeTotalTipsChart();
                        }, 100);
                    }
                });
            });

            observer.observe(document.body, {
                attributes: true
            });
        });

    </script>
    <script>
        async function fetchTPPData() {
            try {
                const response = await fetch('/fetch-tpp');
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                const data = await response.json();
                return data[0]; // Assuming the data is an array and we need the first object
            } catch (error) {
                console.error('Error fetching TPP data:', error);
                return null;
            }
        }

        function updatePieChart(chart, data) {
            chart.data = [
                {
                    product: "Food",
                    sales: parseFloat(data.food_amount),
                    color: am4core.color("#e76e51")
                },
                {
                    product: "Beverage",
                    sales: parseFloat(data.beverage_amount),
                    color: am4core.color("#2c9b91")
                },
                {
                    product: "Spa",
                    sales: parseFloat(data.spa_sales),
                    color: am4core.color("#f2a262")
                },
                {
                    product: "Massage",
                    sales: parseFloat(data.massage_sales),
                    color: am4core.color("#e7c468")
                },
                {
                    product: "Room Bookings",
                    sales: parseFloat(data.room_sales),
                    color: am4core.color("#264754")
                }
            ];
        }

        am4core.ready(async function () {
            // Create chart instance
            am4core.useTheme(am4themes_animated);

            var chart = am4core.create("chartdiv", am4charts.PieChart);
            chart.radius = am4core.percent(60); // Adjust this percentage as needed

            // Fetch data and update chart
            const tppData = await fetchTPPData();
            if (tppData) {
                updatePieChart(chart, tppData);
            }

            // Add and configure Series
            var pieSeries = chart.series.push(new am4charts.PieSeries());
            pieSeries.dataFields.value = "sales";
            pieSeries.dataFields.category = "product";
            pieSeries.slices.template.propertyFields.fill = "color";

            // Disable logo
            chart.logo.disabled = true;

            // Set inner radius for donut shape
            chart.innerRadius = am4core.percent(30);

            // Add spacing between slices and make edges curved
            pieSeries.slices.template.padding = 1;
            pieSeries.slices.template.cornerRadius = 5;
            pieSeries.slices.template.fillOpacity = 1;
            pieSeries.slices.template.strokeWidth = 0;
            pieSeries.slices.template.stroke = am4core.color("#ffffff");

            // Configure labels
            pieSeries.labels.template.disabled = false;
            pieSeries.labels.template.text = "{category}: €{value}";
            pieSeries.labels.template.radius = 1;
            pieSeries.labels.template.fontSize = 12;
            pieSeries.labels.template.maxWidth = 80;
            pieSeries.labels.template.wrap = true;

            // Configure ticks
            pieSeries.ticks.template.disabled = false;
            pieSeries.ticks.template.strokeOpacity = 0.7;
            pieSeries.ticks.template.strokeWidth = 2;
            pieSeries.ticks.template.length = 20; // Default length for all lines

            // Adjust the length for the "Spa" label
            pieSeries.ticks.template.adapter.add("length", function (length, target) {
                if (target.dataItem && target.dataItem.category === "Spa") {
                    return 5; // Adjusted length for the "Spa" line
                }
                return length;
            });

            // Add tooltips
            pieSeries.slices.template.tooltipText = "{category}: €{value}";

            // Function to update chart colors based on theme
            function updateChartColors() {
                var body = document.body;
                var isDarkTheme = body.classList.contains('pure-black') ||
                    body.classList.contains('dark-gray');

                // Update text color for labels and ticks
                pieSeries.labels.template.fill = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
                pieSeries.ticks.template.stroke = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
            }

            // Initial color update
            updateChartColors();

            // Listen for theme changes
            var observer = new MutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    if (mutation.type === "attributes" && mutation.attributeName === "class") {
                        updateChartColors();
                    }
                });
            });

            observer.observe(document.body, {
                attributes: true
            });
        });


    </script>
    <script>
        function fetchCustomers() {
            fetch('/customers')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    const tableBody = document.getElementById('customerTableBody');
                    tableBody.innerHTML = ''; // Clear existing rows

                    // Sort data in reverse order (latest first)
                    data.sort((a, b) => b.id - a.id);

                    data.forEach(customer => {
                        const row = document.createElement('tr');
                        row.className = 'border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted';

                        row.innerHTML = `
                                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0 font-medium">${customer.Name}</td>
                                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.room_number}</td>
                                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.phone_number}</td>
                                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.language}</td>
                                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.order}</td>
                                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0">${customer.Platform}</td>
                                <td class="p-4 align-middle [&amp;:has([role=checkbox])]:pr-0 text-right">${customer.spend}</td>
                            `;

                        tableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error('Error fetching customers:', error);
                });
        }

        // Initial fetch
        fetchCustomers();

        // Refresh every 5 seconds
        setInterval(fetchCustomers, 60000);

        async function fetchAIvsManualData() {
            try {
                console.log("FETCHAIVSMANUALDATA")
                const response = await fetch('/aivsmanual');
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('Error fetching AI vs Manual data:', error);
                return null;
            }
        }

        function calculateStepSize(maxValue) {
            return Math.ceil(maxValue / 5);
        }

    </script>
    <script>
        async function initializeChart() {
            const interactionData = await fetchAIvsManualData();
            if (!interactionData) return;

            const aiData = interactionData.ai_inter[0];
            const manualData = interactionData.manual_inter[0];

            const aiInteractionsData = [
                aiData.mon, aiData.tue, aiData.wed, aiData.thu,
                aiData.fri, aiData.sat, aiData.sun
            ];

            const manualInteractionsData = [
                manualData.mon, manualData.tue, manualData.wed, manualData.thu,
                manualData.fri, manualData.sat, manualData.sun
            ];

            const maxDataValue = Math.max(...aiInteractionsData, ...manualInteractionsData);
            const stepSize = calculateStepSize(maxDataValue);

            // Check if dark mode is active
            const isPureBlack = document.body.classList.contains('pure-black');

            // Set grid color based on theme - use white with opacity for dark mode
            const gridColor = isPureBlack ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

            const interactionChartData = {
                labels: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
                datasets: [
                    {
                        label: 'AI help (Automated)',
                        data: aiInteractionsData,

                        backgroundColor: '#e21d48', // Solid color
                        borderRadius: {
                            topLeft: 5,
                            topRight: 5,
                            bottomLeft: 5,
                            bottomRight: 5
                        },
                        borderSkipped: false
                    },
                    {
                        label: 'Staff help (manual)',
                        data: manualInteractionsData,
                        backgroundColor: '#5ea5f6', // Solid color
                        borderRadius: {
                            topLeft: 5,
                            topRight: 5,
                            bottomLeft: 5,
                            bottomRight: 5
                        },
                        borderSkipped: false
                    }
                ]
            };

            const ctx = document.getElementById('salesOverviewChart').getContext('2d');
            const interactionChart = new Chart(ctx, {
                type: 'bar',
                data: interactionChartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    barPercentage: 0.9,
                    categoryPercentage: 0.8,
                    scales: {
                        x: {
                            grid: {
                                display: false,
                                color: 'transparent'
                            }
                            ,
                            border: { display: false }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: gridColor,
                                borderDash: [5, 5]
                            },
                            border: { display: false },
                            ticks: {
                                stepSize: stepSize
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            enabled: false,
                            external: createCustomTooltipForTips
                        },
                        legend: {
                            display: false // Hide the default legend
                        }
                    }
                }
            });

            // Add theme change observer to update grid colors when theme changes
            const observer = new MutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const isDarkMode = document.body.classList.contains('pure-black');
                        const newGridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

                        // Update grid color
                        interactionChart.options.scales.y.grid.color = newGridColor;
                        interactionChart.update();
                    }
                });
            });

            observer.observe(document.body, {
                attributes: true
            });

            // Set interval to update the chart every minute
            setInterval(() => {
                updateChart(interactionChart);
            }, 60000);
        }

        document.addEventListener('DOMContentLoaded', initializeChart);
    </script>
    <script>
        function externalCustomTooltip(context) {
            let tooltipEl = document.getElementById('chartjs-tooltip');
            if (!tooltipEl) {
                tooltipEl = document.createElement('div');
                tooltipEl.id = 'chartjs-tooltip';
                tooltipEl.innerHTML = '<table></table>';
                // Include left and top in the transition for smooth repositioning
                tooltipEl.style.transition = 'left 0.3s ease, top 0.3s ease, opacity 0.3s ease';
                tooltipEl.style.position = 'absolute';
                tooltipEl.style.pointerEvents = 'none';
                tooltipEl.style.whiteSpace = 'nowrap';
                document.body.appendChild(tooltipEl);
            }
            const tooltipModel = context.tooltip;
            if (tooltipModel.opacity === 0) {
                tooltipEl.style.opacity = 0;
                return;
            }

            // Build the inner HTML
            let innerHtml = '<table>';
            if (tooltipModel.title) {
                tooltipModel.title.forEach(title => {
                    innerHtml += `
            <tr>
                <th style="text-align:left; padding:2px 4px; border-bottom:1px solid var(--dropdown-border, #dee2e6); font-weight:600; color: var(--dropdown-text, #111827);">
                    ${title}
                </th>
            </tr>`;
                });
            }
            if (tooltipModel.body) {
                tooltipModel.body.forEach((body, i) => {
                    const legend = tooltipModel.labelColors && tooltipModel.labelColors[i];
                    const legendColor = legend && legend.backgroundColor ? legend.backgroundColor : 'transparent';
                    const bodyText = body.lines.join('');
                    innerHtml += `
            <tr>
                <td style="display:flex; align-items:center; padding:4px 8px; color: var(--dropdown-text, #111827);">
                    <span style="background:${legendColor}; width:10px; height:10px; border-radius:50%; display:inline-block; margin-right:5px;"></span>
                    <span>${bodyText}</span>
                </td>
            </tr>`;
                });
            }
            innerHtml += '</table>';
            tooltipEl.querySelector('table').innerHTML = innerHtml;

            // Calculate initial position based on caretX and caretY
            const position = context.chart.canvas.getBoundingClientRect();
            let left = position.left + window.pageXOffset + tooltipModel.caretX;
            let top = position.top + window.pageYOffset + tooltipModel.caretY;

            // Temporarily set the position to measure size
            tooltipEl.style.opacity = 1;
            tooltipEl.style.left = left + 'px';
            tooltipEl.style.top = top + 'px';
            tooltipEl.style.padding = tooltipModel.options.padding + 'px';

            // Measure the tooltip's bounding rectangle
            const tooltipRect = tooltipEl.getBoundingClientRect();

            // If tooltip overflows to the left, reposition it using the right side of the caret
            if (tooltipRect.left < 0) {
                left = position.left + window.pageXOffset + tooltipModel.caretX + 10;
            }
            // If tooltip overflows to the right of the viewport, reposition it to the left
            if (tooltipRect.right > window.innerWidth) {
                left = position.left + window.pageXOffset + tooltipModel.caretX - tooltipRect.width - 10;
            }
            tooltipEl.style.left = left + 'px';
            tooltipEl.style.top = top + 'px';
        }
    </script>
    <style>
        .chart-container-2 {
            height: 370px;
            /* Custom height for the second chart */
            position: relative;
            /* Ensure positioning context */
            overflow: hidden;
            /* Add overflow control */
        }

        .chart-container-2 .chart-inner-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
        }
    </style>
</body>

</html>