<script src="https://cdn.tailwindcss.com"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
<link rel="stylesheet" href="../static/styles/custom.css">
<link rel="stylesheet" href="../static/styles/loadinganimations.css">
<script src="../static/js/languagetranslator.js" defer></script>
<script src="../static/js/loading.js" defer></script>
<script src="../static/js/themes.js" defer></script>
<link rel="stylesheet" href="../static/styles/scrollbar.css">

<link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/slate.min.css"/>
<script src="https://unpkg.com/@phosphor-icons/web"></script>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
<script src="https://cdn.amcharts.com/lib/4/core.js"></script>
<script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
<script src="https://kit.fontawesome.com/c6c71387b9.js" crossorigin="anonymous"></script>
<script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
<link rel="preconnect" href="https://rsms.me/" />
<link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
<link rel="stylesheet" href="../static/styles/custom.css">
<script src="../static/js/languagetranslator.js" defer></script>
<script src="https://cdn.jsdelivr.net/npm/uikit@latest/dist/js/uikit.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/uikit@latest/dist/js/uikit-icons.min.js"></script>
<link rel="stylesheet" href="../static/styles/loadinganimations.css">
<link rel="stylesheet" href="../static/styles/chartdropdown.css">
<script src="../static/js/loading.js" defer></script>
<script src="../static/js/chartsdropsdown.js" defer></script>
<script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
<script src="https://cdn.amcharts.com/lib/4/themes/dark.js"></script>
<link rel="stylesheet" href="../static/styles/scrollbar.css">
<link rel="stylesheet" href="https://unpkg.com/franken-wc@0.0.6/dist/css/slate.min.css"/>
<body class="light">
  <div id="loading-overlay" class="loading-overlay">
    <div class="typing-indicator">
      <div class="typing-circle"></div>
      <div class="typing-circle"></div>
      <div class="typing-circle"></div>
      <div class="typing-shadow"></div>
      <div class="typing-shadow"></div>
      <div class="typing-shadow"></div>
    </div>
  </div>
  <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr] overflow-hidden">
    {% include 'sidebar.html' %}
    <div class="flex flex-col">
      <header class="flex h-14 lg:h-[60px] items-center gap-4 border-b  px-6 card justify-between">
        <a class="lg:hidden" href="#">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                <path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9"></path>
                <path d="M12 3v6"></path>
            </svg>
            <span class="sr-only">Home</span>

        </a>
        <h1 class="font-semibold text-lg dark:">JUST A TEST LIL BRO</h1>    
        {% include 'topright.html' %}            
    </header>
</body>
</html>