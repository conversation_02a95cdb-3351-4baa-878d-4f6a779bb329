# Download the helper library from https://www.twilio.com/docs/python/install
import os
from twilio.rest import Client
import json
# Remove this line if these imports aren't needed

# Find your Account SID and Auth Token at twilio.com/console
# and set the environment variables. See http://twil.io/secure
TWILIO_ACCOUNT_SID="**********************************"
TWILIO_AUTH_TOKEN="005d910f85546392a91f58a3878c437c"

date_sid = "HX8373674c94d02d0576fbc9de4bbba938" # New SID for date selection

client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

# Placeholder values for the content variables.
# Please replace these with your actual desired text.
content_vars = {
    '1': 'Please select a time slot for your spa session:',  # Body
    '2': 'Available Times',          # List button text
    '3': '9:00 AM - 10:00 AM',       # Item name for spa_time_slot_1
    '4': '10:00 AM - 11:00 AM',      # Item name for spa_time_slot_2
    '5': '11:00 AM - 12:00 PM',      # Item name for spa_time_slot_3
    '6': '1:00 PM - 2:00 PM'         # Item name for spa_time_slot_4
}

try:
    message = client.messages.create(
        content_sid=date_sid,
        from_='whatsapp:+***********',  # Replace with your Twilio WhatsApp number
        to='whatsapp:+************',    # Replace with the recipient's WhatsApp number
        content_variables=json.dumps(content_vars)
    )
    print(f"Message sent successfully! SID: {message.sid}")
except Exception as e:
    print(f"Error sending message: {e}")