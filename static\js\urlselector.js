document.addEventListener('DOMContentLoaded', function() {
    const selector = document.querySelector('.url-type-selector');
    const btn = selector.querySelector('.url-type-selector-btn');
    const menu = selector.querySelector('.url-type-selector-menu');
    const options = menu.querySelectorAll('.url-type-option');

    btn.addEventListener('click', function() {
        menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
    });

    options.forEach(option => {
        option.addEventListener('click', function() {
            btn.textContent = this.textContent;
            menu.style.display = 'none';
        });
    });

    document.addEventListener('click', function(event) {
        if (!selector.contains(event.target)) {
            menu.style.display = 'none';
        }
    });
});