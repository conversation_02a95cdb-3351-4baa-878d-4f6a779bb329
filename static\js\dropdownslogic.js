document.addEventListener('DOMContentLoaded', function() {
    const filterBtn = document.getElementById('task-filter-btn');
    const filterMenu = document.getElementById('task-filter-menu');
  
    filterBtn.addEventListener('click', function(e) {
      e.stopPropagation();
      const isExpanded = filterBtn.getAttribute('aria-expanded') === 'true';
      filterBtn.setAttribute('aria-expanded', !isExpanded);
      filterMenu.style.display = isExpanded ? 'none' : 'block';
    });
  
    document.addEventListener('click', function(e) {
      if (!filterBtn.contains(e.target) && !filterMenu.contains(e.target)) {
        filterBtn.setAttribute('aria-expanded', 'false');
        filterMenu.style.display = 'none';
      }
    });
  
    function filterTasks(type) {
      console.log('Filtering tasks:', type);
      // Implement your filtering logic here
      filterBtn.setAttribute('aria-expanded', 'false');
      filterMenu.style.display = 'none';
    }
  
    // Make filterTasks function globally accessible
    window.filterTasks = filterTasks;
  });


  function setupDropdownListeners() {
    const optionsButtons = document.querySelectorAll('.user-options-btn');
    let openDropdown = null;

    optionsButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const dropdownMenu = button.nextElementSibling;

            if (openDropdown && openDropdown !== dropdownMenu) {
                openDropdown.classList.add('hidden');
                openDropdown.previousElementSibling.setAttribute('aria-expanded', 'false');
            }

            dropdownMenu.classList.toggle('hidden');
            button.setAttribute('aria-expanded', dropdownMenu.classList.contains('hidden') ? 'false' : 'true');

            if (!dropdownMenu.classList.contains('hidden')) {
                openDropdown = dropdownMenu;
            } else if (openDropdown === dropdownMenu) {
                openDropdown = null;
            }
        });
    });

    document.addEventListener('click', () => {
        if (openDropdown) {
            openDropdown.classList.add('hidden');
            openDropdown.previousElementSibling.setAttribute('aria-expanded', 'false');
            openDropdown = null;
        }
    });

    document.querySelectorAll('.user-options-menu').forEach(menu => {
        menu.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    });
}